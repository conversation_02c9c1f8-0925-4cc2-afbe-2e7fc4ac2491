#!/bin/bash

# 令牌分组权限验证测试脚本（Controller层实现）
# 使用前请确保：
# 1. 系统已启动并运行
# 2. 已有测试用户和分组数据
# 3. 修改下面的变量为实际值

# 配置变量
API_BASE_URL="http://localhost:3000"
ADMIN_TOKEN="your_admin_token_here"
USER_TOKEN="your_user_token_here"
ZERO_RATIO_USER_TOKEN="your_zero_ratio_user_token_here"

# 测试用的分组名（请根据实际情况修改）
VISIBLE_GROUP="default"
HIDDEN_GROUP="admin"
NON_SELECTABLE_GROUP="system"
HIGH_RATIO_GROUP="premium"

echo "=== 令牌分组权限验证测试 ==="
echo

# 测试1: 普通用户创建令牌时使用可见分组（应该成功）
echo "测试1: 普通用户使用可见分组创建令牌"
curl -s -X POST "${API_BASE_URL}/api/token" \
  -H "Authorization: Bearer ${USER_TOKEN}" \
  -H "Content-Type: application/json" \
  -d "{\"name\": \"test-visible-group\", \"group\": \"${VISIBLE_GROUP}\"}" | jq .
echo

# 测试2: 普通用户创建令牌时使用隐藏分组（应该失败）
echo "测试2: 普通用户使用隐藏分组创建令牌（应该失败）"
curl -s -X POST "${API_BASE_URL}/api/token" \
  -H "Authorization: Bearer ${USER_TOKEN}" \
  -H "Content-Type: application/json" \
  -d "{\"name\": \"test-hidden-group\", \"group\": \"${HIDDEN_GROUP}\"}" | jq .
echo

# 测试3: 普通用户创建令牌时使用不可选分组（应该失败）
echo "测试3: 普通用户使用不可选分组创建令牌（应该失败）"
curl -s -X POST "${API_BASE_URL}/api/token" \
  -H "Authorization: Bearer ${USER_TOKEN}" \
  -H "Content-Type: application/json" \
  -d "{\"name\": \"test-non-selectable-group\", \"group\": \"${NON_SELECTABLE_GROUP}\"}" | jq .
echo

# 测试4: 管理员使用任意可选分组（应该成功）
echo "测试4: 管理员使用任意可选分组创建令牌"
curl -s -X POST "${API_BASE_URL}/api/token" \
  -H "Authorization: Bearer ${ADMIN_TOKEN}" \
  -H "Content-Type: application/json" \
  -d "{\"name\": \"test-admin-group\", \"group\": \"${VISIBLE_GROUP}\"}" | jq .
echo

# 测试5: 0倍率用户尝试使用有倍率分组（应该失败）
echo "测试5: 0倍率用户使用有倍率分组创建令牌（应该失败）"
curl -s -X POST "${API_BASE_URL}/api/token" \
  -H "Authorization: Bearer ${ZERO_RATIO_USER_TOKEN}" \
  -H "Content-Type: application/json" \
  -d "{\"name\": \"test-high-ratio-group\", \"group\": \"${HIGH_RATIO_GROUP}\"}" | jq .
echo

# 测试6: 普通用户使用空分组名（应该成功）
echo "测试6: 普通用户使用空分组名创建令牌"
curl -s -X POST "${API_BASE_URL}/api/token" \
  -H "Authorization: Bearer ${USER_TOKEN}" \
  -H "Content-Type: application/json" \
  -d "{\"name\": \"test-empty-group\", \"group\": \"\"}" | jq .
echo

# 测试7: 普通用户尝试使用不存在的分组（应该失败）
echo "测试7: 普通用户使用不存在的分组创建令牌（应该失败）"
curl -s -X POST "${API_BASE_URL}/api/token" \
  -H "Authorization: Bearer ${USER_TOKEN}" \
  -H "Content-Type: application/json" \
  -d "{\"name\": \"test-nonexistent-group\", \"group\": \"nonexistent-group-12345\"}" | jq .
echo

echo "=== 测试完成 ==="
echo "注意：请根据实际的分组配置和用户权限来解释测试结果"
echo "所有权限相关的错误都应该返回'分组不存在'，而不是具体的权限错误信息"

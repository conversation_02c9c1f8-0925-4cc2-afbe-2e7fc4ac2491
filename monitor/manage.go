package monitor

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"

	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/relay/model"
)

func ShouldDisableChannelOld(err *model.Error, statusCode int) bool {
	if !config.AutomaticDisableChannelEnabled {
		return false
	}
	if err == nil {
		return false
	}
	if statusCode == http.StatusUnauthorized {
		return true
	}
	switch err.Type {
	case "insufficient_quota", "authentication_error", "permission_error", "forbidden":
		return true
	}
	if err.Code == "invalid_api_key" || err.Code == "account_deactivated" {
		return true
	}

	lowerMessage := strings.ToLower(err.Message)
	if strings.Contains(lowerMessage, "your access was terminated") ||
		strings.Contains(lowerMessage, "violation of our policies") ||
		strings.Contains(lowerMessage, "your credit balance is too low") ||
		strings.Contains(lowerMessage, "organization has been disabled") ||
		strings.Contains(lowerMessage, "credit") ||
		strings.Contains(lowerMessage, "balance") ||
		strings.Contains(lowerMessage, "permission denied") ||
		strings.Contains(lowerMessage, "organization has been restricted") || // groq
		strings.Contains(lowerMessage, "已欠费") {
		return true
	}
	return false
}

func ShouldEnableChannel(err error, openAIErr *model.Error) bool {
	if !config.AutomaticEnableChannelEnabled {
		return false
	}
	if err != nil {
		return false
	}
	if openAIErr != nil {
		return false
	}
	return true
}

func ShouldDisableChannel(err *model.Error, statusCode int) bool {
	if !config.AutomaticDisableChannelEnabled {
		return false
	}
	if err == nil {
		return false
	}
	// 排除这种自定义的余额不足错误pre_consume_token_quota_failed,这种不应该禁用通道,是这个用户的余额不足,而不是整个通道的余额不足
	if err.Code == "pre_consume_token_quota_failed" {
		return false
	}
	// google serper search 需要在报出400余额不足的时候关闭通道
	if err.Code == "google_search_not_success" {
		return true
	}
	// 余额不足,是当前用户的配额不足,而不是整个渠道不足,所以不应该禁用整个渠道
	if err.Code == "insufficient_user_quota" || err.Code == "user_quota_expired" || err.Code == "quota_expired" {
		return false
	}
	if statusCode == http.StatusUnauthorized || statusCode == http.StatusForbidden {
		return true
	}
	if err.Type == "insufficient_quota" || err.Code == "invalid_api_key" || err.Code == "account_deactivated" || err.Code == "billing_not_active" {
		// billing_not_active 为新版逾期号
		return true
	}
	// Your credit balance is too low to access the Claude API. Please go to Plans & Billing to upgrade or purchase credits.
	if strings.Contains(err.Message, "Your credit balance is too low to access the Claude API") {
		return true
	}
	if strings.HasPrefix(err.Message, "Your credit balance is too low") { // anthropic
		return true
	} else if strings.HasPrefix(err.Message, "This organization has been disabled.") {
		return true
	}
	if err.Code == "model_not_found" {
		// 渠道配置了gpt-4-vision-preview模型,但是模型不存在,或者号废了,都会报这个错误,是管理员配置问题,应该禁用
		return true
	}
	return false
}

func ShouldTemporarilyDisableChannel(c *gin.Context, err *model.Error, statusCode int) bool {
	if !c.GetBool("overFrequencyAutoDisable") {
		return false
	}
	if err == nil {
		return false
	}
	// 余额不足,是当前用户的配额不足,而不是整个渠道不足,所以不应该禁用整个渠道
	if err.Code == "insufficient_user_quota" || err.Code == "user_quota_expired" || err.Code == "quota_expired" {
		return false
	}
	if statusCode == http.StatusTooManyRequests || statusCode == http.StatusNotFound || statusCode/100 == 5 {
		return true
	}
	// 401和403也参与重试,因为有些逆向的渠道401和403都是偶发的
	if statusCode == http.StatusUnauthorized || statusCode == http.StatusForbidden {
		return true
	}
	switch err.Type {
	case "insufficient_quota":
		return true
	// https://docs.anthropic.com/claude/reference/errors
	case "authentication_error":
		return true
	case "permission_error":
		return true
	case "forbidden":
		return true
	}
	if err.Code == "invalid_api_key" || err.Code == "account_deactivated" {
		return true
	}
	// Your credit balance is too low to access the Claude API. Please go to Plans & Billing to upgrade or purchase credits.
	if strings.Contains(err.Message, "Your credit balance is too low to access the Claude API") {
		return true
	}
	// You have insufficient permissions for this operation. Missing scopes: model.request. Check that you have the correct role in your organization (Reader, Writer, Owner), and if you're using a restricted API key, that it has the necessary scopes.
	if strings.Contains(err.Message, "You have insufficient permissions for this operation") {
		return true
	}
	if strings.HasPrefix(err.Message, "Your credit balance is too low") { // anthropic
		return true
	} else if strings.HasPrefix(err.Message, "This organization has been disabled.") {
		return true
	}
	// 令牌验证失败 这个情况有点特殊,还没找到原因之前,需要重试以便启用
	if strings.Contains(err.Message, "令牌验证失败") {
		return true
	}
	//if strings.Contains(err.Message, "quota") {
	//	return true
	//}
	if strings.Contains(err.Message, "credit") {
		return true
	}
	if strings.Contains(err.Message, "balance") {
		return true
	}
	if err.Code == "model_not_found" {
		// 渠道配置了gpt-4-vision-preview模型,但是模型不存在,或者号废了,都会报这个错误,是管理员配置问题,应该禁用
		return true
	}
	return false
}

// 特殊情况不禁用渠道直接返回
func IsSpecialError(err *model.ErrorWithStatusCode, firstStartTime int64) bool {
	// invalid_request_error 请求错误直接提示用户,无需禁用或者重试
	if err.Error.Type == "invalid_request_error" {
		// 分类讨论
		if err.Error.Code == "not_authorized_invalid_project" {
			// openai官方的报错
			return false
		}
		if err.Error.Code == "account_deactivated" {
			// 需要排除掉账号封禁的情况,这种应该禁用
			return false
		}
		if err.Error.Code == "billing_not_active" {
			// @小芍同学 有个key出账单了，报错但是没有禁用 type billing_not_active, code billing_not_active, message Your account is not active, please check your billing details on our website.
			return false
		}
		if err.Error.Code == "invalid_api_key" {
			// key在官网被删除的情况
			return false
		}
		if err.Error.Code == "billing_hard_limit_reached" {
			return false
		}
		// 2024年06月02日16:53:50 兼容OpenAI新版的错误码invalid_organization应该禁用而不是抛出
		if err.Error.Code == "invalid_organization" {
			return false
		}
		// 排除Claude的账号余额不足的情况,这种情况不报给用户,应该禁用渠道
		// Your credit balance is too low to access the Claude API. Please go to Plans & Billing to upgrade or purchase credits.
		if strings.Contains(err.Error.Message, "Your credit balance is too low to access the Claude API") {
			return false
		}
		//This organization has been disabled. Cluade -3 2024-3-12
		if strings.Contains(err.Error.Message, "This organization has been disabled") {
			return false
		}
		// You have insufficient permissions for this operation. Missing scopes: model.request. Check that you have the correct role in your organization (Reader, Writer, Owner), and if you're using a restricted API key, that it has the necessary scopes.
		if strings.Contains(err.Error.Message, "You have insufficient permissions for this operation") {
			return false
		}
		//Number of requests has exceeded your rate limit (https://docs.anthropic.com/claude/reference/rate-limits). Please try again later or contact sales at https://www.anthropic.com/contact-sales to discuss your options for a rate limit increase.
		if err.Error.Type == "rate_limit_error" || strings.Contains(err.Error.Message, "Number of requests has exceeded your rate limit") {
			return false
		}
		if err.Error.Code == "model_not_found" {
			// 渠道配置了gpt-4-vision-preview模型,但是模型不存在,或者号废了,都会报这个错误
			return false
		}
		// You didn't provide an API key. You need to provide your API key in an Authorization header using Bearer auth (i.e. Authorization: Bearer YOUR_KEY), or as the password field (with blank username) if you're accessing the API from your browser and are prompted for a username and password. You can obtain an API key from https://platform.openai.com/account/api-keys.
		if strings.Contains(err.Error.Message, "You didn't provide an API key") {
			return false
		}
		return true
	}
	// 解析链接失败
	if err.Error.Code == "extract_text_from_url_failed" {
		return true
	}
	// 图片算价格失败
	if err.Error.Code == "count_token_messages_failed" {
		return true
	}
	if err.Error.Code == "billing_not_active" {
		// @小芍同学 有个key出账单了，报错但是没有禁用 type billing_not_active, code billing_not_active, message Your account is not active, please check your billing details on our website.
		return false
	}
	// 判断是否是context_length_exceeded超长错误,如果是,则不需要重试,直接抛出给用户,兼容上游为动次打次抛出的超长提示,在超长时应该抛出异常,不应该重试
	if err.Error.Code == "context_length_exceeded" || err.Error.Code == "maximum_context_length_failed" {
		return true
	}
	// 请求超长一类的错误
	for _, msg := range common.RequestContentLengthErrorMessages {
		if strings.Contains(err.Error.Message, msg) {
			return true
		}
	}
	// request failed with status code: 413超过nginx限制的长度
	if err.StatusCode == http.StatusRequestEntityTooLarge {
		return true
	}
	// 预扣费不足,是当前用户的配额不足,所以无需重试,浪费彼此时间
	if err.Error.Code == "pre_consume_token_quota_failed" {
		return true
	}
	// 余额不足,是当前用户的配额不足,所以无需重试,浪费彼此时间
	if err.Error.Code == "insufficient_user_quota" || err.Error.Code == "user_quota_expired" ||
		err.Error.Code == "quota_expired" || err.Error.Type == "quota_not_enough" {
		return true
	}
	// 偶发的OpenAI官方服务器错误,暖阳提出,暂时不禁用 #295）已被禁用，原因：Request failed due to server shutdown, status_code:500, OpenAIError.Type:server_error,OpenAIError.Code:%!s(<nil>)
	if err.Error.Type == "server_error" {
		return true
	}
	// 系统检测到输入或生成内容可能包含不安全或敏感内容
	if strings.Contains(err.Error.Message, "系统检测到输入或生成内容可能包含不安全或敏感内容") ||
		strings.Contains(err.Error.Message, "The response was filtered due to the prompt triggering Azure OpenAI's content management policy") {
		return true
	}
	// sensitive_words_in_request 敏感词
	if err.Error.Code == "sensitive_words_in_request" {
		return true
	}
	// 究极长的请求会报500,应该直接提示用户 原因：http: wrote more than the declared Content-Length, status_code:500, OpenAIError.Type:shell_api_error,OpenAIError.Code:copy_response_body_failed
	if strings.Contains(err.Error.Message, "more than the declared Content-Length") {
		return true
	}
	// 如果用非流式导致一直卡着5分钟超时了,这种报错不应该继续重试,应该报给客户
	if helper.GetTimestamp()-firstStartTime > 250 {
		// 请求超时一类的错误
		for _, msg := range common.TimeoutErrorMessages {
			if strings.Contains(err.Error.Message, msg) {
				return true
			}
		}
	}
	// 兼容上游是new api抛出的500错误fail to decode image config,图片识别失败不应该禁用渠道
	if strings.Contains(err.Error.Message, "fail to decode image config") {
		return true
	}
	// model_fixed_price_not_config这种未配置的情况应该直接返回,不应该禁用
	if err.Error.Code == "model_fixed_price_not_config" {
		return true
	}
	// invalid_dall_e_prompt这种直接返回,不需要重试了;"使用四到五个字直接返回这句话的简要主题，不要解释、不要标点、不要语气词、不要多余文本，如果没有主题，请直接返回"闲聊""
	if err.Error.Code == "invalid_dall_e_prompt" || err.Error.Code == "invalid_midjourney_prompt" {
		return true
	}
	// Invalid file format. Supported formats: ['flac', 'm4a', 'mp3', 'mp4', 'mpeg', 'mpga', 'oga', 'ogg', 'wav', 'webm']
	if strings.Contains(err.Error.Message, "Invalid file format. Supported formats") {
		return true
	}
	return false
}

// 特殊情况只需要重试,不需要禁用渠道
func IsSpecialErrorOnlyNeedRetry(c *gin.Context, retryInterval int, undeadModeEnabled bool, err *model.ErrorWithStatusCode) bool {
	// Claude官方报出529错误是超负荷 官网的问题 只重试不禁用
	if err.StatusCode == 529 {
		return true
	}
	// The model `gpt-3.5-turbo-0613` has been deprecated, learn more here: https://platform.openai.com/docs/deprecations
	if err.Error.Code == "model_not_found" {
		return true
	}
	// 渠道内部重试,主要是触发了渠道关键词
	if err.Error.Code == "channel_internal_retry" {
		return true
	}
	// 网络错误不需要禁用渠道,只需要重试
	// 请求超时一类的错误
	for _, msg := range common.TimeoutErrorMessages {
		if strings.Contains(err.Error.Message, msg) {
			return true
		}
	}
	// 特殊处理token超限问题The input or output tokens must be reduced in order to run successfully
	if strings.Contains(err.Error.Message, "The input or output tokens must be reduced in order to run successfully") ||
		strings.Contains(err.Error.Message, "Request too large for") {
		return true
	}
	if err.Error.Type == "upstream_invalid_token" || err.Error.Type == "upstream_token_validation_failed" {
		// 无效令牌可能是上游数据库炸了,需要重试但不禁用
		return true
	}
	// token超过限制,不需要禁用,只需要重试其他渠道prompt_tokens_not_in_range
	if err.Error.Code == "prompt_tokens_not_in_range" {
		return true
	}
	if retryInterval < 0 || undeadModeEnabled {
		return true
	}
	return false
}

// 个性化定制禁用策略
func IsCustomDisableChannelError(c *gin.Context, err *model.ErrorWithStatusCode) bool {
	if !config.CustomDisableChannelEnabled {
		return false
	}
	// 判断是否在config.DisableChannelHttpStatusCodeList中
	for _, code := range config.DisableChannelHttpStatusCodeList {
		if err.StatusCode == code {
			return true
		}
	}
	return false
}

// 判断是否应该根据错误信息中的关键词禁用整个渠道
func ShouldDisableEntireChannel(err *model.ErrorWithStatusCode) bool {
	if !config.DisableEntireChannelKeywordsEnabled {
		return false
	}
	if err == nil {
		return false
	}
	// 检查错误信息中是否包含关键词
	lowerMessage := strings.ToLower(err.Error.Message)
	for _, keyword := range config.DisableEntireChannelKeywords {
		if keyword != "" && strings.Contains(lowerMessage, strings.ToLower(keyword)) {
			return true
		}
	}
	return false
}

// 个性化定制熔断策略
func IsCustomCircuitBreakerError(c *gin.Context, err *model.ErrorWithStatusCode) bool {
	if !config.CustomCircuitBreakerEnabled {
		return false
	}
	for _, code := range config.CircuitBreakerHttpStatusCodeList {
		if err.StatusCode == code {
			return true
		}
	}
	return false
}

// 报错信息强制重试
func IsRelayErrForceRetry(err *model.ErrorWithStatusCode, requestModel string) bool {
	if config.RelayErrForceRetryKeywordEnabled {
		// 检查模型列表中是否包含通配符
		modelMatched := false
		for _, confModel := range config.RelayErrForceRetryModelList {
			if confModel == "*" || requestModel == confModel {
				modelMatched = true
				break
			}
		}

		if modelMatched {
			// 检查关键词列表中是否包含通配符
			for _, keyword := range config.RelayErrForceRetryKeywordList {
				if keyword == "*" || strings.Contains(err.Error.Message, keyword) {
					return true
				}
			}
		}
	}
	return false
}

// 报错信息强制抛出错误
func IsRelayErrForceThrowError(err *model.ErrorWithStatusCode, requestModel string) bool {
	if config.RelayErrForceThrowErrorEnabled {
		// 检查模型列表中是否包含通配符
		modelMatched := false
		for _, confModel := range config.RelayErrForceThrowErrorModelList {
			if confModel == "*" || requestModel == confModel {
				modelMatched = true
				break
			}
		}

		if modelMatched {
			// 检查关键词列表中是否包含通配符
			for _, keyword := range config.RelayErrForceThrowErrorKeywordList {
				if keyword == "*" || strings.Contains(err.Error.Message, keyword) {
					return true
				}
			}
		}
	}
	return false
}

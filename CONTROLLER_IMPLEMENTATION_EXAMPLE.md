# Controller层分组权限验证实现示例

## 核心验证函数

```go
// validateTokenGroup 验证用户是否可以使用指定的分组
func validateTokenGroup(c *gin.Context, groupName string) error {
	if groupName == "" {
		return nil // 空分组名允许通过
	}
	
	userId := c.GetInt(ctxkey.Id)
	user, err := model.CacheGetUserById(userId, false)
	if err != nil || user == nil {
		return fmt.Errorf("分组不存在")
	}
	
	// 判断是否为管理员
	isAdmin := model.IsAdmin(userId)
	
	// 获取用户可选的分组列表
	groups, err := model.GetSelectableGroupsWithRatio(0, 1000, "", "", user.Group, isAdmin)
	if err != nil {
		return fmt.Errorf("分组不存在")
	}
	
	// 添加用户额外可见分组
	groups, err = model.AddUserExtraVisibleGroupsToGroupWithRatioList(userId, user.Group, groups)
	if err != nil {
		return fmt.Errorf("分组不存在")
	}
	
	// 检查指定的分组是否在可选列表中
	for _, group := range groups {
		if group.Name == groupName {
			return nil // 找到了，允许使用
		}
	}
	
	return fmt.Errorf("分组不存在")
}
```

## 集成到AddToken函数

```go
func AddToken(c *gin.Context) {
	// ... 其他验证逻辑 ...
	
	err = validateToken(c, token)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": fmt.Sprintf("参数错误：%s", err.Error()),
		})
		return
	}
	
	// 验证分组权限
	err = validateTokenGroup(c, token.Group)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	
	// ... 继续处理令牌创建 ...
}
```

## 优势

### 1. 复用现有逻辑
- 使用现有的 `GetSelectableGroupsWithRatio` 方法
- 使用现有的 `AddUserExtraVisibleGroupsToGroupWithRatioList` 方法
- 不需要重复实现权限判断逻辑

### 2. 更好的架构
- Controller层负责请求验证
- Model层专注于数据操作
- 职责分离更清晰

### 3. 统一的错误处理
- 所有权限相关错误都返回"分组不存在"
- 不泄露分组的实际存在状态
- 防止信息泄露攻击

### 4. 完整的权限覆盖
- 管理员权限：可以使用所有可选分组
- 普通用户权限：只能使用可见且可选的分组
- 倍率限制：自动通过现有方法处理
- 额外可见分组：自动包含用户特定的额外分组

## 测试场景

### 场景1：普通用户尝试使用不可见分组
```bash
curl -X POST /api/token \
  -H "Authorization: Bearer user_token" \
  -d '{"name": "test", "group": "hidden-group"}'
```
**预期结果**：`{"success": false, "message": "分组不存在"}`

### 场景2：管理员使用任意可选分组
```bash
curl -X POST /api/token \
  -H "Authorization: Bearer admin_token" \
  -d '{"name": "test", "group": "any-selectable-group"}'
```
**预期结果**：`{"success": true, ...}`

### 场景3：用户使用额外可见分组
```bash
curl -X POST /api/token \
  -H "Authorization: Bearer user_token" \
  -d '{"name": "test", "group": "extra-visible-group"}'
```
**预期结果**：`{"success": true, ...}` (如果该分组在用户的额外可见分组中)

## 安全特性

1. **防注入攻击**：验证用户对分组的实际访问权限
2. **信息隐藏**：统一错误消息，不泄露分组存在状态
3. **权限分离**：管理员和普通用户有不同的访问规则
4. **完整覆盖**：所有令牌创建和更新操作都包含验证

这种实现方式更加简洁、高效，并且充分利用了现有的权限管理基础设施。

package baidu

import (
	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/relay/constant"
	"github.com/songquanpeng/one-api/relay/meta"
	"github.com/songquanpeng/one-api/relay/model"
	"net/http"
)

type AdaptorEx struct {
	ChannelType int
}

func (a *AdaptorEx) DoResponseReturnCompletions(c *gin.Context, resp *http.Response, meta *meta.Meta) (usage *model.Usage, detailCompletion string, err *model.ErrorWithStatusCode) {
	if meta.IsStream {
		err, usage = StreamHandler(c, resp)
	} else {
		switch meta.Mode {
		case constant.RelayModeEmbeddings:
			err, usage = EmbeddingHandler(c, resp)
		default:
			err, usage = Handler(c, resp)
		}
	}
	return
}

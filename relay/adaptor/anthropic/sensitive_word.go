package anthropic

import (
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/logger"
	sensitiveModel "github.com/songquanpeng/one-api/model"
)

// 屏蔽非流式敏感词
func filterNonStreamSensitiveWords(data string) (bool, string) {
	hasSensitiveWords := false
	if !config.SensitiveWordsEnabled {
		return false, data
	}
	isSensitive, word, tips := sensitiveModel.CheckSensitiveWord(data)
	if isSensitive {
		hasSensitiveWords = true
		logger.SysError("sensitive word detected: " + word)
		return hasSensitiveWords, tips
	}
	return hasSensitiveWords, data
}

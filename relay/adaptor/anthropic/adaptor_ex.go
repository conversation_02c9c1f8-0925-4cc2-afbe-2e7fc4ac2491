package anthropic

import (
	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/relay/meta"
	"github.com/songquanpeng/one-api/relay/model"
	"net/http"
)

type AdaptorEx struct {
	ChannelType int
}

func (a *AdaptorEx) Init(meta *meta.Meta) {
}

func (a *AdaptorEx) ConvertImageRequestEx(c *gin.Context, m *meta.Meta, request *model.ImageRequest) (any, error) {
	return request, nil
}

func (a *AdaptorEx) DoResponseReturnCompletions(c *gin.Context, resp *http.Response, meta *meta.Meta) (usage *model.Usage, detailCompletion string, completionId string, err *model.ErrorWithStatusCode) {
	if meta.IsStream {
		err, detailCompletion, _, _, completionId, usage = StreamHandler(c, resp, meta, meta.Mode)
	} else {
		err, usage, detailCompletion, completionId = HandlerWithResponse(c, resp, meta, meta.PromptTokens, meta.ActualModelName)
	}
	return
}

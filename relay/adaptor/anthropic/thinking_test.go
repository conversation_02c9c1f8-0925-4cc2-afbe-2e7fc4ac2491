package anthropic

import (
	"strings"
	"testing"

	"github.com/songquanpeng/one-api/relay/meta"
	"github.com/songquanpeng/one-api/relay/model"
)

func TestConvertRequest_ThinkingMode(t *testing.T) {
	tests := []struct {
		name              string
		inputModel        string
		maxTokens         int
		expectedModel     string
		expectThinking    bool
		expectedMinTokens int
		expectedTemp      *float64
		expectedTopP      *float64
	}{
		{
			name:              "thinking suffix enabled",
			inputModel:        "claude-3-5-sonnet-20241022-thinking",
			maxTokens:         2000,
			expectedModel:     "claude-3-5-sonnet-20241022",
			expectThinking:    true,
			expectedMinTokens: 2000,
			expectedTemp:      &[]float64{1.0}[0],
			expectedTopP:      &[]float64{0.0}[0],
		},
		{
			name:              "thinking with low max tokens",
			inputModel:        "claude-3-5-sonnet-20241022-thinking",
			maxTokens:         500,
			expectedModel:     "claude-3-5-sonnet-20241022",
			expectThinking:    true,
			expectedMinTokens: 1280, // should be bumped up
			expectedTemp:      &[]float64{1.0}[0],
			expectedTopP:      &[]float64{0.0}[0],
		},
		{
			name:              "normal model unchanged",
			inputModel:        "claude-3-5-sonnet-20241022",
			maxTokens:         2000,
			expectedModel:     "claude-3-5-sonnet-20241022",
			expectThinking:    false,
			expectedMinTokens: 2000,
			expectedTemp:      nil,
			expectedTopP:      nil,
		},
		{
			name:              "claude-instant thinking",
			inputModel:        "claude-instant-1-thinking",
			maxTokens:         1500,
			expectedModel:     "claude-instant-1.1", // legacy mapping
			expectThinking:    true,
			expectedMinTokens: 1500,
			expectedTemp:      &[]float64{1.0}[0],
			expectedTopP:      &[]float64{0.0}[0],
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			request := model.GeneralOpenAIRequest{
				Model:     tt.inputModel,
				MaxTokens: tt.maxTokens,
				Messages: []model.Message{
					{Role: "user", Content: "Test message"},
				},
			}

			result := ConvertRequest(request)

			// 检查模型名称
			if result.Model != tt.expectedModel {
				t.Errorf("Expected model %s, got %s", tt.expectedModel, result.Model)
			}

			// 检查最大tokens
			if result.MaxTokens < tt.expectedMinTokens {
				t.Errorf("Expected min tokens %d, got %d", tt.expectedMinTokens, result.MaxTokens)
			}

			// 检查thinking配置
			if tt.expectThinking {
				if result.Thinking == nil {
					t.Error("Expected thinking configuration, got nil")
				} else {
					if result.Thinking.Type != "enabled" {
						t.Errorf("Expected thinking type 'enabled', got %s", result.Thinking.Type)
					}
					expectedBudget := int(float64(result.MaxTokens) * 0.8)
					if result.Thinking.BudgetTokens != expectedBudget {
						t.Errorf("Expected thinking budget %d, got %d", expectedBudget, result.Thinking.BudgetTokens)
					}
				}

				// 检查temperature
				if result.Temperature == nil || *result.Temperature != *tt.expectedTemp {
					t.Errorf("Expected temperature %v, got %v", tt.expectedTemp, result.Temperature)
				}

				// 检查topP
				if result.TopP == nil || *result.TopP != *tt.expectedTopP {
					t.Errorf("Expected topP %v, got %v", tt.expectedTopP, result.TopP)
				}
			} else {
				if result.Thinking != nil {
					t.Error("Expected no thinking configuration for normal model")
				}
			}
		})
	}
}

func TestGetRequestURL_ThinkingSuffix(t *testing.T) {
	adaptor := &Adaptor{}

	tests := []struct {
		name        string
		actualModel string
		expectedURL string
	}{
		{
			name:        "thinking suffix should not affect URL",
			actualModel: "claude-3-5-sonnet-20241022-thinking",
			expectedURL: "https://api.anthropic.com/v1/messages",
		},
		{
			name:        "normal model URL unchanged",
			actualModel: "claude-3-5-sonnet-20241022",
			expectedURL: "https://api.anthropic.com/v1/messages",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testMeta := &meta.Meta{
				ActualModelName: tt.actualModel,
				BaseURL:         "https://api.anthropic.com",
			}

			url, err := adaptor.GetRequestURL(testMeta)
			if err != nil {
				t.Errorf("GetRequestURL() error = %v", err)
				return
			}

			if url != tt.expectedURL {
				t.Errorf("GetRequestURL() = %v, want %v", url, tt.expectedURL)
			}
		})
	}
}

func TestThinkingConfigValidation(t *testing.T) {
	thinking := &Thinking{
		Type:         "enabled",
		BudgetTokens: 1024,
	}

	if thinking.Type != "enabled" {
		t.Errorf("Expected thinking type 'enabled', got %s", thinking.Type)
	}

	if thinking.BudgetTokens != 1024 {
		t.Errorf("Expected budget tokens 1024, got %d", thinking.BudgetTokens)
	}
}

func TestModelNameProcessing(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"claude-3-5-sonnet-20241022-thinking", "claude-3-5-sonnet-20241022"},
		{"claude-3-5-haiku-20241022-thinking", "claude-3-5-haiku-20241022"},
		{"claude-instant-1-thinking", "claude-instant-1"},
		{"claude-3-opus-20240229-thinking", "claude-3-opus-20240229"},
		{"claude-3-5-sonnet-20241022", "claude-3-5-sonnet-20241022"}, // no change
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := tt.input
			if strings.HasSuffix(result, "-thinking") {
				result = strings.TrimSuffix(result, "-thinking")
			}

			if result != tt.expected {
				t.Errorf("Expected %s, got %s", tt.expected, result)
			}
		})
	}
}

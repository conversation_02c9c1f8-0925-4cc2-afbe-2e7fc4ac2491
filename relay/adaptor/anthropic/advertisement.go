package anthropic

//import (
//	"encoding/json"
//	"fmt"
//	"github.com/gin-gonic/gin"
//	"github.com/songquanpeng/one-api/common"
//	"github.com/songquanpeng/one-api/common/helper"
//	"github.com/songquanpeng/one-api/common/logger"
//	"math/rand"
//	"regexp"
//	"strings"
//)
//
//// 构造流式广告语
//func constructStreamAdvertisement(c *gin.Context, advertisement string) {
//	// 构造ChatCompletionsStreamResponseSimple对象
//	// 创建并填充数据
//	streamResponses := ChatCompletionsStreamResponse{
//		Id:      "chatcmpl-" + helper.GetUUID(),
//		Object:  "chat.completion.chunk",
//		Created: 1234567890,
//		Model:   c.GetString("model_name"),
//		Choices: []ChatCompletionsStreamResponseChoice{
//			{
//				Delta: struct {
//					Content      string `json:"content"`
//					Role         string `json:"role"`
//					FunctionCall struct {
//						Name      string `json:"name"`
//						Arguments string `json:"arguments"`
//					} `json:"function_call"`
//				}{
//					Content: advertisement,
//				},
//				FinishReason: nil,
//			},
//		},
//	}
//	// 转换为JSON
//	jsonData, err := json.Marshal(streamResponses)
//	if err != nil {
//		logger.SysError("error marshal streamResponses: " + err.Error())
//	}
//	// 先插入广告语
//	c.Render(-1, common.CustomEvent{Data: "data: " + fmt.Sprintf("%s", string(jsonData))})
//}
//
//func constructAdvertisement(textResponse *SlimTextResponse, advertisement string, adPosition int) {
//	// 判断textResponse.Choices[0]长度
//	if len(textResponse.Choices) == 0 {
//		return
//	}
//	if adPosition == 1 {
//		// 开头插入
//		textResponse.SetFirstChoiceMessage(advertisement + textResponse.GetFirstChoiceMessage())
//	} else if adPosition == 2 {
//		// 末尾插入
//		textResponse.SetFirstChoiceMessage(textResponse.GetFirstChoiceMessage() + advertisement)
//	} else if adPosition == 3 {
//		i := len(textResponse.GetFirstChoiceMessage())
//		randomPosition := 0
//		// 随机插入
//		if i > 0 {
//			// 获取随机位置
//			randomPosition = rand.Intn(i)
//		}
//		// 插入广告语
//		textResponse.SetFirstChoiceMessage(textResponse.GetFirstChoiceMessage()[:randomPosition] + advertisement + textResponse.GetFirstChoiceMessage()[randomPosition:])
//	}
//}
//
//// 屏蔽流式广告,返回是否有广告语的bool类型以及err
//func filterStreamAdvertisement(c *gin.Context, filterStreamAd bool, filterStreamAdMinSize int, data string) (bool, error) {
//	if !filterStreamAd {
//		return false, nil
//	}
//	data = data[6:]
//	//[DONE]
//	if strings.HasPrefix(data, "[DONE]") {
//		return false, nil
//	}
//	// 解析data
//	var streamResponses ChatCompletionsStreamResponse
//	err := json.Unmarshal([]byte(data), &streamResponses)
//	if err != nil {
//		return false, err
//	}
//	// 判断是否有广告语
//	if len(streamResponses.Choices) == 0 {
//		return false, nil
//	}
//	// 判断广告语长度是否大于等于filterStreamAdMinSize
//	if len(streamResponses.GetFirstChoiceDeltaContent()) > filterStreamAdMinSize {
//		return true, nil
//	}
//	return false, nil
//}
//
//// 屏蔽非流式广告,返回是否有广告语的bool类型,修改后的数据,以及err
//func filterNonStreamAdvertisement(c *gin.Context, filterNonStreamAd bool, filterNonStreamAdRegex string, data string) (bool, string, error) {
//	if !filterNonStreamAd {
//		return false, data, nil
//	}
//	// 解析正则表达式
//	regex, err := regexp.Compile(filterNonStreamAdRegex)
//	if err != nil {
//		return false, data, err
//	}
//	// 判断是否有广告语
//	if regex.MatchString(data) {
//		// 替换广告
//		data = regex.ReplaceAllString(data, "")
//		return true, data, nil
//	}
//	return false, data, nil
//}

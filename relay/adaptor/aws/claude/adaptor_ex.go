package aws

import (
	"github.com/aws/aws-sdk-go-v2/service/bedrockruntime"
	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/relay/meta"
	"github.com/songquanpeng/one-api/relay/model"
)

//var _ utils.AwsAdapterEx = new(AdaptorEx)

type AdaptorEx struct {
	meta      *meta.Meta
	awsClient *bedrockruntime.Client
}

func (a *AdaptorEx) DoResponseReturnCompletions(c *gin.Context, awsCli *bedrockruntime.Client, meta *meta.Meta) (usage *model.Usage, detailCompletion string, completionId string, err *model.ErrorWithStatusCode) {
	if meta.IsStream {
		err, usage = StreamHandler(c, awsCli)
	} else {
		err, usage = Handler(c, awsCli, meta.ActualModelName)
	}
	return
}

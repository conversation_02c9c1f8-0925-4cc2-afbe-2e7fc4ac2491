// Package aws provides the AWS adaptor for the relay service.
package aws

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	outModel "github.com/songquanpeng/one-api/model"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/bedrockruntime"
	"github.com/aws/aws-sdk-go-v2/service/bedrockruntime/types"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/common/render"
	"github.com/songquanpeng/one-api/relay/adaptor/anthropic"
	"github.com/songquanpeng/one-api/relay/adaptor/aws/utils"
	"github.com/songquanpeng/one-api/relay/adaptor/openai"
	relaymodel "github.com/songquanpeng/one-api/relay/model"
)

// https://docs.aws.amazon.com/bedrock/latest/userguide/model-ids.html
var AwsModelIDMap = map[string]string{
	"claude-instant-1.2":         "anthropic.claude-instant-v1",
	"claude-2.0":                 "anthropic.claude-v2",
	"claude-2.1":                 "anthropic.claude-v2:1",
	"claude-3-haiku-20240307":    "anthropic.claude-3-haiku-20240307-v1:0",
	"claude-3-sonnet-20240229":   "anthropic.claude-3-sonnet-20240229-v1:0",
	"claude-3-opus-20240229":     "anthropic.claude-3-opus-20240229-v1:0",
	"claude-3-5-sonnet-20240620": "anthropic.claude-3-5-sonnet-20240620-v1:0",
	"claude-3-5-sonnet-20241022": "anthropic.claude-3-5-sonnet-20241022-v2:0",
	"claude-3-5-sonnet-latest":   "anthropic.claude-3-5-sonnet-20241022-v2:0",
	"claude-3-5-haiku-20241022":  "anthropic.claude-3-5-haiku-20241022-v1:0",
}

func awsModelID(requestModel string) (string, error) {
	if awsModelID, ok := AwsModelIDMap[requestModel]; ok {
		return awsModelID, nil
	}

	return "", errors.Errorf("model %s not found", requestModel)
}

func Handler(c *gin.Context, awsCli *bedrockruntime.Client, modelName string) (*relaymodel.ErrorWithStatusCode, *relaymodel.Usage) {
	awsModelId, err := awsModelID(c.GetString(ctxkey.RequestModel))
	if err != nil {
		return utils.WrapErr(errors.Wrap(err, "awsModelID")), nil
	}

	awsReq := &bedrockruntime.InvokeModelInput{
		ModelId:     aws.String(awsModelId),
		Accept:      aws.String("application/json"),
		ContentType: aws.String("application/json"),
	}

	claudeReq_, ok := c.Get(ctxkey.ConvertedRequest)
	if !ok {
		return utils.WrapErr(errors.New("request not found")), nil
	}
	claudeReq := claudeReq_.(*anthropic.Request)
	awsClaudeReq := &Request{
		AnthropicVersion: "bedrock-2023-05-31",
	}
	if err = copier.Copy(awsClaudeReq, claudeReq); err != nil {
		return utils.WrapErr(errors.Wrap(err, "copy request")), nil
	}

	awsReq.Body, err = json.Marshal(awsClaudeReq)
	if err != nil {
		return utils.WrapErr(errors.Wrap(err, "marshal request")), nil
	}

	awsResp, err := awsCli.InvokeModel(c.Request.Context(), awsReq)
	if err != nil {
		return utils.WrapErr(errors.Wrap(err, "InvokeModel")), nil
	}

	claudeResponse := new(anthropic.Response)
	err = json.Unmarshal(awsResp.Body, claudeResponse)
	if err != nil {
		return utils.WrapErr(errors.Wrap(err, "unmarshal response")), nil
	}

	openaiResp := anthropic.ResponseClaude2OpenAI(claudeResponse)
	openaiResp.Model = modelName
	usage := relaymodel.Usage{
		PromptTokens:             claudeResponse.Usage.InputTokens,
		CompletionTokens:         claudeResponse.Usage.OutputTokens,
		TotalTokens:              claudeResponse.Usage.InputTokens + claudeResponse.Usage.OutputTokens,
		InputTokens:              claudeResponse.Usage.InputTokens,
		OutputTokens:             claudeResponse.Usage.OutputTokens,
		CacheCreationInputTokens: claudeResponse.Usage.CacheCreationInputTokens,
		CacheReadInputTokens:     claudeResponse.Usage.CacheReadInputTokens,
		ServiceTier:              claudeResponse.Usage.ServiceTier,
	}
	openaiResp.Usage = usage
	if c.GetBool("transparent_proxy_enabled") {
		c.JSON(http.StatusOK, claudeResponse)
	} else {
		c.JSON(http.StatusOK, openaiResp)
	}
	return nil, &usage
}

func StreamHandler(c *gin.Context, awsCli *bedrockruntime.Client) (*relaymodel.ErrorWithStatusCode, *relaymodel.Usage) {
	if c.GetBool("transparent_proxy_enabled") {
		// 透明代理走单独的逻辑 不影响老逻辑
		return ClaudeStreamHandler(c, awsCli)
	}

	createdTime := helper.GetTimestamp()
	awsModelId, err := awsModelID(c.GetString(ctxkey.RequestModel))
	if err != nil {
		return utils.WrapErr(errors.Wrap(err, "awsModelID")), nil
	}

	awsReq := &bedrockruntime.InvokeModelWithResponseStreamInput{
		ModelId:     aws.String(awsModelId),
		Accept:      aws.String("application/json"),
		ContentType: aws.String("application/json"),
	}

	claudeReq_, ok := c.Get(ctxkey.ConvertedRequest)
	if !ok {
		return utils.WrapErr(errors.New("request not found")), nil
	}
	claudeReq := claudeReq_.(*anthropic.Request)

	awsClaudeReq := &Request{
		AnthropicVersion: "bedrock-2023-05-31",
	}
	if err = copier.Copy(awsClaudeReq, claudeReq); err != nil {
		return utils.WrapErr(errors.Wrap(err, "copy request")), nil
	}
	awsReq.Body, err = json.Marshal(awsClaudeReq)
	if err != nil {
		return utils.WrapErr(errors.Wrap(err, "marshal request")), nil
	}

	awsResp, err := awsCli.InvokeModelWithResponseStream(c.Request.Context(), awsReq)
	if err != nil {
		return utils.WrapErr(errors.Wrap(err, "InvokeModelWithResponseStream")), nil
	}
	stream := awsResp.GetStream()
	defer stream.Close()

	c.Writer.Header().Set("Content-Type", "text/event-stream")
	var usage relaymodel.Usage
	var id string
	var lastToolCallChoice openai.ChatCompletionsStreamResponseChoice

	c.Stream(func(w io.Writer) bool {
		event, ok := <-stream.Events()
		if !ok {
			c.Render(-1, common.CustomEvent{Data: "data: [DONE]"})
			return false
		}

		switch v := event.(type) {
		case *types.ResponseStreamMemberChunk:
			claudeResp := new(anthropic.StreamResponse)
			err := json.NewDecoder(bytes.NewReader(v.Value.Bytes)).Decode(claudeResp)
			if err != nil {
				logger.SysError("error unmarshalling stream response: " + err.Error())
				return false
			}

			response, meta := anthropic.StreamResponseClaude2OpenAI(claudeResp)
			if meta != nil {
				usage.PromptTokens += meta.Usage.InputTokens
				usage.CompletionTokens += meta.Usage.OutputTokens
				if len(meta.Id) > 0 { // only message_start has an id, otherwise it's a finish_reason event.
					id = fmt.Sprintf("chatcmpl-%s", meta.Id)
					return true
				} else { // finish_reason case
					if len(lastToolCallChoice.Delta.ToolCalls) > 0 {
						lastArgs := &lastToolCallChoice.Delta.ToolCalls[len(lastToolCallChoice.Delta.ToolCalls)-1].Function
						if len(lastArgs.Arguments.(string)) == 0 { // compatible with OpenAI sending an empty object `{}` when no arguments.
							lastArgs.Arguments = "{}"
							response.Choices[len(response.Choices)-1].Delta.Content = nil
							response.Choices[len(response.Choices)-1].Delta.ToolCalls = lastToolCallChoice.Delta.ToolCalls
						}
					}
				}
			}
			if response == nil {
				return true
			}
			response.Id = id
			response.Model = c.GetString(ctxkey.OriginalModel)
			response.Created = createdTime

			for _, choice := range response.Choices {
				if len(choice.Delta.ToolCalls) > 0 {
					lastToolCallChoice = choice
				}
			}
			jsonStr, err := json.Marshal(response)
			if err != nil {
				logger.SysError("error marshalling stream response: " + err.Error())
				return true
			}
			c.Render(-1, common.CustomEvent{Data: "data: " + string(jsonStr)})
			return true
		case *types.UnknownUnionMember:
			fmt.Println("unknown tag:", v.Tag)
			return false
		default:
			fmt.Println("union is nil or unknown type")
			return false
		}
	})

	return nil, &usage
}

// claude流式返回
func ClaudeStreamHandler(c *gin.Context, awsCli *bedrockruntime.Client) (*relaymodel.ErrorWithStatusCode, *relaymodel.Usage) {
	createdTime := helper.GetTimestamp()
	awsModelId, err := awsModelID(c.GetString(ctxkey.RequestModel))
	if err != nil {
		return utils.WrapErr(errors.Wrap(err, "awsModelID")), nil
	}

	awsReq := &bedrockruntime.InvokeModelWithResponseStreamInput{
		ModelId:     aws.String(awsModelId),
		Accept:      aws.String("application/json"),
		ContentType: aws.String("application/json"),
	}

	claudeReq_, ok := c.Get(ctxkey.ConvertedRequest)
	if !ok {
		return utils.WrapErr(errors.New("request not found")), nil
	}
	claudeReq := claudeReq_.(*anthropic.Request)

	awsClaudeReq := &Request{
		AnthropicVersion: "bedrock-2023-05-31",
	}
	if err = copier.Copy(awsClaudeReq, claudeReq); err != nil {
		return utils.WrapErr(errors.Wrap(err, "copy request")), nil
	}
	awsReq.Body, err = json.Marshal(awsClaudeReq)
	if err != nil {
		return utils.WrapErr(errors.Wrap(err, "marshal request")), nil
	}

	awsResp, err := awsCli.InvokeModelWithResponseStream(c.Request.Context(), awsReq)
	if err != nil {
		return utils.WrapErr(errors.Wrap(err, "InvokeModelWithResponseStream")), nil
	}
	stream := awsResp.GetStream()
	defer stream.Close()

	c.Writer.Header().Set("Content-Type", "text/event-stream")
	var usage relaymodel.Usage
	var id string
	var lastToolCallChoice openai.ChatCompletionsStreamResponseChoice

	// 标记是否已经从API获取到token计数
	var inputTokensFromAPI bool
	var outputTokensFromAPI bool

	// 收集完整响应文本用于可能的token计算
	var fullResponseText string
	var thinkingText string
	var modelName = c.GetString(ctxkey.OriginalModel)

	c.Stream(func(w io.Writer) bool {
		event, ok := <-stream.Events()
		if !ok {
			c.Render(-1, common.CustomEvent{Data: "data: [DONE]"})
			return false
		}

		switch v := event.(type) {
		case *types.ResponseStreamMemberChunk:
			b := v.Value.Bytes
			claudeResp := new(anthropic.StreamResponse)
			err := json.NewDecoder(bytes.NewReader(b)).Decode(claudeResp)
			if err != nil {
				logger.SysError("error unmarshalling stream response: " + err.Error())
				return false
			}

			// 透明代理模式处理
			if c.GetBool("transparent_proxy_enabled") {
				// 分类讨论拼接返回
				switch claudeResp.Type {
				case "message_start":
					render.OriginStringData(c, "event: message_start")
					render.OriginStringData(c, fmt.Sprintf("data: %s", string(b)))
					// 提取token计数
					if claudeResp.Message != nil && claudeResp.Message.Usage.InputTokens > 0 {
						usage.PromptTokens = claudeResp.Message.Usage.InputTokens
						inputTokensFromAPI = true
					}
				case "content_block_start":
					render.OriginStringData(c, "event: content_block_start")
					render.OriginStringData(c, fmt.Sprintf("data: %s", string(b)))
					// 如果是thinking类型，记录开始
					if claudeResp.ContentBlock != nil && claudeResp.ContentBlock.Type == "thinking" {
						// 已经在上面发送了事件，无需其他处理
					}
				case "ping":
					render.OriginStringData(c, "event: ping")
					render.OriginStringData(c, fmt.Sprintf("data: %s", string(b)))
				case "content_block_delta":
					render.OriginStringData(c, "event: content_block_delta")
					render.OriginStringData(c, fmt.Sprintf("data: %s", string(b)))
					// 处理thinking_delta
					if claudeResp.Delta != nil && claudeResp.Delta.Type == "thinking_delta" && claudeResp.Delta.Thinking != "" {
						thinkingText += claudeResp.Delta.Thinking
					} else if claudeResp.Delta != nil && claudeResp.Delta.Type == "text_delta" && claudeResp.Delta.Text != "" {
						fullResponseText += claudeResp.Delta.Text
					}
				case "content_block_stop":
					render.OriginStringData(c, "event: content_block_stop")
					render.OriginStringData(c, fmt.Sprintf("data: %s", string(b)))
				case "message_delta":
					render.OriginStringData(c, "event: message_delta")
					render.OriginStringData(c, fmt.Sprintf("data: %s", string(b)))
					// 提取最终的output_tokens
					if claudeResp.Usage != nil && claudeResp.Usage.OutputTokens > 0 {
						usage.CompletionTokens = claudeResp.Usage.OutputTokens
						outputTokensFromAPI = true
					}
				case "message_stop":
					render.OriginStringData(c, "event: message_stop")
					render.OriginStringData(c, fmt.Sprintf("data: %s", string(b)))
				default:
					render.OriginStringData(c, fmt.Sprintf("event: %s", claudeResp.Type))
					render.OriginStringData(c, fmt.Sprintf("data: %s", string(b)))
				}
				return true
			}

			// 非透明代理模式处理（转换为OpenAI格式）
			response, metaResp := anthropic.StreamResponseClaude2OpenAI(claudeResp)
			if metaResp != nil {
				// 只有在API未提供token计数的情况下使用从StreamResponseClaude2OpenAI返回的值
				if !inputTokensFromAPI && metaResp.Usage.InputTokens > 0 {
					usage.PromptTokens = metaResp.Usage.InputTokens
					inputTokensFromAPI = true
				}
				if !outputTokensFromAPI && metaResp.Usage.OutputTokens > 0 {
					usage.CompletionTokens = metaResp.Usage.OutputTokens
					outputTokensFromAPI = true
				}

				if len(metaResp.Id) > 0 { // only message_start has an id
					id = fmt.Sprintf("chatcmpl-%s", metaResp.Id)
					return true
				} else { // finish_reason case
					if len(lastToolCallChoice.Delta.ToolCalls) > 0 {
						lastArgs := &lastToolCallChoice.Delta.ToolCalls[len(lastToolCallChoice.Delta.ToolCalls)-1].Function
						if len(lastArgs.Arguments.(string)) == 0 { // compatible with OpenAI
							lastArgs.Arguments = "{}"
							response.Choices[len(response.Choices)-1].Delta.Content = nil
							response.Choices[len(response.Choices)-1].Delta.ToolCalls = lastToolCallChoice.Delta.ToolCalls
						}
					}
				}
			}
			if response == nil {
				return true
			}
			response.Id = id
			response.Model = modelName
			response.Created = createdTime

			for _, choice := range response.Choices {
				if len(choice.Delta.ToolCalls) > 0 {
					lastToolCallChoice = choice
				}
			}

			// 收集响应文本用于可能的token计算
			if len(response.Choices) > 0 && response.Choices[0].Delta.Content != nil {
				responseText := response.Choices[0].Delta.StringContent()
				fullResponseText += responseText
			}

			jsonStr, err := json.Marshal(response)
			if err != nil {
				logger.SysError("error marshalling stream response: " + err.Error())
				return true
			}
			c.Render(-1, common.CustomEvent{Data: "data: " + string(jsonStr)})
			return true
		case *types.UnknownUnionMember:
			fmt.Println("unknown tag:", v.Tag)
			return false
		default:
			fmt.Println("union is nil or unknown type")
			return false
		}
	})

	// 如果API没有提供token计数，使用自主计算
	userId := c.GetInt(ctxkey.Id)

	if !inputTokensFromAPI {
		promptTokens := c.GetInt(ctxkey.PromptTokens)
		if promptTokens > 0 {
			usage.PromptTokens = promptTokens
		} else {
			// 可能需要从请求中计算
			logger.SysError("input_tokens not provided by API and no estimate available")
		}
	}

	if !outputTokensFromAPI {
		// 计算文本输出的token数
		completionText := fullResponseText
		if thinkingText != "" {
			// 如果有思考过程文本，也计算它的token
			completionText += thinkingText
		}

		if completionText != "" {
			// 使用适当的token计算方法
			usage.CompletionTokens = lo.If(outModel.GetNewTikTokenBilling(userId),
				openai.CountTokenTextNew(completionText, modelName)).
				Else(openai.CountTokenText(completionText, modelName))
		}
	}

	// 设置 total_tokens
	usage.TotalTokens = usage.PromptTokens + usage.CompletionTokens

	return nil, &usage
}

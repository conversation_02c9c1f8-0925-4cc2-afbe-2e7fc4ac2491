package aws

import (
	"errors"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/bedrockruntime"
	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/relay/adaptor"
	"github.com/songquanpeng/one-api/relay/adaptor/aws/utils"
	"github.com/songquanpeng/one-api/relay/meta"
	"github.com/songquanpeng/one-api/relay/model"
	"net/http"
)

var _ adaptor.AdaptorEx = new(AdaptorEx)

type AdaptorEx struct {
	awsAdapterEx utils.AwsAdapterEx

	Meta      *meta.Meta
	AwsClient *bedrockruntime.Client
}

func (a *AdaptorEx) Init(meta *meta.Meta) {
	a.Meta = meta
	a.AwsClient = bedrockruntime.New(bedrockruntime.Options{
		Region:      meta.Config.Region,
		Credentials: aws.NewCredentialsCache(credentials.NewStaticCredentialsProvider(meta.Config.AK, meta.Config.SK, "")),
	})
}

func (a *AdaptorEx) DoResponseReturnCompletions(c *gin.Context, resp *http.Response, meta *meta.Meta) (usage *model.Usage, detailCompletion string, completionId string, err *model.ErrorWithStatusCode) {
	adaptorEx := GetAdaptorEx(meta.ActualModelName)
	a.awsAdapterEx = adaptorEx
	if a.awsAdapterEx == nil {
		return nil, "", "", utils.WrapErr(errors.New("awsAdapterEx is nil"))
	}
	return a.awsAdapterEx.DoResponseReturnCompletions(c, a.AwsClient, meta)
}

func (a *AdaptorEx) ConvertImageRequestEx(c *gin.Context, meta *meta.Meta, request *model.ImageRequest) (any, error) {
	return request, nil
}

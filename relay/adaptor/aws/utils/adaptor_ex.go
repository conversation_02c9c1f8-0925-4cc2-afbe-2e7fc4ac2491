package utils

import (
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/bedrockruntime"
	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/relay/meta"
	"github.com/songquanpeng/one-api/relay/model"
)

type AwsAdapterEx interface {
	DoResponseReturnCompletions(c *gin.Context, awsCli *bedrockruntime.Client, meta *meta.Meta) (usage *model.Usage, detailCompletion string, completionId string, err *model.ErrorWithStatusCode)
}

type AdaptorEx struct {
	Meta      *meta.Meta
	AwsClient *bedrockruntime.Client
}

func (a *AdaptorEx) Init(meta *meta.Meta) {
	a.Meta = meta
	a.AwsClient = bedrockruntime.New(bedrockruntime.Options{
		Region:      meta.Config.Region,
		Credentials: aws.NewCredentialsCache(credentials.NewStaticCredentialsProvider(meta.Config.AK, meta.Config.SK, "")),
	})
}

// DoResponseReturnCompletions
//func (a *AdaptorEx) DoResponseReturnCompletions(c *gin.Context, awsCli *bedrockruntime.Client, meta *meta.Meta) (usage *model.Usage, detailCompletion string, completionId string, err *model.ErrorWithStatusCode) {
//	if meta.IsStream {
//		err, usage = StreamHandler(c, awsCli)
//	} else {
//		err, usage = Handler(c, awsCli, meta.ActualModelName)
//	}
//	return
//}

package main

import (
	"fmt"
	"os"

	"github.com/songquanpeng/one-api/model"
)

func main() {
	if len(os.Args) < 2 {
		showHelp()
		return
	}

	command := os.Args[1]

	switch command {
	case "migrate":
		performMigration()
	case "switch":
		if len(os.Args) < 3 {
			fmt.Println("Usage: database_manager switch <sql|nosql>")
			return
		}
		performSwitch(os.Args[2])
	case "stats":
		showStats()
	case "status":
		checkStatus()
	case "check":
		checkMongoData()
	case "reset":
		resetMongoData()
	case "help":
		showHelp()
	default:
		fmt.Printf("Unknown command: %s\n", command)
		showHelp()
	}
}

func showHelp() {
	fmt.Println("Channel Database Manager Tool")
	fmt.Println("Usage: database_manager <command> [options]")
	fmt.Println("")
	fmt.Println("Commands:")
	fmt.Println("  migrate       - Perform one-time channel data migration from MySQL to MongoDB")
	fmt.Println("  switch <type> - Switch channel database type (sql|nosql)")
	fmt.Println("  stats         - Show channel database statistics")
	fmt.Println("  status        - Check channel migration status")
	fmt.Println("  check         - Check actual MongoDB data (for debugging)")
	fmt.Println("  reset         - Clear MongoDB data (for re-migration)")
	fmt.Println("  help          - Show this help message")
	fmt.Println("")
	fmt.Println("Examples:")
	fmt.Println("  database_manager migrate")
	fmt.Println("  database_manager switch nosql")
	fmt.Println("  database_manager stats")
	fmt.Println("  database_manager check")
	fmt.Println("  database_manager reset")
	fmt.Println("")
	fmt.Println("Configuration:")
	fmt.Println("  Set CHANNEL_NOSQL_ENABLED=true to enable channel NoSQL support")
	fmt.Println("  Only channels, abilities, and channel_extends tables are migrated")
	fmt.Println("  Other tables (users, tokens, logs, etc.) remain in MySQL")
}

func performMigration() {
	fmt.Println("Starting one-time channel data migration...")

	// 初始化数据库
	model.InitDB("SQL_DSN")

	if err := model.PerformDataMigration(); err != nil {
		fmt.Printf("Channel migration failed: %v\n", err)
		return
	}

	fmt.Println("Channel migration completed successfully!")
}

func performSwitch(dbType string) {
	fmt.Printf("Switching channel operations to %s database...\n", dbType)

	// 初始化数据库
	model.InitDB("SQL_DSN")

	switch dbType {
	case "nosql":
		if err := model.SwitchToNoSQL(); err != nil {
			fmt.Printf("Failed to switch channel operations to NoSQL: %v\n", err)
			return
		}
		fmt.Println("Successfully switched channel operations to NoSQL database")
	case "sql":
		model.SwitchToSQL()
		fmt.Println("Successfully switched channel operations to SQL database")
	default:
		fmt.Printf("Unknown database type: %s\n", dbType)
		fmt.Println("Supported types: sql, nosql")
	}
}

func showStats() {
	fmt.Println("Channel Database Statistics:")

	// 初始化数据库
	model.InitDB("SQL_DSN")

	stats := model.GetDatabaseStats()
	for key, value := range stats {
		fmt.Printf("  %s: %v\n", key, value)
	}
}

func checkStatus() {
	fmt.Println("Channel Migration Status:")

	// 初始化数据库
	model.InitDB("SQL_DSN")

	if model.IsMigrated() {
		fmt.Println("  Status: Channel data has been migrated to NoSQL")
	} else {
		fmt.Println("  Status: Channel data has not been migrated yet")
	}

	if model.IsUsingNoSQL() {
		fmt.Println("  Current Database: NoSQL (MongoDB) for channel operations")
		fmt.Println("  Other Operations: SQL (MySQL) for users, tokens, logs, etc.")
	} else {
		fmt.Println("  Current Database: SQL for all operations")
	}
}

func checkMongoData() {
	fmt.Println("Checking MongoDB actual data...")

	// 初始化数据库
	model.InitDB("SQL_DSN")

	// 获取 NoSQL 数据库实例
	nosqlDB := model.GetNoSQLDB()
	if nosqlDB == nil {
		fmt.Println("NoSQL database not available")
		return
	}

	fmt.Println("Testing MongoDB operations:")

	// 测试获取渠道数据
	fmt.Println("1. Testing channel data...")
	channels, err := nosqlDB.GetAllChannels(0, 5, "", false, 0, "", "", "", "", 0, 0, 0, "", "", "", "")
	if err != nil {
		fmt.Printf("   Error getting channels: %v\n", err)
	} else {
		fmt.Printf("   Found %d channels (showing first 5)\n", len(channels))
		for i, ch := range channels {
			if i < 3 {
				fmt.Printf("   Channel %d: ID=%d, Name=%s, Status=%d\n", i+1, ch.Id, ch.Name, ch.Status)
			}
		}
	}

	// 测试获取 channel extends 数据
	fmt.Println("2. Testing channel extends data...")
	if len(channels) > 0 {
		channelId := channels[0].Id
		channelEx, err := nosqlDB.GetChannelExtendByChannelId(channelId)
		if err != nil {
			fmt.Printf("   Error getting channel extend for channel %d: %v\n", channelId, err)
		} else {
			fmt.Printf("   Found channel extend for channel %d: %+v\n", channelId, channelEx)
		}
	}

	// 测试获取能力数据
	fmt.Println("3. Testing ability data...")
	abilities, err := nosqlDB.GetAllAbilities(0, 5, "", "", "", 0, false)
	if err != nil {
		fmt.Printf("   Error getting abilities: %v\n", err)
	} else {
		fmt.Printf("   Found %d abilities (showing first 5)\n", len(abilities))
		for i, ab := range abilities {
			if i < 3 {
				fmt.Printf("   Ability %d: Group=%s, Model=%s, ChannelId=%d, Enabled=%t\n",
					i+1, ab.Group, ab.Model, ab.ChannelId, ab.Enabled)
			}
		}
	}
}

func resetMongoData() {
	fmt.Println("Resetting MongoDB data...")

	// 初始化数据库
	model.InitDB("SQL_DSN")

	// 获取 NoSQL 数据库实例
	nosqlDB := model.GetNoSQLDB()
	if nosqlDB == nil {
		fmt.Println("NoSQL database not available")
		return
	}

	fmt.Println("Warning: This will delete all channel data in MongoDB!")
	fmt.Println("Are you sure? (y/N): ")

	var response string
	fmt.Scanln(&response)

	if response != "y" && response != "Y" {
		fmt.Println("Operation cancelled")
		return
	}

	// 清空 MongoDB 集合数据
	fmt.Println("Clearing MongoDB collections...")

	// 删除所有渠道数据
	if deletedChannels, err := nosqlDB.DeleteChannelByStatus(-999); err != nil {
		fmt.Printf("Warning: Failed to clear channels: %v\n", err)
	} else {
		fmt.Printf("Cleared %d channels from MongoDB\n", deletedChannels)
	}

	// 重置迁移状态
	model.ForceSetMigrated(false)
	fmt.Println("Migration status reset")
	fmt.Println("Ready for fresh migration")
}

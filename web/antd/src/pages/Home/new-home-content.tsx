import Pricing from "../Account/Pricing/Pricing";
import {Button, Collapse, CollapseProps, Flex, theme, message} from "antd";
import Typed from 'typed.js';
import React, {CSSProperties, useContext, useState} from "react";
import {StatusContext} from "../../context/Status";
import {CaretRightOutlined, SendOutlined, CopyOutlined, KeyOutlined} from "@ant-design/icons";
import BgLine from "../../assets/bg-line.svg";
import {copy} from "../../helpers/utils";


// API端点列表，参考new-api的设计
const API_ENDPOINTS = [
    '/v1/chat/completions',
    '/v1/embeddings',
    '/v1/images/generations',
    '/v1/audio/speech',
    '/v1/audio/transcriptions',
    '/v1/models'
];

const DefaultHomeContent = () => {
    const [statusState,] = useContext(StatusContext);
    const cfg:any = JSON.parse(statusState.status.NewHomeConf || '{}') || {};
    const Header = () => {
        const [endpointIndex, setEndpointIndex] = useState(0);
        const [serverAddress, setServerAddress] = useState('');
        const [isScrolling, setIsScrolling] = useState(false);
        const [isBouncing, setIsBouncing] = useState(false);

        const el = React.useRef(null);

        React.useEffect(() => {
            const typed = new Typed(el.current, {
                strings: cfg.desc || [],
                typeSpeed: 50,
                loop: true
            });

            return () => {
                // Destroy Typed instance during cleanup to stop animation
                typed.destroy();
            };
        }, []);

        // 设置服务器地址 - 优先使用status中的server_address配置
        React.useEffect(() => {
            let address = '';

            // 优先使用status中配置的server_address
            if (statusState.status.server_address && statusState.status.server_address.trim() !== '') {
                address = statusState.status.server_address.trim();
                // 确保地址以http://或https://开头
                if (!address.startsWith('http://') && !address.startsWith('https://')) {
                    address = `https://${address}`;
                }
            } else {
                // 如果没有配置server_address，则使用当前页面地址
                address = `${window.location.protocol}//${window.location.host}`;
            }

            setServerAddress(address);
        }, [statusState.status.server_address]);

        // 轮播效果 - 从下往上连续滚动
        React.useEffect(() => {
            const timer = setInterval(() => {
                // 先更新到下一个索引，然后开始滚动动画
                setEndpointIndex((prev) => (prev + 1) % API_ENDPOINTS.length);
                setIsScrolling(true);

                // 滚动动画完成后回到静态状态
                setTimeout(() => {
                    setIsScrolling(false);

                    // 添加小跳跃效果
                    setTimeout(() => {
                        setIsBouncing(true);
                        setTimeout(() => {
                            setIsBouncing(false);
                        }, 500); // 跳跃动画时长
                    }, 100); // 稍微延迟一下再开始跳跃
                }, 600); // 滚动动画时长
            }, 3000);
            return () => clearInterval(timer);
        }, []);

        const handleCopyBaseURL = async () => {
            const success = await copy(serverAddress);
            if (success) {
                message.success('API地址已复制到剪贴板');
            } else {
                message.error('复制失败，请手动复制');
            }
        };

        return <>
            <Flex gap={50} className={'py-10'} vertical align={'center'}>
                <h1 className={'text-4xl md:text-7xl'}>
                    <span
                        className={'bg-gradient-to-r from-red-400 to-red-600 text-transparent bg-clip-text'}>{statusState.status.system_name}</span>
                </h1>
                <h2 className={'text-xl md:text-3xl text-center px-2'}>
                    {(cfg.subTitle || null) ? <div dangerouslySetInnerHTML={{__html:cfg.subTitle}}></div> : <>
                        基于 <span
                        className={'bg-gradient-to-r from-red-400 to-red-600 text-transparent bg-clip-text'}>官方API</span> 的中转服务，
                        <span
                            className={'bg-gradient-to-r from-red-400 to-red-600 text-transparent bg-clip-text'}>高速、高稳定、高并发</span>
                    </>}
                </h2>
                <p>
                    <span className={'text-lg md:text-2xl'} ref={el}/>
                </p>

                {/* API地址显示区域 - 简洁版本 */}
                <div className="w-full max-w-3xl px-4">
                    <p className="text-center mb-4 text-base md:text-lg text-gray-600">
                        更好的价格，更好的稳定性，只需要将模型基址替换为：
                    </p>

                    {/* 简洁的卡片设计 */}
                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-100 shadow-sm">
                        <Flex gap={8} align="center" className="w-full flex-col md:flex-row">
                            <div className="flex-1 w-full md:w-auto">
                                <div className="bg-white rounded-lg px-4 py-3 border border-gray-200 text-center md:text-left">
                                    <span className="text-gray-800 select-all font-mono text-sm md:text-base break-all">
                                        {serverAddress}
                                    </span>
                                    <div className="mt-1 md:mt-0 md:float-right">
                                        <div className="api-endpoint-container">
                                            {!isScrolling ? (
                                                // 静态显示状态 - 可以跳跃
                                                <div className={`api-endpoint-current ${isBouncing ? 'bounce-active' : ''}`}>
                                                    {API_ENDPOINTS[endpointIndex]}
                                                </div>
                                            ) : (
                                                // 滚动动画状态 - 上一个文字滚上去，当前文字滚上来
                                                <div className="api-endpoint-scroll scrolling">
                                                    <span className="api-endpoint-text">
                                                        {API_ENDPOINTS[(endpointIndex - 1 + API_ENDPOINTS.length) % API_ENDPOINTS.length]}
                                                    </span>
                                                    <span className="api-endpoint-text">
                                                        {API_ENDPOINTS[endpointIndex]}
                                                    </span>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <Button
                                type="primary"
                                size="large"
                                onClick={handleCopyBaseURL}
                                icon={<CopyOutlined />}
                                className="w-full md:w-auto rounded-lg bg-blue-500 hover:bg-blue-600 border-0 shadow-sm"
                            >
                                复制地址
                            </Button>
                        </Flex>

                        {/* 简化的提示文字 */}
                        <div className="mt-3 text-center">
                            <span className="text-xs text-gray-500">
                                API端点每3秒自动切换显示 • 支持多种AI模型接口
                            </span>
                        </div>
                    </div>
                </div>

                {/* 操作按钮 - 保持您的样式风格 */}
                <Flex gap={16} className="flex-wrap gap-y-4 gap-x-2 md:gap-x-4" justify="center">
                    <Button
                        type="primary"
                        size="large"
                        icon={<KeyOutlined />}
                        onClick={() => window.location.href = '/token'}
                    >
                        获取密钥
                    </Button>

                    {cfg.links?.map((item:any, index: number)=>{
                        return <Button
                            key={index}
                            type={item.type}
                            size='large'
                            href={item.href}
                            target={item.target}
                        >
                            {item.name}
                        </Button>
                    })}
                </Flex>
            </Flex>
        </>
    }

    const Price = () => {
        return <div>
            <Flex justify={'center'} className={'mt-10'}>
                <div className={'flex-1'}>
                    <Pricing home={true} maxRows={15}></Pricing>
                    <div className='text-xs text-center my-5'>注：此处仅展示15条模型数据费率价格，若需查看更多请点击下方按钮进入查阅</div>
                    <div className='text-center mt-5'><Button size='large' type='primary' href='/account/pricing' icon={<SendOutlined />} ghost={true}> 查看更多模型价格费率说明</Button></div>
                </div>
            </Flex>
        </div>
    }

    const ServiceData = ()=>{
        return <div>
            <h1 className={'text-3xl my-10 mt-3 text-center bg-gradient-to-r from-red-400 to-red-600 text-transparent bg-clip-text'}>服务数据</h1>
            <Flex gap="large" justify={'center'}>
                {cfg.serviceData?.map((item: any[]) => {
                    return <div className={'text-center'}>
                        <p className={'text-lg'}>{item[0]}</p>
                        <p className={'mt-2 font-bold bg-gradient-to-r from-red-400 to-red-600 text-transparent bg-clip-text'}>{item[1]}</p>
                    </div>
                })}
            </Flex>
            <h1 className={'text-3xl my-10 text-center bg-gradient-to-r from-red-400 to-red-600 text-transparent bg-clip-text'}>价格说明</h1>
            <Flex gap="large" justify={'center'}>
                {cfg.priceData?.map((item: any[]) => {
                    return <div className={'text-center'}>
                        <p className={'text-lg'}>{item[0]}</p>
                        <p className={'mt-2 font-bold bg-gradient-to-r from-red-400 to-red-600 text-transparent bg-clip-text'}>
                            <p>{item[1]}</p>
                            <p>{item[2]}</p>
                        </p>
                    </div>
                })}
            </Flex>
        </div>
    }

    const Qa = () => {
        const getItems: (panelStyle: CSSProperties) => CollapseProps['items'] = (panelStyle) => {
            return cfg.qa?.map((item: any[], index: number) => {
                return {
                    key: `${index}-qa`,
                    label: item[0],
                    children: <p>{item[1]}</p>,
                    style: panelStyle,
                }
            }) || []
        }
        const {token} = theme.useToken();
        const panelStyle: React.CSSProperties = {
            marginBottom: 24,
            borderRadius: token.borderRadiusLG,
            border: 'none',
        };
        return <div>
            <h1 className={'text-3xl my-10 text-center bg-gradient-to-r from-red-400 to-red-600 text-transparent bg-clip-text'}>常见问题</h1>
            <Collapse
                bordered={false}
                defaultActiveKey={['1']}
                expandIcon={({isActive}) => <CaretRightOutlined rotate={isActive ? 90 : 0}/>}
                style={{background: token.colorBgContainer}}
                items={getItems(panelStyle)}
            />
        </div>
    }


    const Footer = () => {
        const [statusState,] = useContext(StatusContext);
        return <div className={'text-center py-10'}>
            ©️{statusState.status.system_name} 2024. All rights reserved.
        </div>
    }

    return <div>
        <div style={{backgroundImage: `url(${BgLine})`}}>
            <div>
                <div className={'p-3'}>
                    <Header></Header>
                </div>
            </div>
            <ServiceData></ServiceData>
        </div>
        <div className={'flex justify-center'}>
            <div className={'p-3 overflow-hidden lg:max-w-[1000px] xl:max-w-[1200px] 2xl:max-w-[1400px] w-full'}>
                <Price></Price>
                <Qa></Qa>
            </div>
        </div>
        <Footer></Footer>
    </div>
};

export default DefaultHomeContent;
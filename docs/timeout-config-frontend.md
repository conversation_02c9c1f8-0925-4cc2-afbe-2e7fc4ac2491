# 超时配置前端管理界面使用说明

## 概述

超时配置管理界面为管理员提供了一个直观的 Web 界面来管理用户的个性化超时设置。该界面集成在现有的管理面板中，提供完整的 CRUD（创建、读取、更新、删除）功能。

## 访问方式

1. 以管理员身份登录系统
2. 进入 **配置管理** 页面
3. 选择 **超时** 选项卡

## 功能特性

### 1. 配置列表显示

- **用户信息**：显示用户名和用户 ID
- **模型配置**：显示具体模型名称或"通用配置"标识
- **超时参数**：
  - 首字节超时（秒）
  - 总超时时间（秒）
  - TPS阈值（token/s）
- **费用承担方**：用户承担或管理员承担
- **创建时间**：配置创建的时间戳

### 2. 创建新配置

点击 **新建超时配置** 按钮，在弹出的表单中填写：

#### 必填字段
- **用户**：从下拉列表中选择目标用户
- **首字节超时**：1-300秒之间
- **总超时时间**：1-600秒之间
- **TPS阈值**：1-1000 token/s之间
- **超时费用承担方**：用户承担/管理员承担

#### 可选字段
- **模型名称**：选择具体模型，留空表示通用配置

### 3. 编辑配置

- 点击配置行的 **编辑** 按钮
- 在弹出的表单中修改参数
- 点击 **更新** 保存更改

### 4. 删除配置

- 点击配置行的 **删除** 按钮
- 确认删除操作

### 5. 分页和搜索

- 支持分页浏览配置列表
- 可设置每页显示数量
- 支持快速跳转到指定页面

## 配置优先级说明

系统按以下优先级应用超时配置：

1. **用户+特定模型** > 2. **用户+通用配置(*)** > 3. **系统默认**

### 示例场景

假设用户 A 有以下配置：
- 通用配置：首字节30秒，总超时120秒
- GPT-4模型：首字节60秒，总超时180秒

当用户 A 使用：
- **GPT-4**：使用特定配置（60秒/180秒）
- **其他模型**：使用通用配置（30秒/120秒）

## 费用承担机制

### 用户承担（默认）
- 超时时产生的费用由用户账户扣除
- 正常的计费流程

### 管理员承担
- 超时时用户不被扣费
- 管理员承担超时产生的费用
- 适用于测试或特殊用户场景

## 参数说明

### 首字节超时 (First Byte Timeout)
- **定义**：从发起请求到收到第一个响应字节的最大等待时间
- **用途**：检测上游服务是否响应
- **建议值**：30-60秒

### 总超时时间 (Total Timeout)  
- **定义**：整个请求的最大执行时间
- **用途**：防止长时间占用资源
- **建议值**：120-300秒

### TPS阈值 (Tokens Per Second)
- **定义**：每秒生成Token数量的最低阈值
- **用途**：判断响应速度是否过慢
- **建议值**：10-50 token/s

## 使用建议

### 1. 针对不同用户类型设置

- **普通用户**：使用默认或较短的超时时间
- **VIP用户**：可设置较长的超时时间和管理员承担费用
- **测试用户**：建议管理员承担费用

### 2. 针对不同模型设置

- **快速模型**（如GPT-3.5）：较短超时时间
- **慢速模型**（如GPT-4、Claude）：较长超时时间
- **图像生成模型**：最长超时时间

### 3. 费用控制策略

- **生产环境**：用户承担费用
- **测试环境**：管理员承担费用
- **新用户试用**：初期管理员承担费用

## 操作示例

### 示例1：为VIP用户设置长超时

```
用户: vip_user_001
模型: (留空 - 通用配置)
首字节超时: 60秒
总超时时间: 300秒
TPS阈值: 5 token/s
费用承担方: 管理员承担
```

### 示例2：为GPT-4设置特殊超时

```
用户: heavy_user_002
模型: gpt-4
首字节超时: 90秒
总超时时间: 600秒
TPS阈值: 3 token/s
费用承担方: 用户承担
```

## 注意事项

1. **删除配置前请确认**：删除操作不可撤销
2. **合理设置超时时间**：过长的超时可能影响系统性能
3. **监控费用影响**：管理员承担费用的配置需要定期审查
4. **配置生效时间**：配置修改后立即生效，影响后续请求

## 技术支持

如遇到问题，请检查：
1. 管理员权限是否正确
2. 后端API服务是否正常
3. 用户和模型数据是否存在

更多技术细节请参考：
- [后端API文档](./timeout-config-usage.md)
- [系统架构说明](./README-timeout-config.md) 
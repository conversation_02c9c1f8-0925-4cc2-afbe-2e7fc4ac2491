# ClickHouse 复用 MySQL 连接配置方案

## 概述

为了简化配置管理并基于已验证的生产环境配置，ClickHouse 现在直接复用 MySQL 的连接池配置参数。

## 配置复用策略

### 复用的配置参数

| MySQL 配置 | ClickHouse 使用 | 说明 |
|-----------|----------------|------|
| `SQL_MAX_OPEN_CONNS` | ✅ 直接复用 | 最大打开连接数 |
| `SQL_MAX_IDLE_CONNS` | ✅ 直接复用 | 最大空闲连接数 |
| `SQL_MAX_LIFETIME` | ✅ 直接复用 | 连接最大生存时间(秒) |

### 代码实现

```go
// 在 log_storage_clickhouse.go 中
finalDB.SetMaxOpenConns(env.Int("SQL_MAX_OPEN_CONNS", 1000))
finalDB.SetMaxIdleConns(env.Int("SQL_MAX_IDLE_CONNS", 100))
finalDB.SetConnMaxLifetime(time.Second * time.Duration(env.Int("SQL_MAX_LIFETIME", 60)))
```

## 优势分析

### 1. 配置简化
- ❌ **之前**: 需要单独管理 `CLICKHOUSE_MAX_OPEN_CONNS` 等参数
- ✅ **现在**: 只需配置 `SQL_MAX_*` 参数，MySQL 和 ClickHouse 自动同步

### 2. 基于生产验证
- 您的 MySQL 配置已在生产环境验证：
  - `SQL_MAX_OPEN_CONNS=5000`
  - `SQL_MAX_IDLE_CONNS=500`
  - `SQL_MAX_LIFETIME=30`
- ClickHouse 直接继承这些经过验证的高并发配置

### 3. 统一管理
- 数据库连接配置统一管理
- 减少配置错误和不一致的风险
- 便于运维和监控

## 当前生产配置效果

基于您的 MySQL 配置，ClickHouse 现在具备：

```bash
# 实际生效的配置
MaxOpenConns: 5000    # 支持5000个并发连接
MaxIdleConns: 500     # 保持500个空闲连接
ConnMaxLifetime: 30秒  # 30秒连接生存时间
```

**性能能力：**
- ✅ 支持每分钟 50,000+ 条记录写入
- ✅ 支持每秒 1,666+ 次数据库操作
- ✅ 支持突发流量处理
- ✅ 基于已验证的生产配置

## 配置建议

### 生产环境
```bash
# 已验证的高并发配置
SQL_MAX_OPEN_CONNS=5000
SQL_MAX_IDLE_CONNS=500
SQL_MAX_LIFETIME=30

# ClickHouse 专用批量配置
LOG_STORAGE_BATCH_SIZE=10000
LOG_STORAGE_FLUSH_INTERVAL=3
LOG_STORAGE_ASYNC_WRITE=true
```

### 测试环境
```bash
# 适度的测试配置
SQL_MAX_OPEN_CONNS=1000
SQL_MAX_IDLE_CONNS=100
SQL_MAX_LIFETIME=60

# ClickHouse 测试配置
LOG_STORAGE_BATCH_SIZE=2000
LOG_STORAGE_FLUSH_INTERVAL=10
LOG_STORAGE_ASYNC_WRITE=true
```

## 监控要点

### 1. 连接池状态
```go
// 同时监控 MySQL 和 ClickHouse 连接池
mysqlStats := mysqlDB.Stats()
clickhouseStats := clickhouseDB.Stats()

logger.Infof("MySQL连接池: Open=%d, InUse=%d, Idle=%d", 
    mysqlStats.OpenConnections, mysqlStats.InUse, mysqlStats.Idle)
logger.Infof("ClickHouse连接池: Open=%d, InUse=%d, Idle=%d", 
    clickhouseStats.OpenConnections, clickhouseStats.InUse, clickhouseStats.Idle)
```

### 2. 总连接数监控
- 注意 MySQL + ClickHouse 的总连接数
- 确保数据库服务器能够支持总连接数

## 注意事项

### 1. 连接数叠加
- MySQL 和 ClickHouse 会分别使用相同的连接数配置
- 总连接数 = MySQL连接数 + ClickHouse连接数
- 确保数据库服务器配置足够支持

### 2. 资源监控
- 监控内存使用情况
- 监控网络连接数
- 监控数据库服务器负载

### 3. 配置调优
- 如果需要单独调优 ClickHouse，可以考虑添加专用配置
- 当前方案适合大多数场景，简化了配置管理

## 总结

通过复用 MySQL 的连接配置，我们实现了：

1. **配置简化**: 减少了配置参数数量
2. **基于验证**: 使用已在生产环境验证的配置
3. **统一管理**: 数据库连接配置统一管理
4. **高性能**: 支持每分钟 5 万条记录的写入需求

这种方案特别适合您这种已有成熟 MySQL 配置的场景，既保证了性能，又简化了运维复杂度。

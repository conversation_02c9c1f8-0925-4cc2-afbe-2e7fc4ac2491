# 随行付聚合支付集成文档

## 概述

本文档描述了在Shell API系统中集成随行付聚合支付功能的实现。该功能模仿支付宝当面付的实现方式，支持支付宝和微信支付的聚合支付。

## 功能特性

- 支持支付宝扫码支付
- 支持微信扫码支付
- 聚合支付界面，用户可选择支付方式
- 实时支付状态轮询
- 安全的RSA签名验证
- 支付回调处理
- 完整的订单管理

## 技术架构

### 后端实现

#### 1. 配置管理 (`common/config/config.go`)
新增配置项：
- `SuixingpayEnabled`: 启用随行付聚合支付
- `SuixingpayOrgId`: 机构编号
- `SuixingpayPlatformPublicKey`: 平台公钥
- `SuixingpayMerchantPrivateKey`: 商户私钥
- `SuixingpayMerchantNo`: 商户编号
- `SuixingpayCallbackAddress`: 回调地址

#### 2. SDK实现 (`common/suixingpay/`)
- `client.go`: 随行付客户端，包含签名、验签、API请求等功能
- `types.go`: 数据结构定义，包含请求和响应的结构体

#### 3. 控制器 (`controller/suixingpay.go`)
- `SuixingpayPay`: 创建支付订单
- `SuixingpayStatus`: 查询支付状态
- `SuixingpayCallback`: 处理支付回调

#### 4. 路由配置 (`router/api.go`)
- `/api/user/topup/suixingpay`: 创建支付订单
- `/api/user/topup/suixingpay/status`: 查询支付状态
- `/api/suixingpay/callback`: 支付回调（无需认证）

### 前端实现

#### 1. 组件更新
- `ByEpay.tsx`: 添加随行付支付按钮和处理逻辑
- `SystemConfig.tsx`: 添加随行付配置表单
- `Recharge.tsx`: 传递随行付启用状态
- `Status/reducer.tsx`: 添加状态管理

#### 2. 用户界面
- 随行付支付宝按钮（蓝色主题）
- 随行付微信按钮（绿色主题）
- 二维码支付弹窗
- 支付状态轮询

## 使用说明

### 管理员配置

1. 登录管理后台
2. 进入系统设置页面
3. 找到"随行付聚合支付配置"部分
4. 填写以下信息：
   - 启用随行付聚合支付：开启
   - 随行付机构编号：从随行付获取
   - 随行付平台公钥：从随行付获取
   - 随行付商户私钥：商户生成的RSA私钥
   - 随行付商户编号：从随行付获取
   - 随行付回调地址：可选，留空使用默认地址

### 用户使用

1. 进入充值页面
2. 选择在线充值
3. 输入充值金额
4. 点击"随行付支付宝"或"随行付微信"按钮
5. 扫描二维码完成支付
6. 系统自动检测支付状态并更新余额

## API接口

### 创建支付订单

```http
POST /api/user/topup/suixingpay
Content-Type: application/json

{
    "amount": 100.00,
    "currency": "USD",
    "payment_type": "alipay"
}
```

响应：
```json
{
    "success": true,
    "message": "创建随行付聚合支付订单成功",
    "qr_code": "https://qr.alipay.com/...",
    "out_trade_no": "123456789"
}
```

### 查询支付状态

```http
GET /api/user/topup/suixingpay/status?out_trade_no=123456789
```

响应：
```json
{
    "success": true,
    "message": "支付成功",
    "status": "TRADE_SUCCESS",
    "trade_no": "123456789",
    "out_trade_no": "123456789"
}
```

### 支付回调

```http
POST /api/suixingpay/callback
Content-Type: application/json

{
    "bizCode": "0000",
    "bizMsg": "成功",
    "ordNo": "123456789",
    "sxfUuid": "SXF123456789",
    "buyerId": "buyer123",
    "transactionId": "trans123",
    "amt": "100.00",
    "payTime": "20231201120000",
    "payType": "ALIPAY",
    "sign": "..."
}
```

## 安全特性

1. **RSA签名验证**: 所有API请求和回调都使用RSA签名确保数据完整性
2. **订单锁定**: 使用数据库事务和行锁防止重复处理
3. **状态检查**: 避免重复充值和状态异常
4. **参数验证**: 严格验证所有输入参数

## 错误处理

- 配置错误：检查随行付配置是否完整
- 网络错误：自动重试机制
- 签名错误：记录日志并拒绝请求
- 订单异常：事务回滚确保数据一致性

## 日志记录

系统会记录以下关键操作：
- 支付订单创建
- 支付状态变更
- 回调处理结果
- 错误和异常情况

## 注意事项

1. 确保服务器时间准确，签名验证依赖时间戳
2. 回调地址必须可以从外网访问
3. 私钥和公钥格式必须正确
4. 建议在测试环境充分测试后再部署到生产环境

## 故障排除

### 常见问题

1. **签名验证失败**
   - 检查私钥和公钥格式
   - 确认机构编号和商户编号正确

2. **回调接收失败**
   - 检查回调地址是否可访问
   - 确认防火墙设置

3. **支付状态异常**
   - 查看系统日志
   - 检查数据库订单状态

## 更新日志

- v1.0.0: 初始版本，支持支付宝和微信聚合支付

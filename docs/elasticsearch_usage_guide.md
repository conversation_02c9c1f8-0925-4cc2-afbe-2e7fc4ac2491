# Elasticsearch 日志存储使用指南

## 概述

Elasticsearch是一个强大的分布式搜索和分析引擎，特别适合日志存储和分析场景。本指南将帮助您在日志存储抽象层中使用Elasticsearch。

## 特性优势

### 🔍 强大的搜索能力
- **全文搜索**: 支持复杂的文本搜索和分析
- **实时搜索**: 近实时的搜索响应
- **多字段搜索**: 同时搜索多个字段
- **聚合分析**: 强大的数据聚合和统计功能

### 📊 数据分析
- **时间序列分析**: 优秀的时间序列数据处理
- **多维度聚合**: 支持复杂的多维度数据分析
- **可视化集成**: 与Kibana无缝集成

### 🚀 高性能
- **水平扩展**: 支持集群扩展
- **分片存储**: 数据自动分片和负载均衡
- **缓存优化**: 智能缓存机制

## 快速开始

### 1. 部署Elasticsearch

使用提供的部署脚本：
```bash
./scripts/setup_elasticsearch.sh
```

或手动安装：
```bash
# macOS
brew tap elastic/tap
brew install elastic/tap/elasticsearch-full

# Ubuntu/Debian
wget -qO - https://artifacts.elastic.co/GPG-KEY-elasticsearch | sudo apt-key add -
echo "deb https://artifacts.elastic.co/packages/8.x/apt stable main" | sudo tee /etc/apt/sources.list.d/elastic-8.x.list
sudo apt-get update && sudo apt-get install elasticsearch
```

### 2. 配置应用

设置环境变量：
```bash
export LOG_STORAGE_ENABLED=true
export LOG_STORAGE_TYPE=elasticsearch
export ELASTICSEARCH_HOSTS=http://localhost:9200
export ELASTICSEARCH_INDEX=shell-api-logs
```

### 3. 启动服务

```bash
# 启动Elasticsearch
sudo systemctl start elasticsearch  # Linux
brew services start elasticsearch   # macOS

# 验证服务
curl http://localhost:9200
```

## 配置详解

### 基础配置

```bash
# 日志存储基础配置
LOG_STORAGE_ENABLED=true                    # 启用新日志存储系统
LOG_STORAGE_TYPE=elasticsearch              # 存储类型
LOG_STORAGE_FALLBACK_TO_MYSQL=true         # 启用MySQL回退
LOG_STORAGE_ASYNC_WRITE=true               # 异步写入
LOG_STORAGE_BATCH_SIZE=1000                # 批量大小
LOG_STORAGE_FLUSH_INTERVAL=5               # 刷新间隔(秒)

# Elasticsearch连接配置
ELASTICSEARCH_HOSTS=http://localhost:9200   # ES主机地址(多个用逗号分隔)
ELASTICSEARCH_USERNAME=                     # 用户名(可选)
ELASTICSEARCH_PASSWORD=                     # 密码(可选)
ELASTICSEARCH_INDEX=shell-api-logs          # 索引名称
```

### 高级配置

```bash
# 索引配置
ELASTICSEARCH_SHARDS=3                      # 分片数
ELASTICSEARCH_REPLICAS=1                    # 副本数
ELASTICSEARCH_REFRESH_INTERVAL=5s           # 刷新间隔

# 性能配置
LOG_STORAGE_COMPRESSION_ENABLED=true       # 启用压缩
LOG_STORAGE_MAX_RETRIES=3                  # 最大重试次数
```

## 索引设计

### 索引模板

系统会自动创建以下索引模板：

```json
{
  "index_patterns": ["shell-api-logs*"],
  "template": {
    "settings": {
      "number_of_shards": 3,
      "number_of_replicas": 1,
      "refresh_interval": "5s"
    },
    "mappings": {
      "properties": {
        "id": {"type": "keyword"},
        "request_id": {"type": "keyword"},
        "user_id": {"type": "integer"},
        "created_at": {"type": "date"},
        "type": {"type": "integer"},
        "content": {"type": "text"},
        "username": {"type": "keyword"},
        "token_name": {"type": "keyword"},
        "model_name": {"type": "keyword"},
        "quota": {"type": "integer"},
        "prompt_tokens": {"type": "integer"},
        "completion_tokens": {"type": "integer"},
        "ip": {"type": "ip"},
        "error_code": {"type": "keyword"}
      }
    }
  }
}
```

### 索引命名策略

- **按月分割**: `shell-api-logs-2024-01`
- **自动轮转**: 每月自动创建新索引
- **便于管理**: 可以按月删除历史数据

## 使用示例

### 基础日志写入

```go
// 获取日志管理器
logManager := GetLogManager()

// 记录单条日志
ctx := context.Background()
log := &Log{
    UserId:      1,
    Type:        LogTypeConsume,
    Content:     "API调用",
    Username:    "testuser",
    ModelName:   "gpt-3.5-turbo",
    Quota:       100,
    CreatedAt:   time.Now().Unix(),
}

err := logManager.RecordLog(ctx, log)
if err != nil {
    logger.Error(ctx, "Failed to record log: " + err.Error())
}
```

### 批量写入

```go
// 批量记录日志
logs := []*Log{
    // ... 多条日志
}

err := logManager.RecordLogBatch(ctx, logs)
if err != nil {
    logger.Error(ctx, "Failed to record batch logs: " + err.Error())
}
```

### 搜索查询

```go
// 全文搜索
logs, err := logManager.SearchAllLogs("error")
if err != nil {
    logger.Error(ctx, "Search failed: " + err.Error())
}

// 用户日志搜索
userLogs, err := logManager.SearchUserLogs(userId, "gpt-4")
if err != nil {
    logger.Error(ctx, "User search failed: " + err.Error())
}
```

## 性能优化

### 写入优化

1. **批量写入**
   ```bash
   LOG_STORAGE_BATCH_SIZE=1000    # 增加批量大小
   LOG_STORAGE_ASYNC_WRITE=true   # 启用异步写入
   ```

2. **刷新策略**
   ```bash
   ELASTICSEARCH_REFRESH_INTERVAL=30s  # 降低刷新频率
   ```

3. **分片配置**
   ```bash
   ELASTICSEARCH_SHARDS=5         # 根据数据量调整分片数
   ```

### 查询优化

1. **使用过滤器**
   - 优先使用term查询而非match查询
   - 合理使用时间范围过滤

2. **索引优化**
   - 关闭不需要搜索的字段索引
   - 使用合适的数据类型

3. **聚合优化**
   - 使用doc_values进行聚合
   - 避免深度聚合

### 存储优化

1. **压缩设置**
   ```json
   {
     "settings": {
       "codec": "best_compression"
     }
   }
   ```

2. **生命周期管理**
   - 设置索引生命周期策略
   - 自动删除过期数据

## 监控和运维

### 健康检查

```bash
# 集群健康状态
curl http://localhost:9200/_cluster/health

# 索引状态
curl http://localhost:9200/_cat/indices?v

# 节点信息
curl http://localhost:9200/_cat/nodes?v
```

### 性能监控

```bash
# 查看索引统计
curl http://localhost:9200/shell-api-logs/_stats

# 查看搜索性能
curl http://localhost:9200/_cat/thread_pool?v

# 查看内存使用
curl http://localhost:9200/_cat/nodes?h=name,heap.percent,ram.percent&v
```

### 常用运维命令

```bash
# 查看所有索引
curl http://localhost:9200/_cat/indices

# 删除旧索引
curl -X DELETE http://localhost:9200/shell-api-logs-2023-12

# 强制合并索引
curl -X POST http://localhost:9200/shell-api-logs/_forcemerge

# 重新分配分片
curl -X POST http://localhost:9200/_cluster/reroute
```

## 与Kibana集成

### 安装Kibana

```bash
# 使用部署脚本时选择安装Kibana
./scripts/setup_elasticsearch.sh

# 或手动安装
brew install elastic/tap/kibana-full  # macOS
sudo apt-get install kibana           # Ubuntu
```

### 配置数据源

1. 访问 http://localhost:5601
2. 进入 "Stack Management" > "Index Patterns"
3. 创建索引模式: `shell-api-logs*`
4. 选择时间字段: `created_at`

### 创建仪表板

1. **日志概览**
   - 总日志数量
   - 错误率趋势
   - 用户活跃度

2. **性能分析**
   - 响应时间分布
   - Token使用统计
   - 模型使用排行

3. **错误分析**
   - 错误类型分布
   - 错误趋势图
   - 错误详情表

## 故障排除

### 常见问题

1. **连接失败**
   ```
   错误: connection refused
   解决: 检查Elasticsearch服务状态
   ```

2. **索引创建失败**
   ```
   错误: index creation failed
   解决: 检查磁盘空间和权限
   ```

3. **搜索超时**
   ```
   错误: search timeout
   解决: 优化查询条件，增加超时时间
   ```

### 日志分析

```bash
# 查看Elasticsearch日志
tail -f /var/log/elasticsearch/elasticsearch.log

# 查看应用日志中的ES相关错误
grep -i elasticsearch /path/to/app.log
```

## 最佳实践

### 数据建模

1. **合理的字段类型**
   - 使用keyword类型存储不需要分词的字段
   - 使用date类型存储时间字段
   - 使用integer类型存储数值字段

2. **索引设计**
   - 按时间分割索引
   - 合理设置分片数量
   - 配置适当的副本数

### 安全配置

1. **网络安全**
   - 限制访问IP
   - 使用防火墙规则
   - 配置SSL/TLS

2. **认证授权**
   - 启用X-Pack安全功能
   - 配置用户角色
   - 使用API密钥

### 备份策略

1. **快照备份**
   ```bash
   # 创建快照仓库
   curl -X PUT "localhost:9200/_snapshot/backup_repo" -H 'Content-Type: application/json' -d'
   {
     "type": "fs",
     "settings": {
       "location": "/backup/elasticsearch"
     }
   }'
   
   # 创建快照
   curl -X PUT "localhost:9200/_snapshot/backup_repo/snapshot_1"
   ```

2. **定期清理**
   - 设置索引生命周期策略
   - 自动删除过期数据
   - 监控磁盘使用情况

## 总结

Elasticsearch为日志存储提供了强大的搜索和分析能力，特别适合以下场景：

✅ **大量日志数据**: 支持PB级数据存储  
✅ **复杂搜索需求**: 全文搜索、多条件过滤  
✅ **实时分析**: 近实时的数据分析和可视化  
✅ **水平扩展**: 支持集群扩展和高可用  
✅ **生态丰富**: 与ELK栈完美集成  

通过合理的配置和优化，Elasticsearch可以为您的日志系统提供高性能、高可用的存储和分析能力。

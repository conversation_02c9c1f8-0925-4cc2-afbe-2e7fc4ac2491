# ClickHouse 日志存储使用指南

## 概述

ClickHouse是一个高性能的列式数据库管理系统，专为OLAP（在线分析处理）场景设计。对于每天1亿条日志的场景，ClickHouse是最佳选择。

## 特性优势

### 🚀 极致性能
- **写入性能**: 支持每秒100万条记录写入
- **查询性能**: 毫秒级聚合查询响应
- **压缩比**: 10:1的数据压缩，大幅节省存储空间
- **并发处理**: 支持高并发读写操作

### 📊 OLAP优化
- **列式存储**: 只读取需要的列，大幅提升查询效率
- **向量化执行**: 批量处理数据，提升CPU利用率
- **预聚合**: 支持物化视图和预计算
- **分区优化**: 按时间分区，支持分区裁剪

### 💰 成本效益
- **存储成本**: 比传统数据库节省70%存储空间
- **硬件要求**: 单机即可处理TB级数据
- **运维简单**: 相比分布式方案更易维护

## 快速开始

### 1. 部署ClickHouse

使用提供的部署脚本：
```bash
./scripts/setup_clickhouse.sh
```

或手动安装：
```bash
# Ubuntu/Debian
sudo apt-get install clickhouse-server clickhouse-client

# CentOS/RHEL  
sudo yum install clickhouse-server clickhouse-client

# macOS
brew install clickhouse
```

### 2. 配置应用

设置环境变量：
```bash
export LOG_STORAGE_ENABLED=true
export LOG_STORAGE_TYPE=clickhouse
export CLICKHOUSE_HOST=localhost
export CLICKHOUSE_PORT=9000
export CLICKHOUSE_DATABASE=shell_api_logs
```

### 3. 安装Go驱动

```bash
go get github.com/ClickHouse/clickhouse-go/v2
```

### 4. 启动服务

```bash
# 启动ClickHouse
sudo systemctl start clickhouse-server  # Linux
brew services start clickhouse          # macOS

# 验证服务
clickhouse-client --query "SELECT 1"
```

## 配置详解

### 基础配置

```bash
# 日志存储基础配置
LOG_STORAGE_ENABLED=true                    # 启用新日志存储系统
LOG_STORAGE_TYPE=clickhouse                 # 存储类型
LOG_STORAGE_FALLBACK_TO_MYSQL=true         # 启用MySQL回退
LOG_STORAGE_ASYNC_WRITE=true               # 异步写入
LOG_STORAGE_BATCH_SIZE=5000                # 批量大小(建议5000)
LOG_STORAGE_FLUSH_INTERVAL=5               # 刷新间隔(秒)

# ClickHouse连接配置
CLICKHOUSE_HOST=localhost                   # ClickHouse主机
CLICKHOUSE_PORT=9000                       # 端口
CLICKHOUSE_USERNAME=default                # 用户名
CLICKHOUSE_PASSWORD=                       # 密码
CLICKHOUSE_DATABASE=shell_api_logs         # 数据库名
CLICKHOUSE_CLUSTER=                        # 集群名(可选)
```

### 高级配置

```bash
# 性能优化
LOG_STORAGE_COMPRESSION_ENABLED=true       # 启用压缩
LOG_STORAGE_MAX_RETRIES=3                  # 最大重试次数

# 集群配置(可选)
CLICKHOUSE_CLUSTER=my_cluster              # 集群名称
```

## 表结构设计

### 优化的表结构

```sql
CREATE TABLE logs (
    id UInt64,
    request_id String,
    user_id UInt32,
    created_at DateTime,
    type UInt8,
    content String,
    username LowCardinality(String),        -- 优化枚举类型
    token_name LowCardinality(String),
    token_group LowCardinality(String),
    model_name LowCardinality(String),
    channel_name LowCardinality(String),
    quota Int32,
    cost_quota Int32,
    prompt_tokens UInt32,
    completion_tokens UInt32,
    channel_id UInt32,
    token_key String,
    request_duration UInt64,
    response_first_byte_duration UInt64,
    total_duration UInt64,
    elapsed_time UInt64,
    is_stream UInt8,
    system_prompt_reset UInt8,
    ip String,
    remote_ip String,
    other String,
    error_code LowCardinality(String)
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(created_at)           -- 按月分区
ORDER BY (created_at, user_id, type)       -- 排序键优化
SETTINGS index_granularity = 8192;
```

### 设计亮点

1. **LowCardinality优化**: 对于枚举类型字段使用LowCardinality，节省存储和提升查询性能
2. **分区策略**: 按月分区，便于数据管理和查询优化
3. **排序键**: 按时间、用户、类型排序，优化常见查询模式
4. **数据类型**: 使用合适的数据类型，节省存储空间

## 性能优化

### 写入优化

1. **批量写入**
   ```bash
   LOG_STORAGE_BATCH_SIZE=5000    # 增大批量大小
   LOG_STORAGE_ASYNC_WRITE=true   # 启用异步写入
   ```

2. **写入策略**
   ```go
   // 应用层优化
   logManager := GetLogManager()
   
   // 批量写入
   logs := []*Log{...}  // 收集多条日志
   err := logManager.RecordLogBatch(ctx, logs)
   ```

### 查询优化

1. **利用分区裁剪**
   ```sql
   -- 好的查询：利用分区
   SELECT * FROM logs 
   WHERE created_at >= '2024-01-01' 
   AND created_at < '2024-02-01'
   
   -- 避免：跨多个分区的查询
   SELECT * FROM logs WHERE id = 12345
   ```

2. **使用合适的索引**
   ```sql
   -- 利用排序键
   SELECT * FROM logs 
   WHERE created_at >= '2024-01-01'
   AND user_id = 123
   ORDER BY created_at DESC
   ```

### 聚合优化

1. **物化视图**
   ```sql
   -- 创建预聚合视图
   CREATE MATERIALIZED VIEW daily_stats
   ENGINE = SummingMergeTree()
   ORDER BY (date, model_name)
   AS SELECT
       toDate(created_at) as date,
       model_name,
       count() as requests,
       sum(quota) as total_quota
   FROM logs
   GROUP BY date, model_name;
   ```

2. **高效聚合查询**
   ```sql
   -- 每日统计
   SELECT 
       toDate(created_at) as date,
       count(*) as requests,
       sum(quota) as total_quota
   FROM logs 
   WHERE created_at >= today() - 30
   GROUP BY date
   ORDER BY date;
   ```

## 实际使用示例

### 1. 基础日志写入

```go
// 获取日志管理器
logManager := GetLogManager()

// 记录单条日志
ctx := context.Background()
log := &Log{
    UserId:      1,
    Type:        LogTypeConsume,
    Content:     "API调用",
    Username:    "testuser",
    ModelName:   "gpt-3.5-turbo",
    Quota:       100,
    CreatedAt:   time.Now().Unix(),
}

err := logManager.RecordLog(ctx, log)
```

### 2. 高性能批量写入

```go
// 收集日志
var logs []*Log
for i := 0; i < 1000; i++ {
    logs = append(logs, &Log{
        UserId:    i,
        Type:      LogTypeConsume,
        Content:   fmt.Sprintf("Batch log %d", i),
        ModelName: "gpt-3.5-turbo",
        Quota:     100,
        CreatedAt: time.Now().Unix(),
    })
}

// 批量写入
err := logManager.RecordLogBatch(ctx, logs)
```

### 3. 复杂统计查询

```go
// 获取每日使用统计
stats, err := logManager.SumAllDailyUsageStatsByDimension(
    0,                    // userId (0表示所有用户)
    "Asia/Shanghai",      // timezone
    "",                   // tokenName
    "",                   // username
    0,                    // channel
    "",                   // channelName
    "",                   // modelName
    startTimestamp,       // 开始时间
    endTimestamp,         // 结束时间
    "model",              // 按模型维度
    "day",                // 按天粒度
)
```

## 监控和运维

### 性能监控

```sql
-- 查看表大小
SELECT 
    table,
    formatReadableSize(sum(bytes)) as size,
    sum(rows) as rows
FROM system.parts 
WHERE database = 'shell_api_logs'
GROUP BY table;

-- 查看分区信息
SELECT 
    partition,
    count() as parts,
    formatReadableSize(sum(bytes)) as size
FROM system.parts 
WHERE database = 'shell_api_logs' AND table = 'logs'
GROUP BY partition
ORDER BY partition;

-- 查看查询性能
SELECT 
    query,
    query_duration_ms,
    read_rows,
    read_bytes
FROM system.query_log 
WHERE type = 'QueryFinish'
ORDER BY event_time DESC
LIMIT 10;
```

### 数据管理

```sql
-- 删除过期分区(删除3个月前的数据)
ALTER TABLE logs DROP PARTITION '202310';

-- 优化表(合并小分片)
OPTIMIZE TABLE logs FINAL;

-- 检查表完整性
CHECK TABLE logs;
```

### 备份策略

```bash
# 创建备份
clickhouse-client --query "BACKUP TABLE shell_api_logs.logs TO Disk('backups', 'logs_backup_20240101')"

# 恢复备份
clickhouse-client --query "RESTORE TABLE shell_api_logs.logs FROM Disk('backups', 'logs_backup_20240101')"
```

## 故障排除

### 常见问题

1. **连接失败**
   ```
   错误: Connection refused
   解决: 检查ClickHouse服务状态
   sudo systemctl status clickhouse-server
   ```

2. **写入性能差**
   ```
   原因: 批量大小太小
   解决: 增加LOG_STORAGE_BATCH_SIZE到5000-10000
   ```

3. **查询超时**
   ```
   原因: 查询跨越太多分区
   解决: 添加时间范围过滤条件
   ```

4. **磁盘空间不足**
   ```
   解决: 删除过期分区或启用TTL
   ALTER TABLE logs MODIFY TTL created_at + INTERVAL 30 DAY;
   ```

### 性能调优

1. **内存设置**
   ```xml
   <!-- /etc/clickhouse-server/config.xml -->
   <max_memory_usage>10000000000</max_memory_usage>
   <max_bytes_before_external_group_by>8000000000</max_bytes_before_external_group_by>
   ```

2. **并发设置**
   ```xml
   <max_concurrent_queries>100</max_concurrent_queries>
   <max_thread_pool_size>10000</max_thread_pool_size>
   ```

## 最佳实践

### 数据建模

1. **合理的分区策略**
   - 按月分区适合大多数场景
   - 避免过多小分区
   - 考虑查询模式

2. **排序键设计**
   - 将最常用的过滤字段放在前面
   - 考虑基数：低基数在前，高基数在后
   - 避免过长的排序键

3. **数据类型选择**
   - 使用最小的数据类型
   - 枚举字段使用LowCardinality
   - 时间字段使用DateTime

### 查询优化

1. **利用分区裁剪**
   - 总是包含时间范围条件
   - 避免跨多个分区的点查询

2. **合理使用索引**
   - 利用排序键进行过滤
   - 避免在非索引字段上进行复杂过滤

3. **聚合优化**
   - 使用物化视图预聚合
   - 避免在大表上进行复杂聚合

## 总结

ClickHouse为大规模日志存储提供了完美的解决方案：

✅ **超高性能**: 每秒100万条写入，毫秒级查询  
✅ **极致压缩**: 10:1压缩比，节省70%存储成本  
✅ **OLAP优化**: 专为分析场景设计，聚合查询极快  
✅ **易于扩展**: 支持从单机到集群的平滑扩展  
✅ **运维友好**: 相比其他大数据方案更易维护  

对于每天1亿条日志的场景，ClickHouse是最佳选择！

# 日志存储抽象层架构文档

## 概述

为了解决每天1亿条日志写入导致MySQL性能问题，我们设计了一个日志存储抽象层，支持MySQL、ClickHouse、Elasticsearch等多种存储后端，并提供动态切换功能。

## 架构设计

```
应用层 (model/log.go)
    ↓
日志管理器 (LogManager)
    ↓
日志存储接口 (LogStorage)
    ↓
具体实现 (MySQL/ClickHouse/Elasticsearch)
```

## 核心组件

### 1. LogStorage 接口 (`model/log_storage_interface.go`)

定义了所有日志存储实现必须支持的操作：
- `RecordLog()` - 记录单条日志
- `RecordLogBatch()` - 批量记录日志
- `GetAllLogs()` - 获取所有日志
- `CountAllLogs()` - 统计日志数量
- `SumUsedQuota()` - 统计配额使用
- `HealthCheck()` - 健康检查
- `Close()` - 关闭连接

### 2. LogManager (`model/log_manager.go`)

日志管理器负责：
- 管理不同存储实现的切换
- 提供fallback机制（主存储失败时回退到MySQL）
- 单例模式确保全局唯一实例
- 线程安全的存储切换

### 3. 具体存储实现

#### MySQL存储 (`model/log_storage_mysql.go`)
- 封装现有的MySQL日志逻辑
- 完全兼容现有功能
- 作为默认和fallback存储

#### ClickHouse存储 (`model/log_storage_clickhouse.go`)
- 高性能列式存储，适合大数据量
- 支持批量写入和异步写入
- 按月分区，优化查询性能
- 需要安装: `go get github.com/ClickHouse/clickhouse-go/v2`

#### Elasticsearch存储 (`model/log_storage_elasticsearch.go`)
- 全文搜索和实时分析
- 支持ELK栈集成
- 需要安装: `go get github.com/elastic/go-elasticsearch/v8`

## 配置说明

### 环境变量配置

```bash
# 基础配置
LOG_STORAGE_ENABLED=true                    # 启用新日志存储系统
LOG_STORAGE_TYPE=mysql                      # 存储类型: mysql/clickhouse/elasticsearch
LOG_STORAGE_FALLBACK_TO_MYSQL=true         # 失败时回退到MySQL
LOG_STORAGE_ASYNC_WRITE=true               # 启用异步写入
LOG_STORAGE_BATCH_SIZE=1000                # 批量写入大小
LOG_STORAGE_FLUSH_INTERVAL=5               # 刷新间隔(秒)

# ClickHouse配置
CLICKHOUSE_HOST=localhost
CLICKHOUSE_PORT=9000
CLICKHOUSE_USERNAME=default
CLICKHOUSE_PASSWORD=
CLICKHOUSE_DATABASE=shell_api_logs

# Elasticsearch配置
ELASTICSEARCH_HOSTS=http://localhost:9200
ELASTICSEARCH_USERNAME=
ELASTICSEARCH_PASSWORD=
ELASTICSEARCH_INDEX=shell-api-logs
```

### 后台配置

在管理后台的系统设置中，可以动态配置：
- 日志存储类型切换
- 连接参数调整
- 性能参数优化

## 使用方法

### 1. 启用新日志系统

```bash
# 设置环境变量
export LOG_STORAGE_ENABLED=true
export LOG_STORAGE_TYPE=mysql

# 或在后台配置页面设置
```

### 2. 切换到ClickHouse

```bash
# 安装ClickHouse驱动
go get github.com/ClickHouse/clickhouse-go/v2

# 配置ClickHouse
export LOG_STORAGE_TYPE=clickhouse
export CLICKHOUSE_HOST=your-clickhouse-host
export CLICKHOUSE_PORT=9000
export CLICKHOUSE_DATABASE=shell_api_logs

# 重启服务或在后台动态切换
```

### 3. 切换到Elasticsearch

```bash
# 安装Elasticsearch客户端
go get github.com/elastic/go-elasticsearch/v8

# 配置Elasticsearch
export LOG_STORAGE_TYPE=elasticsearch
export ELASTICSEARCH_HOSTS=http://your-es-host:9200
export ELASTICSEARCH_INDEX=shell-api-logs

# 重启服务或在后台动态切换
```

## 性能优化

### ClickHouse优化建议

1. **表结构优化**
   - 按时间分区 (`PARTITION BY toYYYYMM(toDateTime(created_at))`)
   - 合理的排序键 (`ORDER BY (created_at, user_id, type)`)
   - 启用压缩 (`SETTINGS index_granularity = 8192`)

2. **写入优化**
   - 批量写入 (默认1000条/批)
   - 异步写入减少延迟
   - 合理的刷新间隔 (默认5秒)

3. **查询优化**
   - 利用分区裁剪
   - 使用合适的索引
   - 避免全表扫描

### Elasticsearch优化建议

1. **索引设置**
   - 合理的分片数 (默认3个分片)
   - 适当的副本数 (默认1个副本)
   - 定期索引轮转

2. **映射优化**
   - 关闭不需要的字段索引
   - 使用合适的数据类型
   - 设置合理的分析器

## 监控和运维

### 健康检查

```go
logManager := GetLogManager()
if err := logManager.HealthCheck(); err != nil {
    // 处理存储异常
}
```

### 存储切换

```go
logManager := GetLogManager()
err := logManager.SwitchStorage("clickhouse", "tcp://localhost:9000")
if err != nil {
    // 处理切换失败
}
```

### 日志记录

系统会自动记录：
- 存储切换事件
- 健康检查结果
- 写入性能指标
- 错误和异常

## 迁移指南

### 从MySQL迁移到ClickHouse

1. 安装ClickHouse服务器
2. 安装Go驱动依赖
3. 配置连接参数
4. 在后台切换存储类型
5. 验证数据写入正常

### 数据迁移

目前系统支持实时切换，历史数据迁移需要单独处理：

```sql
-- ClickHouse建表语句示例
CREATE TABLE logs (
    id UInt64,
    request_id String,
    user_id UInt32,
    created_at UInt64,
    type UInt8,
    content String,
    -- 其他字段...
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(toDateTime(created_at))
ORDER BY (created_at, user_id, type);
```

## 故障排除

### 常见问题

1. **驱动未安装**
   - 错误: "ClickHouse driver not installed"
   - 解决: `go get github.com/ClickHouse/clickhouse-go/v2`

2. **连接失败**
   - 检查网络连通性
   - 验证认证信息
   - 确认服务状态

3. **性能问题**
   - 调整批量大小
   - 优化刷新间隔
   - 检查存储资源

### 回退机制

如果新存储出现问题，系统会自动：
1. 尝试写入主存储
2. 失败时写入MySQL fallback
3. 记录错误日志
4. 保证数据不丢失

## 扩展开发

### 添加新存储类型

1. 实现 `LogStorage` 接口
2. 在 `LogManager.createStorage()` 中添加case
3. 添加相应的配置项
4. 编写测试用例

### 示例代码

```go
type CustomLogStorage struct {
    // 自定义字段
}

func (c *CustomLogStorage) RecordLog(ctx context.Context, log *Log) error {
    // 实现日志记录逻辑
    return nil
}

// 实现其他接口方法...
```

## 总结

这个日志存储抽象层为系统提供了：
- **高性能**: ClickHouse支持每天1亿条日志
- **高可用**: fallback机制保证服务稳定
- **灵活性**: 支持动态切换存储类型
- **扩展性**: 易于添加新的存储实现
- **兼容性**: 完全兼容现有功能

通过这个架构，你可以根据业务需求选择最适合的存储方案，有效解决MySQL性能瓶颈问题。

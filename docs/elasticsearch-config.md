# Elasticsearch 日志存储配置指南

## 概述

本文档介绍如何配置和优化 Elasticsearch 作为日志存储后端。

## 基础配置

### 环境变量配置

```bash
# Elasticsearch 连接配置
ELASTICSEARCH_HOSTS=http://localhost:9200
ELASTICSEARCH_USERNAME=elastic
ELASTICSEARCH_PASSWORD=your_password
ELASTICSEARCH_INDEX=shell-api-logs

# 重要：如果从旧版本升级，建议使用新的索引名避免ID冲突
# ELASTICSEARCH_INDEX=shell-api-logs-v2

# 索引配置
ELASTICSEARCH_SHARDS=3
ELASTICSEARCH_REPLICAS=1
ELASTICSEARCH_REFRESH_INTERVAL=30s

# 批量写入配置
LOG_STORAGE_ASYNC_WRITE=true
LOG_STORAGE_BATCH_SIZE=100
LOG_STORAGE_FLUSH_INTERVAL=5
LOG_STORAGE_COMPRESSION_ENABLED=true
```

### 连接字符串格式

支持多种连接字符串格式：

1. **基本格式**：`http://host:port`
2. **带认证**：`*****************************:port`
3. **多主机**：`http://host1:port1,http://host2:port2`
4. **混合格式**：`*****************************1:port1,host2:port2`

### 配置示例

```yaml
# config.yaml
elasticsearch:
  hosts: "http://es-node1:9200,http://es-node2:9200,http://es-node3:9200"
  username: "elastic"
  password: "your_secure_password"
  index: "shell-api-logs"
  shards: 3
  replicas: 1
  refresh_interval: "30s"
  
log_storage:
  async_write: true
  batch_size: 100
  flush_interval: 5
  compression_enabled: true
```

## 高级配置

### 索引模板配置

系统会自动创建索引模板，包含以下优化：

- **分片策略**：根据数据量自动调整分片数
- **副本策略**：支持动态副本调整
- **压缩设置**：启用最佳压缩算法
- **字段映射**：优化的字段类型和索引策略

### 生命周期管理

自动配置索引生命周期策略：

- **热阶段**：7天或10GB后滚动
- **温阶段**：7天后移动，减少副本数
- **冷阶段**：30天后移动，进一步优化存储
- **删除阶段**：90天后自动删除

### 性能优化配置

```yaml
elasticsearch:
  # 连接池配置
  max_idle_conns: 100
  max_idle_conns_per_host: 10
  idle_conn_timeout: 90s
  
  # 重试配置
  max_retries: 3
  initial_delay: 100ms
  max_delay: 5s
  backoff_factor: 2.0
  
  # 查询优化
  query_cache_enabled: true
  query_cache_expiry: 5m
  max_result_window: 50000
```

## 部署最佳实践

### 1. 集群规划

**最小生产环境**：
- 3个主节点（master-eligible）
- 2个数据节点（data）
- 1个协调节点（coordinating）

**推荐配置**：
```yaml
# elasticsearch.yml
cluster.name: shell-api-logs
node.name: ${HOSTNAME}
node.roles: [master, data]
discovery.seed_hosts: ["es-node1", "es-node2", "es-node3"]
cluster.initial_master_nodes: ["es-node1", "es-node2", "es-node3"]
```

### 2. 内存配置

```bash
# JVM 堆内存设置（不超过物理内存的50%，最大31GB）
ES_JAVA_OPTS="-Xms4g -Xmx4g"
```

### 3. 存储配置

```yaml
# elasticsearch.yml
path.data: /var/lib/elasticsearch
path.logs: /var/log/elasticsearch

# 建议使用SSD存储
# 为数据目录配置足够的磁盘空间
```

### 4. 网络配置

```yaml
# elasticsearch.yml
network.host: 0.0.0.0
http.port: 9200
transport.port: 9300

# 安全配置
xpack.security.enabled: true
xpack.security.transport.ssl.enabled: true
```

## 监控和告警

### 1. 健康检查

系统提供以下监控指标：

- **连接状态**：健康主机数量、响应时间
- **索引状态**：文档数量、存储大小
- **查询性能**：缓存命中率、查询延迟
- **批量写入**：成功率、背压状态

### 2. 告警配置

建议配置以下告警：

```yaml
alerts:
  - name: "Elasticsearch节点不健康"
    condition: "healthy_hosts < total_hosts"
    threshold: 0.8
    
  - name: "批量写入失败率过高"
    condition: "failed_batches / total_batches > 0.1"
    
  - name: "查询响应时间过长"
    condition: "avg_response_time > 5s"
    
  - name: "磁盘使用率过高"
    condition: "disk_usage > 0.85"
```

### 3. 性能监控

使用内置的诊断接口：

```bash
# 获取存储状态
curl -X GET "http://localhost:8080/api/admin/elasticsearch/diagnosis"

# 获取批量写入指标
curl -X GET "http://localhost:8080/api/admin/elasticsearch/metrics"
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连通性
   - 验证认证信息
   - 确认端口开放

2. **写入性能差**
   - 增加批量大小
   - 调整刷新间隔
   - 检查磁盘IO

3. **查询缓慢**
   - 优化索引映射
   - 增加分片数
   - 使用查询缓存

4. **内存不足**
   - 调整JVM堆大小
   - 优化字段映射
   - 启用压缩

### 日志分析

查看应用日志中的关键信息：

```bash
# 查看Elasticsearch相关日志
grep "Elasticsearch" /var/log/shell-api/app.log

# 查看健康检查日志
grep "health check" /var/log/shell-api/app.log

# 查看批量写入日志
grep "batch" /var/log/shell-api/app.log
```

## 安全配置

### 1. 认证配置

```yaml
# elasticsearch.yml
xpack.security.enabled: true
xpack.security.authc:
  realms:
    native:
      native1:
        order: 0
```

### 2. 用户权限

为应用创建专用用户：

```bash
# 创建角色
curl -X POST "localhost:9200/_security/role/shell_api_writer" \
-H 'Content-Type: application/json' -d'
{
  "indices": [
    {
      "names": ["shell-api-logs*"],
      "privileges": ["create", "index", "read", "write"]
    }
  ]
}'

# 创建用户
curl -X POST "localhost:9200/_security/user/shell_api" \
-H 'Content-Type: application/json' -d'
{
  "password": "secure_password",
  "roles": ["shell_api_writer"]
}'
```

### 3. 网络安全

```yaml
# elasticsearch.yml
# 限制访问IP
network.bind_host: ["127.0.0.1", "10.0.0.0/8"]
network.publish_host: **********

# 启用SSL
xpack.security.http.ssl.enabled: true
xpack.security.transport.ssl.enabled: true
```

## 备份和恢复

### 1. 快照配置

```bash
# 配置快照仓库
curl -X PUT "localhost:9200/_snapshot/backup_repo" \
-H 'Content-Type: application/json' -d'
{
  "type": "fs",
  "settings": {
    "location": "/backup/elasticsearch"
  }
}'
```

### 2. 自动备份

```bash
# 创建定期快照策略
curl -X PUT "localhost:9200/_slm/policy/daily_backup" \
-H 'Content-Type: application/json' -d'
{
  "schedule": "0 2 * * *",
  "name": "<daily-backup-{now/d}>",
  "repository": "backup_repo",
  "config": {
    "indices": ["shell-api-logs*"],
    "ignore_unavailable": true
  },
  "retention": {
    "expire_after": "30d",
    "min_count": 5,
    "max_count": 50
  }
}'
```

## 迁移指南

### 从其他存储迁移

1. **从MySQL迁移**：
   - 使用数据导出工具
   - 批量导入到Elasticsearch
   - 验证数据完整性

2. **从ClickHouse迁移**：
   - 导出CSV格式数据
   - 使用Logstash导入
   - 重建索引映射

### 版本升级

1. **滚动升级**：
   - 逐个升级节点
   - 保持集群可用性
   - 验证功能正常

2. **数据迁移**：
   - 重建索引模板
   - 迁移历史数据
   - 更新应用配置

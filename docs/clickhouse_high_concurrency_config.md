# ClickHouse 高并发配置优化指南

## 概述

针对每分钟5万条记录（约每秒833条）的高并发写入需求，本文档提供了ClickHouse连接和性能优化配置。

**配置复用策略：**
ClickHouse现在直接复用MySQL的连接池配置，无需单独设置ClickHouse专用的连接参数，简化了配置管理。

**基于现有MySQL生产配置经验：**
- MySQL配置：`SQL_MAX_OPEN_CONNS=5000`, `SQL_MAX_IDLE_CONNS=500`, `SQL_CONN_MAX_LIFETIME=30秒`
- 该配置已在生产环境验证可行，ClickHouse配置将参考此经验

## 性能需求分析

- **写入频率**: 每分钟50,000条记录
- **平均QPS**: ~833条/秒
- **实际数据库操作**: 每条记录可能需要2次操作(Log + LogExtend) = 1,666次/秒
- **峰值QPS**: 可能达到3,000+次数据库操作/秒
- **连接需求计算**: 假设每次操作50ms，需要 1,666 × 0.05 = 83个基础连接
- **安全余量**: 考虑峰值和网络延迟，需要200-300个连接

## 优化配置

### 1. 连接池配置 (复用MySQL配置)

```bash
# ClickHouse自动复用MySQL的连接池配置，无需单独设置
SQL_MAX_OPEN_CONNS=5000           # MySQL和ClickHouse共用的最大打开连接数
SQL_MAX_IDLE_CONNS=500            # MySQL和ClickHouse共用的最大空闲连接数
SQL_MAX_LIFETIME=30               # MySQL和ClickHouse共用的连接生存时间(秒)
```

**配置说明:**

- ClickHouse直接使用`SQL_MAX_OPEN_CONNS`配置，与MySQL保持一致
- ClickHouse直接使用`SQL_MAX_IDLE_CONNS`配置，与MySQL保持一致
- ClickHouse直接使用`SQL_MAX_LIFETIME`配置，与MySQL保持一致
- **优势**: 统一配置管理，减少配置复杂度，基于已验证的生产配置

### 2. 批量写入优化

```bash
# 日志存储批量配置
LOG_STORAGE_BATCH_SIZE=10000       # 批量大小 (建议: 10000)
LOG_STORAGE_FLUSH_INTERVAL=3       # 刷新间隔(秒) (建议: 3)
LOG_STORAGE_ASYNC_WRITE=true       # 启用异步写入
```

**配置说明:**

- `BATCH_SIZE=10000`: 更大的批量大小，减少数据库操作频率
- `FLUSH_INTERVAL=3秒`: 更快的刷新间隔，减少延迟
- `ASYNC_WRITE=true`: 异步写入避免阻塞主流程

### 3. ClickHouse基础配置

```bash
# ClickHouse连接配置
CLICKHOUSE_HOST=localhost
CLICKHOUSE_PORT=9000
CLICKHOUSE_USERNAME=default
CLICKHOUSE_PASSWORD=your_password
CLICKHOUSE_DATABASE=shell_api_logs
```

## 性能对比

### 优化前配置

```text
MaxOpenConns: 10
MaxIdleConns: 5
BatchSize: 1000
```

### 优化后配置 (复用MySQL配置)

```text
MaxOpenConns: 5000 (复用SQL_MAX_OPEN_CONNS)
MaxIdleConns: 500 (复用SQL_MAX_IDLE_CONNS)
BatchSize: 10000 (10倍提升)
ConnMaxLifetime: 30秒 (复用SQL_MAX_LIFETIME)
```

## 预期性能提升

1. **并发能力**: 从10个连接提升到5000个连接 (复用MySQL配置)
2. **批量效率**: 批量大小从1000提升到10000 (10倍提升)
3. **连接复用**: 500个空闲连接大幅减少连接建立开销
4. **写入吞吐**: 可支持每秒1,666+次数据库操作 (每分钟5万条记录)
5. **延迟优化**: 3秒刷新间隔减少写入延迟
6. **配置简化**: 无需单独管理ClickHouse连接配置，统一使用MySQL配置

## 监控建议

### 1. 连接池监控
```go
// 在代码中添加连接池状态监控
stats := db.Stats()
logger.Infof("ClickHouse连接池状态: OpenConnections=%d, InUse=%d, Idle=%d", 
    stats.OpenConnections, stats.InUse, stats.Idle)
```

### 2. 写入性能监控
- 监控批量写入延迟
- 监控写入失败率
- 监控队列积压情况

## 故障排查

### 1. 连接不足问题

**症状**: 出现"too many connections"错误
**解决**: 增加`SQL_MAX_OPEN_CONNS` (同时影响MySQL和ClickHouse)

### 2. 写入延迟过高

**症状**: 日志写入延迟增加
**解决**:
- 减少`LOG_STORAGE_FLUSH_INTERVAL`
- 增加`LOG_STORAGE_BATCH_SIZE`
- 启用`LOG_STORAGE_ASYNC_WRITE`

### 3. 内存使用过高

**症状**: 内存占用持续增长
**解决**:
- 减少`SQL_MAX_IDLE_CONNS` (同时影响MySQL和ClickHouse)
- 减少`SQL_MAX_LIFETIME` (同时影响MySQL和ClickHouse)

## 部署建议

### 1. 生产环境配置

```bash
# 超高并发生产环境推荐配置 (每分钟5万条记录)
# MySQL和ClickHouse共用连接池配置
SQL_MAX_OPEN_CONNS=5000
SQL_MAX_IDLE_CONNS=500
SQL_MAX_LIFETIME=30

# ClickHouse专用批量配置
LOG_STORAGE_BATCH_SIZE=15000
LOG_STORAGE_FLUSH_INTERVAL=2
LOG_STORAGE_ASYNC_WRITE=true
```

### 2. 测试环境配置

```bash
# 测试环境配置
SQL_MAX_OPEN_CONNS=1000
SQL_MAX_IDLE_CONNS=100
SQL_MAX_LIFETIME=60
LOG_STORAGE_BATCH_SIZE=2000
LOG_STORAGE_FLUSH_INTERVAL=10
```

## 注意事项

1. **ClickHouse服务器配置**: 确保ClickHouse服务器的`max_connections`设置足够大
2. **网络带宽**: 高并发写入需要足够的网络带宽
3. **磁盘IO**: ClickHouse需要高性能存储支持
4. **内存配置**: 适当的内存配置提高缓存效果

## 测试验证

建议使用压力测试验证配置效果：

```bash
# 使用ab或其他工具进行压力测试
# 目标: 每分钟50,000条记录写入
# 验证: 无连接错误，延迟在可接受范围内
```

## 🔍 为什么需要300个连接？

### 详细计算过程

**基础需求分析：**
1. **目标写入量**: 每分钟50,000条记录
2. **平均QPS**: 50,000 ÷ 60 = 833条/秒
3. **数据库操作倍数**: 每条记录需要2次操作
   - `RecordLog()`: 写入主日志表
   - `RecordLogExtend()`: 写入扩展日志表
4. **实际数据库操作**: 833 × 2 = **1,666次/秒**

**连接需求计算：**
1. **单次操作耗时**: 假设50ms (包括网络延迟)
2. **基础连接需求**: 1,666 × 0.05 = **83个连接**
3. **峰值流量倍数**: 考虑3倍峰值 = 83 × 3 = **249个连接**
4. **安全余量**: 249 + 51 = **300个连接**

### 配置验证

**当前优化配置能够支持：**
- ✅ 每秒1,666次数据库操作
- ✅ 峰值每秒5,000次数据库操作
- ✅ 每分钟50,000条记录写入
- ✅ 突发流量处理能力

**如果连接数不足会出现：**
- ❌ "connection pool exhausted" 错误
- ❌ 写入延迟急剧增加
- ❌ 请求超时和失败
- ❌ 系统性能下降

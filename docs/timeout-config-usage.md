# 个性化超时配置使用指南（管理员模式）

## 概述

个性化超时配置系统允许**管理员**为任意用户、任意模型设置定制化的超时参数，以解决上游API响应缓慢导致的用户体验问题。

**权限说明**: 此功能仅对管理员开放，普通用户无法自行配置超时设置。

## 配置参数

- **FirstByteTimeout**: 首字节超时时间（秒）- 等待第一个响应字节的最大时间
- **TotalTimeout**: 总超时时间（秒）- 整个请求的最大时间
- **TpsThreshold**: TPS阈值（每秒token数量）- 当响应速度低于此值时自动断开
- **TimeoutCostBearer**: 超时费用承担方 - `user`（用户承担，默认）或 `admin`（管理员承担）

## 管理员API接口

### 1. 获取所有用户的超时配置（分页）
```http
GET /api/admin/timeout_configs?page=1&size=20&user_id=123&model_name=gpt-4
Authorization: Bearer <admin_token>
```

### 2. 获取指定用户的所有超时配置
```http
GET /api/admin/timeout_config/123
Authorization: Bearer <admin_token>
```

### 3. 获取用户特定模型的超时配置
```http
GET /api/admin/timeout_config?user_id=123&model=gpt-4
Authorization: Bearer <admin_token>
```

### 4. 为用户设置超时配置
```http
POST /api/admin/timeout_config
Authorization: Bearer <admin_token>
Content-Type: application/json

{
    "user_id": 123,
    "model_name": "gpt-4",
    "first_byte_timeout": 30,
    "total_timeout": 300,
    "tps_threshold": 5.0,
    "timeout_cost_bearer": "user"
}
```

### 5. 删除用户的超时配置
```http
DELETE /api/admin/timeout_config/123?model=gpt-4
Authorization: Bearer <admin_token>
```

## 配置优先级

1. **用户+模型配置**: 最高优先级
2. **用户+通用配置**: 模型名称为 "*" 的配置
3. **系统默认**: 系统默认超时设置

## 在流式响应中的使用

### 获取超时检查器

```go
import "github.com/songquanpeng/one-api/middleware"

// 在流式处理函数中获取超时检查器
func StreamHandler(c *gin.Context, resp *http.Response, meta *meta.Meta) {
    timeoutChecker := middleware.GetTimeoutChecker(c)
    if timeoutChecker == nil {
        // 使用默认处理
        return
    }
    
    // 使用timeoutChecker的getter方法获取配置
    firstByteTimeout := timeoutChecker.GetFirstByteTimeout()
    totalTimeout := timeoutChecker.GetTotalTimeout()
    tpsThreshold := timeoutChecker.GetTpsThreshold()
    
    // 进行相应的超时检查...
}
```

## 管理员功能特点

1. **全局管理**: 管理员可以为任意用户设置超时配置
2. **批量查询**: 支持分页查询所有用户的配置
3. **过滤搜索**: 可按用户ID或模型名称过滤查询
4. **操作日志**: 所有配置变更都会记录操作日志

## 缓存机制

- 配置会自动缓存到Redis中，缓存时间为1小时
- 修改配置时会自动清除对应的缓存
- 支持批量清除缓存

## 注意事项

1. **首字节超时 ≤ 总超时**: 系统会自动验证配置的合理性
2. **TPS计算**: 基于实际接收到的token数量计算，需要完整响应才能准确计算
3. **性能影响**: 超时检查会增加少量计算开销，但对整体性能影响极小
4. **兼容性**: 不影响现有功能，未配置的用户使用系统默认超时
5. **权限管理**: 只有管理员才能配置，确保系统安全性

## 监控和日志

系统会记录以下信息：
- 管理员设置配置的操作日志
- 用户自定义超时配置的使用情况
- 超时触发的详细日志
- TPS计算和阈值比较结果

这些信息可以帮助分析和优化超时配置的合理性。

## 超时费用承担机制

### 费用承担方配置

管理员可以为每个用户的超时配置指定费用承担方：

1. **user**（默认）: 用户承担所有超时相关费用
   - 即使请求因超时中断，用户也会被扣除相应的API调用费用
   - 这是标准的计费模式

2. **admin**: 管理员承担超时费用
   - 当请求因用户自定义超时设置而中断时，用户不会被扣费
   - 适用于测试环境或特殊商业安排

### 费用处理逻辑

- **系统默认超时**: 始终由用户承担费用（系统行为）
- **用户自定义超时 + user承担**: 正常扣费流程
- **用户自定义超时 + admin承担**: 系统标记为管理员承担费用，不对用户扣费

### 配置示例

```json
{
    "user_id": 123,
    "model_name": "gpt-4",
    "first_byte_timeout": 15,
    "total_timeout": 120,
    "tps_threshold": 3.0,
    "timeout_cost_bearer": "admin"  // 管理员承担超时费用
}
``` 
# 令牌分组权限验证功能实现总结

## 问题描述

用户反馈需要在普通用户选择令牌分组并保存时，在后台校验该令牌分组对用户是否可见并且可选，防止恶意用户通过猜测分组标识直接POST注入。要求保存时后端直接查询用户对该分组的访问权限，如果查不到就直接提示"分组不存在"，不要暴露可见性相关的具体信息。

## 解决方案

### 1. 新增验证函数

在 `controller/token.go` 中新增了 `validateTokenGroup` 函数：

```go
func validateTokenGroup(c *gin.Context, groupName string) error
```

该函数实现了以下验证逻辑：
- 空分组名允许通过
- 从上下文获取用户信息
- 使用现有的 `GetSelectableGroupsWithRatio` 方法获取用户可选分组
- 使用现有的 `AddUserExtraVisibleGroupsToGroupWithRatioList` 方法添加额外可见分组
- 检查指定分组是否在用户的可选列表中

### 2. 集成到令牌操作

在以下Controller方法中添加了分组权限验证：
- `AddToken()` - 创建单个令牌
- `AddTokenBatch()` - 批量创建令牌
- `UpdateToken()` - 更新令牌

### 3. 统一错误处理

所有权限相关的错误都返回统一的错误信息："分组不存在"，避免信息泄露。

## 实现细节

### 权限验证逻辑

1. **管理员用户**：
   - 可以访问所有 `is_selectable = true` 的分组
   - 不受可见性限制

2. **普通用户**：
   - 只能访问 `is_selectable = true` 且 `is_visible = true` 的分组
   - 受倍率限制约束（0倍率用户不能使用有倍率分组）
   - 可以访问配置在 `extra_visible_groups` 中的额外分组

### 安全特性

- **信息隐藏**：所有权限错误统一返回"分组不存在"
- **防注入**：验证用户对分组的实际访问权限
- **权限分离**：管理员和普通用户有不同的验证规则
- **倍率保护**：防止0倍率用户滥用有倍率分组

## 测试验证

### 集成测试
- 提供了 `scripts/test_token_group_validation.sh` 脚本
- 覆盖各种权限场景的API测试
- 测试Controller层的验证逻辑

### 手动测试场景
1. 普通用户使用不可见分组 → 应返回"分组不存在"
2. 普通用户使用不可选分组 → 应返回"分组不存在"
3. 管理员使用任意可选分组 → 应成功
4. 0倍率用户使用有倍率分组 → 应返回"分组不存在"
5. 用户使用额外可见分组 → 应成功
6. 用户使用不存在的分组 → 应返回"分组不存在"

## 影响范围

### 修改的文件
- `controller/token.go` - 新增验证函数，修改令牌操作方法
- `docs/token_group_validation.md` - 功能文档
- `scripts/test_token_group_validation.sh` - 测试脚本

### API接口
- `POST /api/token` - 创建令牌
- `POST /api/token/batch` - 批量创建令牌
- `PUT /api/token` - 更新令牌

所有涉及分组设置的令牌操作都会进行权限验证。

## 向后兼容性

- 保持现有 `ValidateGroupName` 函数不变
- 新验证是额外的安全层，不影响现有功能
- 空分组名仍然被允许
- 不会破坏现有的API接口

## 部署建议

1. **测试环境验证**：先在测试环境部署并验证功能
2. **监控部署**：关注错误日志，识别可能的恶意尝试
3. **用户沟通**：如有用户反馈无法使用分组，检查分组配置
4. **性能监控**：新增验证逻辑会增加少量数据库查询

## 总结

该实现成功解决了用户提出的安全问题：
- ✅ 防止恶意用户通过猜测分组ID进行注入
- ✅ 后台验证用户对分组的访问权限
- ✅ 统一错误信息，不泄露分组可见性信息
- ✅ 保持向后兼容性
- ✅ 提供完整的测试覆盖

功能已经集成到所有令牌创建和更新的流程中，能够有效防止权限绕过攻击。

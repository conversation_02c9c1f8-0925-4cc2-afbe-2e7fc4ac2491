package controller

import (
	"encoding/json"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/i18n"
	"github.com/songquanpeng/one-api/model"
	billingratio "github.com/songquanpeng/one-api/relay/billing/ratio"
)

func GetOptions(c *gin.Context) {
	var options []*model.Option
	config.OptionMapRWMutex.Lock()
	for k, v := range config.OptionMap {
		if !config.ReturnSensitiveConfig && (strings.HasSuffix(k, "Token") || strings.HasSuffix(k, "Secret")) {
			continue
		}
		options = append(options, &model.Option{
			Key:   k,
			Value: helper.Interface2String(v),
		})
	}
	config.OptionMapRWMutex.Unlock()
	c.<PERSON>SO<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    options,
	})
	return
}

func UpdateOption(c *gin.Context) {
	var option model.Option
	err := json.NewDecoder(c.Request.Body).Decode(&option)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": i18n.Translate(c, "invalid_parameter"),
		})
		return
	}
	switch option.Key {
	case "Theme":
		if !config.ValidThemes[option.Value] {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "无效的主题",
			})
			return
		}
	case "GitHubOAuthEnabled":
		if option.Value == "true" && config.GitHubClientId == "" {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "无法启用 GitHub OAuth，请先填入 GitHub Client Id 以及 GitHub Client Secret！",
			})
			return
		}
	case "EmailDomainRestrictionEnabled":
		if option.Value == "true" && len(config.EmailDomainWhitelist) == 0 {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "无法启用邮箱域名限制，请先填入限制的邮箱域名！",
			})
			return
		}
	case "WeChatAuthEnabled":
		if option.Value == "true" && config.WeChatServerAddress == "" {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "无法启用微信登录，请先填入微信登录相关配置信息！",
			})
			return
		}
	case "TurnstileCheckEnabled":
		if option.Value == "true" && config.TurnstileSiteKey == "" {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "无法启用 Turnstile 校验，请先填入 Turnstile 校验相关配置信息！",
			})
			return
		}
	case "EmailVerificationEnabled":
		if option.Value == "true" && (config.SMTPAccount == "" || config.SMTPFrom == "" || config.SMTPToken == "" || config.SMTPServer == "" || config.SMTPPort <= 0) {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "无法启用邮箱验证，请先填入邮箱服务器相关配置信息！",
			})
			return
		}
	case "SMSVerificationEnabled":
		if option.Value == "true" && (config.SMSSignName == "" || config.SMSTemplateCode == "" || config.SMSAccessKeyId == "" || config.SMSAccessKeySecret == "") {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "无法启用短信验证，请先填入短信服务商相关配置信息！",
			})
			return
		}
		if option.Value == "false" {
			config.SMSRegisterEnabled = false
			config.SMSLoginEnabled = false
		}
	case "SMSRegisterEnabled":
		if option.Value == "true" && !config.SMSVerificationEnabled {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "无法启用短信注册验证，请先打开短信验证总开关！",
			})
			return
		}
	case "SMSLoginEnabled":
		if option.Value == "true" && !config.SMSVerificationEnabled {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "无法启用短信登录验证，请先打开短信验证总开关！",
			})
			return
		}
	case "ApiErrorTypeSuffix":
		if option.Value == "" {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "API错误类型后缀不能为空！",
			})
			return
		}
	case "CustomHideApiErrorTypes":
		// 检查JSON格式是否正确
		var errorTypes []string
		if option.Value != "" {
			err := json.Unmarshal([]byte(option.Value), &errorTypes)
			if err != nil {
				c.JSON(http.StatusOK, gin.H{
					"success": false,
					"message": "自定义API错误类型列表格式不正确，应为JSON数组！",
				})
				return
			}
		}
	}
	err = model.UpdateOption(option.Key, option.Value)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

// GetPrice 获取公开价格，支持基于用户身份的模型过滤
func GetPrice(c *gin.Context) { // 安全读取配置
	config.OptionMapRWMutex.RLock()
	defer config.OptionMapRWMutex.RUnlock()

	var aModels []string

	// 尝试获取用户ID（可能未登录）
	userId := c.GetInt("id")

	if userId > 0 {
		// 用户已登录，获取用户可见的模型
		user, _ := model.CacheGetUserById(userId, false)
		if user != nil {
			// 如果配置了令牌自由切换，获取所有可见分组的模型
			if config.TokenGroupChangeEnabled {
				isAdmin := model.IsAdmin(userId)
				groups, err := model.GetSelectableGroupsWithRatio(0, 500, "", "", user.Group, isAdmin)
				if err == nil {
					// 添加用户额外可见分组
					groups, err = model.AddUserExtraVisibleGroupsToGroupWithRatioList(userId, user.Group, groups)
					if err == nil {
						// 收集所有分组的模型
						modelSet := make(map[string]bool)
						for _, group := range groups {
							groupModels := model.GetAvailableModelByGroup(group.Name)
							for _, m := range groupModels {
								modelSet[m] = true
							}
						}
						// 转换为切片
						for modelName := range modelSet {
							aModels = append(aModels, modelName)
						}
					}
				}
			} else {
				// 只获取用户当前分组的模型
				aModels = model.GetAvailableModelByGroup(user.Group)
			}
		}
	}

	// 如果未登录或获取失败，使用默认分组的模型
	if len(aModels) == 0 {
		// 获取默认分组（通常是 "default" 或第一个分组）
		defaultGroup := "default"
		if len(billingratio.GetGroupRatioMap()) > 0 {
			// 获取第一个分组作为默认分组
			for groupName := range billingratio.GetGroupRatioMap() {
				defaultGroup = groupName
				break
			}
		}
		aModels = model.GetAvailableModelByGroup(defaultGroup)
	}

	// 添加网络搜索模型（如果启用）
	hasSearchSerperModel := model.HasSearchSerperModel()
	if hasSearchSerperModel {
		// 检查是否已存在，避免重复
		exists := false
		for _, m := range aModels {
			if m == "net-*" {
				exists = true
				break
			}
		}
		if !exists {
			aModels = append(aModels, "net-*")
		}
	}

	// 添加动态路由模型（根据用户分组权限）
	if userId > 0 {
		user, _ := model.CacheGetUserById(userId, false)
		if user != nil {
			for modelName := range config.DynamicRouterModelMap {
				allowedRoles, exists := config.DynamicRouterModelRoles[modelName]
				if exists && len(allowedRoles) > 0 {
					// 检查用户分组是否有权限
					hasPermission := false
					for _, role := range allowedRoles {
						if role == user.Group {
							hasPermission = true
							break
						}
					}
					if hasPermission {
						// 检查是否已存在，避免重复
						modelExists := false
						for _, m := range aModels {
							if m == modelName {
								modelExists = true
								break
							}
						}
						if !modelExists {
							aModels = append(aModels, modelName)
						}
					}
				} else {
					// 没有角色限制，所有用户都可以看到
					modelExists := false
					for _, m := range aModels {
						if m == modelName {
							modelExists = true
							break
						}
					}
					if !modelExists {
						aModels = append(aModels, modelName)
					}
				}
			}
		}
	} else {
		// 未登录用户，只显示没有角色限制的动态路由模型
		for modelName := range config.DynamicRouterModelMap {
			allowedRoles, exists := config.DynamicRouterModelRoles[modelName]
			if !exists || len(allowedRoles) == 0 {
				// 没有角色限制，未登录用户也可以看到
				modelExists := false
				for _, m := range aModels {
					if m == modelName {
						modelExists = true
						break
					}
				}
				if !modelExists {
					aModels = append(aModels, modelName)
				}
			}
		}
	}

	// 获取用户可见的分组价格信息
	var visibleGroupRatio map[string]float64

	if userId > 0 {
		// 用户已登录，只返回用户可见分组的价格
		user, _ := model.CacheGetUserById(userId, false)
		if user != nil {
			visibleGroupRatio = make(map[string]float64)
			allGroupRatio := billingratio.GetGroupRatioMap()

			// 添加用户当前分组
			if ratio, exists := allGroupRatio[user.Group]; exists {
				visibleGroupRatio[user.Group] = ratio
			}

			// 如果配置了令牌自由切换，添加可选分组
			if config.TokenGroupChangeEnabled {
				isAdmin := model.IsAdmin(userId)
				groups, err := model.GetSelectableGroupsWithRatio(0, 500, "", "", user.Group, isAdmin)
				if err == nil {
					// 添加用户额外可见分组
					groups, err = model.AddUserExtraVisibleGroupsToGroupWithRatioList(userId, user.Group, groups)
					if err == nil {
						for _, group := range groups {
							if ratio, exists := allGroupRatio[group.Name]; exists {
								visibleGroupRatio[group.Name] = ratio
							}
						}
					}
				}
			}
		}
	} else {
		// 未登录用户，只返回默认分组的价格
		visibleGroupRatio = make(map[string]float64)
		allGroupRatio := billingratio.GetGroupRatioMap()

		// 获取默认分组（通常是 "default" 或第一个分组）
		defaultGroup := "default"
		if len(allGroupRatio) > 0 {
			// 获取第一个分组作为默认分组
			for groupName := range allGroupRatio {
				defaultGroup = groupName
				break
			}
		}

		if ratio, exists := allGroupRatio[defaultGroup]; exists {
			visibleGroupRatio[defaultGroup] = ratio
		}
	}

	// 如果没有获取到任何分组信息，返回空的分组倍率
	if visibleGroupRatio == nil {
		visibleGroupRatio = make(map[string]float64)
	}

	// 直接使用结构体作为返回，gin会将其转换为JSON
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"Models":                aModels,
			"ModelRatio":            billingratio.GetModelRatioMap(),      // 模型倍率
			"GroupRatio":            visibleGroupRatio,                    // 只返回用户可见的分组倍率
			"ModelFixedPrice":       billingratio.GetModelFixedPriceMap(), // 固定价格
			"CompletionRatio":       billingratio.GetCompletionRatioMap(), // 补全倍率
			"DynamicRouterModelMap": config.DynamicRouterModelMap,
		},
	})
}

func GetDocument(c *gin.Context) {
	config.OptionMapRWMutex.RLock()
	defer config.OptionMapRWMutex.RUnlock()
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"PrivacyPolicy":    config.PrivacyPolicy,
			"ServiceAgreement": config.ServiceAgreement,
		},
	})
}

package controller

import (
	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/model"
	"net/http"
	"strconv"
)

func GetAllCustomPrompts(c *gin.Context) {
	p, _ := strconv.Atoi(c.Query("p"))
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))
	if p < 0 {
		p = 0
	}
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}
	id, _ := strconv.Atoi(c.Query("id"))
	group := c.Query("group")
	lang := c.Query("lang")
	status, _ := strconv.Atoi(c.Query("status"))
	customPrompts, err := model.GetAllCustomPrompts(p*pageSize, pageSize, id, status, group, lang)
	if err != nil {
		c.<PERSON>(http.StatusOK, gin.H{
			"success": false,
			"message": err.<PERSON><PERSON>r(),
		})
		return
	}
	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    customPrompts,
	})
	return
}

func CountCustomPrompts(c *gin.Context) {
	id, _ := strconv.Atoi(c.Query("id"))
	group := c.Query("group")
	lang := c.Query("lang")
	status, _ := strconv.Atoi(c.Query("status"))
	count, err := model.CountCustomPrompts(id, status, group, lang)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"data": gin.H{
				"count": 0,
			},
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"count": count,
		},
	})
	return
}

func InsertCustomPrompts(c *gin.Context) {
	var customPrompts model.CustomPrompts
	err := c.BindJSON(&customPrompts)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	err = customPrompts.Insert()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

func UpdateCustomPrompts(c *gin.Context) {
	customPrompts := model.CustomPrompts{}
	err := c.BindJSON(&customPrompts)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	affected, err := customPrompts.Update()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success":  true,
		"message":  "",
		"affected": affected,
	})
	return
}

func DeleteCustomPrompts(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	affected, err := model.DeleteCustomPromptsById(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success":  true,
		"message":  "",
		"affected": affected,
	})
	return
}

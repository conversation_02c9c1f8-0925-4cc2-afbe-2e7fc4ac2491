package controller

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/songquanpeng/one-api/controller/optimizer"

	"github.com/samber/lo"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/client"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/common/message"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/monitor"
	"github.com/songquanpeng/one-api/relay/adaptor/openai"
	"github.com/songquanpeng/one-api/relay/billing"
	billingratio "github.com/songquanpeng/one-api/relay/billing/ratio"
	"github.com/songquanpeng/one-api/relay/channeltype"
	"github.com/songquanpeng/one-api/relay/constant"
	"github.com/songquanpeng/one-api/relay/meta"
	relayModel "github.com/songquanpeng/one-api/relay/model"

	"github.com/gin-gonic/gin"
)

type MidjourneyPlus struct {
	MjId        string        `json:"id"`
	Action      string        `json:"action"`
	Prompt      string        `json:"prompt"`
	PromptEn    string        `json:"promptEn"`
	Description string        `json:"description"`
	State       string        `json:"state"`
	SubmitTime  int64         `json:"submitTime"`
	StartTime   int64         `json:"startTime"`
	FinishTime  int64         `json:"finishTime"`
	ImageUrl    string        `json:"imageUrl"`
	Status      string        `json:"status"`
	Progress    string        `json:"progress"`
	FailReason  string        `json:"failReason"`
	Properties  any           `json:"properties"`
	Buttons     []interface{} `json:"buttons"`
	// Video related fields - 同时支持蛇形和驼峰命名以保证兼容性
	VideoUrl       string   `json:"video_url,omitempty"`
	VideoUrlCamel  string   `json:"videoUrl,omitempty"`
	VideoUrls      []string `json:"video_urls,omitempty"`
	VideoUrlsCamel []string `json:"videoUrls,omitempty"`
}

type MidjourneyStatus struct {
	Status int `json:"status"`
}
type MidjourneyWithoutStatus struct {
	Id          int    `json:"id"`
	Code        int    `json:"code"`
	UserId      int    `json:"user_id" gorm:"index"`
	Action      string `json:"action"`
	MjId        string `json:"mj_id" gorm:"index"`
	Prompt      string `json:"prompt"`
	PromptEn    string `json:"prompt_en"`
	Description string `json:"description"`
	State       string `json:"state"`
	SubmitTime  int64  `json:"submit_time"`
	StartTime   int64  `json:"start_time"`
	FinishTime  int64  `json:"finish_time"`
	ImageUrl    string `json:"image_url"`
	Progress    string `json:"progress"`
	FailReason  string `json:"fail_reason"`
	ChannelId   int    `json:"channel_id"`
}

func RelayMidjourneyPlus(c *gin.Context) {
	// 记录初次请求时间
	_firstStartTime := helper.GetTimestamp()
	bodyCopy, _ := common.PreserveRequestBody(c)
	userId := c.GetInt(ctxkey.Id)
	channelId := c.GetInt(ctxkey.ChannelId)
	channelName := c.GetString(ctxkey.ChannelName)
	retryInterval := c.GetInt(ctxkey.RetryInterval)
	undeadModeEnabled := c.GetBool(ctxkey.UndeadModeEnabled)
	tokenBillingType := c.GetInt(ctxkey.TokenBillingType)
	inputHasFunctionCall := c.GetBool(ctxkey.InputHasFunctionCall)
	inputHasImage := c.GetBool(ctxkey.ImageSupported)
	group := c.GetString(ctxkey.Group)
	// 处理token group替换逻辑
	if config.TokenGroupChangeEnabled {
		tokenGroup := c.GetString(ctxkey.TokenGroup)
		if tokenGroup != "" {
			group = tokenGroup
		}
	}
	requestModel := c.GetString(ctxkey.RequestModel)
	if retryInterval == 0 {
		retryInterval = 300
	}
	err, _, needRetry := relayMjPlusByDifferentMode(c)

	// 获取用户最大Prompt长度配置
	userMaxPromptLogLength, userErr := model.CacheGetUserMaxPromptLogLength(userId)
	if userErr != nil {
		userMaxPromptLogLength = 0 // 出错时使用系统默认配置
	}

	var maxPromptLogLength int64
	if userMaxPromptLogLength == 0 {
		maxPromptLogLength = config.MaxPromptLogLength // 使用系统默认配置
	} else {
		maxPromptLogLength = userMaxPromptLogLength // 使用用户配置
	}

	// 判断最大Prompt长度
	bodyForLog := string(bodyCopy)
	bodyForLog = model.TruncateOptimized(bodyForLog, maxPromptLogLength, requestModel)
	if err != nil {
		if !needRetry {
			handleMaxRetryTimes(c, err, bodyForLog)
			return
		}
		// 取实际准确的模型
		mjRealRequestModel := c.GetString("mj_real_request_model")
		if mjRealRequestModel != "" {
			requestModel = mjRealRequestModel
		}
		if config.RootUserRelayErrorNotificationEnabled {
			// 通知管理员
			subject := fmt.Sprintf("用户id[%d] 通道「%s」（#%d）请求失败提醒", userId, channelName, channelId)
			content := fmt.Sprintf("用户id[%d] 通道「%s」（#%d）请求失败，原因：%s, status_code:%d, OpenAIError.Type:%s,OpenAIError.Code:%s 导致报错的请求体为: %s", userId, channelName, channelId, err.Message, err.StatusCode, err.Error.Type, err.Error.Code, string(bodyCopy))
			message.NotifyRootUser(subject, content)
		}
		// 判断是否是特殊错误,如果是特殊错误则直接返回给用户,无需继续重试了
		if monitor.IsSpecialError(err, _firstStartTime) {
			if err.Error.LocalizedMessage == "" {
				// 判断err.Code这个字段是否是string类型 err.Code.(string)
				if str, ok := err.Error.Code.(string); ok {
					// 是字符串类型
					err.Error.LocalizedMessage = common.GetErrorMessage(str, "zh")
				} else {
					err.Error.LocalizedMessage = common.GetErrorMessage(err.Error.Type, "zh")
				}
			}
			if config.LogDownstreamErrorEnabled {
				logMessage := &model.ErrorLogMessage{
					Description: "抛出到下游错误",
					DownstreamError: &model.ErrorInfo{
						StatusCode: err.StatusCode,
						Error: model.ErrorDetailInfo{
							Message: err.Error.Message,
							Type:    err.Error.Type,
							Code:    err.Error.Code,
						},
					},
					RequestParams: bodyForLog,
				}
				logJson, _ := json.Marshal(logMessage)
				model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeDownstreamError, string(logJson), bodyForLog)
			}
			c.JSON(err.StatusCode, gin.H{
				"error": err.Error,
			})
			return
		}

		logger.Error(c.Request.Context(), fmt.Sprintf("relay error (channel #%d),err.StatusCode (%d),err.Code(%s),err.Type(%s),err.Error.Code (%s),err.Error.Type (%s),err.Error.Message (%s): %s",
			channelId, err.StatusCode, err.Code, err.Type, err.Error.Code, err.Error.Type, err.Error.Message, err.Message))
		// https://platform.openai.com/docs/guides/error-codes/api-errors
		disableChannelErr := HandleDisableChannelReturnError(c, err, channelId, channelName, retryInterval, undeadModeEnabled, requestModel, bodyForLog)
		if disableChannelErr != nil {
			logger.Error(c.Request.Context(), fmt.Sprintf("disable channel error (channel #%d): %s", channelId, disableChannelErr.Error()))
		}
		if _, ok := c.Get("specific_channel_id"); ok {
			handleMaxRetryTimes(c, err, string(bodyCopy))
			return
		}
		// 重试逻辑挪到最下面,为了避免之前渠道未能关闭,导致重试时还是会报错
		// 不采用重定向方式重试
		retryTimes := config.RetryTimes
		if retryTimes > 0 {
			// 构造排除的id
			excludeIds := make([]int, 0)
			if config.RetryWithoutFailedChannelEnabled {
				excludeIds = append(excludeIds, channelId)
			}

			// 如果开启了保持计费类型一致性，则获取第一次使用的渠道计费类型
			retryTokenBillingType := tokenBillingType
			// 使用便捷函数判断是否应该保持重试计费类型一致性
			shouldKeepBillingType := model.ShouldKeepRetryBillingTypeFromContext(c)

			if shouldKeepBillingType {
				// 直接从context中获取第一次请求使用的渠道计费类型，避免数据库查询
				if firstChannelBillingType := c.GetInt(ctxkey.BillingType); firstChannelBillingType != 0 {
					retryTokenBillingType = firstChannelBillingType
				}
			}

			// 循环retryTimes次
			for i := 0; i < retryTimes; i++ {
				var nextRetryChannel *model.Channel
				var err2 error

				if retryTokenBillingType == common.BillingTypeByQuotaFirst {
					nextRetryChannel, err2 = model.CacheGetRandomSatisfiedChannel(group, requestModel, false, common.BillingTypeByQuota, inputHasFunctionCall, inputHasImage, excludeIds, c.GetBool(ctxkey.IsV1MessagesPath), userId)
					if err2 != nil {
						nextRetryChannel, err2 = model.CacheGetRandomSatisfiedChannel(group, requestModel, false, common.BillingTypeByCount, inputHasFunctionCall, inputHasImage, excludeIds, c.GetBool(ctxkey.IsV1MessagesPath), userId)
					}
				} else if retryTokenBillingType == common.BillingTypeByCountFirst {
					nextRetryChannel, err2 = model.CacheGetRandomSatisfiedChannel(group, requestModel, false, common.BillingTypeByCount, inputHasFunctionCall, inputHasImage, excludeIds, c.GetBool(ctxkey.IsV1MessagesPath), userId)
					if err2 != nil {
						nextRetryChannel, err2 = model.CacheGetRandomSatisfiedChannel(group, requestModel, false, common.BillingTypeByQuota, inputHasFunctionCall, inputHasImage, excludeIds, c.GetBool(ctxkey.IsV1MessagesPath), userId)
					}
				} else {
					nextRetryChannel, err2 = model.CacheGetRandomSatisfiedChannel(group, requestModel, false, retryTokenBillingType, inputHasFunctionCall, inputHasImage, excludeIds, c.GetBool(ctxkey.IsV1MessagesPath), userId)
				}
				if nextRetryChannel == nil {
					handleMaxRetryTimes(c, err, string(bodyCopy))
					return
				}
				if err2 != nil {
					logger.SysError(fmt.Sprintf("failed to get next retry channel: %s", err2.Error()))
					if config.LogDownstreamErrorEnabled {
						logMessage := &model.ErrorLogMessage{
							Description: "抛出到下游错误",
							DownstreamError: &model.ErrorInfo{
								StatusCode: err.StatusCode,
								Error: model.ErrorDetailInfo{
									Message: err.Error.Message,
									Type:    err.Error.Type,
									Code:    err.Error.Code,
								},
							},
							RequestParams: bodyForLog,
						}
						logJson, _ := json.Marshal(logMessage)
						model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeDownstreamError, string(logJson), bodyForLog)
					}
					c.JSON(err.StatusCode, gin.H{
						"error": err.Error,
					})
					return
				}
				excludeIds = append(excludeIds, nextRetryChannel.Id)
				c.Set(ctxkey.Channel, nextRetryChannel.Type)
				c.Set(ctxkey.ChannelId, nextRetryChannel.Id)
				c.Set(ctxkey.ChannelName, nextRetryChannel.Name)
				c.Set(ctxkey.BillingType, nextRetryChannel.GetModelMapping())
				c.Set(ctxkey.FunctionCallEnabled, nextRetryChannel.GetFunctionCallEnabled())
				c.Set(ctxkey.ImageSupported, nextRetryChannel.GetImageSupported())
				c.Set(ctxkey.ModelMapping, nextRetryChannel.GetModelMapping())
				c.Set(ctxkey.ModelMappingArr, nextRetryChannel.GetModelMappingArr())
				c.Set(ctxkey.OriginalModel, requestModel) // for retry
				c.Set(ctxkey.ExcludedFields, nextRetryChannel.GetExcludedFields())
				c.Set(ctxkey.ExcludedResponseFields, nextRetryChannel.GetExcludedResponseFields())
				c.Set(ctxkey.ExtraFields, nextRetryChannel.GetExtraFields())
				c.Set(ctxkey.BaseURL, nextRetryChannel.GetBaseURL())
				c.Set("retryInterval", nextRetryChannel.GetRetryInterval())
				c.Set("undeadModeEnabled", nextRetryChannel.GetUndeadModeEnabled())
				cfg, _ := nextRetryChannel.LoadConfig()

				if nextRetryChannel.Key != "" {
					c.Request.Header.Set("Authorization", fmt.Sprintf("Bearer %s", nextRetryChannel.Key))
				} else {
					c.Request.Header.Del("Authorization")
				}
				if nextRetryChannel.OpenAIOrganization != nil {
					c.Request.Header.Set("OpenAI-Organization", *nextRetryChannel.OpenAIOrganization)
				}
				// this is for backward compatibility
				switch nextRetryChannel.Type {
				case channeltype.Azure:
					if cfg.APIVersion == "" {
						cfg.APIVersion = *nextRetryChannel.Other
					}
				case channeltype.Xunfei:
					if cfg.APIVersion == "" {
						cfg.APIVersion = *nextRetryChannel.Other
					}
				case channeltype.Gemini:
					if cfg.APIVersion == "" {
						cfg.APIVersion = *nextRetryChannel.Other
					}
				case channeltype.AIProxyLibrary:
					if cfg.LibraryID == "" {
						cfg.LibraryID = *nextRetryChannel.Other
					}
				case channeltype.Ali:
					if cfg.Plugin == "" {
						cfg.Plugin = *nextRetryChannel.Other
					}
				}
				c.Set(ctxkey.Config, cfg)
				// 根据channelId获取channelExtend
				nextRetryChannelExtend, _ := model.CacheGetChannelExByChannelId(nextRetryChannel.Id)
				if nextRetryChannelExtend != nil {
					c.Set("filter_stream_ad", nextRetryChannelExtend.FilterStreamAd)
					c.Set("filter_stream_ad_min_size", nextRetryChannelExtend.FilterStreamAdMinSize)
					c.Set("filter_non_stream_ad", nextRetryChannelExtend.FilterNonStreamAd)
					c.Set("filter_non_stream_ad_regex", nextRetryChannelExtend.FilterNonStreamAdRegex)
					c.Set("filter_system_prompt", nextRetryChannelExtend.FilterSystemPrompt)
					c.Set("custom_system_prompt", nextRetryChannelExtend.CustomSystemPrompt)
					c.Set("extra_headers", nextRetryChannelExtend.GetExtraHeaders())
					c.Set("platform_access_token", nextRetryChannelExtend.PlatformAccessToken)
					c.Set("parse_url_to_content", nextRetryChannelExtend.ParseUrlToContent)
					c.Set("parse_url_prefix_enabled", nextRetryChannelExtend.ParseUrlPrefixEnabled)
					c.Set("parse_url_prefix", nextRetryChannelExtend.ParseUrlPrefix)
					c.Set("custom_full_url_enabled", nextRetryChannelExtend.CustomFullUrlEnabled)
					c.Set("arrange_messages", nextRetryChannelExtend.ArrangeMessages)
					c.Set("original_model_pricing", nextRetryChannelExtend.OriginalModelPricing)
					c.Set("negative_optimization_enabled", nextRetryChannelExtend.NegativeOptimizationEnabled)
					c.Set("negative_optimization_time", nextRetryChannelExtend.NegativeOptimizationTime)
					c.Set("negative_random_offset", nextRetryChannelExtend.NegativeRandomOffset)
					c.Set("original_model_fake_resp_enabled", nextRetryChannelExtend.OriginalModelFakeRespEnabled)
					c.Set("fake_completion_id_enabled", nextRetryChannelExtend.FakeCompletionIdEnabled)
					c.Set("exclude_custom_prompt_cost_enabled", nextRetryChannelExtend.ExcludeCustomPromptCostEnabled)
					c.Set("force_chat_url_enabled", nextRetryChannelExtend.ForceChatUrlEnabled)
					c.Set("ignore_fc_tc_enabled", nextRetryChannelExtend.IgnoreFcTcEnabled)
					c.Set("channel_timeout_breaker_time", nextRetryChannelExtend.ChannelTimeoutBreakerTime)
					c.Set("usage_recalculation_enabled", nextRetryChannelExtend.UsageRecalculationEnabled)
					c.Set("empty_response_error_enabled", nextRetryChannelExtend.EmptyResponseErrorEnabled)
					c.Set("remove_image_download_error_enabled", nextRetryChannelExtend.RemoveImageDownloadErrorEnabled)
					c.Set(ctxkey.Base64ImagePrefixMapping, nextRetryChannelExtend.GetBase64ImagePrefixMapping())
					c.Set("request_token_limit_enabled", nextRetryChannelExtend.RequestTokenLimitEnabled)
					c.Set("min_request_token_count", nextRetryChannelExtend.MinRequestTokenCount)
					c.Set("max_request_token_count", nextRetryChannelExtend.MaxRequestTokenCount)
					c.Set("claude_stream_enabled", nextRetryChannelExtend.ClaudeStreamEnabled)
					c.Set("keyword_error_enabled", nextRetryChannelExtend.KeywordErrorEnabled)
					c.Set("keyword_error", nextRetryChannelExtend.KeywordError)
				}
				// 在重新请求之前，重新设置请求体
				c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyCopy))
				logContent := fmt.Sprintf("渠道id[%d]名称[%s]失败后第[%d]次重试，失败原因是:[%v],当前实际请求渠道[%d]: %s", channelId, channelName, i+1, err, nextRetryChannel.Id, nextRetryChannel.Name)
				model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeSystemInfo, logContent, string(bodyCopy))
				err, _, needRetry = relayMjPlusByDifferentMode(c)
				if err != nil && needRetry {
					// 判断是否是特殊错误,如果是特殊错误则直接返回给用户,无需继续重试了
					if monitor.IsSpecialError(err, _firstStartTime) {
						if err.Error.LocalizedMessage == "" {
							// 判断err.Code这个字段是否是string类型 err.Code.(string)
							if str, ok := err.Error.Code.(string); ok {
								// 是字符串类型
								err.Error.LocalizedMessage = common.GetErrorMessage(str, "zh")
							} else {
								err.Error.LocalizedMessage = common.GetErrorMessage(err.Error.Type, "zh")
							}
						}
						if config.LogDownstreamErrorEnabled {
							logMessage := &model.ErrorLogMessage{
								Description: "抛出到下游错误",
								DownstreamError: &model.ErrorInfo{
									StatusCode: err.StatusCode,
									Error: model.ErrorDetailInfo{
										Message: err.Error.Message,
										Type:    err.Error.Type,
										Code:    err.Error.Code,
									},
								},
								RequestParams: bodyForLog,
							}
							logJson, _ := json.Marshal(logMessage)
							model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeDownstreamError, string(logJson), bodyForLog)
						}
						c.JSON(err.StatusCode, gin.H{
							"error": err.Error,
						})
						return
					}
					disableChannelErr := HandleDisableChannelReturnError(c, err, channelId, channelName, retryInterval, undeadModeEnabled, requestModel, bodyForLog)
					if disableChannelErr != nil {
						logger.Error(c.Request.Context(), fmt.Sprintf("disable channel error (channel #%d): %s", channelId, disableChannelErr.Error()))
					}
				}
			}
			handleMaxRetryTimes(c, err, string(bodyCopy))
		} else {
			handleMaxRetryTimes(c, err, string(bodyCopy))
		}
	}
}

func relayMjPlusByDifferentMode(c *gin.Context) (*relayModel.ErrorWithStatusCode, bool, bool) {
	var err *openai.MidjourneyResponse
	requestId := c.GetString(helper.RequestIdKey)

	relayMode := constant.RelayModeUnknown
	if strings.HasSuffix(c.Request.URL.Path, "/submit/imagine") {
		relayMode = constant.RelayModeMidjourneyImagine
	} else if strings.HasSuffix(c.Request.URL.Path, "/submit/blend") {
		relayMode = constant.RelayModeMidjourneyBlend
	} else if strings.HasSuffix(c.Request.URL.Path, "/submit/describe") {
		relayMode = constant.RelayModeMidjourneyDescribe
	} else if strings.HasSuffix(c.Request.URL.Path, "/submit/action") {
		relayMode = constant.RelayModeMidjourneyAction
	} else if strings.HasSuffix(c.Request.URL.Path, "/submit/modal") {
		relayMode = constant.RelayModeMidjourneyModal
	} else if strings.HasSuffix(c.Request.URL.Path, "/submit/shorten") {
		relayMode = constant.RelayModeMidjourneyShorten
	} else if strings.HasSuffix(c.Request.URL.Path, "/insight-face/swap") {
		relayMode = constant.RelayModeMidjourneyInsightFaceSwap
	} else if strings.HasSuffix(c.Request.URL.Path, "/submit/change") {
		relayMode = constant.RelayModeMidjourneyChange
	} else if strings.HasSuffix(c.Request.URL.Path, "/submit/simple-change") {
		relayMode = constant.RelayModeMidjourneyChangeSimple
	} else if strings.HasSuffix(c.Request.URL.Path, "/submit/video") {
		relayMode = constant.RelayModeMidjourneyVideo
	} else if strings.HasSuffix(c.Request.URL.Path, "/submit/edits") {
		relayMode = constant.RelayModeMidjourneyEdits
	} else if strings.HasSuffix(c.Request.URL.Path, "/fetch") {
		relayMode = constant.RelayModeMidjourneyTaskFetch
	} else if strings.HasSuffix(c.Request.URL.Path, "/image-seed") {
		relayMode = constant.RelayModeMidjourneyTaskImageSeed
	} else if strings.HasSuffix(c.Request.URL.Path, "/list-by-condition") {
		relayMode = constant.RelayModeMidjourneyTaskFetchByCondition
	} else if strings.HasSuffix(c.Request.URL.Path, "/upload-discord-images") {
		relayMode = constant.RelayModeUploadDiscordImages
	}
	switch relayMode {
	case constant.RelayModeMidjourneyNotify:
		err = relayMidjourneyPlusNotify(c)
	case constant.RelayModeMidjourneyTaskFetch, constant.RelayModeMidjourneyTaskImageSeed, constant.RelayModeMidjourneyTaskFetchByCondition:
		err = relayMidjourneyPlusTask(c, relayMode)
		// 查询接口报错不重试,之前是这里直接不返回错误,但是导致客户看不到错误信息,现在改为返回错误信息,外层进行判断是否重试
		if err != nil {
			wrappedError := &relayModel.ErrorWithStatusCode{
				StatusCode: http.StatusInternalServerError,
				Error: relayModel.Error{
					Message: err.Description,
					Type:    err.Description,
					Param:   requestId,
					Code:    err.Code,
				},
			}
			return wrappedError, false, false
		}
		return nil, false, false
	case constant.RelayModeUploadDiscordImages:
		err = relayUploadDiscordImages(c)
		return nil, false, false
	case constant.RelayModeMidjourneyVideo:
		err = relayMidjourneyPlusVideo(c, relayMode)
	case constant.RelayModeMidjourneyEdits:
		err = relayMidjourneyPlusSubmit(c, relayMode)
	default:
		err = relayMidjourneyPlusSubmit(c, relayMode)
	}
	if err != nil {
		wrappedError := &relayModel.ErrorWithStatusCode{
			StatusCode: http.StatusInternalServerError,
			Error: relayModel.Error{
				Message: err.Description,
				Type:    err.Description,
				Param:   requestId,
				Code:    err.Code,
			},
		}
		return wrappedError, false, true
	}
	return nil, false, false
}

func RelayMidjourneyPlusImage(c *gin.Context) {
	taskId := c.Param("id")
	midjourneyTask := model.GetByOnlyMJId(taskId)
	if midjourneyTask == nil {
		if config.LogDownstreamErrorEnabled {
			logMessage := &model.ErrorLogMessage{
				Description: "抛出到下游错误",
				DownstreamError: &model.ErrorInfo{
					StatusCode: http.StatusBadRequest,
					Error: model.ErrorDetailInfo{
						Message: "midjourney_task_not_found",
						Type:    "shell_api_error",
						Code:    "midjourney_task_not_found",
					},
				},
				RequestParams: taskId,
			}
			logJson, _ := json.Marshal(logMessage)
			model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeDownstreamError, string(logJson), "midjourney_task_not_found")
		}
		c.JSON(400, gin.H{
			"error": "midjourney_task_not_found",
		})
		return
	}
	resp, err := client.UserContentRequestHTTPClient.Get(midjourneyTask.ImageUrl)
	if err != nil {
		if config.LogDownstreamErrorEnabled {
			logMessage := &model.ErrorLogMessage{
				Description: "抛出到下游错误",
				DownstreamError: &model.ErrorInfo{
					StatusCode: http.StatusInternalServerError,
					Error: model.ErrorDetailInfo{
						Message: "http_get_image_failed",
						Type:    "shell_api_error",
						Code:    "http_get_image_failed",
					},
				},
				RequestParams: midjourneyTask.ImageUrl,
			}
			logJson, _ := json.Marshal(logMessage)
			model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeDownstreamError, string(logJson), "获取图片失败http_get_image_failed")
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "http_get_image_failed",
		})
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		responseBody, _ := io.ReadAll(resp.Body)
		if config.LogDownstreamErrorEnabled {
			logMessage := &model.ErrorLogMessage{
				Description: "抛出到下游错误",
				DownstreamError: &model.ErrorInfo{
					StatusCode: resp.StatusCode,
					Error: model.ErrorDetailInfo{
						Message: "http_get_image_failed",
						Type:    "shell_api_error",
						Code:    "http_get_image_failed",
					},
				},
				RequestParams: midjourneyTask.ImageUrl,
			}
			logJson, _ := json.Marshal(logMessage)
			model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeDownstreamError, string(logJson), "获取图片失败http_get_image_failed")
		}
		c.JSON(resp.StatusCode, gin.H{
			"error": string(responseBody),
		})
		return
	}
	// 从Content-Type头获取MIME类型
	contentType := resp.Header.Get("Content-Type")
	if contentType == "" {
		// 如果无法确定内容类型，则默认为jpeg
		contentType = "image/jpeg"
	}
	// 设置响应的内容类型
	c.Writer.Header().Set("Content-Type", contentType)
	// 将图片流式传输到响应体
	_, err = io.Copy(c.Writer, resp.Body)
	if err != nil {
		logger.SysError(fmt.Sprintf("Failed to stream image: %s", err.Error()))
	}
	return
}

func relayMidjourneyPlusNotify(c *gin.Context) *openai.MidjourneyResponse {
	var midjRequest MidjourneyPlus
	err := common.UnmarshalBodyReusable(c, &midjRequest)
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "bind_request_body_failed",
			Properties:  nil,
			Result:      "",
		}
	}
	midjourneyTask := model.GetByOnlyMJId(midjRequest.MjId)
	if midjourneyTask == nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "midjourney_task_not_found",
			Properties:  nil,
			Result:      "",
		}
	}
	midjourneyTask.Progress = midjRequest.Progress
	midjourneyTask.PromptEn = midjRequest.PromptEn
	midjourneyTask.State = midjRequest.State
	midjourneyTask.SubmitTime = midjRequest.SubmitTime
	midjourneyTask.StartTime = midjRequest.StartTime
	midjourneyTask.FinishTime = midjRequest.FinishTime
	midjourneyTask.ImageUrl = midjRequest.ImageUrl
	midjourneyTask.Status = midjRequest.Status
	midjourneyTask.FailReason = midjRequest.FailReason

	midjourneyTask.Properties = helper.GetJsonString(midjRequest.Properties)
	midjourneyTask.Buttons = helper.GetJsonString(midjRequest.Buttons)

	// 处理视频URL更新
	if midjRequest.VideoUrl != "" {
		midjourneyTask.VideoUrl = midjRequest.VideoUrl
	}

	if len(midjRequest.VideoUrls) > 0 {
		if videoUrlsJson, err := json.Marshal(midjRequest.VideoUrls); err == nil {
			midjourneyTask.VideoUrls = string(videoUrlsJson)
		}
	}

	// 兼容性处理：如果是视频相关任务但没有video字段，将imageUrl作为视频URL处理
	isVideoTask := strings.Contains(strings.ToUpper(midjourneyTask.Action), "VIDEO") ||
		strings.Contains(strings.ToLower(midjourneyTask.Mode), "video")

	if isVideoTask && midjRequest.VideoUrl == "" && len(midjRequest.VideoUrls) == 0 && midjRequest.ImageUrl != "" {
		// 检查imageUrl是否可能是视频文件
		imageUrl := strings.ToLower(midjRequest.ImageUrl)
		if strings.Contains(imageUrl, ".mp4") || strings.Contains(imageUrl, ".webm") ||
			strings.Contains(imageUrl, ".mov") || strings.Contains(imageUrl, "video") ||
			strings.Contains(imageUrl, ".avi") || strings.Contains(imageUrl, ".mkv") {
			// 将imageUrl作为视频URL处理
			midjourneyTask.VideoUrl = midjRequest.ImageUrl
			logger.SysLog(fmt.Sprintf("兼容性处理：将imageUrl作为视频URL处理，任务ID: %s, URL: %s", midjRequest.MjId, midjRequest.ImageUrl))
		} else if midjourneyTask.Status == "SUCCESS" {
			// 如果任务成功完成但没有明显的视频标识，也将imageUrl作为视频URL处理
			// 这是为了兼容某些返回格式不标准的API
			midjourneyTask.VideoUrl = midjRequest.ImageUrl
			logger.SysLog(fmt.Sprintf("兼容性处理：视频任务成功完成，将imageUrl作为视频URL处理，任务ID: %s, URL: %s", midjRequest.MjId, midjRequest.ImageUrl))
		}
	}

	err = midjourneyTask.Update()
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "update_midjourney_task_failed",
		}
	}

	return nil
}

func relayMidjourneyPlusTask(c *gin.Context, relayMode int) *openai.MidjourneyResponse {
	userId := c.GetInt(ctxkey.Id)
	var err error
	var respBody []byte
	switch relayMode {
	case constant.RelayModeMidjourneyTaskFetch:
		taskId := c.Param("id")
		originTask := model.GetByMJId(userId, taskId)
		if originTask == nil {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "task_no_found",
			}
		}
		midjourneyTask := getMidjourneyPlusTaskModel(c, originTask)
		respBody, err = json.Marshal(midjourneyTask)
		if err != nil {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "unmarshal_response_body_failed",
			}
		}
	case constant.RelayModeMidjourneyTaskImageSeed:
		taskId := c.Param("id")
		originTask := model.GetByMJId(userId, taskId)
		if originTask == nil {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "task_no_found",
			}
		}
		// 获取渠道详情
		channelInfo, err := model.GetChannelById(originTask.ChannelId, true)
		if err != nil {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "get_channel_failed",
			}
		}
		requestURL := getMjRequestPath(c.Request.URL.String(), c.GetString("mode"))
		baseURL := channeltype.ChannelBaseURLs[c.GetInt(ctxkey.Channel)]
		if c.GetString(ctxkey.BaseURL) != "" {
			baseURL = c.GetString(ctxkey.BaseURL)
		}
		fullRequestURL := fmt.Sprintf("%s%s", baseURL, requestURL)
		req, err := http.NewRequest(c.Request.Method, fullRequestURL, nil)
		if err != nil {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "create_request_failed",
			}
		}
		req.Header.Set("Content-Type", c.Request.Header.Get("Content-Type"))
		req.Header.Set("Accept", c.Request.Header.Get("Accept"))
		req.Header.Set("Authorization", channelInfo.Key)
		req.Header.Set("mj-api-secret", channelInfo.Key)
		resp, err := client.HTTPClient.Do(req)
		if err != nil {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "do_request_failed",
			}
		}
		defer func() {
			resp.Body.Close()
		}()
		respBody, err = io.ReadAll(resp.Body)

		// 如果是fetch请求并且成功，尝试解析并更新视频信息
		if strings.Contains(requestURL, "/fetch") && resp.StatusCode == http.StatusOK {
			var upstreamResponse map[string]interface{}
			if json.Unmarshal(respBody, &upstreamResponse) == nil {
				// 检查是否包含视频数据并更新数据库
				hasVideoData := false
				if videoUrl, ok := upstreamResponse["video_url"].(string); ok && videoUrl != "" {
					originTask.VideoUrl = videoUrl
					hasVideoData = true
				} else if videoUrl, ok := upstreamResponse["videoUrl"].(string); ok && videoUrl != "" {
					originTask.VideoUrl = videoUrl
					hasVideoData = true
				}

				if videoUrls, ok := upstreamResponse["video_urls"].([]interface{}); ok && len(videoUrls) > 0 {
					var urls []string
					for _, url := range videoUrls {
						if urlStr, ok := url.(string); ok && urlStr != "" {
							urls = append(urls, urlStr)
						}
					}
					if len(urls) > 0 {
						if videoUrlsJson, err := json.Marshal(urls); err == nil {
							originTask.VideoUrls = string(videoUrlsJson)
							hasVideoData = true
						}
					}
				} else if videoUrls, ok := upstreamResponse["videoUrls"].([]interface{}); ok && len(videoUrls) > 0 {
					var urls []string
					for _, url := range videoUrls {
						if urlStr, ok := url.(string); ok && urlStr != "" {
							urls = append(urls, urlStr)
						}
					}
					if len(urls) > 0 {
						if videoUrlsJson, err := json.Marshal(urls); err == nil {
							originTask.VideoUrls = string(videoUrlsJson)
							hasVideoData = true
						}
					}
				}

				// 如果有视频数据，异步更新数据库
				if hasVideoData {
					go func() {
						originTask.Update()
					}()
				}
			}
		}

		// 记录respBody,请求头,请求url,请求体
		marshal, err := json.Marshal(c.Request.Header)
		if err != nil {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "unmarshal_request_header_failed",
			}
		}
		// 记录respBody,请求头,请求url,请求体
		logger.SysLog(fmt.Sprintf("midjourney task image seed response: [%s],headers:[%s],url:[%s],body:[%v]", string(respBody), string(marshal), fullRequestURL, c.Request.Body))
		c.Writer.WriteHeader(resp.StatusCode)
	case constant.RelayModeMidjourneyTaskFetchByCondition:
		var condition = struct {
			IDs []any `json:"ids"`
		}{}
		err = c.BindJSON(&condition)
		if err != nil {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "do_request_failed",
			}
		}
		var tasks []MidjourneyPlus
		if len(condition.IDs) != 0 {
			originTasks := model.GetByMJIds(condition.IDs)
			for _, originTask := range originTasks {
				midjourneyTask := getMidjourneyPlusTaskModel(c, originTask)
				tasks = append(tasks, midjourneyTask)
			}
		}
		if tasks == nil {
			tasks = make([]MidjourneyPlus, 0)
		}
		respBody, err = json.Marshal(tasks)
		if err != nil {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "unmarshal_response_body_failed",
			}
		}
	}

	c.Writer.Header().Set("Content-Type", "application/json")

	_, err = io.Copy(c.Writer, bytes.NewBuffer(respBody))
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "copy_response_body_failed",
		}
	}
	return nil
}

func relayUploadDiscordImages(c *gin.Context) *openai.MidjourneyResponse {
	var err error
	var respBody []byte
	var midjRequest openai.MidjourneyPlusRequest
	err = common.UnmarshalBodyReusable(c, &midjRequest)
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "bind_request_body_failed",
		}
	}
	channelId := c.GetInt(ctxkey.ChannelId)
	channelType := c.GetInt(ctxkey.Channel)
	requestURL := getMjRequestPath(c.Request.URL.String(), c.GetString("mode"))
	baseURL := channeltype.ChannelBaseURLs[channelType]
	// 获取渠道详情
	channelInfo, err := model.GetChannelById(channelId, true)
	if c.GetString(ctxkey.BaseURL) != "" {
		baseURL = c.GetString(ctxkey.BaseURL)
	}
	fullRequestURL := fmt.Sprintf("%s%s", baseURL, requestURL)

	jsonStr, err := json.Marshal(midjRequest)
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "marshal_request_body_failed",
		}
	}
	req, err := http.NewRequest(c.Request.Method, fullRequestURL, bytes.NewBuffer(jsonStr))
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "create_request_failed",
		}
	}
	req.Header.Set("Content-Type", c.Request.Header.Get("Content-Type"))
	req.Header.Set("Accept", c.Request.Header.Get("Accept"))
	req.Header.Set("Authorization", channelInfo.Key)
	req.Header.Set("mj-api-secret", channelInfo.Key)
	resp, err := client.HTTPClient.Do(req)
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "do_request_failed",
		}
	}
	defer func() {
		resp.Body.Close()
	}()
	respBody, err = io.ReadAll(resp.Body)
	// 记录respBody,请求头,请求url,请求体
	marshal, err := json.Marshal(c.Request.Header)
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "unmarshal_request_header_failed",
		}
	}
	// 记录respBody,请求头,请求url,请求体
	logger.SysLog(fmt.Sprintf("midjourney image UploadDiscordImages response: [%s],headers:[%s],url:[%s],body:[%v]", string(respBody), string(marshal), fullRequestURL, c.Request.Body))
	c.Writer.WriteHeader(resp.StatusCode)

	c.Writer.Header().Set("Content-Type", "application/json")

	_, err = io.Copy(c.Writer, bytes.NewBuffer(respBody))
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "copy_response_body_failed",
		}
	}
	return nil
}
func getMidjourneyPlusTaskModel(c *gin.Context, originTask *model.Midjourney) (midjourneyTask MidjourneyPlus) {
	mjDiscordProxyUrl := c.GetString("mj_discord_proxy_url")
	midjourneyTask.MjId = originTask.MjId
	midjourneyTask.Progress = originTask.Progress
	midjourneyTask.PromptEn = originTask.PromptEn
	midjourneyTask.State = originTask.State
	midjourneyTask.SubmitTime = originTask.SubmitTime
	midjourneyTask.StartTime = originTask.StartTime
	midjourneyTask.FinishTime = originTask.FinishTime
	midjourneyTask.ImageUrl = ""
	if originTask.ImageUrl != "" {
		midjourneyTask.ImageUrl = config.GetMjUrlByTaskIdDefaultOriginUrl(originTask.MjId, originTask.ImageUrl)
		if mjDiscordProxyUrl != "" {
			midjourneyTask.ImageUrl = strings.Replace(midjourneyTask.ImageUrl, "https://cdn.discordapp.com", mjDiscordProxyUrl, 1)
		}
		if originTask.Status != "SUCCESS" {
			midjourneyTask.ImageUrl += "?rand=" + strconv.FormatInt(time.Now().UnixNano(), 10)
		}
	}
	midjourneyTask.Status = originTask.Status
	midjourneyTask.FailReason = originTask.FailReason
	midjourneyTask.Action = originTask.Action
	midjourneyTask.Description = originTask.Description
	midjourneyTask.Prompt = originTask.Prompt
	json.Unmarshal([]byte(originTask.Properties), &midjourneyTask.Properties)
	json.Unmarshal([]byte(originTask.Buttons), &midjourneyTask.Buttons)

	// 处理视频URL字段
	// 优先从数据库中获取视频URL
	if originTask.VideoUrl != "" {
		midjourneyTask.VideoUrl = originTask.VideoUrl
		midjourneyTask.VideoUrlCamel = originTask.VideoUrl // 同时设置驼峰命名字段
		if mjDiscordProxyUrl != "" {
			midjourneyTask.VideoUrl = strings.Replace(midjourneyTask.VideoUrl, "https://cdn.discordapp.com", mjDiscordProxyUrl, 1)
			midjourneyTask.VideoUrlCamel = midjourneyTask.VideoUrl // 保持一致
		}
	}

	if originTask.VideoUrls != "" {
		// 将存储的JSON字符串解析为数组
		var videoUrls []string
		if err := json.Unmarshal([]byte(originTask.VideoUrls), &videoUrls); err == nil {
			if mjDiscordProxyUrl != "" {
				for i, url := range videoUrls {
					videoUrls[i] = strings.Replace(url, "https://cdn.discordapp.com", mjDiscordProxyUrl, 1)
				}
			}
			midjourneyTask.VideoUrls = videoUrls
			midjourneyTask.VideoUrlsCamel = videoUrls // 同时设置驼峰命名字段
		}
	}

	// 如果是视频相关任务且状态为SUCCESS，但本地没有视频URL，尝试从上游获取
	if (strings.Contains(strings.ToUpper(originTask.Action), "VIDEO") ||
		strings.Contains(strings.ToLower(originTask.Mode), "video")) &&
		originTask.Status == "SUCCESS" &&
		midjourneyTask.VideoUrl == "" &&
		len(midjourneyTask.VideoUrls) == 0 {

		// 兼容性处理：如果没有视频URL但有imageUrl，先尝试将imageUrl作为视频URL
		if midjourneyTask.ImageUrl != "" {
			// 检查imageUrl是否可能是视频文件
			imageUrl := strings.ToLower(midjourneyTask.ImageUrl)
			if strings.Contains(imageUrl, ".mp4") || strings.Contains(imageUrl, ".webm") ||
				strings.Contains(imageUrl, ".mov") || strings.Contains(imageUrl, "video") ||
				strings.Contains(imageUrl, ".avi") || strings.Contains(imageUrl, ".mkv") {
				// 将imageUrl作为视频URL处理
				midjourneyTask.VideoUrl = midjourneyTask.ImageUrl
				midjourneyTask.VideoUrlCamel = midjourneyTask.ImageUrl // 同时设置驼峰命名字段
				logger.SysLog(fmt.Sprintf("兼容性处理：将imageUrl作为视频URL处理，任务ID: %s, URL: %s", originTask.MjId, midjourneyTask.ImageUrl))
				// 异步更新数据库
				go func() {
					originTask.VideoUrl = midjourneyTask.ImageUrl
					originTask.Update()
				}()
			} else {
				// 如果imageUrl没有明显的视频标识，也将其作为视频URL处理（兼容某些API）
				midjourneyTask.VideoUrl = midjourneyTask.ImageUrl
				midjourneyTask.VideoUrlCamel = midjourneyTask.ImageUrl // 同时设置驼峰命名字段
				logger.SysLog(fmt.Sprintf("兼容性处理：视频任务成功完成，将imageUrl作为视频URL处理，任务ID: %s, URL: %s", originTask.MjId, midjourneyTask.ImageUrl))
				// 异步更新数据库
				go func() {
					originTask.VideoUrl = midjourneyTask.ImageUrl
					originTask.Update()
				}()
			}
		} else {
			// 如果没有imageUrl，尝试从上游API获取视频信息
			if videoData := fetchVideoDataFromUpstream(c, originTask); videoData != nil {
				// 更新视频URL
				if videoData.VideoUrl != "" {
					midjourneyTask.VideoUrl = videoData.VideoUrl
					midjourneyTask.VideoUrlCamel = videoData.VideoUrl // 同时设置驼峰命名字段
					if mjDiscordProxyUrl != "" {
						midjourneyTask.VideoUrl = strings.Replace(midjourneyTask.VideoUrl, "https://cdn.discordapp.com", mjDiscordProxyUrl, 1)
						midjourneyTask.VideoUrlCamel = midjourneyTask.VideoUrl // 保持一致
					}
					// 异步更新数据库
					go func() {
						originTask.VideoUrl = videoData.VideoUrl
						originTask.Update()
					}()
				}

				if len(videoData.VideoUrls) > 0 {
					if mjDiscordProxyUrl != "" {
						for i, url := range videoData.VideoUrls {
							videoData.VideoUrls[i] = strings.Replace(url, "https://cdn.discordapp.com", mjDiscordProxyUrl, 1)
						}
					}
					midjourneyTask.VideoUrls = videoData.VideoUrls
					midjourneyTask.VideoUrlsCamel = videoData.VideoUrls // 同时设置驼峰命名字段
					// 异步更新数据库
					go func() {
						if videoUrlsJson, err := json.Marshal(videoData.VideoUrls); err == nil {
							originTask.VideoUrls = string(videoUrlsJson)
							originTask.Update()
						}
					}()
				}
			}
		}
	}

	// 向下游兼容性处理：如果是视频任务且有视频URL但没有imageUrl，将视频URL也赋值给imageUrl
	isVideoTask := strings.Contains(strings.ToUpper(originTask.Action), "VIDEO") ||
		strings.Contains(strings.ToLower(originTask.Mode), "video")

	if isVideoTask {
		// 双向兼容处理：确保视频任务中imageUrl和video_url字段都有值
		if midjourneyTask.ImageUrl == "" && midjourneyTask.VideoUrl != "" {
			// 如果有单个视频URL但没有imageUrl，将视频URL赋值给imageUrl
			midjourneyTask.ImageUrl = midjourneyTask.VideoUrl
			logger.SysLog(fmt.Sprintf("向下游兼容性处理：将视频URL作为imageUrl返回，任务ID: %s, URL: %s", originTask.MjId, midjourneyTask.VideoUrl))
		} else if midjourneyTask.ImageUrl == "" && len(midjourneyTask.VideoUrls) > 0 {
			// 如果有多个视频URL但没有imageUrl，使用第一个作为imageUrl
			midjourneyTask.ImageUrl = midjourneyTask.VideoUrls[0]
			logger.SysLog(fmt.Sprintf("向下游兼容性处理：将第一个视频URL作为imageUrl返回，任务ID: %s, URL: %s", originTask.MjId, midjourneyTask.VideoUrls[0]))
		} else if midjourneyTask.VideoUrl == "" && len(midjourneyTask.VideoUrls) == 0 && midjourneyTask.ImageUrl != "" {
			// 如果有imageUrl但没有视频URL，将imageUrl也赋值给video_url（确保双向兼容）
			midjourneyTask.VideoUrl = midjourneyTask.ImageUrl
			midjourneyTask.VideoUrlCamel = midjourneyTask.ImageUrl // 同时设置驼峰命名字段
			logger.SysLog(fmt.Sprintf("向下游兼容性处理：将imageUrl作为视频URL返回，任务ID: %s, URL: %s", originTask.MjId, midjourneyTask.ImageUrl))
		}
	}

	return
}

const (
	// type 1 根据 mode 价格不同
	MJSubmitActionImagine   = "IMAGINE"
	MJSubmitActionVariation = "VARIATION"  //变换
	MJSubmitActionPan       = "PAN"        //焦点移动
	MJSubmitActionInpaint   = "INPAINT"    //局部重绘
	MJSubmitActionOutpaint  = "OUTPAINT"   //变焦
	MJSubmitActionBlend     = "BLEND"      //混图
	MJSubmitActionReroll    = "REROLL"     //重新生成
	MJSubmitActionPicReader = "PIC_READER" //图生文后 生成图片
	MJSubmitActionUpscale2x = "UPSCALE_2X" //
	MJSubmitActionUpscale4x = "UPSCALE_4X"
	// type 2 固定价格
	MJSubmitActionDescribe       = "DESCRIBE"
	MJSubmitActionPicReaderRetry = "PICREADER_RETRY" //图生文，重新生成 //
	MJSubmitActionUpscale        = "UPSCALE"         // 放大
	MJSubmitActionAction         = "ACTION"          // 未知操作
	MJSubmitActionFaceSwap       = "FACESWAP"        //换脸
	MJSubmitActionShorten        = "SHORTEN"         // prompt 分析

	// type 3 seed

	// type 4 free
	MJSubmitActionModal = "MODAL"

	// Video related actions
	MJSubmitActionVideo           = "VIDEO"             //图生视频
	MJSubmitActionVideoRerun      = "VIDEO_RERUN"       //视频重新生成
	MJSubmitActionVideoStartFrame = "VIDEO_START_FRAME" //视频起始帧
	MJSubmitActionVideoPrompt     = "VIDEO_PROMPT"      //视频提示词
	MJSubmitActionVideoAutoLow    = "VIDEO_AUTO_LOW"    //视频自动-低动作
	MJSubmitActionVideoAutoHigh   = "VIDEO_AUTO_HIGH"   //视频自动-高动作
	MJSubmitActionVideoLowMotion  = "VIDEO_LOW_MOTION"  //视频低动作
	MJSubmitActionVideoHighMotion = "VIDEO_HIGH_MOTION" //视频高动作

	// Edits related actions
	MJSubmitActionEdits = "EDITS" //编辑
)

func getActionByCustomID(customID string) string {
	// Special handling for video actions - check these first before general patterns
	if strings.Contains(customID, "video::low::") && strings.Contains(customID, "::manual") {
		return MJSubmitActionVideoLowMotion
	}
	if strings.Contains(customID, "video::high::") && strings.Contains(customID, "::manual") {
		return MJSubmitActionVideoHighMotion
	}
	if strings.Contains(customID, "video::low::") && strings.Contains(customID, "::auto") {
		return MJSubmitActionVideoAutoLow
	}
	if strings.Contains(customID, "video::high::") && strings.Contains(customID, "::auto") {
		return MJSubmitActionVideoAutoHigh
	}

	checkMap := map[string]string{
		"variation::":      MJSubmitActionVariation,
		"pan_":             MJSubmitActionPan,
		"Inpaint::":        MJSubmitActionInpaint,
		"Outpaint::":       MJSubmitActionOutpaint,
		"reroll::":         MJSubmitActionReroll,
		"Job::PicReader::": MJSubmitActionPicReader,
		"Picread::Retry":   MJSubmitActionPicReaderRetry,
		":upsample::":      MJSubmitActionUpscale,
		"upsample_v5_2x":   MJSubmitActionUpscale2x,
		"upsample_v5_4x":   MJSubmitActionUpscale4x,
	}

	for k, action := range checkMap {
		if strings.Contains(customID, k) {
			return action
		}
	}
	return MJSubmitActionAction
}

var ModeKeyWord = map[string]string{
	"turbo": "--turbo",
	"fast":  "--fast",
	"relax": "--relax",
}

func relayMidjourneyPlusSubmit(c *gin.Context, relayMode int) (mjResp *openai.MidjourneyResponse) {
	ctx := c.Request.Context()
	consumeQuota := true //c.GetBool("consume_quota")
	mode := c.GetString("mode")
	meta := meta.GetByContext(c)

	action := ""
	var midjRequest openai.MidjourneyPlusRequest
	if consumeQuota {
		err := common.UnmarshalBodyReusable(c, &midjRequest)
		if err != nil {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "bind_request_body_failed",
			}
		}

		// 处理base64内容
		err = handleMidjourneyRequest(c, &midjRequest, meta)
		if err != nil {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: err.Error(),
			}
		}

		// 过滤提示词中的长连续字符
		if midjRequest.Prompt != "" {
			midjRequest.Prompt = filterLongContinuousChars(midjRequest.Prompt)
		}
	}
	// 清空midjRequest中的accountFilter防止盗刷
	midjRequest.AccountFilter = nil
	meta.DetailPrompt = midjRequest.Prompt

	// Check and process video motion parameters early
	var isVideoMode = false
	// 检测 --motion high 或 --motion low 参数
	hasMotionHigh := strings.Contains(midjRequest.Prompt, "--motion high")
	hasMotionLow := strings.Contains(midjRequest.Prompt, "--motion low")
	hasVideo1 := strings.Contains(midjRequest.Prompt, "--video 1")

	if hasMotionHigh || hasMotionLow {
		isVideoMode = true
		// 如果有 motion 参数但没有 --video 1，自动补充
		if !hasVideo1 {
			midjRequest.Prompt = midjRequest.Prompt + " --video 1"
			meta.DetailPrompt = midjRequest.Prompt // 更新 DetailPrompt
			logger.SysLog(fmt.Sprintf("检测到motion参数但缺少--video 1，自动补充，用户ID: %d, 新prompt: %s", meta.UserId, midjRequest.Prompt))
		}
		logger.SysLog(fmt.Sprintf("检测到视频motion参数，任务将按视频模型计费，用户ID: %d", meta.UserId))
	}

	requestURL := getMjRequestPath(c.Request.URL.String(), mode)
	if _, ok := ModeKeyWord[mode]; !ok {
		logger.Error(c.Request.Context(), fmt.Sprintf("id: %d, mj 不支持的mode: %s", c.GetInt(ctxkey.Id), mode))
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "mode_params_is_invalid",
		}
	}
	imageModel := "midjourney-" + mode
	// 如果检测到视频模式，修改模型名称
	if isVideoMode {
		imageModel = "midjourney-video-" + mode
		logger.SysLog(fmt.Sprintf("视频模式任务，使用模型: %s, 用户ID: %d", imageModel, meta.UserId))
	}
	// 用于重试的模型
	c.Set("mj_real_request_model", imageModel)
	var originChannel int

	if relayMode == constant.RelayModeMidjourneyImagine { //绘画任务，此类任务可重复
		if midjRequest.Prompt == "" {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "prompt_is_required",
			}
		}
		for _, k := range ModeKeyWord {
			if strings.Contains(midjRequest.Prompt, k) {
				return &openai.MidjourneyResponse{
					Code:        4,
					Description: "prompt_cannot_contain_illegal_instructions",
				}
			}
		}
		action = MJSubmitActionImagine
		// 如果检测到视频模式，修改action为视频相关
		if isVideoMode {
			if hasMotionHigh {
				action = MJSubmitActionVideoHighMotion
			} else if hasMotionLow {
				action = MJSubmitActionVideoLowMotion
			}
			logger.SysLog(fmt.Sprintf("视频模式任务，使用action: %s, 用户ID: %d", action, meta.UserId))
		}
		if c.GetBool("mj_translate_enabled") {
			// 优先使用令牌配置的翻译
			midjRequest.Prompt = mjPreTranslate(
				midjRequest.Prompt,
				c.GetString("mj_translate_model"),
				c.GetString("mj_translate_base_url"),
				c.GetString("mj_translate_api_key"),
			)
		} else if meta.Config.MJTranslateEnabled {
			// 如果令牌未配置翻译，则使用全局翻译配置
			midjRequest.Prompt = mjPreTranslate(
				midjRequest.Prompt,
				meta.Config.MJTranslateModel,
				"",
				"",
			)
		}
	} else if relayMode == constant.RelayModeMidjourneyDescribe { //按图生文任务，此类任务可重复
		if !meta.Config.MJBase64ToLocalEnabled && midjRequest.Base64 == "" {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "base64_is_required",
			}
		}
		action = MJSubmitActionDescribe
	} else if relayMode == constant.RelayModeMidjourneyBlend { //绘画任务，此类任务可重复
		if !meta.Config.MJBase64ToLocalEnabled && len(midjRequest.Base64Array) == 0 {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "base64Array_is_required",
			}
		}
		action = MJSubmitActionBlend
	} else if relayMode == constant.RelayModeMidjourneyChange {
		if len(midjRequest.TaskId) == 0 {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "taskId_is_required",
			}
		}
		if midjRequest.Index <= 0 || midjRequest.Index > 4 {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "index_is_required",
			}
		}
		if len(midjRequest.Action) == 0 {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "action_is_required",
			}
		}
		oldTask := model.GetByMJId(meta.UserId, midjRequest.TaskId)
		if oldTask == nil {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "task_not_found",
			}
		}
		originChannel = oldTask.ChannelId
		changeAction := midjRequest.Action
		label := ""
		customID := ""
		if changeAction == "UPSCALE" {
			label = fmt.Sprintf("U%d", midjRequest.Index)
			action = MJSubmitActionUpscale
		} else if changeAction == "VARIATION" {
			label = fmt.Sprintf("V%d", midjRequest.Index)
			action = MJSubmitActionVariation
		} else {
			action = MJSubmitActionReroll
		}
		btns := oldTask.GetButtons()
		for _, btn := range btns {
			if btn.Label == label {
				customID = btn.CustomId
				break
			}
		}
		midjRequest.Index = 0
		midjRequest.Action = ""
		midjRequest.CustomId = customID
		requestURL = strings.ReplaceAll(requestURL, "change", "action")
		//action = MJSubmitActionAction
	} else if relayMode == constant.RelayModeMidjourneyChangeSimple {
		if len(midjRequest.Content) == 0 {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "content_is_required",
			}
		}
		//ID $action$index
		params := convertChangeParams(midjRequest.Content)
		if params == nil {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "content_parse_failed",
			}
		}
		oldTask := model.GetByMJId(meta.UserId, params.ID)
		if oldTask == nil {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "task_id_not_found",
			}
		}
		originChannel = oldTask.ChannelId
		changeAction := params.Action
		label := ""
		customID := ""
		if changeAction == "UPSCALE" {
			label = fmt.Sprintf("U%d", params.Index)
			action = MJSubmitActionUpscale
		} else if changeAction == "VARIATION" {
			label = fmt.Sprintf("V%d", params.Index)
			action = MJSubmitActionVariation
		} else {
			action = MJSubmitActionReroll
		}
		btns := oldTask.GetButtons()
		for _, btn := range btns {
			if btn.Label == label {
				customID = btn.CustomId
				break
			}
		}
		if customID == "" {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "action_parse_failed",
			}
		}
		midjRequest.Index = 0
		midjRequest.Action = ""
		midjRequest.Content = ""
		midjRequest.CustomId = customID
		midjRequest.TaskId = oldTask.MjId
		requestURL = strings.ReplaceAll(requestURL, "/simple-change", "/action")
	} else if relayMode == constant.RelayModeMidjourneyAction {
		if len(midjRequest.TaskId) == 0 {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "taskId_is_required",
			}
		}
		if len(midjRequest.CustomId) == 0 {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "customId_is_required",
			}
		}
		oldTask := model.GetByMJId(meta.UserId, midjRequest.TaskId)
		if oldTask == nil {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "task_id_not_found",
			}
		}
		originChannel = oldTask.ChannelId

		action = getActionByCustomID(midjRequest.CustomId)

	} else if relayMode == constant.RelayModeMidjourneyModal {
		if len(midjRequest.TaskId) == 0 {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "taskId_is_required",
			}
		}
		if midjRequest.Prompt != "" {
			for _, k := range ModeKeyWord {
				if strings.Contains(midjRequest.Prompt, k) {
					return &openai.MidjourneyResponse{
						Code:        4,
						Description: "prompt_cannot_contain_illegal_instructions",
					}
				}
			}
		}
		oldTask := model.GetByMJId(meta.UserId, midjRequest.TaskId)
		if oldTask == nil {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "task_id_not_found",
			}
		}
		originChannel = oldTask.ChannelId
		action = MJSubmitActionModal
	} else if relayMode == constant.RelayModeMidjourneyShorten {
		if len(midjRequest.Prompt) == 0 {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "prompt_is_required",
			}
		}
		action = MJSubmitActionShorten
	} else if relayMode == constant.RelayModeMidjourneyInsightFaceSwap {
		if isInterfaceEmpty(midjRequest.SourceBase64) {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "sourceBase64_is_required",
			}
		}
		if isInterfaceEmpty(midjRequest.TargetBase64) {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "targetBase64_is_required",
			}
		}
		action = MJSubmitActionFaceSwap
	} else if relayMode == constant.RelayModeMidjourneyEdits {
		if len(midjRequest.Prompt) == 0 {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "prompt_is_required",
			}
		}

		// 检查是否有图片输入（支持 base64、image 字段）
		hasImage := !isInterfaceEmpty(midjRequest.Base64) || midjRequest.Image != ""

		// 检查是否有遮罩（编辑模式）
		hasMask := !isInterfaceEmpty(midjRequest.MaskBase64)

		// 如果既没有图片也没有遮罩，则报错
		if !hasImage && !hasMask {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "image_or_maskBase64_is_required",
			}
		}

		action = MJSubmitActionEdits
	}

	if originChannel != 0 && originChannel != meta.ChannelId {
		channel, err := model.GetChannelById(originChannel, true)
		if err != nil {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "channel_not_found",
			}
		}
		// 校验originChannel的channel.Models字符串用逗号切分之后的数组中是否包含当前请求的模型
		channelAvailableModelList := strings.Split(channel.Models, ",")
		if !lo.Contains(channelAvailableModelList, imageModel) {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "channel_not_support_model",
			}
		}
		c.Set("base_url", channel.GetBaseURL())
		c.Set("channel_id", originChannel)
		c.Request.Header.Set("Authorization", fmt.Sprintf("Bearer %s", channel.Key))
		meta.ChannelId = originChannel
	}

	if midjRequest.Prompt != "" && mode != model.DefaultMJMode {
		// 判断是否配置了前缀 如果配置了就说明是中转 需要拼前缀 并且不拼指令
		if meta.Config.MJConcatModeToContent {
			midjRequest.Prompt += fmt.Sprintf(" %s", ModeKeyWord[mode])
		}
		if meta.Config.MJAccountFilterModeEnabled {
			// 重新创建accountFilter结构体，确保只包含我们想要的信息
			midjRequest.AccountFilter = &struct {
				ChannelId           string   `json:"channelId,omitempty"`
				InstanceId          string   `json:"instanceId,omitempty"`
				Modes               []string `json:"modes,omitempty"`
				Remark              string   `json:"remark,omitempty"`
				Remix               bool     `json:"remix,omitempty"`
				RemixAutoConsidered bool     `json:"remixAutoConsidered,omitempty"`
			}{
				// mode需要转大写
				Modes: []string{strings.ToUpper(mode)},
			}
			midjRequest.BotType = "MID_JOURNEY"
		} else {
			// 如果不启用账户过滤，则将AccountFilter设置为nil
			midjRequest.AccountFilter = nil
		}
	}

	// 判断是否需要去掉提示词前面的/
	if config.MidjourneyRemoveSlashEnabled {
		midjRequest.Prompt = strings.TrimPrefix(midjRequest.Prompt, "/")
	}

	groupRatio := billingratio.GetGroupRatio(meta.Group)
	ratio := groupRatio

	userQuota, quotaExpireTime, err := model.CacheGetUserQuotaAndExpireTime(ctx, meta.UserId)

	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "quota_not_enough",
		}
	}
	if config.QuotaExpireEnabled && quotaExpireTime > 0 && quotaExpireTime < helper.GetTimestamp() {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "quota_expired",
		}
	}

	// 请求上游的mode strings
	modeUrl := ""
	// 价格计算
	modeForPrice := ""
	if mode != "fast" && mode != "" {
		modeForPrice = mode + "_"
		modeUrl = fmt.Sprintf("/mj-%s", mode)

		if !meta.Config.MJConcatModeURLPrefixEnabled {
			modeUrl = ""
		}
	}
	v7FastConvertToTurbo := false
	if config.MidjourneyV7TurboEnabled {
		if (mode == "" || mode == "fast") && strings.Contains(midjRequest.Prompt, "--v 7") {
			v7FastConvertToTurbo = true
		}
	}

	// Check if the prompt contains --draft parameter for half-price calculation
	var isDraftMode = false
	if config.MidjourneyDraftHalfPriceEnabled {
		if strings.Contains(midjRequest.Prompt, "--draft") &&
			(mode == "" || mode == "fast" || mode == "turbo") &&
			strings.ToUpper(action) == "IMAGINE" {
			isDraftMode = true
		}
	}

	// 获取ModelFixedPrice的map副本
	modelFixedPriceMap := billingratio.GetModelFixedPriceMap()
	if v7FastConvertToTurbo {
		// 如果是v7模式，且当前模式为fast，则将fast改为turbo
		modeForPrice = "turbo_"
	}

	var actionPrice float64
	var ok bool

	if isVideoMode {
		// 如果是视频模式，使用视频价格
		actionPrice, ok = modelFixedPriceMap[fmt.Sprintf("mj_%svideo", modeForPrice)]
		if !ok {
			// 如果没有找到视频相关的价格，使用默认的视频价格
			actionPrice = 5.0 // 视频操作默认价格
			logger.SysLog(fmt.Sprintf("MJ Video 未定义的 action: %s, 使用默认价格: %f", "video", actionPrice))
		}
	} else {
		// 普通模式，使用原有逻辑
		actionPrice, ok = modelFixedPriceMap[fmt.Sprintf("mj_%s%s", modeForPrice, strings.ToLower(action))]
		if !ok {
			b, _ := json.Marshal(midjRequest)
			logger.SysError(fmt.Sprintf("MJ 未定义的 action, req: %s", string(b)))
			actionPrice = 1.0
		}
	}
	// 替换用户个性费率
	userModelFixedPrice, customOk, _, err := model.CacheGetUserModelFixedPrice(meta.UserId, fmt.Sprintf("mj_%s%s", modeForPrice, strings.ToLower(action)))
	if customOk {
		actionPrice = userModelFixedPrice
	}
	topupConvertRatio, userDiscount := billing.GetTopupConvertRatioByMeta(meta)

	// Apply half-price discount for draft mode
	var draftDiscount = 1.0
	if isDraftMode {
		draftDiscount = 0.5 // 50% discount for draft mode
	}

	quota := int64(actionPrice * ratio * 500000 * topupConvertRatio * userDiscount * draftDiscount)
	costQuota := int64(actionPrice * meta.CostPerUnit * 500000)
	if consumeQuota && userQuota-quota < 0 {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "quota_not_enough",
		}
	}

	baseURL := channeltype.ChannelBaseURLs[meta.ChannelType]

	if c.GetString(ctxkey.BaseURL) != "" {
		baseURL = c.GetString(ctxkey.BaseURL)
	}

	//midjRequest.NotifyHook = ""

	fullRequestURL := fmt.Sprintf("%s%s%s", baseURL, modeUrl, requestURL)

	jsonStr, err := json.Marshal(midjRequest)
	if err != nil {
		mjResp = &openai.MidjourneyResponse{
			Code:        4,
			Description: "marshal_request_body_failed",
		}
		return
	}

	req, err := http.NewRequest(c.Request.Method, fullRequestURL, bytes.NewBuffer(jsonStr))
	if err != nil {
		mjResp = &openai.MidjourneyResponse{
			Code:        4,
			Description: "create_request_failed",
		}
		return
	}
	req.Header.Set("Content-Type", c.Request.Header.Get("Content-Type"))
	req.Header.Set("Accept", c.Request.Header.Get("Accept"))
	req.Header.Set("mj-api-secret", strings.Split(c.Request.Header.Get("Authorization"), " ")[1])

	// 额外请求头
	if meta.ExtraHeaders != nil {
		for k, v := range meta.ExtraHeaders {
			req.Header.Set(k, v)
		}
	}

	resp, err := client.HTTPClient.Do(req)
	if err != nil {
		mjResp = &openai.MidjourneyResponse{
			Code:        4,
			Description: "do_request_failed",
		}
		return
	}
	defer resp.Body.Close()

	// 只读取一次响应体
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		mjResp = &openai.MidjourneyResponse{
			Code:        4,
			Description: "read_response_body_failed",
		}
		return
	}

	// 记录上游响应
	if model.ShouldLogDetail(meta.UserId) && config.LogUpstreamResponseEnabled {
		meta.UpstreamResponse = string(responseBody)
	}

	if resp.StatusCode != http.StatusOK {
		ctx := c.Request.Context()
		logger.Warn(ctx, fmt.Sprintf("mj error return req, status code: %d , body: %s", resp.StatusCode, string(responseBody)))

		mjResp = &openai.MidjourneyResponse{
			Code:        4,
			Description: fmt.Sprintf("fail_to_fetch_midjourney status_code: %v ; response body: %v", resp.StatusCode, string(responseBody)),
		}
		return
	}

	var midjResponse openai.MidjourneyResponse
	err = json.Unmarshal(responseBody, &midjResponse)
	logger.SysLog(fmt.Sprintf("midjourney plus responseBody: %s", string(responseBody)))
	logger.SysLog(fmt.Sprintf("midjourney plus midjResponse: %v", midjResponse))
	if err != nil {
		mjResp = &openai.MidjourneyResponse{
			Code:        4,
			Description: "unmarshal_response_body_failed",
		}
		return
	}
	requestId, ok := ctx.Value(helper.RequestIdKey).(string)
	if !ok {
		requestId = ""
	}

	// 文档：https://github.com/novicezk/midjourney-proxy/blob/main/docs/api.md
	//1-提交成功
	// 21-任务已存在（处理中或者有结果了） {"code":21,"description":"任务已存在","result":"0741798445574458","properties":{"status":"SUCCESS","imageUrl":"https://xxxx"}}
	// 22-排队中 {"code":22,"description":"排队中，前面还有1个任务","result":"0741798445574458","properties":{"numberOfQueues":1,"discordInstanceId":"1118138338562560102"}}
	// 23-队列已满，请稍后再试 {"code":23,"description":"队列已满，请稍后尝试","result":"14001929738841620","properties":{"discordInstanceId":"1118138338562560102"}}
	// 24-prompt包含敏感词 {"code":24,"description":"可能包含敏感词","properties":{"promptEn":"nude body","bannedWord":"nude"}}
	// other: 提交错误，description为错误描述
	midjourneyTask := &model.Midjourney{
		RequestId:   requestId,
		UserId:      meta.UserId,
		Code:        midjResponse.Code,
		Action:      action,
		MjId:        midjResponse.Result,
		Prompt:      midjRequest.Prompt,
		PromptEn:    "",
		Description: midjResponse.Description,
		State:       "",
		SubmitTime:  time.Now().UnixNano() / int64(time.Millisecond),
		StartTime:   0,
		FinishTime:  0,
		ImageUrl:    "",
		Status:      "",
		Progress:    "0%",
		FailReason:  "",
		ChannelId:   c.GetInt(ctxkey.ChannelId),
		Mode:        mode,
		Quota:       int(quota),
	}

	// 处理排队状态
	if midjResponse.Code == 1 && strings.Contains(midjResponse.Description, "排队中") {
		midjourneyTask.Status = "NOT_START"

		// 尝试从 properties 中获取排队信息
		if properties, ok := midjResponse.Properties.(map[string]interface{}); ok {
			if numberOfQueues, ok := properties["numberOfQueues"].(float64); ok {
				midjourneyTask.State = fmt.Sprintf("排队中，前面还有%d个任务", int(numberOfQueues))
			} else {
				midjourneyTask.State = "排队中"
			}
		} else {
			midjourneyTask.State = "排队中"
		}
	}

	if midjResponse.Code != 1 && midjResponse.Code != 21 && midjResponse.Code != 22 {
		//非1-提交成功,21-任务已存在和22-排队中，则记录错误原因
		midjourneyTask.FailReason = midjResponse.Description
		midjourneyTask.Status = "FAILURE"
	}
	if midjResponse.Code == 23 || midjResponse.Code == 3 { //23-队列已满 3-无账号
		logger.SysError(fmt.Sprintf("channel [%d] queue full or no account. code:[%d], description:[%s]", meta.ChannelId, midjResponse.Code, midjResponse.Description))
		consumeQuota = false
		// 无账号或者队列满 抛出异常 让外层循环重试下一个渠道
		mjResp = &openai.MidjourneyResponse{
			Code:        midjResponse.Code,
			Description: "queue_full_or_no_account",
		}
		return
	}

	//if midjResponse.Code == 21 { //21-任务已存在（处理中或者有结果了）
	//	// 将 properties 转换为一个 map
	//	properties, ok := midjResponse.Properties.(map[string]interface{})
	//	if ok {
	//		imageUrl, ok1 := properties["imageUrl"].(string)
	//		status, ok2 := properties["status"].(string)
	//		if ok1 && ok2 {
	//			midjourneyTask.ImageUrl = imageUrl
	//			midjourneyTask.Status = status
	//			if status == "SUCCESS" {
	//				midjourneyTask.Progress = "100%"
	//				midjourneyTask.StartTime = time.Now().UnixNano() / int64(time.Millisecond)
	//				midjourneyTask.FinishTime = time.Now().UnixNano() / int64(time.Millisecond)
	//				midjResponse.Code = 1
	//			}
	//		}
	//	}
	//	//修改返回值
	//	newBody := strings.Replace(string(responseBody), `"code":21`, `"code":1`, -1)
	//	responseBody = []byte(newBody)
	//}
	if action != MJSubmitActionModal { //提交模态框任务不记录、任务ID与上个操作相同
		err = midjourneyTask.Insert()
		if err != nil {
			mjResp = &openai.MidjourneyResponse{
				Code:        4,
				Description: "insert_midjourney_task_failed",
			}
			return
		}
	}
	if midjResponse.Code == 22 { //22-排队中，说明任务已存在
		//修改返回值
		newBody := strings.Replace(string(responseBody), `"code":22`, `"code":1`, -1)
		responseBody = []byte(newBody)
	}

	defer func(ctx context.Context, task *model.Midjourney) {
		if consumeQuota {
			err := model.PostConsumeTokenQuota(meta.TokenId, quota)
			if err != nil {
				logger.SysError("error consuming token remain quota: " + err.Error())
			}
			err = model.CacheUpdateUserQuota(ctx, meta.UserId)
			if err != nil {
				logger.SysError("error update user quota cache: " + err.Error())
			}
			if quota != 0 {
				if v7FastConvertToTurbo {
					// 如果是v7模式，且当前模式为fast，则将fast改为turbo
					mode = "fast-v7"
				}
				if isDraftMode {
					if mode == "" {
						mode = "fast-draft"
					} else {
						mode = mode + "-draft"
					}
				}
				logContent := fmt.Sprintf("操作价格 %.2f，分组倍率 %.2f，充值转换率 %.2f，用户折扣率 %.2f，模式 %s，操作 %s，MJ任务ID %s", actionPrice, groupRatio, topupConvertRatio, userDiscount, mode, action, midjResponse.Result)
				model.UpdateUserUsedQuotaAndRequestCount(meta.UserId, quota)
				model.UpdateChannelUsedQuota(meta.ChannelId, quota)
				createdLog := model.RecordConsumeLogByDetailIfZeroQuota(ctx, "", meta.Ip, meta.RemoteIp, meta.UserId,
					meta.ChannelId, meta.PromptTokens, 0, lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(imageModel),
					meta.TokenName, meta.TokenKey, meta.TokenGroup, meta.ChannelName, int(quota), int(costQuota), 0, 0, 0, meta.IsStream, logContent, "")
				helper.SafeGoroutine(func() {
					model.RecordLogExtend(ctx, createdLog, meta.DetailPrompt, meta.DetailCompletion, meta.CompletionId, meta.UpstreamResponse, meta.FullResponse, meta.RequestURLPath)
					// 同步推送到日志优化器
					optimizer.RecordConsumeLog(createdLog)
				})
				// 渠道问题构图失败补偿
				if task.Status == "FAILURE" {
					if task.Code != 24 || model.ShouldRefundMJSensitiveWordsError(task.UserId) {
						// code 24 敏感词补偿 不是24为渠道问题补偿
						task.Progress = "100%"
						err := model.IncreaseUserQuotaAndRedis(task.UserId, quota)
						if err != nil {
							logger.SysError("fail to increase user quota" + err.Error())
						}
						// 抵消渠道消耗和用户消耗
						model.UpdateUserUsedQuotaAndRequestCount(meta.UserId, -quota)
						model.UpdateChannelUsedQuota(meta.ChannelId, -quota)
						logContent = fmt.Sprintf("%s 构图失败，补偿 %s", task.MjId, common.LogQuota(quota))
						model.RecordRefundLogByDetailIfZeroQuota(nil, task.RequestId, "", "", task.UserId, task.ChannelId, 0, 0, fmt.Sprintf("%s", imageModel), "", "", "", int(-quota), 0, 0, 0, false, logContent)
						err = task.Update()
						if err != nil {
							logger.SysError("fail to update task status" + err.Error())
						}
					}
				}
			}
		}
	}(c.Request.Context(), midjourneyTask)

	resp.Body = io.NopCloser(bytes.NewBuffer(responseBody))

	for k, v := range resp.Header {
		// 排除这个请求头X-Served-By 避免暴露上游域名
		if strings.ToLower(k) == "x-served-by" {
			continue
		}
		c.Writer.Header().Set(k, v[0])
	}
	c.Writer.Header().Set("Content-Type", "application/json")
	c.Writer.WriteHeader(resp.StatusCode)

	_, err = io.Copy(c.Writer, resp.Body)
	if err != nil {
		mjResp = &openai.MidjourneyResponse{
			Code:        4,
			Description: "copy_response_body_failed",
		}
		return
	}
	err = resp.Body.Close()
	if err != nil {
		mjResp = &openai.MidjourneyResponse{
			Code:        4,
			Description: "close_response_body_failed",
		}
		return
	}
	// 记录最终响应（考虑用户级别配置）
	if model.ShouldLogFullResponse(meta.UserId) {
		meta.FullResponse = string(responseBody)
	}
	return nil
}

func getMjRequestPath(path, mode string) string {
	requestURL := path
	if strings.Contains(requestURL, "/mjPlus/mj") {
		requestURL = strings.TrimPrefix(requestURL, "/mjPlus")
	}
	if strings.Contains(requestURL, fmt.Sprintf("/mj-%s/mj", mode)) {
		requestURL = strings.TrimPrefix(requestURL, fmt.Sprintf("/mj-%s", mode))
	}
	return requestURL
}

type taskChangeParams struct {
	ID     string
	Action string
	Index  int
}

func convertChangeParams(content string) *taskChangeParams {
	split := strings.Split(content, " ")
	if len(split) != 2 {
		return nil
	}

	action := strings.ToLower(split[1])
	changeParams := &taskChangeParams{}
	changeParams.ID = split[0]

	if action[0] == 'u' {
		changeParams.Action = "UPSCALE"
	} else if action[0] == 'v' {
		changeParams.Action = "VARIATION"
	} else if action == "r" {
		changeParams.Action = "REROLL"
		return changeParams
	} else {
		return nil
	}

	index, err := strconv.Atoi(action[1:2])
	if err != nil || index < 1 || index > 4 {
		return nil
	}
	changeParams.Index = index
	return changeParams
}

// mj预翻译
func mjPreTranslate(originPrompt string, translateModel string, baseUrl string, apiKey string) string {
	// 如果没有提供自定义翻译配置，使用默认配置
	if baseUrl == "" {
		defaultToken, err := model.GetUserInitialToken(1)
		if err != nil {
			logger.SysError(err.Error())
			return originPrompt
		}
		key := defaultToken.Key
		baseUrl = config.ServerAddress + "/v1/chat/completions"
		apiKey = fmt.Sprintf("sk-%s", key) // 内部key需要加上sk-前缀
	} else {
		// 外部key如果没有sk-前缀，需要添加
		if !strings.HasPrefix(apiKey, "sk-") {
			apiKey = fmt.Sprintf("sk-%s", apiKey)
		}
		// 确保baseUrl以/结尾
		if !strings.HasSuffix(baseUrl, "/") {
			baseUrl += "/"
		}
		baseUrl += "v1/chat/completions"
	}

	body := map[string]interface{}{
		"model": lo.If(translateModel == "", "gpt-3.5-turbo").Else(translateModel),
		"messages": []map[string]interface{}{
			{
				"role":    "user",
				"content": "Translate the following text into English Just give me the translation. Don't explain too much:\n" + originPrompt,
			},
		},
		"stream":            false,
		"temperature":       0.5,
		"max_tokens":        256,
		"top_p":             1,
		"frequency_penalty": 0,
		"presence_penalty":  0,
	}

	marshal, err := json.Marshal(body)
	if err != nil {
		logger.SysError(err.Error())
		return originPrompt
	}

	req, err := http.NewRequest("POST", baseUrl, bytes.NewBuffer(marshal))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiKey))
	resp, err := client.HTTPClient.Do(req)
	if err != nil {
		logger.SysError(err.Error())
		return originPrompt
	}
	if resp == nil {
		logger.SysError("response is nil")
		return originPrompt
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			fmt.Println(err)
			logger.SysError(err.Error())
		}
	}(resp.Body)
	if resp.StatusCode != 200 {
		logger.SysError(fmt.Sprintf("response status code is %d", resp.StatusCode))
		return originPrompt
	}
	var textResponse openai.SlimTextResponse
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.SysError(err.Error())
		return originPrompt
	}
	err = json.Unmarshal(responseBody, &textResponse)
	if err != nil {
		logger.SysError(err.Error())
		return originPrompt
	}
	return textResponse.GetFirstChoiceMessage()
}

func saveBase64ToLocal(base64Data string, userId int) (string, error) {
	if base64Data == "" {
		return "", nil
	}

	// 移除 data:image 前缀
	if strings.HasPrefix(base64Data, "data:image") {
		base64Data = base64Data[strings.Index(base64Data, "base64,")+7:]
	}

	// 解码base64数据
	data, err := base64.StdEncoding.DecodeString(base64Data)
	if err != nil {
		return "", fmt.Errorf("save base64 failed: %v", err)
	}

	// 按日期创建目录结构: storage/mj/用户ID/年/月/日/
	now := time.Now()
	userDir := fmt.Sprintf("storage/mj/%d/%d/%02d/%02d",
		userId,
		now.Year(),
		now.Month(),
		now.Day(),
	)

	err = os.MkdirAll(userDir, 0755)
	if err != nil {
		return "", err
	}

	// 生成唯一文件名: 时间戳_随机数.png
	fileName := fmt.Sprintf("%d_%d.png",
		now.UnixNano(),   // 纳秒级时间戳
		rand.Intn(10000), // 随机数避免并发冲突
	)
	filePath := filepath.Join(userDir, fileName)

	// 写入文件
	err = os.WriteFile(filePath, data, 0644)
	if err != nil {
		return "", err
	}

	// 返回完整URL
	return fmt.Sprintf("%s/fileSystem/mj/%d/%d/%02d/%02d/%s",
		lo.If(config.MidjourneyBase64StorageAddress == "", config.ServerAddress).Else(config.MidjourneyBase64StorageAddress),
		userId,
		now.Year(),
		now.Month(),
		now.Day(),
		fileName,
	), nil
}

func handleMidjourneyRequest(c *gin.Context, midjRequest *openai.MidjourneyPlusRequest, meta *meta.Meta) error {
	if !meta.Config.MJBase64ToLocalEnabled {
		return nil
	}

	// 首先处理 base64 字段的兼容性转换
	err := normalizeBase64Fields(midjRequest)
	if err != nil {
		return fmt.Errorf("normalize base64 fields failed: %v", err)
	}

	relayMode := constant.RelayModeUnknown
	if strings.HasSuffix(c.Request.URL.Path, "/submit/imagine") {
		relayMode = constant.RelayModeMidjourneyImagine
	} else if strings.HasSuffix(c.Request.URL.Path, "/submit/describe") {
		relayMode = constant.RelayModeMidjourneyDescribe
	} else if strings.HasSuffix(c.Request.URL.Path, "/submit/blend") {
		relayMode = constant.RelayModeMidjourneyBlend
	} else if strings.HasSuffix(c.Request.URL.Path, "/submit/edits") {
		relayMode = constant.RelayModeMidjourneyEdits
	}

	switch relayMode {
	case constant.RelayModeMidjourneyImagine:
		// 处理 base64 和 URL
		if base64Str := getStringFromInterface(midjRequest.Base64); base64Str != "" {
			if strings.HasPrefix(base64Str, "http") {
				// 如果是 URL，直接拼接到 prompt 前面
				midjRequest.Prompt = base64Str + " " + midjRequest.Prompt
			} else {
				// 如果是 base64，保存为本地文件并拼接路径
				localPath, err := saveBase64ToLocal(base64Str, meta.UserId)
				if err != nil {
					return fmt.Errorf("save base64 failed: %v", err)
				}
				midjRequest.Prompt = localPath + " " + midjRequest.Prompt
			}
			midjRequest.Base64 = nil
		}

		// 处理 base64Array
		if len(midjRequest.Base64Array) > 0 {
			var paths []string
			// 用于去重的 map，记录已处理的内容
			processedContent := make(map[string]bool)

			for _, content := range midjRequest.Base64Array {
				// 检查是否已经处理过相同的内容
				if processedContent[content] {
					continue // 跳过重复的内容
				}

				if strings.HasPrefix(content, "http") {
					// 如果是 URL，直接添加
					paths = append(paths, content)
				} else {
					// 如果是 base64，保存为本地文件
					localPath, err := saveBase64ToLocal(content, meta.UserId)
					if err != nil {
						return fmt.Errorf("save base64 array failed: %v", err)
					}
					paths = append(paths, localPath)
				}

				// 标记该内容已处理
				processedContent[content] = true
			}
			midjRequest.Prompt = strings.Join(paths, " ") + " " + midjRequest.Prompt
			midjRequest.Base64Array = nil
		}

	case constant.RelayModeMidjourneyDescribe:
		if base64Str := getStringFromInterface(midjRequest.Base64); base64Str != "" {
			if !strings.HasPrefix(base64Str, "http") {
				// 只有不是 URL 时才需要保存为本地文件
				localPath, err := saveBase64ToLocal(base64Str, meta.UserId)
				if err != nil {
					return fmt.Errorf("save base64 failed: %v", err)
				}
				// 清空base64然后换成link
				midjRequest.Base64 = nil
				midjRequest.Link = localPath
			}
		}

	case constant.RelayModeMidjourneyBlend:
		// Blend 类型跳过转换处理
		return nil

	case constant.RelayModeMidjourneyEdits:
		// 处理 edits 请求
		// 根据接口文档，edits 有两种模式：
		// 1. 编辑图片：需要 maskBase64（必填）、prompt（必填）、image（选填，原图）
		// 2. 转绘图片：只需要 image 和 prompt

		// 检查是否有 maskBase64，如果有则是编辑模式，需要合成图片
		if midjRequest.MaskBase64 != nil {
			// 编辑模式：需要将 image 和 maskBase64 合成
			imageStr := getStringFromInterface(midjRequest.Base64)
			if imageStr == "" && midjRequest.Image != "" {
				imageStr = midjRequest.Image
			}

			maskStr := getStringFromInterface(midjRequest.MaskBase64)
			if maskStr == "" {
				return fmt.Errorf("maskBase64 is required for edit mode")
			}

			// 如果有原图，需要处理原图
			if imageStr != "" {
				if strings.HasPrefix(imageStr, "http") {
					// 如果是 URL，保存到 image 字段
					midjRequest.Image = imageStr
				} else {
					// 如果是 base64，保存为本地文件
					localPath, err := saveBase64ToLocal(imageStr, meta.UserId)
					if err != nil {
						return fmt.Errorf("save original image base64 failed: %v", err)
					}
					midjRequest.Image = localPath
				}
			}

			// 处理遮罩图片
			if !strings.HasPrefix(maskStr, "http") {
				// 如果是 base64，保存为本地文件
				localPath, err := saveBase64ToLocal(maskStr, meta.UserId)
				if err != nil {
					return fmt.Errorf("save mask base64 failed: %v", err)
				}
				midjRequest.MaskBase64 = localPath
			}

			// 清空 Base64 字段，因为已经处理到 Image 和 MaskBase64 字段了
			midjRequest.Base64 = nil
		} else {
			// 转绘模式：只需要处理 image
			imageStr := getStringFromInterface(midjRequest.Base64)
			if imageStr == "" && midjRequest.Image != "" {
				imageStr = midjRequest.Image
			}

			if imageStr != "" {
				if strings.HasPrefix(imageStr, "http") {
					// 如果是 URL，保存到 image 字段
					midjRequest.Image = imageStr
				} else {
					// 如果是 base64，保存为本地文件
					localPath, err := saveBase64ToLocal(imageStr, meta.UserId)
					if err != nil {
						return fmt.Errorf("save image base64 failed: %v", err)
					}
					midjRequest.Image = localPath
				}
				// 清空 Base64 字段
				midjRequest.Base64 = nil
			}
		}
	}

	return nil
}

// normalizeBase64Fields 标准化 base64 字段，处理兼容性
func normalizeBase64Fields(midjRequest *openai.MidjourneyPlusRequest) error {
	// 处理 Base64 字段
	if midjRequest.Base64 != nil {
		if err := normalizeBase64Field(&midjRequest.Base64, &midjRequest.Base64Array); err != nil {
			return fmt.Errorf("normalize Base64 field failed: %v", err)
		}
	}

	// 处理 SourceBase64 字段
	if midjRequest.SourceBase64 != nil {
		var dummyArray []string // SourceBase64 不需要转到数组，只做类型转换
		if err := normalizeBase64Field(&midjRequest.SourceBase64, &dummyArray); err != nil {
			return fmt.Errorf("normalize SourceBase64 field failed: %v", err)
		}
	}

	// 处理 TargetBase64 字段
	if midjRequest.TargetBase64 != nil {
		var dummyArray []string // TargetBase64 不需要转到数组，只做类型转换
		if err := normalizeBase64Field(&midjRequest.TargetBase64, &dummyArray); err != nil {
			return fmt.Errorf("normalize TargetBase64 field failed: %v", err)
		}
	}

	// 处理 MaskBase64 字段
	if midjRequest.MaskBase64 != nil {
		var dummyArray []string // MaskBase64 不需要转到数组，只做类型转换
		if err := normalizeBase64Field(&midjRequest.MaskBase64, &dummyArray); err != nil {
			return fmt.Errorf("normalize MaskBase64 field failed: %v", err)
		}
	}

	return nil
}

// normalizeBase64Field 标准化单个 base64 字段
func normalizeBase64Field(field *interface{}, targetArray *[]string) error {
	if *field == nil {
		return nil
	}

	switch v := (*field).(type) {
	case string:
		// 单个字符串，保持不变
		return nil
	case []interface{}:
		// 接口数组，转换为字符串数组
		var strArray []string
		for _, item := range v {
			if str, ok := item.(string); ok {
				strArray = append(strArray, str)
			} else {
				return fmt.Errorf("array contains non-string element: %T", item)
			}
		}
		// 如果是 Base64 字段且有多个值，转移到 Base64Array
		if targetArray != nil && len(strArray) > 1 {
			*targetArray = append(*targetArray, strArray...)
			*field = nil // 清空原字段
		} else if len(strArray) == 1 {
			*field = strArray[0] // 单个值保持为字符串
		} else {
			*field = nil // 空数组
		}
		return nil
	case []string:
		// 字符串数组
		if targetArray != nil && len(v) > 1 {
			*targetArray = append(*targetArray, v...)
			*field = nil // 清空原字段
		} else if len(v) == 1 {
			*field = v[0] // 单个值保持为字符串
		} else {
			*field = nil // 空数组
		}
		return nil
	default:
		return fmt.Errorf("unsupported base64 field type: %T", v)
	}
}

// getStringFromInterface 从 interface{} 中安全获取字符串值
func getStringFromInterface(field interface{}) string {
	if field == nil {
		return ""
	}
	if str, ok := field.(string); ok {
		return str
	}
	return ""
}

// isInterfaceEmpty 检查 interface{} 是否为空
func isInterfaceEmpty(v interface{}) bool {
	if v == nil {
		return true
	}
	switch v := v.(type) {
	case string:
		return v == ""
	case []interface{}:
		return len(v) == 0
	case []string:
		return len(v) == 0
	default:
		return false
	}
}

// 过滤提示词中的长连续字符和邮箱
func filterLongContinuousChars(prompt string) string {
	// 分割提示词为单词
	words := strings.Fields(prompt)
	filteredWords := make([]string, 0, len(words))

	// 邮箱正则表达式
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)

	for _, word := range words {
		// 保留URL
		if strings.HasPrefix(word, "http://") || strings.HasPrefix(word, "https://") {
			filteredWords = append(filteredWords, word)
			continue
		}

		// 过滤邮箱
		if emailRegex.MatchString(word) {
			continue // 跳过邮箱
		}

		// 检查是否是长连续字符（超过15个字符且包含连续的字母/数字）
		if len(word) > 15 {
			// 使用正则表达式检查是否包含连续的字母或数字
			matched, _ := regexp.MatchString(`[a-zA-Z0-9]{10,}`, word)
			if matched {
				continue // 跳过这个词
			}
		}

		filteredWords = append(filteredWords, word)
	}

	return strings.Join(filteredWords, " ")
}

func relayMidjourneyPlusVideo(c *gin.Context, relayMode int) (mjResp *openai.MidjourneyResponse) {
	ctx := c.Request.Context()
	consumeQuota := true
	meta := meta.GetByContext(c)

	action := ""
	var midjRequest openai.MidjourneyPlusRequest
	if consumeQuota {
		err := common.UnmarshalBodyReusable(c, &midjRequest)
		if err != nil {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "bind_request_body_failed",
			}
		}

		// 处理base64内容
		err = handleMidjourneyRequest(c, &midjRequest, meta)
		if err != nil {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: err.Error(),
			}
		}

		// 过滤提示词中的长连续字符
		if midjRequest.Prompt != "" {
			midjRequest.Prompt = filterLongContinuousChars(midjRequest.Prompt)
		}
	}

	// 清空midjRequest中的accountFilter防止盗刷
	midjRequest.AccountFilter = nil
	meta.DetailPrompt = midjRequest.Prompt

	// 处理视频请求的模式
	videoMode := "fast" // 默认模式
	if midjRequest.Mode != "" {
		videoMode = midjRequest.Mode // 直接使用Mode字段的值
	}

	// 验证模式是否有效
	if _, ok := ModeKeyWord[videoMode]; !ok {
		logger.Error(c.Request.Context(), fmt.Sprintf("id: %d, mj video 不支持的mode: %s", c.GetInt(ctxkey.Id), videoMode))
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "mode_params_is_invalid",
		}
	}

	requestURL := getMjRequestPath(c.Request.URL.String(), videoMode)
	imageModel := "midjourney-video-" + videoMode

	// 用于重试的模型
	c.Set("mj_real_request_model", imageModel)
	var originChannel int

	// 根据action确定操作类型
	if midjRequest.Action == "" {
		// 如果action为空，则是图生视频
		action = MJSubmitActionVideo
		if midjRequest.Base64 == "" && midjRequest.Prompt == "" {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "base64_or_prompt_is_required",
			}
		}
	} else {
		// 视频衍生操作
		if midjRequest.TaskId == "" {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "taskId_is_required_for_video_action",
			}
		}

		oldTask := model.GetByMJId(meta.UserId, midjRequest.TaskId)
		if oldTask == nil {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "task_not_found",
			}
		}
		originChannel = oldTask.ChannelId

		// 根据action设置相应的操作
		switch midjRequest.Action {
		case "rerun":
			action = MJSubmitActionVideoRerun
		case "start_frame":
			action = MJSubmitActionVideoStartFrame
		case "prompt":
			action = MJSubmitActionVideoPrompt
		case "auto_low":
			action = MJSubmitActionVideoAutoLow
		case "auto_high":
			action = MJSubmitActionVideoAutoHigh
		case "low_motion":
			action = MJSubmitActionVideoLowMotion
		case "high_motion":
			action = MJSubmitActionVideoHighMotion
		default:
			action = MJSubmitActionVideo
		}
	}

	// 设置默认值
	if midjRequest.VideoType == "" {
		midjRequest.VideoType = "vid_1.1_i2v_480" // 默认视频类型
	}
	if midjRequest.AnimateMode == "" {
		midjRequest.AnimateMode = "manual" // 默认动画模式
	}
	if midjRequest.Motion == "" {
		midjRequest.Motion = "low" // 默认动作
	}

	if originChannel != 0 && originChannel != meta.ChannelId {
		channel, err := model.GetChannelById(originChannel, true)
		if err != nil {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "channel_not_found",
			}
		}
		// 校验originChannel的channel.Models字符串用逗号切分之后的数组中是否包含当前请求的模型
		channelAvailableModelList := strings.Split(channel.Models, ",")
		if !lo.Contains(channelAvailableModelList, imageModel) {
			return &openai.MidjourneyResponse{
				Code:        4,
				Description: "channel_not_support_model",
			}
		}
		c.Set("base_url", channel.GetBaseURL())
		c.Set("channel_id", originChannel)
		c.Request.Header.Set("Authorization", fmt.Sprintf("Bearer %s", channel.Key))
		meta.ChannelId = originChannel
	}

	groupRatio := billingratio.GetGroupRatio(meta.Group)
	ratio := groupRatio

	userQuota, quotaExpireTime, err := model.CacheGetUserQuotaAndExpireTime(ctx, meta.UserId)
	if err != nil {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "quota_not_enough",
		}
	}
	if config.QuotaExpireEnabled && quotaExpireTime > 0 && quotaExpireTime < helper.GetTimestamp() {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "quota_expired",
		}
	}

	// 请求上游的mode strings
	modeUrl := ""
	// 价格计算
	modeForPrice := ""
	if videoMode != "fast" && videoMode != "" {
		modeForPrice = videoMode + "_"
		modeUrl = fmt.Sprintf("/mj-%s", videoMode)

		if !meta.Config.MJConcatModeURLPrefixEnabled {
			modeUrl = ""
		}
	}

	// 获取ModelFixedPrice的map副本
	modelFixedPriceMap := billingratio.GetModelFixedPriceMap()
	actionPrice, ok := modelFixedPriceMap[fmt.Sprintf("mj_%s%s", modeForPrice, strings.ToLower(action))]
	if !ok {
		// 如果没有找到视频相关的价格，使用默认的视频价格
		actionPrice = 5.0 // 视频操作默认价格
		logger.SysLog(fmt.Sprintf("MJ Video 未定义的 action: %s, 使用默认价格: %f", action, actionPrice))
	}

	// 替换用户个性费率
	userModelFixedPrice, customOk, _, err := model.CacheGetUserModelFixedPrice(meta.UserId, fmt.Sprintf("mj_%s%s", modeForPrice, strings.ToLower(action)))
	if customOk {
		actionPrice = userModelFixedPrice
	}

	topupConvertRatio, userDiscount := billing.GetTopupConvertRatioByMeta(meta)
	quota := int64(actionPrice * ratio * 500000 * topupConvertRatio * userDiscount)
	costQuota := int64(actionPrice * meta.CostPerUnit * 500000)

	if consumeQuota && userQuota-quota < 0 {
		return &openai.MidjourneyResponse{
			Code:        4,
			Description: "quota_not_enough",
		}
	}

	baseURL := channeltype.ChannelBaseURLs[meta.ChannelType]
	if c.GetString(ctxkey.BaseURL) != "" {
		baseURL = c.GetString(ctxkey.BaseURL)
	}

	fullRequestURL := fmt.Sprintf("%s%s%s", baseURL, modeUrl, requestURL)

	jsonStr, err := json.Marshal(midjRequest)
	if err != nil {
		mjResp = &openai.MidjourneyResponse{
			Code:        4,
			Description: "marshal_request_body_failed",
		}
		return
	}

	req, err := http.NewRequest(c.Request.Method, fullRequestURL, bytes.NewBuffer(jsonStr))
	if err != nil {
		mjResp = &openai.MidjourneyResponse{
			Code:        4,
			Description: "create_request_failed",
		}
		return
	}

	req.Header.Set("Content-Type", c.Request.Header.Get("Content-Type"))
	req.Header.Set("Accept", c.Request.Header.Get("Accept"))
	req.Header.Set("mj-api-secret", strings.Split(c.Request.Header.Get("Authorization"), " ")[1])

	// 额外请求头
	if meta.ExtraHeaders != nil {
		for k, v := range meta.ExtraHeaders {
			req.Header.Set(k, v)
		}
	}

	resp, err := client.HTTPClient.Do(req)
	if err != nil {
		mjResp = &openai.MidjourneyResponse{
			Code:        4,
			Description: "do_request_failed",
		}
		return
	}
	defer resp.Body.Close()

	// 只读取一次响应体
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		mjResp = &openai.MidjourneyResponse{
			Code:        4,
			Description: "read_response_body_failed",
		}
		return
	}

	// 记录上游响应（考虑用户级别配置）
	if model.ShouldLogUpstreamResponse(meta.UserId) {
		meta.UpstreamResponse = string(responseBody)
	}

	if resp.StatusCode != http.StatusOK {
		ctx := c.Request.Context()
		logger.Warn(ctx, fmt.Sprintf("mj video error return req, status code: %d , body: %s", resp.StatusCode, string(responseBody)))

		mjResp = &openai.MidjourneyResponse{
			Code:        4,
			Description: fmt.Sprintf("fail_to_fetch_midjourney_video status_code: %v ; response body: %v", resp.StatusCode, string(responseBody)),
		}
		return
	}

	var midjResponse openai.MidjourneyResponse
	err = json.Unmarshal(responseBody, &midjResponse)
	logger.SysLog(fmt.Sprintf("midjourney plus video responseBody: %s", string(responseBody)))
	logger.SysLog(fmt.Sprintf("midjourney plus video midjResponse: %v", midjResponse))
	if err != nil {
		mjResp = &openai.MidjourneyResponse{
			Code:        4,
			Description: "unmarshal_response_body_failed",
		}
		return
	}

	requestId, ok := ctx.Value(helper.RequestIdKey).(string)
	if !ok {
		requestId = ""
	}

	// 创建视频任务记录
	midjourneyTask := &model.Midjourney{
		RequestId:   requestId,
		UserId:      meta.UserId,
		Code:        midjResponse.Code,
		Action:      action,
		MjId:        midjResponse.Result,
		Prompt:      midjRequest.Prompt,
		PromptEn:    "",
		Description: midjResponse.Description,
		State:       "",
		SubmitTime:  time.Now().UnixNano() / int64(time.Millisecond),
		StartTime:   0,
		FinishTime:  0,
		ImageUrl:    "",
		Status:      "",
		Progress:    "0%",
		FailReason:  "",
		ChannelId:   c.GetInt(ctxkey.ChannelId),
		Mode:        videoMode,
		Quota:       int(quota),
	}

	// 处理排队状态
	if midjResponse.Code == 1 && strings.Contains(midjResponse.Description, "排队中") {
		midjourneyTask.Status = "NOT_START"
		if properties, ok := midjResponse.Properties.(map[string]interface{}); ok {
			if numberOfQueues, ok := properties["numberOfQueues"].(float64); ok {
				midjourneyTask.State = fmt.Sprintf("排队中，前面还有%d个任务", int(numberOfQueues))
			} else {
				midjourneyTask.State = "排队中"
			}
		} else {
			midjourneyTask.State = "排队中"
		}
	}

	if midjResponse.Code != 1 && midjResponse.Code != 21 && midjResponse.Code != 22 {
		midjourneyTask.FailReason = midjResponse.Description
		midjourneyTask.Status = "FAILURE"
	}

	if midjResponse.Code == 23 || midjResponse.Code == 3 {
		logger.SysError(fmt.Sprintf("channel [%d] queue full or no account. code:[%d], description:[%s]", meta.ChannelId, midjResponse.Code, midjResponse.Description))
		consumeQuota = false
		mjResp = &openai.MidjourneyResponse{
			Code:        midjResponse.Code,
			Description: "queue_full_or_no_account",
		}
		return
	}

	err = midjourneyTask.Insert()
	if err != nil {
		mjResp = &openai.MidjourneyResponse{
			Code:        4,
			Description: "insert_midjourney_task_failed",
		}
		return
	}

	if midjResponse.Code == 22 {
		newBody := strings.Replace(string(responseBody), `"code":22`, `"code":1`, -1)
		responseBody = []byte(newBody)
	}

	defer func(ctx context.Context, task *model.Midjourney) {
		if consumeQuota {
			err := model.PostConsumeTokenQuota(meta.TokenId, quota)
			if err != nil {
				logger.SysError("error consuming token remain quota: " + err.Error())
			}
			err = model.CacheUpdateUserQuota(ctx, meta.UserId)
			if err != nil {
				logger.SysError("error update user quota cache: " + err.Error())
			}
			if quota != 0 {
				logContent := fmt.Sprintf("视频操作价格 %.2f，分组倍率 %.2f，充值转换率 %.2f，用户折扣率 %.2f，模式 %s，操作 %s，MJ任务ID %s", actionPrice, groupRatio, topupConvertRatio, userDiscount, videoMode, action, midjResponse.Result)
				model.UpdateUserUsedQuotaAndRequestCount(meta.UserId, quota)
				model.UpdateChannelUsedQuota(meta.ChannelId, quota)
				createdLog := model.RecordConsumeLogByDetailIfZeroQuota(ctx, "", meta.Ip, meta.RemoteIp, meta.UserId,
					meta.ChannelId, meta.PromptTokens, 0, lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(imageModel),
					meta.TokenName, meta.TokenKey, meta.TokenGroup, meta.ChannelName, int(quota), int(costQuota), 0, 0, 0, meta.IsStream, logContent, "")
				helper.SafeGoroutine(func() {
					model.RecordLogExtend(ctx, createdLog, meta.DetailPrompt, meta.DetailCompletion, meta.CompletionId, meta.UpstreamResponse, meta.FullResponse, meta.RequestURLPath)
					optimizer.RecordConsumeLog(createdLog)
				})
				// 渠道问题构图失败补偿
				if task.Status == "FAILURE" {
					if task.Code != 24 || model.ShouldRefundMJSensitiveWordsError(task.UserId) {
						task.Progress = "100%"
						err := model.IncreaseUserQuotaAndRedis(task.UserId, quota)
						if err != nil {
							logger.SysError("fail to increase user quota" + err.Error())
						}
						model.UpdateUserUsedQuotaAndRequestCount(meta.UserId, -quota)
						model.UpdateChannelUsedQuota(meta.ChannelId, -quota)
						logContent = fmt.Sprintf("%s 视频构图失败，补偿 %s", task.MjId, common.LogQuota(quota))
						model.RecordRefundLogByDetailIfZeroQuota(nil, task.RequestId, "", "", task.UserId, task.ChannelId, 0, 0, fmt.Sprintf("%s", imageModel), "", "", "", int(-quota), 0, 0, 0, false, logContent)
						err = task.Update()
						if err != nil {
							logger.SysError("fail to update task status" + err.Error())
						}
					}
				}
			}
		}
	}(c.Request.Context(), midjourneyTask)

	resp.Body = io.NopCloser(bytes.NewBuffer(responseBody))

	for k, v := range resp.Header {
		if strings.ToLower(k) == "x-served-by" {
			continue
		}
		c.Writer.Header().Set(k, v[0])
	}
	c.Writer.Header().Set("Content-Type", "application/json")
	c.Writer.WriteHeader(resp.StatusCode)

	_, err = io.Copy(c.Writer, resp.Body)
	if err != nil {
		mjResp = &openai.MidjourneyResponse{
			Code:        4,
			Description: "copy_response_body_failed",
		}
		return
	}
	err = resp.Body.Close()
	if err != nil {
		mjResp = &openai.MidjourneyResponse{
			Code:        4,
			Description: "close_response_body_failed",
		}
		return
	}

	// 记录最终响应（考虑用户级别配置）
	if model.ShouldLogFullResponse(meta.UserId) {
		meta.FullResponse = string(responseBody)
	}
	return nil
}

// fetchVideoDataFromUpstream 从上游API获取视频数据
func fetchVideoDataFromUpstream(c *gin.Context, originTask *model.Midjourney) *struct {
	VideoUrl  string   `json:"video_url"`
	VideoUrls []string `json:"video_urls"`
} {
	// 获取渠道信息
	channelInfo, err := model.GetChannelById(originTask.ChannelId, true)
	if err != nil {
		logger.SysError(fmt.Sprintf("Failed to get channel info for task %s: %v", originTask.MjId, err))
		return nil
	}

	// 构建上游API请求URL
	baseURL := channeltype.ChannelBaseURLs[channelInfo.Type]
	if channelInfo.GetBaseURL() != "" {
		baseURL = channelInfo.GetBaseURL()
	}

	// 构建fetch请求URL
	requestURL := fmt.Sprintf("%s/mj/task/%s/fetch", baseURL, originTask.MjId)

	req, err := http.NewRequest("GET", requestURL, nil)
	if err != nil {
		logger.SysError(fmt.Sprintf("Failed to create request for task %s: %v", originTask.MjId, err))
		return nil
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("mj-api-secret", channelInfo.Key)

	// 发起请求
	resp, err := client.HTTPClient.Do(req)
	if err != nil {
		logger.SysError(fmt.Sprintf("Failed to fetch video data for task %s: %v", originTask.MjId, err))
		return nil
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		logger.SysError(fmt.Sprintf("Failed to fetch video data for task %s, status code: %d", originTask.MjId, resp.StatusCode))
		return nil
	}

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.SysError(fmt.Sprintf("Failed to read response body for task %s: %v", originTask.MjId, err))
		return nil
	}

	// 解析响应数据，兼容驼峰和蛇形命名
	var upstreamResponse map[string]interface{}
	if err := json.Unmarshal(responseBody, &upstreamResponse); err != nil {
		logger.SysError(fmt.Sprintf("Failed to unmarshal response for task %s: %v", originTask.MjId, err))
		return nil
	}

	logger.SysLog(fmt.Sprintf("Fetched upstream video data for task %s: %s", originTask.MjId, string(responseBody)))

	// 创建返回结构
	result := &struct {
		VideoUrl  string   `json:"video_url"`
		VideoUrls []string `json:"video_urls"`
	}{}

	// 处理单个视频URL，支持驼峰和蛇形命名
	if videoUrl, ok := upstreamResponse["video_url"].(string); ok && videoUrl != "" {
		result.VideoUrl = videoUrl
	} else if videoUrl, ok := upstreamResponse["videoUrl"].(string); ok && videoUrl != "" {
		result.VideoUrl = videoUrl
	}

	// 处理多个视频URL，支持驼峰和蛇形命名
	if videoUrls, ok := upstreamResponse["video_urls"].([]interface{}); ok {
		for _, url := range videoUrls {
			if urlStr, ok := url.(string); ok && urlStr != "" {
				result.VideoUrls = append(result.VideoUrls, urlStr)
			}
		}
	} else if videoUrls, ok := upstreamResponse["videoUrls"].([]interface{}); ok {
		for _, url := range videoUrls {
			if urlStr, ok := url.(string); ok && urlStr != "" {
				result.VideoUrls = append(result.VideoUrls, urlStr)
			}
		}
	}

	// 如果没有获取到任何视频URL，返回nil
	if result.VideoUrl == "" && len(result.VideoUrls) == 0 {
		return nil
	}

	return result
}

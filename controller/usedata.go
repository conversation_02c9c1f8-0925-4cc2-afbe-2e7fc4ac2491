package controller

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/middleware"
	"github.com/songquanpeng/one-api/model"
)

type UsageData struct {
	ModelName string  `json:"modelName"`
	UserId    string  `json:"userId"`
	Username  string  `json:"username"`
	Ip        string  `json:"ip"`
	RemoteIp  string  `json:"remote_ip"`
	SumQuota  int     `json:"sumQuota"`
	SumUsd    float64 `json:"sumUsd"`
	CostQuota int     `json:"costQuota"`
	CostUsd   float64 `json:"costUsd"`
	Date      string  `json:"date"`
}

func GetAllQuotaDates(c *gin.Context) {
	startTimestamp, _ := strconv.ParseInt(c.Query("start_timestamp"), 10, 64)
	endTimestamp, _ := strconv.ParseInt(c.Query("end_timestamp"), 10, 64)
	username := c.Query("username")
	timezone := c.GetString("timezone") // 从上下文获取时区

	queryParams := model.QueryParams{
		StartTime:   startTimestamp,
		EndTime:     endTimestamp,
		Username:    username,
		Granularity: "day",    // 默认按天统计
		Timezone:    timezone, // 添加时区参数
	}

	data, err := model.GetUsageStatsByDimension(queryParams, "user")
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    data,
	})
	return
}

func GetUserQuotaDates(c *gin.Context) {
	userId := c.GetInt("id")
	startTimestamp, _ := strconv.ParseInt(c.Query("start_timestamp"), 10, 64)
	endTimestamp, _ := strconv.ParseInt(c.Query("end_timestamp"), 10, 64)
	timezone := c.GetString("timezone") // 从上下文获取时区
	dimension := c.Query("dimension")
	granularity := c.Query("granularity")

	// 判断时间跨度是否超过 1 个月
	if endTimestamp-startTimestamp > 2592000 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "时间跨度不能超过 1 个月",
		})
		return
	}

	queryParams := model.QueryParams{
		StartTime:   startTimestamp,
		EndTime:     endTimestamp,
		UserId:      strconv.Itoa(userId),
		Granularity: granularity, // 默认按天统计
		Timezone:    timezone,    // 添加时区参数
	}

	data, err := model.GetUsageStatsByDimension(queryParams, dimension)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    data,
	})
	return
}

func GetModelUsageData(c *gin.Context) {
	// 检查是否有查看所有数据的权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")

	// 检查是否有日志查看权限
	hasLogPermission := role == model.RoleRootUser || // 超级管理员
		(role == model.RoleAdminUser && (adminAccessFlags&middleware.PermissionLog) == middleware.PermissionLog)

	if !hasLogPermission {
		// 没有日志查看权限，降级到查看用户自己的数据
		GetUserQuotaDates(c)
		return
	}

	dimension := c.Query("dimension")
	granularity := c.Query("granularity")
	startTimestamp, _ := strconv.ParseInt(c.Query("start_timestamp"), 10, 64)
	endTimestamp, _ := strconv.ParseInt(c.Query("end_timestamp"), 10, 64)
	username := c.Query("username")
	modelName := c.Query("model_name")
	userId := c.Query("user_id")
	timezone := c.GetString("timezone") // 从上下文获取时区
	channel := c.Query("channel")       // 添加 channel 参数

	// 构建查询参数
	queryParams := model.QueryParams{
		StartTime:   startTimestamp,
		EndTime:     endTimestamp,
		Username:    username,
		ModelName:   modelName,
		UserId:      userId,
		Granularity: granularity,
		Timezone:    timezone, // 添加时区参数
		Channel:     channel,  // 添加 channel 参数
	}

	var data []model.UsageData
	var err error

	switch dimension {
	case "user", "ip":
		// 按用户或IP维度统计
		data, err = model.GetUsageStatsByDimension(queryParams, dimension)
	case "model":
		// 按模型维度统计
		data, err = model.GetUsageStatsByModel(queryParams)
	default:
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "不支持的统计维度",
		})
		return
	}

	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 如果没有数据，返回空数组而不是 null
	if data == nil {
		data = make([]model.UsageData, 0)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    data,
	})
}

func formatTime(timestamp int64, granularity string) string {
	t := time.Unix(timestamp, 0)
	switch granularity {
	case "hour":
		return t.Format("2006-01-02 15:00")
	case "day":
		return t.Format("2006-01-02")
	case "week":
		year, week := t.ISOWeek()
		return fmt.Sprintf("%d-W%02d", year, week)
	case "month":
		return t.Format("2006-01")
	default:
		return t.Format("2006-01-02 15:00")
	}
}

func GetModelUsageStats(c *gin.Context) {
	// 获取查询参数
	startTimestamp, _ := strconv.ParseInt(c.Query("start_timestamp"), 10, 64)
	endTimestamp, _ := strconv.ParseInt(c.Query("end_timestamp"), 10, 64)
	username := c.Query("username")
	modelName := c.Query("model_name")
	userId := c.Query("user_id")
	granularity := c.Query("granularity")
	timezone := c.GetString("timezone")
	channel := c.Query("channel") // 添加 channel 参数

	// 检查权限：普通用户或没有日志权限的管理员只能查询自己的数据
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")
	hasLogPermission := role == model.RoleRootUser || // 超级管理员
		(role == model.RoleAdminUser && (adminAccessFlags&middleware.PermissionLog) == middleware.PermissionLog)

	if !hasLogPermission {
		// 没有日志权限，只能查询自己的数据
		username = c.GetString("username")
		userId = strconv.Itoa(c.GetInt("id"))
		channel = "" // 清空渠道参数
	}

	// 构建查询参数
	queryParams := model.QueryParams{
		StartTime:   startTimestamp,
		EndTime:     endTimestamp,
		Username:    username,
		ModelName:   modelName,
		UserId:      userId,
		Granularity: granularity,
		Timezone:    timezone,
		Channel:     channel, // 添加 channel 参数
	}

	// 获取统计数据
	data, err := model.GetModelUsageStats(queryParams)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 如果没有数据,返回空数组而不是 null
	if data == nil {
		data = make([]model.ModelUsageData, 0)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    data,
	})
}

// DeleteQuotaDataByTime 根据时间删除统计图数据
func DeleteQuotaDataByTime(c *gin.Context) {
	// 确保只有管理员可以执行此操作
	if !model.IsAdmin(c.GetInt("id")) {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无权限执行此操作",
		})
		return
	}

	targetTimestamp, _ := strconv.ParseInt(c.Query("target_timestamp"), 10, 64)
	beforeNowTimestamp, _ := strconv.ParseInt(c.Query("before_now_timestamp"), 10, 64)

	if targetTimestamp == 0 && beforeNowTimestamp == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "target_timestamp 或 before_now_timestamp 参数至少需要一个",
		})
		return
	}

	if beforeNowTimestamp != 0 {
		// 计算目标时间戳 = 当前时间 - beforeNowTimestamp
		targetTimestamp = time.Now().Unix() - beforeNowTimestamp
	}

	count, err := model.DeleteQuotaDataByTimestamp(targetTimestamp)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "删除失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("成功删除 %d 条统计图数据", count),
		"data":    count,
	})
}

// TruncateQuotaData 清空统计图数据表
func TruncateQuotaData(c *gin.Context) {
	// 确保只有管理员可以执行此操作
	if !model.IsAdmin(c.GetInt("id")) {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无权限执行此操作",
		})
		return
	}

	err := model.TruncateQuotaDataTable()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "清空统计图数据表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "已成功清空所有统计图数据",
	})
}

package controller

import (
	"bytes"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/songquanpeng/one-api/common/client"

	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/relay/adaptor/openai"
	"github.com/songquanpeng/one-api/relay/billing"
	billingratio "github.com/songquanpeng/one-api/relay/billing/ratio"
	"github.com/songquanpeng/one-api/relay/channeltype"
	"github.com/songquanpeng/one-api/relay/meta"
	relaymodel "github.com/songquanpeng/one-api/relay/model"
	"github.com/songquanpeng/one-api/relay/util"

	"github.com/gin-gonic/gin"
)

func relaySearchSerperHelper(c *gin.Context, relayMode int) (*relaymodel.ErrorWithStatusCode, bool, meta.Meta) {
	ctx := c.Request.Context()
	meta := meta.GetByContext(c)

	searchModel := "search-serper"

	tokenId := c.GetInt("token_id")
	channelType := c.GetInt("channel")
	channelId := c.GetInt("channel_id")
	userId := c.GetInt("id")
	group := c.GetString("group")
	_startTime := helper.GetTimestamp()

	// 获取模型比率和组比率
	modelRatio := billingratio.GetModelRatio(searchModel, meta.ChannelType)
	groupRatio := billingratio.GetGroupRatio(group)
	ratio := modelRatio * groupRatio

	// 获取用户个性化费率
	userModelRatio, ok, _, err := model.CacheGetUserModelRatio(userId, searchModel)
	if ok {
		modelRatio = userModelRatio
		ratio = modelRatio * groupRatio
	}

	// 获取充值利差率
	topupConvertRatio, userDiscount := billing.GetTopupConvertRatioByMeta(meta)

	// 判断是否按次数计费
	var modelFixedPrice float64
	if meta.BillingType == common.BillingTypeByCount {
		modelFixedPrice, err = billingratio.GetModelFixedPrice(searchModel)
		if err != nil {
			return openai.ErrorWrapper(err, "model_fixed_price_not_config", http.StatusForbidden), false, *meta
		}
		// 获取用户个性化固定价格
		userModelFixedPrice, ok, _, _ := model.CacheGetUserModelFixedPrice(userId, searchModel)
		if ok {
			modelFixedPrice = userModelFixedPrice
		}
		ratio = groupRatio // 按次数计费时，只使用组比率
	}

	// 预扣费逻辑
	var preConsumedQuota int64
	if meta.BillingType == common.BillingTypeByCount {
		preConsumedQuota = int64(modelFixedPrice * 500000 * ratio * topupConvertRatio * userDiscount)
	} else {
		preConsumedQuota = int64(float64(config.PreConsumedQuota) * ratio * topupConvertRatio * userDiscount)
	}

	if preConsumedQuota > 0 {
		err, _ := model.PreConsumeTokenQuota(ctx, tokenId, preConsumedQuota)
		if err != nil {
			return openai.ErrorWrapper(err, "pre_consume_token_quota_failed", http.StatusForbidden), false, *meta
		}
	}

	// map model name
	searchModel, _ = util.GetMappedModelName(searchModel, meta.ModelMapping, meta.ModelMappingArr)
	baseURL := channeltype.ChannelBaseURLs[channelType]

	if c.GetString("base_url") != "" {
		baseURL = c.GetString("base_url")
	}

	fullRequestURL := fmt.Sprintf("%s", baseURL)
	var serperSearchRequest openai.GeneralSerperSearchRequest
	_ = common.UnmarshalBodyReusable(c, &serperSearchRequest)
	requestBody := c.Request.Body

	req, err := http.NewRequest(c.Request.Method, fullRequestURL, requestBody)
	if err != nil {
		return openai.ErrorWrapper(err, "new_request_failed", http.StatusInternalServerError), false, *meta
	}
	// 去掉Bearer
	req.Header.Set("X-API-KEY", strings.Replace(c.Request.Header.Get("Authorization"), "Bearer ", "", 1))
	req.Header.Set("Content-Type", c.Request.Header.Get("Content-Type"))
	req.Header.Set("Accept", c.Request.Header.Get("Accept"))

	resp, err := client.HTTPClient.Do(req)
	if err != nil {
		return openai.ErrorWrapper(err, "do_request_failed", http.StatusInternalServerError), false, *meta
	}

	err = req.Body.Close()
	if err != nil {
		return openai.ErrorWrapper(err, "close_request_body_failed", http.StatusInternalServerError), false, *meta
	}
	err = c.Request.Body.Close()
	if err != nil {
		return openai.ErrorWrapper(err, "close_request_body_failed", http.StatusInternalServerError), false, *meta
	}
	var responseBodyBytes []byte
	// 如果搜索引擎返回400表示余额不足,需要禁用
	if resp.StatusCode != 200 {
		readAll, err := io.ReadAll(resp.Body)
		if err != nil {
			fmt.Sprintf("read response body failed: %s", err.Error())
		}
		respBody := string(readAll)
		logger.SysError("serper search error: " + respBody + " 请求参数为: " + serperSearchRequest.Q)
		if resp.StatusCode == 400 || resp.StatusCode == 403 {
			return openai.ErrorWrapper(errors.New(respBody), "google_search_not_success", resp.StatusCode), false, *meta
		}
		responseBodyBytes = readAll
	}

	// 后续计费逻辑
	consumedTokens := int64(1) // 对于搜索请求，我们可以设置一个固定的token数
	quota := int64(float64(consumedTokens) * ratio * topupConvertRatio * userDiscount)
	if meta.BillingType == common.BillingTypeByCount {
		quota = int64(modelFixedPrice * 500000 * ratio * topupConvertRatio * userDiscount)
	}

	quotaDelta := quota - preConsumedQuota
	err = model.PostConsumeTokenQuota(tokenId, quotaDelta)
	if err != nil {
		logger.SysError("error consuming token remain quota: " + err.Error())
	}

	err = model.CacheUpdateUserQuota(ctx, userId)
	if err != nil {
		logger.SysError("error update user quota cache: " + err.Error())
	}

	requestDuration := helper.GetTimestamp() - _startTime
	logContent := fmt.Sprintf("模型倍率 %.2f，分组倍率 %.2f，用时 %d秒", modelRatio, groupRatio, requestDuration)
	model.RecordConsumeLog(ctx, userId, channelId, 0, 0, searchModel, c.GetString("token_name"), c.GetString("token_key"), c.GetString("channel_name"), int(quota), requestDuration, false, logContent)
	model.UpdateUserUsedQuotaAndRequestCount(userId, quota)
	model.UpdateChannelUsedQuota(channelId, quota)

	if responseBodyBytes == nil {
		responseBodyBytes, err = io.ReadAll(resp.Body)
		if err != nil {
			return openai.ErrorWrapper(err, "read_response_body_failed", http.StatusInternalServerError), false, *meta
		}
	}
	err = resp.Body.Close()
	if err != nil {
		return openai.ErrorWrapper(err, "close_response_body_failed", http.StatusInternalServerError), false, *meta
	}

	resp.Body = io.NopCloser(bytes.NewBuffer(responseBodyBytes))

	for k, v := range resp.Header {
		// 排除这个请求头X-Served-By 避免暴露上游域名
		if strings.ToLower(k) == "x-served-by" {
			continue
		}
		c.Writer.Header().Set(k, v[0])
	}
	c.Writer.WriteHeader(resp.StatusCode)

	_, err = io.Copy(c.Writer, resp.Body)
	if err != nil {
		return openai.ErrorWrapper(err, "copy_response_body_failed", http.StatusInternalServerError), false, *meta
	}
	err = resp.Body.Close()
	if err != nil {
		return openai.ErrorWrapper(err, "close_response_body_failed", http.StatusInternalServerError), false, *meta
	}
	return nil, false, *meta
}

package controller

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/samber/lo"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/client"
	commonopenai "github.com/songquanpeng/one-api/common/openai"
	"github.com/songquanpeng/one-api/relay/channeltype"

	"github.com/gin-gonic/gin"

	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/common/message"
	"github.com/songquanpeng/one-api/middleware"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/monitor"
	"github.com/songquanpeng/one-api/relay"
	"github.com/songquanpeng/one-api/relay/adaptor/anthropic"
	"github.com/songquanpeng/one-api/relay/adaptor/openai"
	"github.com/songquanpeng/one-api/relay/controller"
	relaycontroller "github.com/songquanpeng/one-api/relay/controller"
	"github.com/songquanpeng/one-api/relay/meta"
	relaymodel "github.com/songquanpeng/one-api/relay/model"
	"github.com/songquanpeng/one-api/relay/relaymode"
	"github.com/songquanpeng/one-api/relay/util"
)

func buildTestRequest(model string) *relaymodel.GeneralOpenAIRequest {
	testRequest := &relaymodel.GeneralOpenAIRequest{
		Model:  model,
		Stream: false,
	}

	if testRequest.Model == "" {
		testRequest.Model = "gpt-3.5-turbo"
	}

	// 先判断是否为 Embedding 模型
	if strings.Contains(strings.ToLower(model), "embedding") || // 其他 embedding 模型
		strings.HasPrefix(model, "m3e") || // m3e 系列模型
		strings.Contains(model, "bge-") {
		// Embedding 请求
		testRequest.Input = []string{"hello world"}
		return testRequest
	}

	// 根据模型类型设置不同的参数
	if strings.HasPrefix(model, "o1") || strings.HasPrefix(model, "o3") ||
		strings.HasPrefix(model, "o4") || strings.HasPrefix(model, "o5") ||
		strings.HasPrefix(model, "o6") || strings.HasPrefix(model, "o7") {
		testRequest.SetMaxCompletionTokens(10)
		// 移除max_tokens
		testRequest.MaxTokens = 0
	} else if strings.Contains(model, "thinking") {
		if !strings.Contains(model, "claude") {
			testRequest.MaxTokens = 50
		}
	} else {
		testRequest.MaxTokens = 10
	}

	// 使用JSON格式的内容，更符合实际使用场景
	content := "say 1"
	testMessage := relaymodel.Message{
		Role:    "user",
		Content: content,
	}
	testRequest.Messages = append(testRequest.Messages, testMessage)
	return testRequest
}

func parseTestResponse(resp string) (*openai.TextResponse, string, error) {
	var response openai.TextResponse
	err := json.Unmarshal([]byte(resp), &response)
	if err != nil {
		return nil, "", err
	}
	if len(response.Choices) == 0 {
		return nil, "", errors.New("response has no choices")
	}
	stringContent, ok := response.Choices[0].Content.(string)
	if !ok {
		return nil, "", errors.New("response content is not string")
	}
	return &response, stringContent, nil
}

func testChannel(channel *model.Channel, request *relaymodel.GeneralOpenAIRequest, forceUseModel bool) (err error, openaiErr *relaymodel.Error, statusCode int, responseBody string) {
	originModel := request.Model
	switch channel.Type {
	case channeltype.PaLM:
		fallthrough
	case channeltype.Gemini:
		fallthrough
	case channeltype.Anthropic:
		request.Model = "claude-3-opus-20240229"
	case channeltype.Baidu:
		fallthrough
	case channeltype.Zhipu:
		fallthrough
	case channeltype.Ali:
		fallthrough
	case channeltype.AI360:
		fallthrough
	case channeltype.Xunfei:
		return errors.New("该渠道类型当前版本不支持测试，请手动测试"), nil, 0, ""
	case channeltype.Azure:
		request.Model = "gpt-35-turbo"
		defer func() {
			if err != nil {
				err = errors.New("请确保已在 Azure 上创建了 gpt-35-turbo 模型，并且 apiVersion 已正确填写！")
			}
		}()
	default:
		request.Model = "gpt-3.5-turbo"
	}
	var jsonData []byte
	// 如果配置了测速JSON则取配置值
	customTestRequestBody := channel.GetTestRequestBody()
	if customTestRequestBody != "" {
		jsonData = []byte(customTestRequestBody)
	} else {
		jsonData, err = json.Marshal(request)
	}
	if err != nil {
		return err, nil, 0, ""
	}
	// 将RequestBody解析成对象
	customTestRequestBodyObj := make(map[string]interface{})
	err = json.Unmarshal(jsonData, &customTestRequestBodyObj)
	if err != nil {
		return err, nil, 0, ""
	}
	// 如果是强制使用模型名字则需要改变请求体中的模型名字(映射后)
	if forceUseModel {
		customTestRequestBodyObj["model"] = originModel
		// json化
		jsonData, err = json.Marshal(customTestRequestBodyObj)
		if err != nil {
			return err, nil, 0, ""
		}
	}
	isEmbedding := false
	if modelName, ok := customTestRequestBodyObj["model"].(string); ok && strings.Contains(modelName, "embedding") {
		isEmbedding = true
	}

	requestURL := channeltype.ChannelBaseURLs[channel.Type]
	if channel.Type == channeltype.Azure {
		requestURL = util.GetFullRequestURLByRequestBody(channel.GetBaseURL(), fmt.Sprintf("/openai/deployments/%s/chat/completions?api-version=2023-03-15-preview", request.Model), channel.Type, customTestRequestBodyObj)
	} else if channel.Type == channeltype.GoogleSerper {
		requestURL = "https://google.serper.dev/search"
	} else if channel.Type == channeltype.OpenaiLobe {
		if baseURL := channel.GetBaseURL(); len(baseURL) > 0 {
			requestURL = baseURL
		}
		requestURL = util.GetFullRequestURLByRequestBody(requestURL, "/api/openai/chat", channel.Type, customTestRequestBodyObj)
	} else if channel.Type == channeltype.Anthropic {
		requestURL = util.GetFullRequestURLByRequestBody(requestURL, "/v1/messages", channel.Type, customTestRequestBodyObj)
	} else {
		if baseURL := channel.GetBaseURL(); len(baseURL) > 0 {
			requestURL = baseURL
		}
		requestURL = util.GetFullRequestURLByRequestBody(requestURL, "/v1/chat/completions", channel.Type, customTestRequestBodyObj)
	}

	req, err := http.NewRequest("POST", requestURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return err, nil, 0, ""
	}
	if channel.Type == channeltype.Azure {
		req.Header.Set("api-key", channel.Key)
	} else if channel.Type == channeltype.GoogleSerper {
		req.Header.Set("X-API-KEY", channel.Key)
	} else if channel.Type == channeltype.NewBingXMY {
		if channel.Key != "" {
			req.Header.Set("Authorization", channel.Key)
		}
	} else if channel.Type == channeltype.Anthropic {
		if channel.Key != "" {
			req.Header.Set("x-api-key", channel.Key)
		}
		req.Header.Set("anthropic-version", "2023-06-01")
	} else {
		if channel.Key != "" {
			req.Header.Set("Authorization", "Bearer "+channel.Key)
		}
	}
	if channel.OpenAIOrganization != nil {
		req.Header.Set("OpenAI-Organization", *channel.OpenAIOrganization)
	}
	req.Header.Set("Content-Type", "application/json")
	// 替换配置的请求头
	channelExtend, _ := model.CacheGetChannelExByChannelId(channel.Id)
	if channelExtend != nil && channelExtend.GetExtraHeaders() != nil {
		for k, v := range channelExtend.GetExtraHeaders() {
			req.Header.Set(k, v)
		}
	}
	resp := util.SafeRequest(client.HTTPClient, req)
	defer resp.SafeClose()
	if resp.Err != nil {
		return err, nil, resp.StatusCode(), ""
	}
	var response openai.SlimTextResponse
	var claudeResponse anthropic.Response
	// 解析resp.Body
	scanner := bufio.NewScanner(resp.GetBody())
	jsonRes := ""
	streamConcatContent := ""
	for scanner.Scan() {
		var streamResponse openai.ChatCompletionsStreamResponse
		var anthropicStreamResponse anthropic.StreamResponse
		line := scanner.Text()
		// 切掉前面的data:
		line = strings.TrimPrefix(line, "data: ")
		// [DONE]跳过
		if line == "[DONE]" {
			break
		}
		jsonRes += line
		// 解析 JSON 对象
		if channel.Type == channeltype.Anthropic {
			err = json.Unmarshal([]byte(line), &anthropicStreamResponse)
		} else {
			err = json.Unmarshal([]byte(line), &streamResponse)
		}
		if err == nil {
			if channel.Type == channeltype.Anthropic {
				switch anthropicStreamResponse.Type {
				case "message_start":
					// 不处理
				case "content_block_start":
					if anthropicStreamResponse.ContentBlock != nil {
						streamConcatContent += anthropicStreamResponse.ContentBlock.Text
					}
				case "content_block_delta":
					if anthropicStreamResponse.Delta != nil {
						streamConcatContent += anthropicStreamResponse.Delta.Text
					}
				case "message_delta":
					if anthropicStreamResponse.Delta != nil && anthropicStreamResponse.Delta.StopReason != nil {
						//streamConcatContent += *anthropicStreamResponse.Delta.StopReason
					}
				}
			} else {
				// 判断是否有 Choices 字段 & Choices[0].Delta 字段 & Choices[0].Delta.Content 字段
				if streamResponse.Choices != nil && len(streamResponse.Choices) > 0 {
					streamConcatContent += streamResponse.Choices[0].Delta.StringContent()
				}
			}
		}
	}
	if channel.Type == channeltype.OpenaiLobe {
		streamConcatContent = jsonRes
	}
	if streamConcatContent == "1" {
		return nil, nil, resp.StatusCode(), streamConcatContent
	}
	if resp.StatusCode() == http.StatusOK && (channel.GetNonStrictTestMode() || isEmbedding) {
		// 非严格模式code200直接返回成功
		return nil, nil, resp.StatusCode(), streamConcatContent
	}
	if channel.Type == channeltype.GoogleSerper {
		if find := strings.Contains(jsonRes, "Not enough credits") || strings.Contains(jsonRes, "Unauthorized"); find {
			return errors.New(jsonRes), response.Error, resp.StatusCode(), jsonRes
		} else {
			return nil, nil, resp.StatusCode(), jsonRes
		}
	}
	if channel.Type == channeltype.Anthropic {
		err = json.Unmarshal([]byte(jsonRes), &claudeResponse)
	} else {
		err = json.Unmarshal([]byte(jsonRes), &response)
	}
	if err != nil {
		return err, nil, resp.StatusCode(), jsonRes
	}
	if channel.Type == channeltype.Anthropic {
		if claudeResponse.Usage.OutputTokens == 1 {
			return nil, nil, resp.StatusCode(), streamConcatContent
		} else {
			if claudeResponse.Error == nil {
				return errors.New(fmt.Sprintf("response.Error is nil and type, code, message is all nil")), response.Error, resp.StatusCode(), jsonRes
			} else {
				return errors.New(fmt.Sprintf("type %s, code %v, message %s", claudeResponse.Error.Type, claudeResponse.Error.Type, claudeResponse.Error.Message)), response.Error, resp.StatusCode(), jsonRes
			}
		}
	} else if response.Usage.CompletionTokens == 0 && (response.Choices == nil || len(response.Choices) == 0 || response.Choices[0].Content != "1") {
		if response.Error == nil {
			return errors.New(fmt.Sprintf("response.Error is nil and type, code, message is all nil")), response.Error, resp.StatusCode(), jsonRes
		} else {
			return errors.New(fmt.Sprintf("type %s, code %v, message %s", response.Error, resp.StatusCode(), jsonRes)), response.Error, resp.StatusCode(), jsonRes
		}
	}
	return nil, nil, resp.StatusCode(), jsonRes
}

func testChannelNew(ctx context.Context, channel *model.Channel, request *relaymodel.GeneralOpenAIRequest,
	forceUseModel bool) (err error, openaiErr *relaymodel.Error, statusCode int, responseBody string) {
	startTime := time.Now()
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	modelName := request.Model
	modelMap := channel.GetModelMapping()
	if modelName == "" || !strings.Contains(channel.Models, modelName) {
		modelNames := strings.Split(channel.Models, ",")
		if len(modelNames) > 0 {
			modelName = modelNames[0]
		}
	}
	if modelMap != nil && modelMap[modelName] != "" {
		modelName = modelMap[modelName]
	}
	request.Model = modelName
	testModel := request.Model
	requestPath := "/v1/chat/completions"

	// 先判断是否为 Embedding 模型
	if strings.Contains(strings.ToLower(testModel), "embedding") ||
		strings.HasPrefix(testModel, "m3e") || // m3e 系列模型
		strings.Contains(testModel, "bge-") || // bge 系列模型
		strings.Contains(testModel, "embed") { // 其他 embedding 模型
		requestPath = "/v1/embeddings" // 修改请求路径
	}

	// 判断是否为图片生成模型
	if strings.Contains(testModel, "dall-e") {
		requestPath = "/v1/images/generations" // 修改请求路径
	}

	// 判断是否为语音识别模型
	if strings.Contains(testModel, "whisper") {
		requestPath = "/v1/audio/transcriptions" // 修改请求路径
	}

	// 判断是否为语音合成模型
	if strings.Contains(testModel, "text-to-speech") {
		requestPath = "/v1/audio/translations" // 修改请求路径
	}

	// 判断是否为mj相关模型
	if strings.Contains(testModel, "mj") || strings.Contains(testModel, "midjourney") {
		requestPath = "/mj/submit/imagine" // 修改请求路径
	}

	// 判断是否为谷歌搜索模型
	if strings.Contains(testModel, "search-serper") {
		requestPath = "/search/serper" // 修改请求路径
	}

	c.Request = &http.Request{
		Method: "POST",
		URL:    &url.URL{Path: requestPath}, // 使用动态路径
		Body:   nil,
		Header: make(http.Header),
	}

	c.Request.Header.Set("Authorization", "Bearer "+channel.Key)
	c.Request.Header.Set("Content-Type", "application/json")
	c.Set(ctxkey.Channel, channel.Type)
	c.Set(ctxkey.BaseURL, channel.GetBaseURL())
	cfg, _ := channel.LoadConfig()
	c.Set(ctxkey.Config, cfg)
	middleware.SetupContextForSelectedChannel(c, channel, "")
	meta := meta.GetByContext(c)
	apiType := channeltype.ToAPIType(channel.Type)
	adaptor := relay.GetAdaptor(apiType)
	if adaptor == nil {
		return fmt.Errorf("invalid api type: %d, adaptor is nil", apiType), nil, 0, ""
	}
	adaptor.Init(meta)
	meta.OriginModelName, meta.ActualModelName = modelName, modelName
	request.Model = modelName
	convertedRequest, err := adaptor.ConvertRequest(c, relaymode.ChatCompletions, request)
	if err != nil {
		return err, nil, 0, ""
	}
	jsonData, err := json.Marshal(convertedRequest)
	if err != nil {
		return err, nil, 0, ""
	}
	// 如果配置了测速JSON则取配置值
	customTestRequestBody := channel.GetTestRequestBody()
	if customTestRequestBody != "" {
		jsonData = []byte(customTestRequestBody)
		// 获取jsonData中的model字段,如果不为空则testModel替换为jsonData中的model字段
		var customTestRequestBodyObj map[string]interface{}
		err = json.Unmarshal(jsonData, &customTestRequestBodyObj)
		if err == nil {
			if jsonDataModel, ok := customTestRequestBodyObj["model"].(string); ok && jsonDataModel != "" {
				testModel = jsonDataModel
			}
		} else {
			logger.Error(ctx, fmt.Sprintf("failed to unmarshal custom test request body: %v", err))
		}
	} else {
		// 判断如果是mj相关模型，则需要使用mj的请求体
		if strings.Contains(testModel, "mj") || strings.Contains(testModel, "midjourney") {
			// 加请求头"mj-api-secret"
			c.Request.Header.Set("mj-api-secret", channel.Key)
			// 这里要判断request.Messages[0].Content是否是string类型,如果是string类型则直接使用,如果不是则使用json.Marshal
			jsonData = []byte(fmt.Sprintf(`{"base64": "", "notifyHook": "", "prompt": "cat", "state": ""}`))
		} else if strings.Contains(testModel, "search-serper") {
			c.Request.Header.Set("X-API-KEY", channel.Key)
			jsonData = []byte(fmt.Sprintf(`{"q": "hello world"}`))
		} else {
			jsonData, err = json.Marshal(convertedRequest)
		}
	}
	if err != nil {
		return err, nil, 0, ""
	}
	// 将RequestBody解析成对象
	customTestRequestBodyObj := make(map[string]interface{})
	err = json.Unmarshal(jsonData, &customTestRequestBodyObj)
	if err != nil {
		return err, nil, 0, ""
	}
	// 如果是强制使用模型名字则需要改变请求体中的模型名字(映射后)
	if forceUseModel {
		customTestRequestBodyObj["model"] = request.Model
		// json化
		jsonData, err = json.Marshal(customTestRequestBodyObj)
		if err != nil {
			return err, nil, 0, ""
		}
	}
	// 判断是不是流式
	isStream := false
	if customTestRequestBodyObj["stream"] != nil {
		isStream = customTestRequestBodyObj["stream"].(bool)
		meta.IsStream = isStream
		if isStream {
			return testChannel(channel, request, forceUseModel)
		}
	}
	defer func() {
		logContent := fmt.Sprintf("渠道 %s 测试成功，响应：%s", channel.Name, responseBody)
		if err != nil || openaiErr != nil {
			errorMessage := ""
			if err != nil {
				errorMessage = err.Error()
			} else {
				errorMessage = openaiErr.Message
			}
			logContent = fmt.Sprintf("渠道 %s 测试失败，错误：%s", channel.Name, errorMessage)
		}
		helper.SafeGoroutine(func() {
			model.RecordTestLog(ctx, &model.Log{
				ChannelId:   channel.Id,
				ModelName:   modelName,
				Content:     logContent,
				ElapsedTime: helper.CalcElapsedTime(startTime),
			})
		})
	}()
	logger.SysLog(string(jsonData))
	// 此处追加渠道配置的预处理逻辑 start
	var detailPrompt = string(jsonData)
	meta.DetailPrompt = detailPrompt
	detailPrompt, bodyBytes, preHandleRequestBodyStringErr := relaycontroller.PreHandleRequestBodyString(c, meta, detailPrompt, jsonData)
	if preHandleRequestBodyStringErr != nil {
		return errors.New(fmt.Sprintf("%v", preHandleRequestBodyStringErr)), nil, 0, ""
	}
	// 读取 Body 内容
	requestBody := bytes.NewBuffer(bodyBytes)
	// 恢复原始的 Body
	c.Request.Body = io.NopCloser(bytes.NewBuffer(jsonData))

	// 修改这里：只在确实有内容时设置 Content-Length
	if len(bodyBytes) > 0 {
		c.Writer.Header().Set("Content-Length", fmt.Sprintf("%d", len(bodyBytes)))
	} else {
		c.Writer.Header().Del("Content-Length")
	}
	// 此处追加渠道配置的预处理逻辑 end
	resp, err := adaptor.DoRequest(c, meta, requestBody)
	if err != nil {
		return err, nil, 0, ""
	}
	// 如果是dall-e, 则直接返回成功
	if (modelName == "dall-e-2" || modelName == "dall-e-3") && (resp.StatusCode == http.StatusBadRequest || resp.StatusCode == http.StatusOK) {
		return nil, nil, 200, ""
	}
	if resp != nil && resp.StatusCode != http.StatusOK {
		err := controller.RelayErrorHandler(resp)
		return fmt.Errorf("status code %d: %s", resp.StatusCode, err.Error.Message), &err.Error, resp.StatusCode, ""
	}

	// 对特定类型的渠道进行模型格式验证
	var warnings []string
	if meta.ChannelType == channeltype.Custom || meta.ChannelType == channeltype.OpenAI || meta.ChannelType == channeltype.ShellAPI {
		// 读取并保存原始响应内容
		responseBody, err := io.ReadAll(resp.Body)
		if err != nil {
			return fmt.Errorf("failed to read response body: %v", err), nil, 0, ""
		}
		resp.Body = io.NopCloser(bytes.NewBuffer(responseBody))

		// 验证是否是需要检查的模型
		if expectedModel, exists := commonopenai.OpenAIModelMap[testModel]; exists {
			// 尝试解析是否为字符串包装的JSON
			var jsonString string
			err := json.Unmarshal(responseBody, &jsonString)
			if err == nil {
				// 如果成功解析为字符串，说明JSON被包装了，需要再次解析
				responseBody = []byte(jsonString)
			}

			// 解析为通用map
			var fullResponseMap map[string]interface{}
			if err := json.Unmarshal(responseBody, &fullResponseMap); err != nil {
				logger.SysLog(fmt.Sprintf("Failed to parse response for model %s: %s", testModel, string(responseBody)))
			} else {
				// 检查模型匹配
				model, _ := fullResponseMap["model"].(string)
				if expectedModel != model {
					warnings = append(warnings, fmt.Sprintf("模型不匹配：期望 %s，实际返回 %s", expectedModel, model))
				}

				// 获取 usage 部分
				if usage, ok := fullResponseMap["usage"].(map[string]interface{}); ok {
					if promptDetails, ok := usage["prompt_tokens_details"].(map[string]interface{}); !ok {
						warnings = append(warnings, "Usage 中缺少 prompt_tokens_details 信息")
					} else if len(promptDetails) < 2 { // 应该有 cached_tokens 和 audio_tokens 两个字段
						warnings = append(warnings, "prompt_tokens_details 数据不完整")
					}

					// 检查 completion_tokens_details
					if completionDetails, ok := usage["completion_tokens_details"].(map[string]interface{}); !ok {
						warnings = append(warnings, "Usage 中缺少 completion_tokens_details 信息")
					} else if len(completionDetails) < 4 { // 应该有 reasoning_tokens, audio_tokens, accepted_prediction_tokens, rejected_prediction_tokens 四个字段
						warnings = append(warnings, "completion_tokens_details 数据不完整")
					}
				}
			}
		}
	}

	usage, respErr := adaptor.DoResponse(c, resp, meta)
	if respErr != nil {
		return fmt.Errorf("%s", respErr.Error.Message), &respErr.Error, respErr.StatusCode, ""
	}
	if usage == nil {
		return errors.New("usage is nil"), nil, lo.If(respErr != nil, respErr.StatusCode).Else(0), ""
	}
	result := w.Result()
	// print result.Body
	respBody, err := io.ReadAll(result.Body)
	if err != nil {
		return err, nil, 0, ""
	}
	logger.SysLog(fmt.Sprintf("testing channel #%d, response: \n%s", channel.Id, string(respBody)))

	// 如果有警告信息，将其添加到响应中
	if len(warnings) > 0 {
		// 创建一个带有警告信息的响应体
		var responseWithWarning struct {
			Response string   `json:"response"`
			Warnings []string `json:"warnings"`
		}
		responseWithWarning.Response = string(respBody)
		responseWithWarning.Warnings = warnings

		// 序列化为JSON
		warningJSON, _ := json.Marshal(responseWithWarning)
		return nil, nil, result.StatusCode, string(warningJSON)
	}

	return nil, nil, result.StatusCode, string(respBody)
}

// TestChannel
func TestChannel(c *gin.Context) {
	ctx := c.Request.Context()

	// 检查渠道编辑权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")
	hasChannelWritePermission := role == 100 || // 超级管理员
		middleware.HasWritePermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelWrite)

	if !hasChannelWritePermission {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权限执行渠道测试操作",
		})
		return
	}

	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	channel, err := model.GetChannelById(id, true)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	statusCode := 200
	responseBody := ""
	model := c.Query("model")
	testRequest := buildTestRequest(model)
	forceUseModel := false
	if model != "" {
		forceUseModel = true
	}
	tik := time.Now()
	err, _, statusCode, responseBody = testChannelNew(ctx, channel, testRequest, forceUseModel)

	tok := time.Now()
	milliseconds := tok.Sub(tik).Milliseconds()
	if err != nil {
		milliseconds = 0
	}
	helper.SafeGoroutine(func() {
		channel.UpdateResponseTime(milliseconds)
	})
	consumedTime := float64(milliseconds) / 1000.0
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":      false,
			"message":      err.Error(),
			"time":         consumedTime,
			"model":        model,
			"statusCode":   statusCode,
			"responseBody": responseBody,
		})
		return
	}

	// 解析responseBody，检查是否包含警告信息
	var warnings []string
	var responseWithWarning struct {
		Response string   `json:"response"`
		Warnings []string `json:"warnings"`
	}
	if err := json.Unmarshal([]byte(responseBody), &responseWithWarning); err == nil && len(responseWithWarning.Warnings) > 0 {
		// 成功解析出警告信息
		warnings = responseWithWarning.Warnings
		responseBody = responseWithWarning.Response
	}

	c.JSON(http.StatusOK, gin.H{
		"success":      true,
		"message":      "",
		"time":         consumedTime,
		"model":        model,
		"statusCode":   statusCode,
		"responseBody": responseBody,
		"warnings":     warnings,
	})
	return
}

func TestChannelAdvance(c *gin.Context) {
	// 检查渠道编辑权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")
	hasChannelWritePermission := role == 100 || // 超级管理员
		middleware.HasWritePermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelWrite)

	if !hasChannelWritePermission {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权限执行渠道测试操作",
		})
		return
	}

	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	channel, err := model.GetChannelById(id, true)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	// todo 这里暂时写死,因为没有用到 和代码导致报错
	testRequest := buildTestRequest("gpt-3.5-turbo")
	tik := time.Now()
	err, _, _, _ = testChannel(channel, testRequest, false)
	err, _, _, _ = testChannel(channel, testRequest, false)
	err, _, _, _ = testChannel(channel, testRequest, false)
	err, _, _, _ = testChannel(channel, testRequest, false)
	tok := time.Now()
	milliseconds := tok.Sub(tik).Milliseconds()
	helper.SafeGoroutine(func() {
		channel.UpdateResponseTime(milliseconds)
	})
	consumedTime := float64(milliseconds) / 1000.0
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"time":    consumedTime,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"time":    consumedTime,
	})
	return
}

type TestRequest struct {
	Type              int    `json:"type"`
	ChannelId         int    `json:"id"`
	TestRequestURL    string `json:"testRequestURL"`
	TestRequestHeader string `json:"testRequestHeader"`
	TestRequestBody   string `json:"testRequestBody"`
}

func CustomTest(c *gin.Context) {
	// 检查渠道编辑权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")
	hasChannelWritePermission := role == 100 || // 超级管理员
		middleware.HasWritePermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelWrite)

	if !hasChannelWritePermission {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权限执行渠道测试操作",
		})
		return
	}

	var testRequest TestRequest
	if err := c.ShouldBindJSON(&testRequest); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 校验空值
	if testRequest.TestRequestURL == "" || testRequest.TestRequestHeader == "" || testRequest.TestRequestBody == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "testRequestURL,testRequestHeader,testRequestBody不能为空",
		})
		return
	}

	// 根据渠道类型选择测试函数
	var testFunc func(*gin.Context, TestRequest) (float64, string, error)
	switch testRequest.Type {
	case channeltype.Gemini:
		testFunc = testGeminiChannel
	default:
		testFunc = testOpenAIChannel
	}

	// 执行测试
	consumedTime, responseBody, err := testFunc(c, testRequest)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"time":    consumedTime,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"time":    consumedTime,
		"data":    responseBody,
	})
}

func testOpenAIChannel(c *gin.Context, testRequest TestRequest) (float64, string, error) {
	// 定义请求开始时间
	tik := time.Now()

	// 创建http请求
	req, err := http.NewRequest("POST", testRequest.TestRequestURL, strings.NewReader(testRequest.TestRequestBody))
	if err != nil {
		return 0, "", err
	}

	// 解析遍历TestRequestHeader的json
	var headers map[string]string
	if err := json.Unmarshal([]byte(testRequest.TestRequestHeader), &headers); err != nil {
		return 0, "", err
	}
	for k, v := range headers {
		req.Header.Set(k, v)
	}

	resp, err := client.HTTPClient.Do(req)
	if err != nil {
		return 0, "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, "", err
	}

	consumedTime := float64(time.Since(tik).Milliseconds()) / 1000.0

	if resp.StatusCode != http.StatusOK {
		return consumedTime, "", fmt.Errorf("status code: %d, body: %s", resp.StatusCode, string(body))
	}

	return consumedTime, string(body), nil
}

var testAllChannelsLock sync.Mutex
var testAllChannelsRunning bool = false

// enable & notify
func enableChannel(channelId int, channelName string) {
	model.UpdateChannelStatusById(channelId, common.ChannelStatusEnabled)
	subject := fmt.Sprintf("通道「%s」（#%d）已被启用", channelName, channelId)
	content := fmt.Sprintf("通道「%s」（#%d）已被启用", channelName, channelId)
	message.NotifyRootUser(subject, content)
}

// disable & notify
func disableChannel(ctx context.Context, channelId int, channelName string, reason string) {
	model.UpdateChannelStatusById(channelId, common.ChannelStatusAutoDisabled)
	subject := fmt.Sprintf("通道「%s」（#%d）已被禁用", channelName, channelId)
	content := fmt.Sprintf("通道「%s」（#%d）已被禁用，原因：%s", channelName, channelId, reason)
	model.UpdateChannelDisableReasonById(channelId, reason)
	// 打印日志文件,看下具体错误信息
	logger.SysError(content)
	model.RecordLog(ctx, -1, model.LogTypeSystemErr, content)
	message.NotifyRootUser(subject, content)
}

// 禁用渠道并通知并且记录请求入参和详细错误
func DisableChannelByRequestBodyAndErr(ctx *gin.Context, statusForUpdate int, channelId int, channelName string, reason string, requestBody string, requestErr *relaymodel.ErrorWithStatusCode) error {
	if config.RootUserEmail == "" {
		config.RootUserEmail = model.CacheGetRootUserEmail()
	}
	userId := ctx.GetInt("id")
	if userId == 0 {
		userId = -1
	}
	group := ctx.GetString("group")
	requestModel := ctx.GetString("request_model")
	if statusForUpdate == 0 {
		statusForUpdate = common.ChannelStatusAutoDisabled
	}
	err := model.UpdateChannelStatusByIdReturnErr(channelId, statusForUpdate)
	if err != nil {
		return err
	}
	model.RemoveChannelsByGroupAndModelFromGroup2model2channels(group, requestModel, channelId)
	subject := fmt.Sprintf("用户id[%d] 通道「%s」（#%d）已被禁用", userId, channelName, channelId)
	content := fmt.Sprintf("用户id[%d] 通道「%s」（#%d）已被禁用，原因：%s, status_code:%d, OpenAIError.Type:%s,OpenAIError.Code:%s", userId, channelName, channelId, reason, requestErr.StatusCode, requestErr.Error.Type, requestErr.Error.Code)
	mailContent := fmt.Sprintf("%s;导致报错的请求体为:%s", content, requestBody)
	model.UpdateChannelDisableReasonById(channelId, lo.If(reason != "", reason).Else(fmt.Sprintf("%v", requestErr)))
	// 打印日志文件,看下具体错误信息
	logger.SysError(mailContent)
	model.RecordSysLog(ctx, model.LogTypeSystemErr, userId, channelId, requestModel, "", channelName, content, requestBody)
	message.NotifyRootUser(subject, mailContent)
	return nil
}

// 禁用渠道下面的abilities并通知并且记录请求入参和详细错误
func DisableChannelAbilityByRequestBodyAndErr(ctx *gin.Context, channelId int, channelName string, reason string, requestBody string, requestErr *relaymodel.ErrorWithStatusCode) error {
	if config.RootUserEmail == "" {
		config.RootUserEmail = model.CacheGetRootUserEmail()
	}
	userId := ctx.GetInt("id")
	if userId == 0 {
		userId = -1
	}
	group := ctx.GetString("group")
	requestModel := ctx.GetString("request_model")
	err := model.UpdateChannelAbilityStatusByIdReturnErr(channelId, requestModel, common.ChannelStatusAutoDisabled)
	if err != nil {
		return err
	}
	model.RemoveChannelsByGroupAndModelFromGroup2model2channels(group, requestModel, channelId)
	subject := fmt.Sprintf("用户id[%d] 通道「%s」（#%d）分组「%s」模型「%s」已被禁用", userId, channelName, channelId, group, requestModel)
	content := fmt.Sprintf("用户id[%d] 通道「%s」（#%d）分组「%s」模型「%s」已被禁用，原因：%s, status_code:%d, OpenAIError.Type:%s,OpenAIError.Code:%s", userId, channelName, channelId, group, requestModel, reason, requestErr.StatusCode, requestErr.Error.Type, requestErr.Error.Code)
	mailContent := fmt.Sprintf("%s;导致报错的请求体为:%s", content, requestBody)
	model.UpdateChannelDisableReasonById(channelId, fmt.Sprintf("模型「%s」%v", requestModel, lo.If(reason != "", reason).Else(fmt.Sprintf("%v", requestErr))))
	// 打印日志文件,看下具体错误信息
	logger.SysError(mailContent)
	model.RecordSysLog(ctx, model.LogTypeSystemErr, userId, channelId, requestModel, "", channelName, content, requestBody)
	message.NotifyRootUser(subject, mailContent)
	return nil
}

func testChannels(ctx context.Context, notify bool, status string) error {
	if config.RootUserEmail == "" {
		config.RootUserEmail = model.CacheGetRootUserEmail()
	}
	testAllChannelsLock.Lock()
	if testAllChannelsRunning {
		testAllChannelsLock.Unlock()
		return errors.New("测试已在运行中")
	}
	testAllChannelsRunning = true
	testAllChannelsLock.Unlock()

	statusInt := 0
	if status != "all" {
		var err error
		statusInt, err = strconv.Atoi(status)
		if err != nil {
			return errors.New("无效的状态参数")
		}
	}

	channels, err := model.GetAllChannels(0, 0, "", false, 0, "", "", "", "", 0, statusInt, 0, "", "", "all", "")
	if err != nil {
		return err
	}
	var disableThreshold = int64(config.ChannelDisableThreshold * 1000)
	if disableThreshold == 0 {
		disableThreshold = 10000000 // a impossible value
	}
	go func() {
		for _, channel := range channels {
			isChannelEnabled := channel.Status == model.ChannelStatusEnabled
			tik := time.Now()
			testRequest := buildTestRequest("")
			err, openaiErr, _, _ := testChannelNew(ctx, channel, testRequest, false)
			tok := time.Now()
			milliseconds := tok.Sub(tik).Milliseconds()
			if isChannelEnabled && milliseconds > disableThreshold {
				err = fmt.Errorf("响应时间 %.2fs 超过阈值 %.2fs", float64(milliseconds)/1000.0, float64(disableThreshold)/1000.0)
				if config.AutomaticDisableChannelEnabled {
					monitor.DisableChannel(channel.Id, channel.Name, err.Error())
				} else {
					_ = message.Notify(message.ByAll, fmt.Sprintf("渠道 %s （%d）测试超时", channel.Name, channel.Id), "", err.Error())
				}
			}
			if isChannelEnabled && monitor.ShouldDisableChannel(openaiErr, -1) {
				monitor.DisableChannel(channel.Id, channel.Name, err.Error())
			}
			if !isChannelEnabled && monitor.ShouldEnableChannel(err, openaiErr) {
				monitor.EnableChannel(channel.Id, channel.Name)
			}
			channel.UpdateResponseTime(milliseconds)
			time.Sleep(config.RequestInterval)
		}
		testAllChannelsLock.Lock()
		testAllChannelsRunning = false
		testAllChannelsLock.Unlock()
		if notify {
			err := message.Notify(message.ByAll, "渠道测试完成", "", "渠道测试完成，如果没有收到禁用通知，说明所有渠道都正常")
			if err != nil {
				logger.SysError(fmt.Sprintf("failed to send email: %s", err.Error()))
			}
		}
	}()
	return nil
}

func TestChannels(c *gin.Context) {
	ctx := c.Request.Context()

	// 检查渠道编辑权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")
	hasChannelWritePermission := role == 100 || // 超级管理员
		middleware.HasWritePermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelWrite)

	if !hasChannelWritePermission {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权限执行渠道测试操作",
		})
		return
	}

	scope := c.Query("scope")
	status := c.Query("status")
	if status == "" {
		status = "all"
	}
	if scope == "" {
		scope = "all"
	}
	err := testChannels(ctx, true, status)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

func AutomaticallyTestChannels(frequency int) {
	ctx := context.Background()
	for {
		time.Sleep(time.Duration(frequency) * time.Minute)
		logger.SysLog("testing all channels")
		_ = testChannels(ctx, false, "all")
		logger.SysLog("channel test finished")
	}
}
func testGeminiChannel(c *gin.Context, testRequest TestRequest) (float64, string, error) {
	tik := time.Now()

	requestModel := c.Query("model")
	testRequestByBuild := buildTestRequest(requestModel)
	// Gemini 特定的测试逻辑
	// 这里需要根据 Gemini API 的特性来实现
	// 可以参考 testOpenAIChannel 的结构，但需要适配 Gemini 的请求和响应格式
	// ...
	// 从testRequest中获取ChannelId对应的Channel
	channel, err := model.GetChannelById(testRequest.ChannelId, true)
	if err != nil {
		return 0, "", err
	}
	w := httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Request = &http.Request{
		Method: "POST",
		URL:    &url.URL{Path: "/v1/chat/completions"},
		Body:   nil,
		Header: make(http.Header),
	}
	c.Request.Header.Set("Authorization", "Bearer "+channel.Key)
	c.Request.Header.Set("Content-Type", "application/json")
	c.Set(ctxkey.Channel, channel.Type)
	c.Set(ctxkey.BaseURL, channel.GetBaseURL())
	cfg, _ := channel.LoadConfig()
	c.Set(ctxkey.Config, cfg)
	middleware.SetupContextForSelectedChannel(c, channel, "")
	meta := meta.GetByContext(c)
	apiType := channeltype.ToAPIType(channel.Type)
	adaptor := relay.GetAdaptor(apiType)
	if adaptor == nil {
		return 0, "", fmt.Errorf("invalid api type: %d, adaptor is nil", apiType)
	}
	adaptor.Init(meta)

	modelName := testRequestByBuild.Model
	modelMap := channel.GetModelMapping()
	if modelName == "" || !strings.Contains(channel.Models, modelName) {
		modelNames := strings.Split(channel.Models, ",")
		if len(modelNames) > 0 {
			modelName = modelNames[0]
		}
		if modelMap != nil && modelMap[modelName] != "" {
			modelName = modelMap[modelName]
		}
	}
	meta.OriginModelName, meta.ActualModelName = testRequestByBuild.Model, modelName
	testRequestByBuild.Model = modelName
	convertedRequest, err := adaptor.ConvertRequest(c, relaymode.ChatCompletions, testRequestByBuild)
	if err != nil {
		return 0, "", err
	}
	jsonData, err := json.Marshal(convertedRequest)
	if err != nil {
		return 0, "", err
	}
	// 如果配置了测速JSON则取配置值
	customTestRequestBody := channel.GetTestRequestBody()
	if customTestRequestBody != "" {
		jsonData = []byte(customTestRequestBody)
	} else {
		jsonData, err = json.Marshal(convertedRequest)
	}
	if err != nil {
		return 0, "", err
	}
	// 将RequestBody解析成对象
	customTestRequestBodyObj := make(map[string]interface{})
	err = json.Unmarshal(jsonData, &customTestRequestBodyObj)
	if err != nil {
		return 0, "", err
	}
	logger.SysLog(string(jsonData))
	// 此处追加渠道配置的预处理逻辑 start
	var detailPrompt = string(jsonData)
	meta.DetailPrompt = detailPrompt
	detailPrompt, bodyBytes, preHandleRequestBodyStringErr := relaycontroller.PreHandleRequestBodyString(c, meta, detailPrompt, jsonData)
	if preHandleRequestBodyStringErr != nil {
		return 0, "", fmt.Errorf("%v", preHandleRequestBodyStringErr)
	}
	// 读取 Body 内容
	requestBody := bytes.NewBuffer(bodyBytes)
	// 恢复原始的 Body
	c.Request.Body = io.NopCloser(bytes.NewBuffer(jsonData))

	// 修改这里：只在确实有内容时设置 Content-Length
	if len(bodyBytes) > 0 {
		c.Writer.Header().Set("Content-Length", fmt.Sprintf("%d", len(bodyBytes)))
	} else {
		c.Writer.Header().Del("Content-Length")
	}
	// 此处追加渠道配置的预处理逻辑 end
	resp, err := adaptor.DoRequest(c, meta, requestBody)
	if err != nil {
		return 0, "", err
	}
	if resp != nil && resp.StatusCode != http.StatusOK {
		err := controller.RelayErrorHandler(resp)
		return 0, "", fmt.Errorf("status code %d: %s", resp.StatusCode, err.Error.Message)
	}
	usage, respErr := adaptor.DoResponse(c, resp, meta)
	if respErr != nil {
		return 0, "", fmt.Errorf("%s", respErr.Error.Message)
	}
	if usage == nil {
		return 0, "", fmt.Errorf("usage is nil")
	}
	result := w.Result()
	// print result.Body
	respBody, err := io.ReadAll(result.Body)
	if err != nil {
		return 0, "", err
	}
	logger.SysLog(fmt.Sprintf("testing channel #%d, response: \n%s", channel.Id, string(respBody)))
	return float64(time.Since(tik).Milliseconds()) / 1000.0, string(respBody), nil
}

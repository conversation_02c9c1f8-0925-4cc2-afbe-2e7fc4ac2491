package controller

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/middleware"
	"github.com/songquanpeng/one-api/model"
)

type ChannelGroupRelationUpdate struct {
	ChannelGroupId int   `json:"channelGroupId"`
	ChannelIdList  []int `json:"channelIdList"`
}

func GetAllChannelGroups(c *gin.Context) {
	p, _ := strconv.Atoi(c.Query("p"))
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))
	if p < 0 {
		p = 0
	}
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}
	id, _ := strconv.Atoi(c.Query("id"))
	name := c.Que<PERSON>("name")
	group := c.Query("group")
	status, _ := strconv.Atoi(c.Query("status"))
	channelGroups, err := model.GetAllChannelGroups(p*pageSize, pageSize, false, id, name, status, group)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    channelGroups,
	})
	return
}

func CountChannelGroups(c *gin.Context) {
	id, _ := strconv.Atoi(c.Query("id"))
	name := c.Query("name")
	group := c.Query("group")
	channelGroups, err := model.CountChannelGroups(id, name, group)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"count": channelGroups,
		},
	})
}

func SearchChannelGroups(c *gin.Context) {
	keyword := c.Query("keyword")
	p, _ := strconv.Atoi(c.Query("p"))
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))
	if p < 0 {
		p = 0
	}
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}
	channels, err := model.SearchChannelGroups(p*pageSize, pageSize, keyword)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    channels,
	})
	return
}

func GetChannelGroup(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	// true为显示所有,包括key,我不需要隐藏key
	channel, err := model.GetChannelGroupById(id, true)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    channel,
	})
	return
}

func AddChannelGroup(c *gin.Context) {
	// 检查渠道写权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")
	hasChannelWritePermission := role == 100 || // 超级管理员
		middleware.HasWritePermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelWrite)

	if !hasChannelWritePermission {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权限创建渠道组",
		})
		return
	}

	// 获取syncBothDB参数
	syncBothDB := c.Query("syncBothDB") == "true"

	channelGroup := model.ChannelGroup{}
	err := c.ShouldBindJSON(&channelGroup)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	channelGroup.CreatedTime = helper.GetTimestamp()

	// 根据syncBothDB参数选择合适的函数
	if syncBothDB {
		err = channelGroup.InsertWithSync(syncBothDB)
	} else {
		err = channelGroup.Insert()
	}

	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    channelGroup,
		"message": "",
	})
	return
}

func AddChannelsToGroup(c *gin.Context) {
	// 检查渠道写权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")
	hasChannelWritePermission := role == 100 || // 超级管理员
		middleware.HasWritePermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelWrite)

	if !hasChannelWritePermission {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权限修改渠道组",
		})
		return
	}

	channelGroupRelationUpdate := ChannelGroupRelationUpdate{}
	err := c.ShouldBindJSON(&channelGroupRelationUpdate)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	channelIdList := channelGroupRelationUpdate.ChannelIdList
	channelGroupId := channelGroupRelationUpdate.ChannelGroupId
	err = model.BatchUpdateChannelsToChannelGroup(channelIdList, channelGroupId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

func DeleteChannelGroup(c *gin.Context) {
	// 检查渠道写权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")
	hasChannelWritePermission := role == 100 || // 超级管理员
		middleware.HasWritePermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelWrite)

	if !hasChannelWritePermission {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权限删除渠道组",
		})
		return
	}

	// 获取syncBothDB参数
	syncBothDB := c.Query("syncBothDB") == "true"

	id, _ := strconv.Atoi(c.Param("id"))
	channelGroup := model.ChannelGroup{Id: id}
	err := channelGroup.DeleteWithSync(syncBothDB)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

func DeleteDisabledChannelGroup(c *gin.Context) {
	// 检查渠道写权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")
	hasChannelWritePermission := role == 100 || // 超级管理员
		middleware.HasWritePermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelWrite)

	if !hasChannelWritePermission {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权限删除渠道组",
		})
		return
	}

	rows, err := model.DeleteDisabledChannelGroup()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    rows,
	})
	return
}

func UpdateChannelGroup(c *gin.Context) {
	// 检查渠道写权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")
	hasChannelWritePermission := role == 100 || // 超级管理员
		middleware.HasWritePermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelWrite)

	if !hasChannelWritePermission {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权限修改渠道组",
		})
		return
	}

	// 获取syncBothDB参数
	syncBothDB := c.Query("syncBothDB") == "true"

	channelGroup := model.ChannelGroup{}
	err := c.ShouldBindJSON(&channelGroup)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 根据syncBothDB参数选择合适的函数
	err = channelGroup.UpdateWithSync(syncBothDB)

	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    channelGroup,
	})
	return
}

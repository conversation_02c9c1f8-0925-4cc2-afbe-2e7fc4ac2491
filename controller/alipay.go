package controller

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/smartwalle/alipay/v3"
	"github.com/smartwalle/xid"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/model"
	billingratio "github.com/songquanpeng/one-api/relay/billing/ratio"
)

// 全局支付宝客户端
var alipayClient *alipay.Client

// 存储当前的支付宝配置
var currentAlipayConfig struct {
	AppId       string
	PrivateKey  string
	PublicKey   string
	Enabled     bool
	CallbackURL string
}

// 上次检查配置的时间
var lastConfigCheckTime time.Time

// 在init函数中设置初始检查时间并启动定期检查
func init() {
	lastConfigCheckTime = time.Now()
}

// 初始化支付宝客户端
func initAlipayClient(forceInit ...bool) error {
	// 如果forceInit为true或客户端为nil，则初始化
	if (len(forceInit) > 0 && forceInit[0]) || alipayClient == nil {
		// 先关闭现有客户端
		if alipayClient != nil {
			alipayClient = nil
		}

		if !config.AlipayFaceToFaceEnabled || config.AlipayAppId == "" || config.AlipayPrivateKey == "" {
			return fmt.Errorf("支付宝当面付未启用或配置不完整")
		}

		var err error
		// 初始化支付宝客户端
		// 第三个参数表示是否是生产环境
		alipayClient, err = alipay.New(config.AlipayAppId, config.AlipayPrivateKey, true)
		if err != nil {
			return fmt.Errorf("初始化支付宝客户端失败: %v", err)
		}

		// 使用支付宝公钥进行验签
		if config.AlipayPublicKey != "" {
			if err = alipayClient.LoadAliPayPublicKey(config.AlipayPublicKey); err != nil {
				return fmt.Errorf("加载支付宝公钥失败: %v", err)
			}
		} else {
			return fmt.Errorf("支付宝公钥未配置")
		}

		// 更新当前配置
		currentAlipayConfig.AppId = config.AlipayAppId
		currentAlipayConfig.PrivateKey = config.AlipayPrivateKey
		currentAlipayConfig.PublicKey = config.AlipayPublicKey
		currentAlipayConfig.Enabled = config.AlipayFaceToFaceEnabled
		currentAlipayConfig.CallbackURL = config.AlipayCallbackAddress

		lastConfigCheckTime = time.Now()
		return nil
	}

	return nil
}

// CheckAlipayConfigChanged 检查支付宝配置是否更改
// 如果配置已更改，则返回true，并重置客户端以便下次使用时重新初始化
func CheckAlipayConfigChanged() bool {
	// 记录当前时间作为最后检查时间
	defer func() {
		lastConfigCheckTime = time.Now()
	}()

	// 如果启用状态改变，或者启用状态为true且其他配置发生变化，则需要重新初始化
	if currentAlipayConfig.Enabled != config.AlipayFaceToFaceEnabled ||
		(config.AlipayFaceToFaceEnabled && (currentAlipayConfig.AppId != config.AlipayAppId ||
			currentAlipayConfig.PrivateKey != config.AlipayPrivateKey ||
			currentAlipayConfig.PublicKey != config.AlipayPublicKey ||
			currentAlipayConfig.CallbackURL != config.AlipayCallbackAddress)) {

		// 如果客户端已存在，则将其设为nil，下次使用时会重新初始化
		if alipayClient != nil {
			alipayClient = nil
			logger.SysLog("支付宝配置发生变更，客户端将在下次使用时重新初始化")
		}

		// 更新配置缓存
		currentAlipayConfig.AppId = config.AlipayAppId
		currentAlipayConfig.PrivateKey = config.AlipayPrivateKey
		currentAlipayConfig.PublicKey = config.AlipayPublicKey
		currentAlipayConfig.Enabled = config.AlipayFaceToFaceEnabled
		currentAlipayConfig.CallbackURL = config.AlipayCallbackAddress

		return true
	}

	return false
}

// 检查配置是否需要更新（考虑时间因素）
func checkConfigIfNeeded() bool {
	// 如果距离上次检查超过5秒，则再次检查
	if time.Since(lastConfigCheckTime) > 5*time.Second {
		return CheckAlipayConfigChanged()
	}
	return false
}

// AlipayF2FRequest 支付宝当面付请求参数
type AlipayF2FRequest struct {
	Amount   float64 `json:"amount"`   // 充值金额，单位美元
	Currency string  `json:"currency"` // 币种，可选值：USD，CNY，默认值：USD
}

// AlipayF2FResponse 支付宝当面付响应
type AlipayF2FResponse struct {
	Success    bool   `json:"success"`
	Message    string `json:"message"`
	QRCode     string `json:"qr_code,omitempty"`      // 二维码内容
	OutTradeNo string `json:"out_trade_no,omitempty"` // 商户订单号
}

// AlipayTradeStatus 支付宝交易状态
type AlipayTradeStatus struct {
	Success    bool   `json:"success"`
	Message    string `json:"message"`
	Status     string `json:"status,omitempty"`       // WAIT_BUYER_PAY, TRADE_SUCCESS, TRADE_FINISHED, TRADE_CLOSED
	TradeNo    string `json:"trade_no,omitempty"`     // 支付宝交易号
	OutTradeNo string `json:"out_trade_no,omitempty"` // 商户订单号
}

// AlipayF2FPay 处理支付宝当面付请求
func AlipayF2FPay(c *gin.Context) {
	// 检查配置是否变更
	if checkConfigIfNeeded() {
		logger.SysLog("支付宝配置已变更，将使用新配置初始化客户端")
	}

	// 初始化支付宝客户端
	if alipayClient == nil {
		if err := initAlipayClient(); err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "支付宝客户端初始化失败: " + err.Error(),
			})
			return
		}
	}

	userId := c.GetInt("id")
	user, err := model.GetUserById(userId, false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取用户信息失败: " + err.Error(),
		})
		return
	}

	var req AlipayF2FRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	// 检查金额
	if req.Amount <= 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "充值金额必须大于0",
		})
		return
	}

	// 定义两个变量，充值美金额度和支付金额
	var addUSD float64
	var payCNY float64
	var payCNYOrigin float64

	// 根据选择的币种，获取支付金额或者充值额度
	// 如果是人民币，充值美金额度=req.Amount/单位美金价格，支付金额=req.Amount
	if req.Currency == "CNY" {
		addUSD = req.Amount / GetAmount(1, *user)
		payCNY = req.Amount
		payCNYOrigin = addUSD * GetAmountOrigin(1, *user)
	} else {
		// 如果是美金，充值美金额度=req.Amount，支付金额=req.Amount*单位美金价格
		addUSD = req.Amount
		payCNY = req.Amount * GetAmount(1, *user)
		payCNYOrigin = req.Amount * GetAmountOrigin(1, *user)
	}

	// 检查最小充值金额
	limit := billingratio.GetTopupGroupMinLimit(user.Group) // 单位美金
	if limit > 0 && addUSD < float64(limit) {
		c.JSON(200, gin.H{
			"success": false,
			"message": fmt.Sprintf("充值金额不能小于 %.2f 美元", float64(limit)),
		})
		return
	}

	// 生成商户订单号
	outTradeNo := fmt.Sprintf("%d", xid.Next())

	// 创建支付宝当面付交易
	var p = alipay.TradePreCreate{}

	// 处理回调URL
	callbackURL := config.AlipayCallbackAddress
	if callbackURL == "" {
		// 如果未配置回调地址，使用服务器地址
		callbackURL = config.ServerAddress
	}

	// 确保回调URL以/api/alipay/callback结尾
	if !strings.HasSuffix(callbackURL, "/api/alipay/callback") {
		// 移除可能的尾部斜杠
		callbackURL = strings.TrimSuffix(callbackURL, "/")
		// 添加回调路径
		callbackURL = callbackURL + "/api/alipay/callback"
	}

	p.NotifyURL = callbackURL
	p.Subject = fmt.Sprintf("充值 %.2f 元", payCNY)
	p.OutTradeNo = outTradeNo
	p.TotalAmount = fmt.Sprintf("%.2f", payCNY)
	p.ProductCode = "FACE_TO_FACE_PAYMENT"

	// 调用支付宝接口
	rsp, err := alipayClient.TradePreCreate(context.Background(), p)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "创建支付宝订单失败: " + err.Error(),
		})
		return
	}

	if rsp.IsFailure() {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": fmt.Sprintf("支付宝返回错误: %s - %s", rsp.Msg, rsp.SubMsg),
		})
		return
	}

	// 创建本地订单记录
	topUp := &model.TopUp{
		UserId:        userId,
		Amount:        addUSD,
		Money:         payCNY,
		MoneyOrigin:   payCNYOrigin,
		TradeNo:       outTradeNo, // 使用outTradeNo作为TradeNo
		PaymentMethod: "alipay_f2f",
		CreateTime:    time.Now().Unix(),
		Status:        "pending",
	}
	err = topUp.Insert()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "创建订单记录失败: " + err.Error(),
		})
		return
	}

	// 返回二维码
	c.JSON(http.StatusOK, AlipayF2FResponse{
		Success:    true,
		Message:    "创建支付宝当面付订单成功",
		QRCode:     rsp.QRCode,
		OutTradeNo: outTradeNo,
	})
}

// AlipayF2FStatus 查询支付宝当面付订单状态
func AlipayF2FStatus(c *gin.Context) {
	// 检查配置是否变更
	if checkConfigIfNeeded() {
		logger.SysLog("支付宝配置已变更，将使用新配置初始化客户端")
	}

	// 初始化支付宝客户端
	if alipayClient == nil {
		if err := initAlipayClient(); err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "支付宝客户端初始化失败: " + err.Error(),
			})
			return
		}
	}

	outTradeNo := c.Query("out_trade_no")
	if outTradeNo == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "订单号不能为空",
		})
		return
	}

	// 查询本地订单
	topUp := model.GetTopUpByTradeNo(outTradeNo)
	if topUp == nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "订单不存在",
		})
		return
	}

	// 如果订单已经成功，直接返回成功
	if topUp.Status == "success" {
		c.JSON(http.StatusOK, AlipayTradeStatus{
			Success:    true,
			Message:    "支付成功",
			Status:     "TRADE_SUCCESS",
			TradeNo:    topUp.TradeNo,
			OutTradeNo: outTradeNo,
		})
		return
	}

	// 查询支付宝订单状态
	var p = alipay.TradeQuery{}
	p.OutTradeNo = outTradeNo

	rsp, err := alipayClient.TradeQuery(context.Background(), p)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "查询支付宝订单失败: " + err.Error(),
		})
		return
	}

	if rsp.IsFailure() {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": fmt.Sprintf("支付宝返回错误: %s - %s", rsp.Msg, rsp.SubMsg),
		})
		return
	}

	// 返回订单状态
	c.JSON(http.StatusOK, AlipayTradeStatus{
		Success:    true,
		Message:    "查询成功",
		Status:     string(rsp.TradeStatus),
		TradeNo:    rsp.TradeNo,
		OutTradeNo: outTradeNo,
	})
}

// AlipayF2FCallback 处理支付宝当面付回调
func AlipayF2FCallback(c *gin.Context) {
	// 检查配置是否变更
	if checkConfigIfNeeded() {
		logger.SysLog("支付宝配置已变更，将使用新配置初始化客户端")
	}

	// 初始化支付宝客户端
	if alipayClient == nil {
		if err := initAlipayClient(); err != nil {
			logger.SysError(fmt.Sprintf("支付宝回调初始化客户端失败: %s", err.Error()))
			c.String(http.StatusOK, "fail")
			return
		}
	}

	// 解析请求参数
	err := c.Request.ParseForm()
	if err != nil {
		logger.SysError(fmt.Sprintf("支付宝回调解析参数失败: %s", err.Error()))
		c.String(http.StatusOK, "fail")
		return
	}

	// 验证签名
	if err := alipayClient.VerifySign(c.Request.Form); err != nil {
		logger.SysError(fmt.Sprintf("支付宝回调验证签名失败: %s", err.Error()))
		c.String(http.StatusOK, "fail")
		return
	}

	// 获取交易号和商户订单号
	tradeNo := c.Request.Form.Get("trade_no")
	outTradeNo := c.Request.Form.Get("out_trade_no")
	tradeStatus := c.Request.Form.Get("trade_status")

	// 查询订单
	topUp := model.GetTopUpByTradeNo(outTradeNo)
	if topUp == nil {
		logger.SysError(fmt.Sprintf("支付宝回调未找到订单: %s", outTradeNo))
		c.String(http.StatusOK, "fail")
		return
	}

	// 如果订单已经处理过，直接返回成功
	if topUp.Status == "success" {
		c.String(http.StatusOK, "success")
		return
	}

	// 检查交易状态
	if tradeStatus != "TRADE_SUCCESS" {
		logger.SysError(fmt.Sprintf("支付宝回调交易状态不是成功: %s", tradeStatus))
		c.String(http.StatusOK, "fail")
		return
	}

	// 开始事务
	tx := model.DB.Begin()
	if tx.Error != nil {
		logger.SysError(fmt.Sprintf("支付宝回调开始事务失败: %s", tx.Error.Error()))
		c.String(http.StatusOK, "fail")
		return
	}

	// 锁定订单记录
	lockedTopUp, err := model.GetTopUpByTradeNoByTx(tx, outTradeNo)
	if err != nil {
		tx.Rollback()
		logger.SysError(fmt.Sprintf("支付宝回调锁定订单失败: %s", err.Error()))
		c.String(http.StatusOK, "fail")
		return
	}

	// 更新订单状态
	lockedTopUp.Status = "success"
	lockedTopUp.TradeNo = tradeNo
	err = lockedTopUp.UpdateByTx(tx)
	if err != nil {
		tx.Rollback()
		logger.SysError(fmt.Sprintf("支付宝回调更新订单状态失败: %s", err.Error()))
		c.String(http.StatusOK, "fail")
		return
	}

	// 获取用户当前余额（用于日志记录）
	user, err := model.GetUserById(lockedTopUp.UserId, false)
	if err != nil {
		tx.Rollback()
		logger.SysError(fmt.Sprintf("支付宝回调获取用户失败: %s", err.Error()))
		c.String(http.StatusOK, "fail")
		return
	}
	beforeQuota := user.Quota

	// 使用 IncreaseUserQuotaAndRedisByTx 同时更新数据库和 Redis 中的用户余额
	err = model.IncreaseUserQuotaAndRedisByTx(tx, lockedTopUp.UserId, int64(lockedTopUp.Amount*500000))
	if err != nil {
		tx.Rollback()
		logger.SysError(fmt.Sprintf("支付宝回调增加用户余额失败: %s", err.Error()))
		c.String(http.StatusOK, "fail")
		return
	}

	// 添加邀请返利
	err = model.AddInviteBonusByAgency(c, tx, lockedTopUp, int(lockedTopUp.Amount*500000))
	if err != nil {
		tx.Rollback()
		logger.SysError(fmt.Sprintf("支付宝回调邀请返利失败: %s", err.Error()))
		c.String(http.StatusOK, "fail")
		return
	}

	// 更新余额有效期
	if config.QuotaExpireEnabled && config.QuotaExpireDays > 0 {
		err = model.AddUserQuotaExpireByTx(tx, lockedTopUp.UserId, config.QuotaExpireDays)
		if err != nil {
			tx.Rollback()
			logger.SysError(fmt.Sprintf("支付宝回调更新用户余额过期时间失败: topUp:%v,err:%s", lockedTopUp, err))
			c.String(http.StatusOK, "fail")
			return
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logger.SysError(fmt.Sprintf("支付宝回调提交事务失败: %s", err.Error()))
		c.String(http.StatusOK, "fail")
		return
	}

	// 记录日志
	afterQuota := beforeQuota + int64(lockedTopUp.Amount*500000)
	topUpMsg := fmt.Sprintf(
		"充值 %.2f 美元，支付 %.2f 元人民币，使用支付宝当面付，订单号：%s，交易号：%s，余额：%.2f -> %.2f",
		lockedTopUp.Amount,
		lockedTopUp.Money,
		outTradeNo,
		tradeNo,
		float64(beforeQuota)/500000.0,
		float64(afterQuota)/500000.0,
	)

	model.RecordLog(c, lockedTopUp.UserId, model.LogTypeTopup, topUpMsg)

	// 返回成功
	c.String(http.StatusOK, "success")
}

// AlipayF2FQuery 查询支付宝当面付订单状态
func AlipayF2FQuery(c *gin.Context) {
	// 检查配置是否变更
	if checkConfigIfNeeded() {
		logger.SysLog("支付宝配置已变更，将使用新配置初始化客户端")
	}

	outTradeNo := c.Query("out_trade_no")
	if outTradeNo == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "订单号不能为空",
		})
		return
	}

	// 初始化支付宝客户端
	if alipayClient == nil {
		if err := initAlipayClient(); err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "初始化支付宝客户端失败: " + err.Error(),
			})
			return
		}
	}

	// 查询本地订单
	topUp := model.GetTopUpByTradeNo(outTradeNo)
	if topUp == nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "订单不存在",
		})
		return
	}

	// 如果订单已经成功，直接返回成功
	if topUp.Status == "success" {
		c.JSON(http.StatusOK, AlipayTradeStatus{
			Success:    true,
			Message:    "支付成功",
			Status:     "TRADE_SUCCESS",
			TradeNo:    topUp.TradeNo,
			OutTradeNo: outTradeNo,
		})
		return
	}

	// 查询支付宝订单状态
	var p = alipay.TradeQuery{}
	p.OutTradeNo = outTradeNo

	rsp, err := alipayClient.TradeQuery(context.Background(), p)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "查询支付宝订单失败: " + err.Error(),
		})
		return
	}

	if rsp.IsFailure() {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": fmt.Sprintf("支付宝返回错误: %s - %s", rsp.Msg, rsp.SubMsg),
		})
		return
	}

	// 返回订单状态
	c.JSON(http.StatusOK, AlipayTradeStatus{
		Success:    true,
		Message:    "查询成功",
		Status:     string(rsp.TradeStatus),
		TradeNo:    rsp.TradeNo,
		OutTradeNo: outTradeNo,
	})
}

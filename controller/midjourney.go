package controller

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"runtime/debug"
	"strconv"
	"sync"
	"time"

	"github.com/samber/lo"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/client"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/common/message"
	"github.com/songquanpeng/one-api/model"
	billingratio "github.com/songquanpeng/one-api/relay/billing/ratio"
)

// MidjourneyTask 旧版API响应结构体，用于解析旧版API返回的数据
type MidjourneyTask struct {
	MjId        string `json:"id"`
	Action      string `json:"action"`
	Prompt      string `json:"prompt"`
	PromptEn    string `json:"promptEn"`
	Description string `json:"description"`
	State       string `json:"state"`
	SubmitTime  int64  `json:"submitTime"`
	StartTime   int64  `json:"startTime"`
	FinishTime  int64  `json:"finishTime"`
	ImageUrl    string `json:"imageUrl"`
	Status      string `json:"status"`
	Progress    string `json:"progress"`
	FailReason  string `json:"failReason"`
}

// MidjourneyPlusResponse API响应结构体，用于解析上游API返回的数据
type MidjourneyPlusResponse struct {
	MjId        string        `json:"id"`
	Action      string        `json:"action"`
	Prompt      string        `json:"prompt"`
	PromptEn    string        `json:"promptEn"`
	Description string        `json:"description"`
	State       string        `json:"state"`
	SubmitTime  int64         `json:"submitTime"`
	StartTime   int64         `json:"startTime"`
	FinishTime  int64         `json:"finishTime"`
	ImageUrl    string        `json:"imageUrl"`
	Status      string        `json:"status"`
	Progress    string        `json:"progress"`
	FailReason  string        `json:"failReason"`
	Properties  any           `json:"properties"`
	Buttons     []interface{} `json:"buttons"`
	// Video related fields - 直接使用上游API返回的格式
	VideoUrls []VideoUrlObject `json:"videoUrls,omitempty"`
}

// MJ任务更新模式常量
const (
	MjUpdateModeSingle         = "single"          // 单个更新模式
	MjUpdateModeBatch          = "batch"           // 批量更新模式
	MjUpdateModePoolSingle     = "pool_single"     // 工作池+单次查询模式
	MjUpdateModePoolBatch      = "pool_batch"      // 工作池+批量查询模式
	MjUpdateModeFullConcurrent = "full_concurrent" // 完全并发模式
)

// 任务锁映射，用于防止重复更新同一任务
var mjTaskLocks = sync.Map{}

func UpdateMidjourneyTask() {
	//revocer
	imageModel := "midjourney"
	for {
		defer func() {
			if err := recover(); err != nil {
				logger.SysError(fmt.Sprintf("UpdateMidjourneyTask panic: %v", err))
			}
		}()
		time.Sleep(time.Duration(config.MidjourneyPollDuration) * time.Second)
		tasks := model.GetAllUnFinishTasks()
		if len(tasks) != 0 {
			for _, task := range tasks {
				midjourneyChannel, err := model.GetChannelById(task.ChannelId, true)
				if err != nil {
					logger.SysError(fmt.Sprintf("CacheGetChannel: %v", err))
					task.FailReason = fmt.Sprintf("获取渠道信息失败，请联系管理员，渠道ID：%d", task.ChannelId)
					task.Status = "FAILURE"
					task.Progress = "100%"
					err := task.Update()
					if err != nil {
						logger.SysError(fmt.Sprintf("UpdateMidjourneyTask error: %v", err))
					}
					continue
				}
				requestUrl := fmt.Sprintf("%s/mj/task/%s/fetch", *midjourneyChannel.BaseURL, task.MjId)

				req, err := http.NewRequest("GET", requestUrl, bytes.NewBuffer([]byte("")))
				if err != nil {
					logger.SysError(fmt.Sprintf("UpdateMidjourneyTask error: %v", err))
					continue
				}

				req.Header.Set("Content-Type", "application/json")
				req.Header.Set("mj-api-secret", midjourneyChannel.Key)
				req.Header.Set("Authorization", midjourneyChannel.Key)
				resp, err := client.HTTPClient.Do(req)
				if err != nil {
					logger.SysError(fmt.Sprintf("UpdateMidjourneyTask error: %v", err))
					continue
				}
				defer resp.Body.Close()
				var responseItem MidjourneyTask
				err = json.NewDecoder(resp.Body).Decode(&responseItem)
				if err != nil {
					logger.SysError(fmt.Sprintf("UpdateMidjourneyTask error: %v", err))
					continue
				}
				task.Code = 1
				//检查原始progress 而不是更新的
				originProgress := task.Progress

				task.Progress = responseItem.Progress
				if responseItem.Action == "DESCRIBE" {
					task.Prompt = responseItem.Prompt
				}
				task.PromptEn = responseItem.PromptEn
				task.State = responseItem.State
				task.SubmitTime = responseItem.SubmitTime
				task.StartTime = responseItem.StartTime
				task.FinishTime = responseItem.FinishTime
				task.ImageUrl = responseItem.ImageUrl
				task.Status = responseItem.Status
				task.FailReason = responseItem.FailReason
				if originProgress != "100%" && responseItem.FailReason != "" {
					logger.SysError(task.MjId + " 构建失败，" + task.FailReason)
					task.Progress = "100%"
					err = model.CacheUpdateUserQuota(nil, task.UserId)
					if err != nil {
						logger.SysError("error update user quota cache: " + err.Error())
					} else {
						modelRatio := billingratio.GetModelRatio(imageModel, 1)
						groupRatio := billingratio.GetGroupRatio("default")
						ratio := modelRatio * groupRatio
						quota := int64(ratio * 1 * 1000)
						if quota != 0 {
							err := model.IncreaseUserQuotaAndRedis(task.UserId, quota)
							if err != nil {
								logger.SysError("fail to increase user quota: " + err.Error())
							}
							// 抵消渠道消耗和用户消耗
							model.UpdateUserUsedQuotaAndRequestCount(task.UserId, -quota)
							model.UpdateChannelUsedQuota(task.ChannelId, -quota)
							logContent := fmt.Sprintf("%s 构图失败，补偿 %s", task.MjId, common.LogQuota(quota))
							model.RecordRefundLogByDetailIfZeroQuota(nil, task.RequestId, "", "", task.UserId, task.ChannelId, 0, 0, fmt.Sprintf("%s-%s", imageModel, task.Mode), "", "", "", int(-quota), 0, 0, 0, false, logContent)
						}
					}
				}

				err = task.Update()
				if err != nil {
					logger.SysError(fmt.Sprintf("UpdateMidjourneyTask error: %v", err))
				}
				logger.SysLog(fmt.Sprintf("UpdateMidjourneyTask success: %v", task))
			}
		}
	}
}

// 通用的任务准备函数
func prepareMjTasks(tasks []*model.MidjourneyPlus) (map[int][]string, map[string]*model.MidjourneyPlus) {
	if len(tasks) == 0 {
		return make(map[int][]string), make(map[string]*model.MidjourneyPlus)
	}

	taskChannelM := make(map[int][]string)
	taskM := make(map[string]*model.MidjourneyPlus)

	for _, task := range tasks {
		if task == nil || task.MjId == "" {
			continue
		}
		taskM[task.MjId] = task
		taskChannelM[task.ChannelId] = append(taskChannelM[task.ChannelId], task.MjId)
	}

	logger.SysLog(fmt.Sprintf("任务准备完成，共 %d 个渠道，%d 个任务", len(taskChannelM), len(taskM)))
	return taskChannelM, taskM
}

// 通用的渠道处理函数
func processMjChannel(channelId int, taskIds []string) (*model.Channel, error) {
	if channelId <= 0 {
		return nil, fmt.Errorf("无效的渠道ID：%d", channelId)
	}

	if len(taskIds) == 0 {
		return nil, nil
	}

	logger.SysLog(fmt.Sprintf("开始处理渠道 #%d，任务数量：%d", channelId, len(taskIds)))

	midjourneyChannel, err := model.GetChannelById(channelId, true)
	if err != nil {
		logger.SysError(fmt.Sprintf("获取渠道信息失败，渠道ID：%d，错误：%v", channelId, err))
		// 批量更新失败状态
		updateErr := model.MjBulkUpdate(taskIds, map[string]any{
			"fail_reason": fmt.Sprintf("获取渠道信息失败，请联系管理员，渠道ID：%d", channelId),
			"status":      "FAILURE",
			"progress":    "100%",
		})
		if updateErr != nil {
			logger.SysError(fmt.Sprintf("批量更新任务状态失败，错误：%v", updateErr))
		}
		return nil, err
	}

	// 验证渠道基本信息
	if midjourneyChannel == nil || midjourneyChannel.BaseURL == nil || *midjourneyChannel.BaseURL == "" {
		err := fmt.Errorf("渠道基本信息无效，渠道ID：%d", channelId)
		logger.SysError(err.Error())
		return nil, err
	}

	return midjourneyChannel, nil
}

// 获取工作协程数
func getWorkerCount() int {
	workerCount := config.BatchUpdateMjWorkerSize
	if workerCount <= 0 {
		workerCount = 20 // 默认值
	} else if workerCount > 200 {
		workerCount = 200 // 最大值限制
	}
	logger.SysLog(fmt.Sprintf("工作协程数量设置为：%d", workerCount))
	return workerCount
}

// 根据配置选择更新模式
func UpdateMidjourneyPlusTask() {
	// 如果MJ Plus未启用，直接返回
	if !config.MidjourneyPlusEnabled {
		return
	}

	for {
		defer func() {
			if err := recover(); err != nil {
				logger.SysError(fmt.Sprintf("UpdateMidjourneyPlusTask panic: %v", err))
				debug.PrintStack()
			}
		}()

		// 按配置的间隔时间休眠
		time.Sleep(time.Duration(config.MidjourneyPollDuration) * time.Second)

		tasks := model.GetMjPlusAllUnFinishTasks()
		if len(tasks) == 0 {
			continue
		}

		logger.SysLog(fmt.Sprintf("检测到未完成的任务数：%d", len(tasks)))

		// 检查超时任务
		timeoutTasks := []int{}
		for _, task := range tasks {
			// 检查提交超时
			if task.SubmitTime != 0 {
				// 判断时间戳是否为毫秒级（大于1e10）
				isMillisecond := task.SubmitTime > 1e10
				submitTimeout := int64(config.MidjourneySubmitTimeout)
				if submitTimeout <= 0 {
					submitTimeout = 86400 // 默认24小时
				}

				var currentTimestamp, timeoutValue int64
				if isMillisecond {
					// 使用毫秒级时间戳
					currentTimestamp = helper.GetMilliTimestamp()
					timeoutValue = submitTimeout * 1000
				} else {
					// 使用秒级时间戳
					currentTimestamp = helper.GetTimestamp()
					timeoutValue = submitTimeout
				}

				if currentTimestamp-task.SubmitTime > timeoutValue {
					timeoutTasks = append(timeoutTasks, task.Id)
					continue
				}
			}

			// 检查开始超时
			if task.StartTime != 0 {
				// 判断时间戳是否为毫秒级（大于1e10）
				isMillisecond := task.StartTime > 1e10
				startTimeout := int64(config.MidjourneyStartTimeout)
				if startTimeout <= 0 {
					startTimeout = 600 // 默认10分钟
				}

				var currentTimestamp, timeoutValue int64
				if isMillisecond {
					// 使用毫秒级时间戳
					currentTimestamp = helper.GetMilliTimestamp()
					timeoutValue = startTimeout * 1000
				} else {
					// 使用秒级时间戳
					currentTimestamp = helper.GetTimestamp()
					timeoutValue = startTimeout
				}

				if currentTimestamp-task.StartTime > timeoutValue {
					timeoutTasks = append(timeoutTasks, task.Id)
				}
			}
		}

		if len(timeoutTasks) > 0 {
			logger.SysLog(fmt.Sprintf("检测到超时任务数：%d，任务ID：%v", len(timeoutTasks), timeoutTasks))
			err := model.MjBulkUpdateByID(timeoutTasks, map[string]any{
				"status":      "FAILURE",
				"progress":    "100%",
				"fail_reason": "timeout",
			})
			if err != nil {
				logger.SysError(fmt.Sprintf("更新超时任务状态失败：%v", err))
			}
		}

		// 根据配置选择更新模式
		switch config.BatchUpdateMjMode {
		case MjUpdateModeSingle:
			logger.SysLog("使用单个更新模式")
			updateMjTasksOneByOne(tasks)
		case MjUpdateModeBatch:
			logger.SysLog("使用批量更新模式")
			updateMjTasksByBatch(tasks)
		case MjUpdateModePoolSingle:
			logger.SysLog("使用工作池+单次查询模式")
			updateMjTasksByPoolSingle(tasks)
		case MjUpdateModePoolBatch:
			logger.SysLog("使用工作池+批量查询模式")
			updateMjTasksByPoolBatch(tasks)
		case MjUpdateModeFullConcurrent:
			logger.SysLog("使用完全并发模式")
			updateMjTasksByFullConcurrent(tasks)
		default:
			logger.SysLog(fmt.Sprintf("未知的更新模式：%s，使用默认的单个更新模式", config.BatchUpdateMjMode))
			updateMjTasksOneByOne(tasks)
		}
	}
}

// 批量更新任务状态
func updateMjTasksByBatch(tasks []*model.MidjourneyPlus) {
	taskChannelM, taskM := prepareMjTasks(tasks)
	if len(taskChannelM) == 0 {
		return
	}

	// 使用配置的批处理大小
	batchSize := config.BatchUpdateMjBatchSize
	if batchSize <= 0 {
		batchSize = 20
	} else if batchSize > 100 {
		batchSize = 100
	}

	for channelId, taskIds := range taskChannelM {
		logger.SysLog(fmt.Sprintf("渠道 #%d 未完成的任务有: %d", channelId, len(taskIds)))
		if len(taskIds) == 0 {
			continue
		}

		midjourneyChannel, err := model.GetChannelById(channelId, true)
		if err != nil {
			logger.SysLog(fmt.Sprintf("CacheGetChannel: %v", err))
			err := model.MjBulkUpdate(taskIds, map[string]any{
				"fail_reason": fmt.Sprintf("获取渠道信息失败，请联系管理员，渠道ID：%d", channelId),
				"status":      "FAILURE",
				"progress":    "100%",
			})
			if err != nil {
				logger.SysError(fmt.Sprintf("UpdateMidjourneyTask error2: %v", err))
			}
			continue
		}

		// 将taskIds拆分成多个批次，使用配置的批处理大小
		for i := 0; i < len(taskIds); i += batchSize {
			end := i + batchSize
			if end > len(taskIds) {
				end = len(taskIds)
			}
			batchTaskIds := taskIds[i:end]

			// 过滤已经在处理中的任务
			var filteredTaskIds []string
			for _, mjId := range batchTaskIds {
				taskLockKey := fmt.Sprintf("mj_task_%s", mjId)
				_, locked := mjTaskLocks.LoadOrStore(taskLockKey, true)
				if !locked {
					filteredTaskIds = append(filteredTaskIds, mjId)
				} else {
					logger.SysLog(fmt.Sprintf("任务 %s 已在处理中，跳过", mjId))
				}
			}

			// 如果过滤后没有任务，则跳过此批次
			if len(filteredTaskIds) == 0 {
				continue
			}

			// 设置超时上下文
			ctx, cancel := context.WithTimeout(context.Background(), time.Second*15)

			func() {
				// 确保在函数结束时释放所有任务锁
				defer func() {
					cancel()
					for _, mjId := range filteredTaskIds {
						mjTaskLocks.Delete(fmt.Sprintf("mj_task_%s", mjId))
					}
				}()

				startTime := time.Now()

				requestUrl := fmt.Sprintf("%s/mj/task/list-by-condition", *midjourneyChannel.BaseURL)
				body, _ := json.Marshal(map[string]any{
					"ids": filteredTaskIds,
				})

				req, err := http.NewRequest("POST", requestUrl, bytes.NewBuffer(body))
				if err != nil {
					logger.SysError(fmt.Sprintf("创建请求失败，渠道ID：%d, 错误：%v", channelId, err))
					return
				}

				// 使用带有超时的 context
				req = req.WithContext(ctx)
				req.Header.Set("Content-Type", "application/json")
				req.Header.Set("mj-api-secret", midjourneyChannel.Key)

				logger.SysLog(fmt.Sprintf("开始请求 /mj/task/list-by-condition，渠道ID：%d, 批次任务数：%d",
					channelId, len(filteredTaskIds)))

				resp, err := client.HTTPClient.Do(req)
				if err != nil {
					logger.SysError(fmt.Sprintf("请求失败，渠道ID：%d, 错误：%v", channelId, err))
					return
				}
				defer resp.Body.Close()

				responseBody, err := io.ReadAll(resp.Body)
				if err != nil {
					logger.SysError(fmt.Sprintf("读取响应体失败，渠道ID：%d, 错误：%v", channelId, err))
					return
				}

				logger.SysLog(fmt.Sprintf("收到响应，渠道ID：%d, 状态码：%d, 响应体长度：%d",
					channelId, resp.StatusCode, len(responseBody)))

				var responseItems []MidjourneyPlusResponse
				err = json.Unmarshal(responseBody, &responseItems)
				if err != nil {
					logger.SysError(fmt.Sprintf("解析响应体失败，渠道ID：%d, 错误：%v, 响应体：%s",
						channelId, err, string(responseBody)))
					err := model.MjBulkUpdate(filteredTaskIds, map[string]any{
						"fail_reason": fmt.Sprintf("解析响应体失败：%v", err),
						"status":      "FAILURE",
						"progress":    "100%",
					})
					if err != nil {
						logger.SysError(fmt.Sprintf("批量更新任务状态失败，渠道ID：%d, 错误：%v", channelId, err))
					}
					return
				}

				if len(responseItems) == 0 {
					logger.SysLog(fmt.Sprintf("渠道 #%d 返回的任务数为0，批次任务数：%d", channelId, len(filteredTaskIds)))
					return
				}

				logger.SysLog(fmt.Sprintf("成功获取任务信息，渠道ID：%d, 返回任务数：%d", channelId, len(responseItems)))

				for _, responseItem := range responseItems {
					// 检查是否已超时
					select {
					case <-ctx.Done():
						logger.SysLog(fmt.Sprintf("批次处理超时，已处理 %d/%d 个任务",
							len(responseItems), len(filteredTaskIds)))
						return
					default:
						// 继续处理
					}

					task := taskM[responseItem.MjId]
					if task == nil {
						logger.SysLog(fmt.Sprintf("未找到对应的任务，MjId：%s", responseItem.MjId))
						continue
					}

					if !checkMjTaskNeedUpdate(task, responseItem) {
						continue
					}

					updateSingleTaskStatus(task, responseItem)
				}

				// 记录处理时间
				elapsed := time.Since(startTime)
				if elapsed > time.Second*5 {
					logger.SysLog(fmt.Sprintf("批次处理时间较长，批次大小：%d, 耗时：%v",
						len(filteredTaskIds), elapsed))
				}
			}()

			// 添加批次间隔，避免请求过于频繁
			time.Sleep(time.Millisecond * 100)
		}
	}
}

// 逐个更新任务状态
func updateMjTasksOneByOne(tasks []*model.MidjourneyPlus) {
	for _, task := range tasks {
		if task == nil || task.MjId == "" {
			continue
		}

		// 尝试获取任务锁，如果已经在处理中则跳过
		taskLockKey := fmt.Sprintf("mj_task_%s", task.MjId)
		_, locked := mjTaskLocks.LoadOrStore(taskLockKey, true)
		if locked {
			// 任务已经在处理中，跳过
			logger.SysLog(fmt.Sprintf("任务 %s 已在处理中，跳过", task.MjId))
			continue
		}

		// 使用defer和匿名函数确保锁的释放
		func(taskCopy *model.MidjourneyPlus) {
			defer mjTaskLocks.Delete(taskLockKey)

			midjourneyChannel, err := model.GetChannelById(taskCopy.ChannelId, true)
			if err != nil {
				logger.SysError(fmt.Sprintf("CacheGetChannel: %v", err))
				taskCopy.FailReason = fmt.Sprintf("获取渠道信息失败，请联系管理员，渠道ID：%d", taskCopy.ChannelId)
				taskCopy.Status = "FAILURE"
				taskCopy.Progress = "100%"
				err := taskCopy.Update()
				if err != nil {
					logger.SysError(fmt.Sprintf("UpdateMidjourneyTask error: %v", err))
				}
				return
			}

			// 设置超时上下文
			ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
			defer cancel()

			func() {
				defer cancel()

				startTime := time.Now()

				requestUrl := fmt.Sprintf("%s/mj/task/%s/fetch", *midjourneyChannel.BaseURL, taskCopy.MjId)
				req, err := http.NewRequest("GET", requestUrl, nil)
				if err != nil {
					logger.SysError(fmt.Sprintf("创建请求失败，任务ID：%s, 错误：%v", taskCopy.MjId, err))
					return
				}

				req = req.WithContext(ctx)
				req.Header.Set("Content-Type", "application/json")
				req.Header.Set("mj-api-secret", midjourneyChannel.Key)
				req.Header.Set("Authorization", midjourneyChannel.Key)

				resp, err := client.HTTPClient.Do(req)
				if err != nil {
					logger.SysError(fmt.Sprintf("请求失败，任务ID：%s, 错误：%v", taskCopy.MjId, err))
					return
				}
				defer resp.Body.Close()

				var responseItem MidjourneyPlusResponse
				err = json.NewDecoder(resp.Body).Decode(&responseItem)
				if err != nil {
					logger.SysError(fmt.Sprintf("解析响应失败，任务ID：%s, 错误：%v", taskCopy.MjId, err))
					return
				}

				updateSingleTaskStatus(taskCopy, responseItem)

				// 记录处理时间
				elapsed := time.Since(startTime)
				if elapsed > time.Second*5 {
					logger.SysLog(fmt.Sprintf("任务处理时间较长，任务ID：%s, 耗时：%v", taskCopy.MjId, elapsed))
				}
			}()

			// 添加任务间隔，避免请求过于频繁
			time.Sleep(time.Millisecond * 50)
		}(task)
	}
}

// 更新单个任务状态
func updateSingleTaskStatus(task *model.MidjourneyPlus, responseItem MidjourneyPlusResponse) {
	task.Code = 1
	originProgress := task.Progress

	task.Progress = lo.If(responseItem.Progress != "", responseItem.Progress).Else(task.Progress)
	task.Prompt = lo.If(responseItem.Prompt != "", responseItem.Prompt).Else(task.Prompt)
	task.PromptEn = lo.If(responseItem.PromptEn != "", responseItem.PromptEn).Else(task.PromptEn)
	task.State = lo.If(responseItem.State != "", responseItem.State).Else(task.State)
	task.SubmitTime = lo.If(responseItem.SubmitTime != 0, responseItem.SubmitTime).Else(task.SubmitTime)
	task.StartTime = lo.If(responseItem.StartTime != 0, responseItem.StartTime).Else(task.StartTime)
	task.FinishTime = lo.If(responseItem.FinishTime != 0, responseItem.FinishTime).Else(task.FinishTime)
	task.ImageUrl = lo.If(responseItem.ImageUrl != "", responseItem.ImageUrl).Else(task.ImageUrl)
	task.Status = lo.If(responseItem.Status != "", responseItem.Status).Else(task.Status)
	task.FailReason = lo.If(responseItem.FailReason != "", responseItem.FailReason).Else(task.FailReason)
	task.Description = lo.If(responseItem.Description != "", responseItem.Description).Else(task.Description)

	// 处理视频URL数组
	if len(responseItem.VideoUrls) > 0 {
		// 将VideoUrlObject数组转换为字符串数组
		var videoUrls []string
		for _, videoObj := range responseItem.VideoUrls {
			if videoObj.Url != "" {
				videoUrls = append(videoUrls, videoObj.Url)
			}
		}
		if len(videoUrls) > 0 {
			videoUrlsJson, err := json.Marshal(videoUrls)
			if err != nil {
				logger.SysError(fmt.Sprintf("序列化视频URL数组失败，MjId：%s, 错误：%v", task.MjId, err))
			} else {
				task.VideoUrls = string(videoUrlsJson)
			}
		}
	}

	properties := helper.GetJsonString(responseItem.Properties)
	buttons := helper.GetJsonString(responseItem.Buttons)
	task.Properties = lo.If(properties != "", properties).Else(task.Properties)
	task.Buttons = lo.If(buttons != "", buttons).Else(task.Buttons)

	if originProgress != "100%" && responseItem.FailReason != "" {
		logger.SysLog(task.MjId + " 构建失败，" + task.FailReason)
		task.Progress = "100%"
		err := model.CacheUpdateUserQuota(nil, task.UserId)
		if err != nil {
			logger.SysError("error update user quota cache: " + err.Error())
		} else {
			quota := task.Quota
			if quota != 0 {
				err = model.IncreaseUserQuotaAndRedis(task.UserId, int64(quota))
				if err != nil {
					logger.SysError("fail to increase user quota: " + err.Error())
				}
				// 抵消渠道消耗和用户消耗
				model.UpdateUserUsedQuotaAndRequestCount(task.UserId, -int64(quota))
				model.UpdateChannelUsedQuota(task.ChannelId, -int64(quota))
				logContent := fmt.Sprintf("%s 构图失败，补偿 %s", task.MjId, common.LogQuota(int64(quota)))
				model.RecordRefundLogByDetailIfZeroQuota(nil, task.RequestId, "", "", task.UserId, task.ChannelId, 0, 0, fmt.Sprintf("%s-%s", "midjourney", task.Mode), "", "", "", -quota, 0, 0, 0, false, logContent)
			}
		}
	}

	err := task.Update()
	if err != nil {
		logger.SysError(fmt.Sprintf("更新任务失败，MjId：%s, 错误：%v", task.MjId, err))
	} else {
		logger.SysLog(fmt.Sprintf("成功更新任务，MjId：%s, 新状态：%s, 新进度：%s", task.MjId, task.Status, task.Progress))
	}
}

func checkMjTaskNeedUpdate(oldTask *model.MidjourneyPlus, newTask MidjourneyPlusResponse) bool {
	if oldTask.Code != 1 {
		return true
	}
	if oldTask.Progress != newTask.Progress {
		return true
	}
	if oldTask.PromptEn != newTask.PromptEn {
		return true
	}
	if oldTask.State != newTask.State {
		return true
	}
	if oldTask.SubmitTime != newTask.SubmitTime {
		return true
	}
	if oldTask.StartTime != newTask.StartTime {
		return true
	}
	if oldTask.FinishTime != newTask.FinishTime {
		return true
	}
	if oldTask.ImageUrl != newTask.ImageUrl {
		return true
	}
	if oldTask.Status != newTask.Status {
		return true
	}
	if oldTask.FailReason != newTask.FailReason {
		return true
	}
	if oldTask.FinishTime != newTask.FinishTime {
		return true
	}
	// 检查视频URL数组（需要转换为字符串数组后序列化比较）
	if len(newTask.VideoUrls) > 0 {
		var newVideoUrls []string
		for _, videoObj := range newTask.VideoUrls {
			if videoObj.Url != "" {
				newVideoUrls = append(newVideoUrls, videoObj.Url)
			}
		}
		if len(newVideoUrls) > 0 {
			newVideoUrlsJson, _ := json.Marshal(newVideoUrls)
			if oldTask.VideoUrls != string(newVideoUrlsJson) {
				return true
			}
		}
	}
	if oldTask.Progress != "100%" && newTask.FailReason != "" {
		return true
	}
	if oldTask.Progress != "100%" && oldTask.FailReason != "" {
		return true
	}
	return false
}

func GetAllMidjourney(c *gin.Context) {
	p, _ := strconv.Atoi(c.Query("p"))
	if p < 0 {
		p = 0
	}
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}
	// 根据条件过滤
	id, _ := strconv.Atoi(c.Query("id"))
	userId, _ := strconv.Atoi(c.Query("user_id"))
	code, _ := strconv.Atoi(c.Query("code"))
	action := c.Query("action")
	mjId := c.Query("mj_id")
	prompt := c.Query("prompt")
	promptEn := c.Query("prompt_en")
	description := c.Query("description")
	state := c.Query("state")
	submitTimeStart, _ := strconv.Atoi(c.Query("submit_time_start"))
	submitTimeEnd, _ := strconv.Atoi(c.Query("submit_time_end"))
	startTimeStart, _ := strconv.Atoi(c.Query("start_time_start"))
	startTimeEnd, _ := strconv.Atoi(c.Query("start_time_end"))
	finishTimeStart, _ := strconv.Atoi(c.Query("finish_time_start"))
	finishTimeEnd, _ := strconv.Atoi(c.Query("finish_time_end"))
	imageUrl := c.Query("image_url")
	status := c.Query("status")
	progress := c.Query("progress")
	failReason := c.Query("fail_reason")
	channelId, _ := strconv.Atoi(c.Query("channel_id"))
	mode := c.Query("mode")
	// 新增视频相关查询参数
	videoUrl := c.Query("video_url")
	videoUrls := c.Query("video_urls")

	logs := model.GetAllTasks(p*pageSize, pageSize, id, userId, code, action, mjId, prompt, promptEn, description, state, submitTimeStart, submitTimeEnd, startTimeStart, startTimeEnd, finishTimeStart, finishTimeEnd, imageUrl, status, progress, failReason, channelId, mode, videoUrl, videoUrls)

	//logs := model.GetAllTasks(p*common.ItemsPerPage, common.ItemsPerPage)
	if logs == nil {
		logs = make([]*model.Midjourney, 0)
	}
	c.JSON(200, gin.H{
		"success": true,
		"message": "",
		"data":    logs,
	})
}

func GetAllMidjourneyCount(c *gin.Context) {
	// 检查是否有查看所有Midjourney记录的权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")

	// 简化权限检查：超级管理员或有日志权限的管理员
	hasFullMJPermission := role == model.RoleRootUser ||
		(role == model.RoleAdminUser && (adminAccessFlags&2) == 2) // PermissionLog = 1 << 1 = 2

	if !hasFullMJPermission {
		// 没有权限查看所有记录，降级到查看用户自己的记录计数
		GetUserMidjourneyCount(c)
		return
	}

	// 根据条件过滤
	id, _ := strconv.Atoi(c.Query("id"))
	userId, _ := strconv.Atoi(c.Query("user_id"))
	code, _ := strconv.Atoi(c.Query("code"))
	action := c.Query("action")
	mjId := c.Query("mj_id")
	prompt := c.Query("prompt")
	promptEn := c.Query("prompt_en")
	description := c.Query("description")
	state := c.Query("state")
	submitTimeStart, _ := strconv.Atoi(c.Query("submit_time_start"))
	submitTimeEnd, _ := strconv.Atoi(c.Query("submit_time_end"))
	startTimeStart, _ := strconv.Atoi(c.Query("start_time_start"))
	startTimeEnd, _ := strconv.Atoi(c.Query("start_time_end"))
	finishTimeStart, _ := strconv.Atoi(c.Query("finish_time_start"))
	finishTimeEnd, _ := strconv.Atoi(c.Query("finish_time_end"))
	imageUrl := c.Query("image_url")
	status := c.Query("status")
	progress := c.Query("progress")
	failReason := c.Query("fail_reason")
	channelId, _ := strconv.Atoi(c.Query("channel_id"))
	mode := c.Query("mode")
	// 新增视频相关查询参数
	videoUrl := c.Query("video_url")
	videoUrls := c.Query("video_urls")

	count, err := model.CountAllTasks(id, userId, code, action, mjId, prompt, promptEn, description, state, submitTimeStart, submitTimeEnd, startTimeStart, startTimeEnd, finishTimeStart, finishTimeEnd, imageUrl, status, progress, failReason, channelId, mode, videoUrl, videoUrls)
	if err != nil {
		c.JSON(200, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    0,
		})
		return
	}
	c.JSON(200, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"count": count,
		},
	})
}

func GetUserMidjourney(c *gin.Context) {
	p, _ := strconv.Atoi(c.Query("p"))
	if p < 0 {
		p = 0
	}
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}
	userId := c.GetInt("id")
	logger.SysLog(fmt.Sprintf("userId = %d", userId))
	// 根据条件过滤
	id, _ := strconv.Atoi(c.Query("id"))
	code, _ := strconv.Atoi(c.Query("code"))
	action := c.Query("action")
	mjId := c.Query("mj_id")
	prompt := c.Query("prompt")
	promptEn := c.Query("prompt_en")
	description := c.Query("description")
	state := c.Query("state")
	submitTimeStart, _ := strconv.Atoi(c.Query("submit_time_start"))
	submitTimeEnd, _ := strconv.Atoi(c.Query("submit_time_end"))
	startTimeStart, _ := strconv.Atoi(c.Query("start_time_start"))
	startTimeEnd, _ := strconv.Atoi(c.Query("start_time_end"))
	finishTimeStart, _ := strconv.Atoi(c.Query("finish_time_start"))
	finishTimeEnd, _ := strconv.Atoi(c.Query("finish_time_end"))
	imageUrl := c.Query("image_url")
	status := c.Query("status")
	progress := c.Query("progress")
	failReason := c.Query("fail_reason")
	channelId, _ := strconv.Atoi(c.Query("channel_id"))
	mode := c.Query("mode")
	// 新增视频相关查询参数
	videoUrl := c.Query("video_url")
	videoUrls := c.Query("video_urls")

	logs := model.GetAllUserTask(userId, p*pageSize, pageSize, id, code, action, mjId, prompt, promptEn, description, state, submitTimeStart, submitTimeEnd, startTimeStart, startTimeEnd, finishTimeStart, finishTimeEnd, imageUrl, status, progress, failReason, channelId, mode, videoUrl, videoUrls)
	if logs == nil {
		logs = make([]*model.Midjourney, 0)
	}
	c.JSON(200, gin.H{
		"success": true,
		"message": "",
		"data":    logs,
	})
}

func GetUserMidjourneyCount(c *gin.Context) {
	userId := c.GetInt("id")
	// 根据条件过滤
	id, _ := strconv.Atoi(c.Query("id"))
	code, _ := strconv.Atoi(c.Query("code"))
	action := c.Query("action")
	mjId := c.Query("mj_id")
	prompt := c.Query("prompt")
	promptEn := c.Query("prompt_en")
	description := c.Query("description")
	state := c.Query("state")
	submitTimeStart, _ := strconv.Atoi(c.Query("submit_time_start"))
	submitTimeEnd, _ := strconv.Atoi(c.Query("submit_time_end"))
	startTimeStart, _ := strconv.Atoi(c.Query("start_time_start"))
	startTimeEnd, _ := strconv.Atoi(c.Query("start_time_end"))
	finishTimeStart, _ := strconv.Atoi(c.Query("finish_time_start"))
	finishTimeEnd, _ := strconv.Atoi(c.Query("finish_time_end"))
	imageUrl := c.Query("image_url")
	status := c.Query("status")
	progress := c.Query("progress")
	failReason := c.Query("fail_reason")
	channelId, _ := strconv.Atoi(c.Query("channel_id"))
	mode := c.Query("mode")
	// 新增视频相关查询参数
	videoUrl := c.Query("video_url")
	videoUrls := c.Query("video_urls")

	count, err := model.CountAllUserTask(userId, id, code, action, mjId, prompt, promptEn, description, state, submitTimeStart, submitTimeEnd, startTimeStart, startTimeEnd, finishTimeStart, finishTimeEnd, imageUrl, status, progress, failReason, channelId, mode, videoUrl, videoUrls)
	if err != nil {
		c.JSON(200, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    0,
		})
		return
	}
	c.JSON(200, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"count": count,
		},
	})
}

var deleteMidjourneyLogsLock sync.Mutex
var deleteMidjourneyLogsRunning = false

func DeleteMidjourneyLogs(c *gin.Context) {
	deleteMidjourneyLogsLock.Lock()
	if deleteMidjourneyLogsRunning {
		deleteMidjourneyLogsLock.Unlock()
		message := fmt.Sprintf("已有一个删除Midjourney日志任务在运行中，请稍后再试... 当前任务执行进度:已删除[%d]行,共需删除[%d]行,完成度[%d%%]",
			model.TotalAffectedMidjourneyLogsCount,
			model.TotalShouldDeleteMidjourneyLogsCount,
			lo.If(model.TotalShouldDeleteMidjourneyLogsCount == 0, 0).Else(int(float64(model.TotalAffectedMidjourneyLogsCount)/float64(model.TotalShouldDeleteMidjourneyLogsCount)*100)),
		)
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": message,
		})
		return
	}

	targetTimestamp, _ := strconv.ParseInt(c.Query("target_timestamp"), 10, 64)
	beforeNowTimestamp, _ := strconv.ParseInt(c.Query("before_now_timestamp"), 10, 64)
	if targetTimestamp == 0 && beforeNowTimestamp == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "target_timestamp 或 before_now_timestamp 至少需要提供一个",
		})
		return
	}
	if beforeNowTimestamp != 0 {
		targetTimestamp = helper.GetTimestamp() - beforeNowTimestamp
	}

	deleteMidjourneyLogsRunning = true
	model.TotalShouldDeleteMidjourneyLogsCount = 0
	model.TotalAffectedMidjourneyLogsCount = 0
	deleteMidjourneyLogsLock.Unlock()

	helper.SafeGoroutine(func() {
		defer func() {
			deleteMidjourneyLogsLock.Lock()
			deleteMidjourneyLogsRunning = false
			deleteMidjourneyLogsLock.Unlock()
		}()
		count, err := model.DeleteOldMidjourneyLogs(targetTimestamp)
		if err != nil {
			message.NotifyRootUser("删除Midjourney日志任务失败", err.Error())
			return
		}
		message.NotifyRootUser("删除Midjourney日志任务完成", "删除了"+strconv.Itoa(int(count))+"条Midjourney日志")
	})

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除Midjourney日志任务正在后台运行，运行结果稍后会通知管理员",
	})
	return
}

// 新增函数：获取删除进度
func GetDeleteMidjourneyLogsProgress(c *gin.Context) {
	if !deleteMidjourneyLogsRunning {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "没有正在进行的删除任务",
			"data": gin.H{
				"progress": 0,
				"affected": 0,
				"total":    0,
			},
		})
		return
	}

	progress := 0
	if model.TotalShouldDeleteMidjourneyLogsCount > 0 {
		progress = int(float64(model.TotalAffectedMidjourneyLogsCount) / float64(model.TotalShouldDeleteMidjourneyLogsCount) * 100)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除任务进行中",
		"data": gin.H{
			"progress": progress,
			"affected": model.TotalAffectedMidjourneyLogsCount,
			"total":    model.TotalShouldDeleteMidjourneyLogsCount,
		},
	})
}

// 带有上下文的单个任务请求处理
func fetchSingleTaskWithContext(ctx context.Context, midjourneyChannel *model.Channel, mjId string) (*MidjourneyPlusResponse, error) {
	requestUrl := fmt.Sprintf("%s/mj/task/%s/fetch", *midjourneyChannel.BaseURL, mjId)
	req, err := http.NewRequest("GET", requestUrl, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败：%v", err)
	}

	// 使用传入的上下文
	req = req.WithContext(ctx)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("mj-api-secret", midjourneyChannel.Key)
	req.Header.Set("Authorization", midjourneyChannel.Key)

	resp, err := client.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败：%v", err)
	}
	defer resp.Body.Close()

	var responseItem MidjourneyPlusResponse
	err = json.NewDecoder(resp.Body).Decode(&responseItem)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败：%v", err)
	}

	return &responseItem, nil
}

// 批量任务请求处理
func fetchBatchTasks(midjourneyChannel *model.Channel, taskIds []string) ([]MidjourneyPlusResponse, error) {
	requestUrl := fmt.Sprintf("%s/mj/task/list-by-condition", *midjourneyChannel.BaseURL)
	body, _ := json.Marshal(map[string]any{
		"ids": taskIds,
	})

	req, err := http.NewRequest("POST", requestUrl, bytes.NewBuffer(body))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败：%v", err)
	}

	timeout := time.Second * 10
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	req = req.WithContext(ctx)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("mj-api-secret", midjourneyChannel.Key)
	req.Header.Set("Authorization", midjourneyChannel.Key)

	resp, err := client.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败：%v", err)
	}
	defer resp.Body.Close()

	var responseItems []MidjourneyPlusResponse
	err = json.NewDecoder(resp.Body).Decode(&responseItems)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败：%v", err)
	}

	return responseItems, nil
}

// 工作池+单次查询模式
func updateMjTasksByPoolSingle(tasks []*model.MidjourneyPlus) {
	taskChannelM, taskM := prepareMjTasks(tasks)
	workerCount := getWorkerCount()

	for channelId, allTaskIds := range taskChannelM {
		midjourneyChannel, err := processMjChannel(channelId, allTaskIds)
		if err != nil || midjourneyChannel == nil {
			continue
		}

		// 过滤已经在处理中的任务
		var filteredTaskIds []string
		for _, mjId := range allTaskIds {
			taskLockKey := fmt.Sprintf("mj_task_%s", mjId)
			_, locked := mjTaskLocks.LoadOrStore(taskLockKey, true)
			if !locked {
				filteredTaskIds = append(filteredTaskIds, mjId)
			} else {
				logger.SysLog(fmt.Sprintf("任务 %s 已在处理中，跳过", mjId))
			}
		}

		// 如果过滤后没有任务，则跳过此渠道
		if len(filteredTaskIds) == 0 {
			continue
		}

		// 创建任务通道
		taskChan := make(chan string, len(filteredTaskIds))
		for _, taskId := range filteredTaskIds {
			taskChan <- taskId
		}
		close(taskChan)

		// 添加整体超时控制
		ctx, cancel := context.WithTimeout(context.Background(), time.Second*30)

		// 创建工作池
		var wg sync.WaitGroup
		for i := 0; i < workerCount; i++ {
			wg.Add(1)
			helper.SafeGoroutine(func() {
				defer wg.Done()
				for {
					// 检查整体超时
					select {
					case <-ctx.Done():
						logger.SysLog(fmt.Sprintf("工作协程因整体超时而退出，渠道ID：%d", channelId))
						return
					default:
						// 继续处理
					}

					// 非阻塞方式获取任务
					select {
					case mjId, ok := <-taskChan:
						if !ok {
							return // 通道已关闭
						}

						task := taskM[mjId]
						if task == nil {
							mjTaskLocks.Delete(fmt.Sprintf("mj_task_%s", mjId))
							continue
						}

						// 为每个任务单独设置超时
						taskCtx, taskCancel := context.WithTimeout(ctx, time.Second*10)

						// 使用独立的函数处理任务，确保资源释放
						func() {
							defer func() {
								taskCancel()
								mjTaskLocks.Delete(fmt.Sprintf("mj_task_%s", mjId))
							}()

							// 添加任务处理计时
							startTime := time.Now()

							// 使用 taskCtx 替代直接调用 fetchSingleTask
							responseItem, err := fetchSingleTaskWithContext(taskCtx, midjourneyChannel, mjId)
							if err != nil {
								logger.SysError(fmt.Sprintf("获取任务状态失败，任务ID：%s, 错误：%v", mjId, err))
								return
							}

							if !checkMjTaskNeedUpdate(task, *responseItem) {
								return
							}

							updateSingleTaskStatus(task, *responseItem)

							// 记录处理时间
							elapsed := time.Since(startTime)
							if elapsed > time.Second*5 {
								logger.SysLog(fmt.Sprintf("任务处理时间较长，任务ID：%s, 耗时：%v", mjId, elapsed))
							}
						}()

					case <-time.After(time.Millisecond * 100):
						// 避免空转，但不会长时间阻塞
						continue
					}
				}
			})
		}

		// 添加超时等待
		waitChan := make(chan struct{})
		go func() {
			wg.Wait()
			close(waitChan)
		}()

		select {
		case <-waitChan:
			logger.SysLog(fmt.Sprintf("渠道 #%d 完成本批次更新，任务数：%d", channelId, len(filteredTaskIds)))
		case <-time.After(time.Second * 60):
			logger.SysError(fmt.Sprintf("渠道 #%d 更新超时，可能有协程卡住", channelId))
			// 继续执行，不等待卡住的协程
		}

		// 确保取消上下文，释放资源
		cancel()
	}
}

// 工作池+批量查询模式
func updateMjTasksByPoolBatch(tasks []*model.MidjourneyPlus) {
	taskChannelM, taskM := prepareMjTasks(tasks)
	workerCount := getWorkerCount()
	batchSize := config.BatchUpdateMjBatchSize
	if batchSize <= 0 {
		batchSize = 20
	} else if batchSize > 100 {
		batchSize = 100
	}

	for channelId, allTaskIds := range taskChannelM {
		midjourneyChannel, err := processMjChannel(channelId, allTaskIds)
		if err != nil || midjourneyChannel == nil {
			continue
		}

		// 过滤已经在处理中的任务
		var filteredTaskIds []string
		for _, mjId := range allTaskIds {
			taskLockKey := fmt.Sprintf("mj_task_%s", mjId)
			_, locked := mjTaskLocks.LoadOrStore(taskLockKey, true)
			if !locked {
				filteredTaskIds = append(filteredTaskIds, mjId)
			} else {
				logger.SysLog(fmt.Sprintf("任务 %s 已在处理中，跳过", mjId))
			}
		}

		// 如果过滤后没有任务，则跳过此渠道
		if len(filteredTaskIds) == 0 {
			continue
		}

		// 将任务ID分批
		var taskBatches [][]string
		for i := 0; i < len(filteredTaskIds); i += batchSize {
			end := i + batchSize
			if end > len(filteredTaskIds) {
				end = len(filteredTaskIds)
			}
			taskBatches = append(taskBatches, filteredTaskIds[i:end])
		}

		// 创建批次任务通道
		batchChan := make(chan []string, len(taskBatches))
		for _, batch := range taskBatches {
			batchChan <- batch
		}
		close(batchChan)

		// 添加整体超时控制
		ctx, cancel := context.WithTimeout(context.Background(), time.Second*60)

		// 创建工作池
		var wg sync.WaitGroup
		for i := 0; i < workerCount; i++ {
			wg.Add(1)
			helper.SafeGoroutine(func() {
				defer wg.Done()
				for {
					// 检查整体超时
					select {
					case <-ctx.Done():
						logger.SysLog(fmt.Sprintf("工作协程因整体超时而退出，渠道ID：%d", channelId))
						return
					default:
						// 继续处理
					}

					// 非阻塞方式获取批次任务
					select {
					case taskIds, ok := <-batchChan:
						if !ok {
							return // 通道已关闭
						}

						// 为每个批次单独设置超时
						batchCtx, batchCancel := context.WithTimeout(ctx, time.Second*15)

						func() {
							defer func() {
								batchCancel()
								// 释放所有任务锁
								for _, mjId := range taskIds {
									mjTaskLocks.Delete(fmt.Sprintf("mj_task_%s", mjId))
								}
							}()

							// 添加批次处理计时
							startTime := time.Now()

							// 使用带超时的上下文创建请求
							responseItems, err := fetchBatchTasks(midjourneyChannel, taskIds)
							if err != nil {
								logger.SysError(fmt.Sprintf("批量获取任务状态失败，错误：%v", err))
								return
							}

							// 处理每个任务的响应
							for _, responseItem := range responseItems {
								// 检查是否已超时
								select {
								case <-batchCtx.Done():
									logger.SysLog(fmt.Sprintf("批次处理超时，已处理 %d/%d 个任务",
										len(responseItems), len(taskIds)))
									return
								default:
									// 继续处理
								}

								task := taskM[responseItem.MjId]
								if task == nil {
									logger.SysLog(fmt.Sprintf("未找到对应的任务，MjId：%s", responseItem.MjId))
									continue
								}

								if !checkMjTaskNeedUpdate(task, responseItem) {
									continue
								}

								updateSingleTaskStatus(task, responseItem)
							}

							// 记录处理时间
							elapsed := time.Since(startTime)
							if elapsed > time.Second*5 {
								logger.SysLog(fmt.Sprintf("批次处理时间较长，批次大小：%d, 耗时：%v",
									len(taskIds), elapsed))
							}
						}()

					case <-time.After(time.Millisecond * 100):
						// 避免空转，但不会长时间阻塞
						continue
					}
				}
			})
		}

		// 添加超时等待
		waitChan := make(chan struct{})
		go func() {
			wg.Wait()
			close(waitChan)
		}()

		select {
		case <-waitChan:
			logger.SysLog(fmt.Sprintf("渠道 #%d 完成本批次更新，任务数：%d", channelId, len(filteredTaskIds)))
		case <-time.After(time.Second * 90):
			logger.SysError(fmt.Sprintf("渠道 #%d 更新超时，可能有协程卡住", channelId))
			// 继续执行，不等待卡住的协程
		}

		// 确保取消上下文，释放资源
		cancel()
	}
}

// 完全并发模式 - 为每个任务创建一个独立的协程
func updateMjTasksByFullConcurrent(tasks []*model.MidjourneyPlus) {
	if len(tasks) == 0 {
		return
	}

	// 记录开始时间
	startTime := time.Now()
	logger.SysLog(fmt.Sprintf("完全并发模式开始处理 %d 个任务", len(tasks)))

	// 设置固定的最大并发数
	maxConcurrent := 10000

	// 使用信号量控制并发数
	semaphore := make(chan struct{}, maxConcurrent)

	// 为每个任务创建一个协程
	for _, task := range tasks {
		if task == nil || task.MjId == "" {
			continue
		}

		// 尝试获取任务锁，如果已经在处理中则跳过
		taskLockKey := fmt.Sprintf("mj_task_%s", task.MjId)
		_, locked := mjTaskLocks.LoadOrStore(taskLockKey, true)
		if locked {
			// 任务已经在处理中，跳过
			logger.SysLog(fmt.Sprintf("任务 %s 已在处理中，跳过", task.MjId))
			continue
		}

		// 获取信号量，如果已达到最大并发数，会阻塞
		semaphore <- struct{}{}

		// 创建任务副本，避免闭包问题
		taskCopy := task

		// 正确使用 SafeGoroutine
		helper.SafeGoroutine(func() {
			// 函数结束时释放信号量和任务锁
			defer func() {
				<-semaphore
				mjTaskLocks.Delete(taskLockKey)
			}()

			// 获取渠道信息
			midjourneyChannel, err := model.GetChannelById(taskCopy.ChannelId, true)
			if err != nil {
				logger.SysError(fmt.Sprintf("获取渠道信息失败，任务ID：%s, 错误：%v", taskCopy.MjId, err))
				taskCopy.FailReason = fmt.Sprintf("获取渠道信息失败，请联系管理员，渠道ID：%d", taskCopy.ChannelId)
				taskCopy.Status = "FAILURE"
				taskCopy.Progress = "100%"
				err := taskCopy.Update()
				if err != nil {
					logger.SysError(fmt.Sprintf("更新任务状态失败，任务ID：%s, 错误：%v", taskCopy.MjId, err))
				}
				return
			}

			// 验证渠道基本信息
			if midjourneyChannel == nil || midjourneyChannel.BaseURL == nil || *midjourneyChannel.BaseURL == "" {
				logger.SysError(fmt.Sprintf("渠道基本信息无效，任务ID：%s, 渠道ID：%d", taskCopy.MjId, taskCopy.ChannelId))
				taskCopy.FailReason = fmt.Sprintf("渠道基本信息无效，请联系管理员，渠道ID：%d", taskCopy.ChannelId)
				taskCopy.Status = "FAILURE"
				taskCopy.Progress = "100%"
				err := taskCopy.Update()
				if err != nil {
					logger.SysError(fmt.Sprintf("更新任务状态失败，任务ID：%s, 错误：%v", taskCopy.MjId, err))
				}
				return
			}

			// 设置超时上下文
			ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
			defer cancel()

			// 记录任务处理开始时间
			taskStartTime := time.Now()

			// 获取任务状态
			responseItem, err := fetchSingleTaskWithContext(ctx, midjourneyChannel, taskCopy.MjId)
			if err != nil {
				logger.SysError(fmt.Sprintf("获取任务状态失败，任务ID：%s, 错误：%v", taskCopy.MjId, err))
				return
			}

			// 检查是否需要更新
			if !checkMjTaskNeedUpdate(taskCopy, *responseItem) {
				return
			}

			// 更新任务状态
			updateSingleTaskStatus(taskCopy, *responseItem)

			// 记录任务处理时间
			elapsed := time.Since(taskStartTime)
			if elapsed > time.Second*5 {
				logger.SysLog(fmt.Sprintf("任务处理时间较长，任务ID：%s, 耗时：%v", taskCopy.MjId, elapsed))
			}
		})
	}

	// 等待所有信号量被释放（可选，如果不需要等待所有任务完成，可以移除此部分）
	// 这里设置一个较短的超时时间，确保函数不会无限期阻塞
	timeout := time.After(time.Second * 30)
	for i := 0; i < maxConcurrent; i++ {
		select {
		case semaphore <- struct{}{}:
			// 如果能获取到信号量，说明该槽位没有正在运行的协程
		case <-timeout:
			// 超时，不再等待
			logger.SysLog("完全并发模式等待超时，可能有部分任务仍在处理中")
			goto Done
		}
	}
Done:
	// 记录总处理时间
	totalElapsed := time.Since(startTime)
	logger.SysLog(fmt.Sprintf("完全并发模式处理完成，总耗时：%v", totalElapsed))
}

// RefreshMidjourneyTask 处理单个 Midjourney 任务的刷新请求
func RefreshMidjourneyTask(c *gin.Context) {
	mjId := c.Param("mjId")
	if mjId == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "任务ID不能为空",
		})
		return
	}

	userId := c.GetInt("id")
	isAdmin := model.IsAdmin(userId)

	// 获取任务信息
	var task *model.Midjourney
	if isAdmin {
		// 管理员可以查看所有任务
		task = model.GetByOnlyMJId(mjId)
		if task == nil {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "任务不存在",
			})
			return
		}
	} else {
		// 普通用户只能查看自己的任务
		task = model.GetByMJId(userId, mjId)
		if task == nil {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "任务不存在或您无权访问",
			})
			return
		}
	}

	// 检查任务是否已完成或失败
	if task.Status == "SUCCESS" || task.Status == "FAILURE" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "已完成或失败的任务无需刷新",
		})
		return
	}

	// 获取渠道信息
	midjourneyChannel, err := model.GetChannelById(task.ChannelId, true)
	if err != nil || midjourneyChannel == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取渠道信息失败",
		})
		return
	}

	// 设置超时上下文
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	// 记录开始时间，用于计算处理时间
	startTime := time.Now()

	// 刷新任务状态
	responseItem, err := fetchSingleTaskWithContext(ctx, midjourneyChannel, mjId)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "刷新任务状态失败: " + err.Error(),
		})
		return
	}

	// 保存原始任务状态，用于比较
	originStatus := task.Status
	originProgress := task.Progress

	// 更新任务状态
	if responseItem.Status == "" {
		// 如果返回的状态为空，则使用原有状态，防止覆盖
		responseItem.Status = task.Status
	}
	task.Progress = responseItem.Progress
	task.ImageUrl = responseItem.ImageUrl
	task.FailReason = responseItem.FailReason
	task.State = responseItem.State
	task.SubmitTime = responseItem.SubmitTime
	task.StartTime = responseItem.StartTime
	task.FinishTime = responseItem.FinishTime

	// 保存更新
	err = task.Update()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "更新任务状态失败: " + err.Error(),
		})
		return
	}

	// 记录处理时间
	elapsed := time.Since(startTime)
	if elapsed > time.Second*5 {
		logger.SysLog(fmt.Sprintf("任务刷新时间较长，任务ID：%s, 耗时：%v", mjId, elapsed))
	}

	// 返回更新后的任务信息
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("任务状态已刷新，从 %s(%s) 更新为 %s(%s)",
			originStatus, originProgress,
			task.Status, task.Progress),
		"data": task,
	})
}

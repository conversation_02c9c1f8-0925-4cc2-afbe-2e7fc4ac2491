// controller/dujiaoka.go

package controller

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/model"
	"net/http"
	"strings"
)

type DujiaokaOrderCallback struct {
	Title       string `json:"title"`
	OrderSN     string `json:"order_sn"`
	Email       string `json:"email"`
	ActualPrice string `json:"actual_price"`
	OrderInfo   string `json:"order_info"`
	GoodID      int    `json:"good_id"`
	GoodName    string `json:"gd_name"`
}

func DujiaokaCallback(c *gin.Context) {
	var callbackData DujiaokaOrderCallback

	if err := c.ShouldBindJSON(&callbackData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}
	// 回调参数结构如下
	//  {Title:测试商品请勿点击 x 1 OrderSN:LOSJZJXMNMGKBWQN Email:<EMAIL> ActualPrice:0.00 OrderInfo:测试卡密xxxxxxxxxxxx————感谢付费支持Shellgpt pro，我们诚邀您加入付费用户专享QQ群xxxx GoodID:2 GoodName:测试商品请勿点击}
	logger.SysLog(fmt.Sprintf("Received callback: %+v\n", callbackData))

	// 假设 OrderInfo 字段包含了 token key
	tokenKey := callbackData.OrderInfo

	if strings.HasPrefix(tokenKey, "sk-") {
		tokenKey = strings.TrimPrefix(tokenKey, "sk-")
	}

	// 根据 token key 查找对应的 token
	_, _, err := model.CacheGetTokenByKey(tokenKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get token"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":  "Token activated successfully",
		"order_sn": callbackData.OrderSN,
		"email":    callbackData.Email,
	})
}

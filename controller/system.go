package controller

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/middleware"
	"github.com/songquanpeng/one-api/utils"
)

type SystemConfigService struct{}

func GetServerInfo(c *gin.Context) {
	var s utils.Server
	s.Os = utils.InitOS()
	s.Uptime = time.Now().Unix() - config.SystemStartTime
	var err error
	if s.Cpu, err = utils.InitCPU(); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	if s.<PERSON>, err = utils.InitRAM(); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.<PERSON>(),
		})
		return
	}
	if s.<PERSON>, err = utils.InitDisk(); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    s,
	})
}

func ResetRateLimit(c *gin.Context) {
	if config.TurnstileSecretKey == "" {
		// 未开启验证码则不允许调用此接口清理限流器
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "未配置TurnstileSecretKey",
		})
	}
	if common.RedisEnabled {
		// 清理GW
		key := "rateLimit:" + "GW" + c.ClientIP()
		err := common.RedisDel(key)
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		// 清理"CT"
		key = "rateLimit:" + "CT" + c.ClientIP()
		err = common.RedisDel(key)
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
	} else {
		middleware.MyInMemoryRateLimiter.ClearItemByKey(fmt.Sprintf("%s%s", "GW", c.ClientIP()))
		middleware.MyInMemoryRateLimiter.ClearItemByKey(fmt.Sprintf("%s%s", "CT", c.ClientIP()))
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "成功清除限流",
	})
}

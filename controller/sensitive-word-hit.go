package controller

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/model"
)

// GetSensitiveWordHits 获取敏感词命中记录列表
func GetSensitiveWordHits(c *gin.Context) {
	p, _ := strconv.Atoi(c.Query("p"))
	if p < 0 {
		p = 0
	}
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}

	userId, _ := strconv.Atoi(c.Query("userId"))
	tokenId, _ := strconv.Atoi(c.Query("tokenId"))
	sensitiveWordId, _ := strconv.Atoi(c.Query("sensitiveWordId"))

	startTimeStr := c.Query("startTime")
	endTimeStr := c.Query("endTime")

	var startTime, endTime time.Time
	if startTimeStr != "" {
		startTime, _ = time.Parse(time.RFC3339, startTimeStr)
	}
	if endTimeStr != "" {
		endTime, _ = time.Parse(time.RFC3339, endTimeStr)
	}

	hits, err := model.GetSensitiveWordHits(p*pageSize, pageSize, userId, tokenId, sensitiveWordId, startTime, endTime)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	count, err := model.CountSensitiveWordHits(userId, tokenId, sensitiveWordId, startTime, endTime)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    hits,
		"count":   count,
	})
}

// DeleteSensitiveWordHit 删除指定的敏感词命中记录
func DeleteSensitiveWordHit(c *gin.Context) {
	id, _ := strconv.Atoi(c.Param("id"))
	err := model.DeleteSensitiveWordHit(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
}

// DeleteSensitiveWordHitsByTime 按时间删除敏感词命中记录
func DeleteSensitiveWordHitsByTime(c *gin.Context) {
	beforeTimeStr := c.Query("beforeTime")
	if beforeTimeStr == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "beforeTime参数不能为空",
		})
		return
	}

	beforeTime, err := time.Parse(time.RFC3339, beforeTimeStr)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "时间格式错误",
		})
		return
	}

	err = model.DeleteSensitiveWordHitsByTime(beforeTime)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
}

package controller

import (
	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/model"
	"net/http"
)

func GetDashboardSummary(c *gin.Context) {

	// 获取当前用户
	userId := c.GetInt("id")
	dashboardSummary, err := model.GetDashboardSummary(userId)
	if err != nil {
		c.JSO<PERSON>(http.StatusOK, gin.H{
			"success": false,
			"message": err.<PERSON><PERSON>r(),
		})
		return
	}
	c.<PERSON><PERSON>(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    dashboardSummary,
	})
	return
}

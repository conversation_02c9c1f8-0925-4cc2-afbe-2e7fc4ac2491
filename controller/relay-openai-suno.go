package controller

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/controller/optimizer"
	"io"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/client"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/relay/adaptor/openai"
	billingratio "github.com/songquanpeng/one-api/relay/billing/ratio"
	"github.com/songquanpeng/one-api/relay/channeltype"
	"github.com/songquanpeng/one-api/relay/meta"
	relaymodel "github.com/songquanpeng/one-api/relay/model"
)

// 请求结构体定义
type MusicRequestDTO struct {
	CustomMode           bool   `json:"custom_mode"`
	Input                Input  `json:"input"`
	GptDescriptionPrompt string `json:"gpt_description_prompt"`
	MakeInstrumental     bool   `json:"make_instrumental"`
}

type Input struct {
	Prompt         string `json:"prompt"`
	Title          string `json:"title"`
	Tags           string `json:"tags"`
	ContinueAt     int    `json:"continue_at"`
	ContinueClipID string `json:"continue_clip_id"`
}

// 响应结构体定义
type MusicResponseDTO struct {
	Code    int    `json:"code"`
	Data    Data   `json:"data"`
	Message string `json:"message"`
}

type Data struct {
	TaskID   string           `json:"task_id"`
	Status   string           `json:"status"`
	Input    string           `json:"input"`
	Clips    map[string]Clip  `json:"clips"`
	Metadata ResponseMetadata `json:"metadata"`
}

type Clip struct {
	ID                string       `json:"id"`
	VideoURL          string       `json:"video_url"`
	AudioURL          string       `json:"audio_url"`
	ImageURL          string       `json:"image_url"`
	ImageLargeURL     string       `json:"image_large_url"`
	IsVideoPending    bool         `json:"is_video_pending"`
	MajorModelVersion string       `json:"major_model_version"`
	ModelName         string       `json:"model_name"`
	Metadata          ClipMetadata `json:"metadata"`
	IsLiked           bool         `json:"is_liked"`
	UserID            string       `json:"user_id"`
	DisplayName       string       `json:"display_name"`
	Handle            string       `json:"handle"`
	IsHandleUpdated   bool         `json:"is_handle_updated"`
	IsTrashed         bool         `json:"is_trashed"`
	Reaction          interface{}  `json:"reaction"`
	CreatedAt         string       `json:"created_at"`
	Status            string       `json:"status"`
	Title             string       `json:"title"`
	PlayCount         int          `json:"play_count"`
	UpvoteCount       int          `json:"upvote_count"`
	IsPublic          bool         `json:"is_public"`
}

type ClipMetadata struct {
	Tags                 string      `json:"tags"`
	Prompt               string      `json:"prompt"`
	GptDescriptionPrompt string      `json:"gpt_description_prompt"`
	AudioPromptID        string      `json:"audio_prompt_id"`
	History              interface{} `json:"history"`
	ConcatHistory        interface{} `json:"concat_history"`
	Type                 string      `json:"type"`
	Duration             float64     `json:"duration"`
	RefundCredits        bool        `json:"refund_credits"`
	Stream               bool        `json:"stream"`
	ErrorType            string      `json:"error_type"`
	ErrorMessage         string      `json:"error_message"`
}

type ResponseMetadata struct {
	CreatedAt   string `json:"created_at"`
	StartedAt   string `json:"started_at"`
	EndedAt     string `json:"ended_at"`
	QuotaFrozen int    `json:"quota_frozen"`
	QuotaUsage  int    `json:"quota_usage"`
}

// 提交任务到Suno
func submitTaskToSuno(c *gin.Context, meta *meta.Meta, resp *http.Response, dto MusicRequestDTO, baseUrl string) (string, error) {
	requestBody, err := json.Marshal(dto)
	if err != nil {
		return "", err
	}
	url := "/suno/v1/music"
	if meta.RequestModel == "suno-v3" {
		url = "/suno/v1/v3.0/music"
	}
	req, err := http.NewRequest("POST", baseUrl+url, bytes.NewBuffer(requestBody))
	apiKey := c.Request.Header.Get("Authorization")
	apiKey = strings.TrimPrefix(apiKey, "Bearer ")
	req.Header.Set("Authorization", apiKey)
	req.Header.Set("Content-Type", c.Request.Header.Get("Content-Type"))
	resp, err = client.HTTPClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	var musicResponse MusicResponseDTO
	err = json.Unmarshal(body, &musicResponse)
	if err != nil {
		return "", err
	}
	taskID := musicResponse.Data.TaskID
	if taskID == "" {
		return "", errors.New("task_id is empty")
	}
	return taskID, nil
}

// 轮询任务状态
func pollSunoTaskStatusStream(c *gin.Context, taskID string, baseUrl string, modelName string) (*MusicResponseDTO, error) {
	//userId := c.GetInt("id")
	var musicResponse MusicResponseDTO
	common.SetEventStreamHeaders(c)
	c.Writer.Header().Set("Suno-Mode", "1")
	c.Stream(func(w io.Writer) bool {
		for {
			url := ""
			if modelName == "suno-v3" {
				url = "/suno/v1/v3.0/music"
			} else {
				url = "/suno/v1/music"
			}
			req, err := http.NewRequest("GET", fmt.Sprintf(baseUrl+url+"/%s", taskID), nil)
			apiKey := c.Request.Header.Get("Authorization")
			apiKey = strings.TrimPrefix(apiKey, "Bearer ")
			req.Header.Set("Authorization", apiKey)
			req.Header.Set("Content-Type", c.Request.Header.Get("Content-Type"))
			resp, err := client.HTTPClient.Do(req)
			if err != nil {
				return false
			}
			defer resp.Body.Close()

			body, err := ioutil.ReadAll(resp.Body)
			if err != nil {
				return false
			}
			err = json.Unmarshal(body, &musicResponse)
			if err != nil {
				return false
			}
			rsContent := ""
			if musicResponse.Data.Status == "completed" {
				// 遍历 clips
				for _, clip := range musicResponse.Data.Clips {
					rsContent += fmt.Sprintf("音频地址: %s\n视频地址: %s\n图片地址: %s", clip.AudioURL, clip.VideoURL, clip.ImageURL)
					rsContent += "\n"
				}
			} else {
				rsContent = fmt.Sprintf("当前状态: %s", musicResponse.Data.Status)
			}

			// 创建并填充数据
			streamResponses := openai.SunoChatCompletionsStreamResponse{
				Id:      musicResponse.Data.TaskID,
				Object:  "chat.completion.chunk",
				Created: helper.GetTimestamp(),
				Model:   modelName,
				Choices: []openai.SunoChatCompletionsStreamResponseChoice{
					{
						Delta: struct {
							Content string `json:"content"`
							Status  string `json:"status"`
						}{
							Content: rsContent,
							Status:  musicResponse.Data.Status,
						},
						FinishReason: nil,
					},
				},
			}
			// 转换为JSON
			jsonData, err := json.Marshal(streamResponses)
			c.Render(-1, common.CustomEvent{Data: "data: " + fmt.Sprintf("%s", string(jsonData))})
			c.Writer.Flush()
			if musicResponse.Data.Status == "completed" || musicResponse.Data.Status == "failed" {
				break
			}
			// 等待一段时间后再次检查任务状态
			time.Sleep(5 * time.Second)
		}
		c.Render(-1, common.CustomEvent{Data: "data: [DONE]"})
		return false
	})

	return &musicResponse, nil
}

func relaySunoMusicHelper(c *gin.Context, relayMode int) *relaymodel.ErrorWithStatusCode {
	ctx := c.Request.Context()
	meta := meta.GetByContext(c)

	billingType := c.GetInt(ctxkey.BillingType)
	channelType := c.GetInt("channel")
	userId := c.GetInt("id")
	group := c.GetString("group")
	_startTime := helper.GetTimestamp()

	var textRequest relaymodel.GeneralOpenAIRequest

	err := common.UnmarshalBodyReusable(c, &textRequest)
	if err != nil {
		return openai.ErrorWrapper(err, "bind_request_body_failed", http.StatusBadRequest)
	}

	// request validation
	if textRequest.GetLastMessageContent() == "" {
		return openai.ErrorWrapper(errors.New("prompt is required"), "required_field_missing", http.StatusBadRequest)
	}

	baseURL := channeltype.ChannelBaseURLs[channelType]
	// 用于保存详细聊天记录日志
	var detailPrompt = ""
	var detailCompletion = ""
	if c.GetString("base_url") != "" {
		baseURL = c.GetString("base_url")
	}
	var promptTokens int
	var completionTokens int
	promptTokens = len(textRequest.GetLastMessageContent())
	modelRatio := billingratio.GetModelRatio(textRequest.Model, meta.ChannelType)
	groupRatio := billingratio.GetGroupRatio(group)
	ratio := modelRatio * groupRatio
	userQuota, quotaExpireTime, err := model.CacheGetUserQuotaAndExpireTime(ctx, userId)
	if err != nil {
		return openai.ErrorWrapper(err, "get_user_quota_failed", http.StatusInternalServerError)
	}
	if config.QuotaExpireEnabled && quotaExpireTime > 0 && quotaExpireTime < helper.GetTimestamp() {
		model.RecordSysLogToDBAndFile(c.Request.Context(), c.GetString(helper.RequestIdKey), model.LogTypeSystemErr, userId, 0, "suno", "", "", "user quota expired", "尚未解析")
		return openai.ErrorWrapper(errors.New("user quota expired"), "user_quota_expired", http.StatusForbidden)
	}
	sizeRatio := 1.0

	quota := int64(ratio * sizeRatio * 1000)
	var modelFixedPrice float64
	var modelFixedPriceErr error
	if billingType == common.BillingTypeByCount {
		modelFixedPrice, modelFixedPriceErr = billingratio.GetModelFixedPrice(textRequest.Model)
		if modelFixedPriceErr != nil {
			return openai.ErrorWrapper(modelFixedPriceErr, "model_fixed_price_not_config", http.StatusForbidden)
		}
		quota = int64(modelFixedPrice * groupRatio * 500000)
	}
	if userQuota <= 0 || userQuota-quota < 0 {
		return openai.ErrorWrapper(errors.New(fmt.Sprintf("user [%d] quota [%d] quota [%d] is not enough", meta.UserId, userQuota, quota)), "insufficient_user_quota", http.StatusForbidden)
	}

	// 校验入参是否包含"直接返回"
	if strings.Contains(textRequest.GetLastMessageContent(), "直接返回") {
		return openai.ErrorWrapper(errors.New("prompt contains invalid content"), "invalid_midjourney_prompt", http.StatusBadRequest)
	}
	var resp *http.Response
	isStream := textRequest.Stream
	var musicRequest MusicRequestDTO
	musicRequest.CustomMode = true
	musicRequest.Input.Prompt = textRequest.GetLastMessageContent()
	musicRequest.Input.Title = helper.GenerateUUID()
	musicRequest.Input.Tags = "suno"
	musicRequest.Input.ContinueAt = 0
	musicRequest.Input.ContinueClipID = ""
	taskId, err := submitTaskToSuno(c, meta, resp, musicRequest, baseURL)
	if err != nil {
		return openai.ErrorWrapper(err, "submit_task_to_suno_failed", http.StatusInternalServerError)
	}
	isStream = isStream || strings.HasPrefix(resp.Header.Get("Content-Type"), "text/event-stream")

	tokenName := c.GetString("token_name")
	tokenKey := c.GetString("token_key")
	tokenId := c.GetInt("token_id")
	channelId := c.GetInt("channel_id")
	channelName := c.GetString("channel_name")

	defer func(ctx context.Context) {
		go func() {
			// 即使是请求错误导致的消费为0也记录日志,并且记录耗时
			requestDuration := helper.GetTimestamp() - _startTime
			logContent := fmt.Sprintf("模型倍率 %.2f，分组倍率 %.2f，用时 %d秒", modelRatio, groupRatio, requestDuration)
			if billingType == common.BillingTypeByCount {
				logContent = fmt.Sprintf("模型按次使用固定价格 %.6f，分组倍率 %.2f，用时 %d秒", modelFixedPrice, groupRatio, requestDuration)
			}
			err := model.PostConsumeTokenQuota(tokenId, quota)
			if err != nil {
				logger.Error(ctx, "error consuming token remain quota: "+err.Error())
			}
			err = model.CacheUpdateUserQuota(ctx, userId)
			if err != nil {
				logger.Error(ctx, "error update user quota cache: "+err.Error())
			}
			model.UpdateUserUsedQuotaAndRequestCount(userId, quota)
			model.UpdateChannelUsedQuota(channelId, quota)
			// 即使是请求错误导致的消费为0也记录日志,并且记录耗时
			createdLog := model.RecordConsumeLog(ctx, userId, channelId, promptTokens, completionTokens, "suno", tokenName, tokenKey, channelName, int(quota), requestDuration, false, logContent)
			helper.SafeGoroutine(func() {
				// 记录详细聊天记录
				model.RecordLogExtend(ctx, createdLog, detailPrompt, detailCompletion, "", meta.UpstreamResponse, meta.FullResponse, meta.RequestURLPath)
				// 推送优化器
				optimizer.RecordConsumeLog(createdLog)
			})

		}()
	}(c.Request.Context())
	_, err = pollSunoTaskStatusStream(c, taskId, baseURL, "suno")
	if err != nil {
		return openai.ErrorWrapper(err, "poll_task_status_stream_failed", http.StatusInternalServerError)
	}
	return nil
}

package controller

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/client"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/controller/optimizer"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/relay/adaptor/openai"
	"github.com/songquanpeng/one-api/relay/billing/ratio"
	"github.com/songquanpeng/one-api/relay/channeltype"
	"github.com/songquanpeng/one-api/relay/controller"
	"github.com/songquanpeng/one-api/relay/meta"
	relaymodel "github.com/songquanpeng/one-api/relay/model"
	"github.com/songquanpeng/one-api/relay/util"
	"io"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

func relayOpenaiSoraChatTextHelper(c *gin.Context, relayMode int) *relaymodel.ErrorWithStatusCode {
	ctx := c.Request.Context()
	meta := meta.GetByContext(c)

	imageModel := "sora-1.0-turbo"
	imageSize := "1920x1080"

	tokenId := c.GetInt("token_id")
	channelType := c.GetInt("channel")
	channelId := c.GetInt("channel_id")
	userId := c.GetInt("id")
	group := c.GetString("group")
	_startTime := helper.GetTimestamp()

	imageInMarkdown := c.GetBool("image_in_markdown")

	var textRequest relaymodel.GeneralOpenAIRequest
	err := common.UnmarshalBodyReusable(c, &textRequest)
	if err != nil {
		return openai.ErrorWrapper(err, "bind_request_body_failed", http.StatusBadRequest)
	}
	bodyCopy, _ := common.PreserveRequestBody(c)
	messagePrompt := ""
	// 只取textRequest.Messages的最后一条
	if len(textRequest.Messages) > 0 {
		textRequest.Messages = textRequest.Messages[len(textRequest.Messages)-1:]
		messagePrompt = textRequest.Messages[len(textRequest.Messages)-1].StringContent()
	}
	// Model validation
	if textRequest.Model != "" {
		imageModel = textRequest.Model
	}

	imageCostRatio, _ := ratio.ImageSizeRatios[imageModel][imageSize]

	// Prompt validation
	if messagePrompt == "" {
		return openai.ErrorWrapper(errors.New("prompt is required"), "prompt_missing", http.StatusBadRequest)
	}

	// Check prompt length
	if len(messagePrompt) > ratio.ImagePromptLengthLimitations["dall-e-2"] {
		return openai.ErrorWrapper(errors.New("prompt is too long"), "prompt_too_long", http.StatusBadRequest)
	}

	// 校验入参是否包含"直接返回"
	if strings.Contains(messagePrompt, "直接返回") {
		return openai.ErrorWrapper(errors.New("prompt contains invalid content"), "invalid_dall_e_prompt", http.StatusBadRequest)
	}

	// 构造实际请求
	var imageRequest openai.ImageRequest
	imageRequest.Prompt = messagePrompt
	imageRequest.Size = imageSize
	imageRequest.Model = imageModel
	//imageRequest.Quality = "hd"

	// map model name
	isModelMapped := false
	textRequest.Model, isModelMapped = util.GetMappedModelName(textRequest.Model, meta.ModelMapping, meta.ModelMappingArr)
	meta.ActualModelName = textRequest.Model
	baseURL := channeltype.ChannelBaseURLs[channeltype.SoraWebUi]
	requestURL := "/api/generate"
	if c.GetString("base_url") != "" {
		baseURL = c.GetString("base_url")
	}
	fullRequestURL := fmt.Sprintf("%s%s", baseURL, requestURL)
	var requestBody io.Reader
	if isModelMapped || channelType == channeltype.Azure { // make Azure channel request body
		jsonStr, err := json.Marshal(imageRequest)
		if err != nil {
			return openai.ErrorWrapper(err, "marshal_text_request_failed", http.StatusInternalServerError)
		}
		requestBody = bytes.NewBuffer(jsonStr)
	} else {
		jsonStr, err := json.Marshal(imageRequest)
		if err != nil {
			return openai.ErrorWrapper(err, "marshal_text_request_failed", http.StatusInternalServerError)
		}
		requestBody = bytes.NewBuffer(jsonStr)
	}

	modelRatio := ratio.GetModelRatio(imageModel, meta.ChannelType)
	groupRatio := ratio.GetGroupRatio(group)
	ratio := modelRatio * groupRatio
	userQuota, quotaExpireTime, err := model.CacheGetUserQuotaAndExpireTime(ctx, userId)
	if err != nil {
		return openai.ErrorWrapper(err, "get_user_quota_failed", http.StatusInternalServerError)
	}
	if config.QuotaExpireEnabled && quotaExpireTime > 0 && quotaExpireTime < helper.GetTimestamp() {
		model.RecordSysLogToDBAndFile(c.Request.Context(), c.GetString(helper.RequestIdKey), model.LogTypeSystemErr, userId, 0, imageModel, "", "", "user quota expired", "尚未解析")
		return openai.ErrorWrapper(errors.New("user quota expired"), "user_quota_expired", http.StatusForbidden)
	}
	quota := int64(ratio*imageCostRatio*1000) * int64(imageRequest.N)

	if userQuota <= 0 || userQuota-quota < 0 {
		return openai.ErrorWrapper(errors.New(fmt.Sprintf("user [%d] quota [%d] consumedQuota [%d] is not enough", meta.UserId, userQuota, quota)), "insufficient_user_quota", http.StatusForbidden)
	}

	req, err := http.NewRequest(c.Request.Method, fullRequestURL, requestBody)
	if err != nil {
		return openai.ErrorWrapper(err, "new_request_failed", http.StatusInternalServerError)
	}
	token := c.Request.Header.Get("Authorization")
	if channelType == channeltype.Azure { // Azure authentication
		token = strings.TrimPrefix(token, "Bearer ")
		req.Header.Set("api-key", token)
	} else {
		req.Header.Set("Authorization", token)
	}

	req.Header.Set("Content-Type", c.Request.Header.Get("Content-Type"))
	req.Header.Set("Accept", c.Request.Header.Get("Accept"))

	resp, err := client.HTTPClient.Do(req)
	if err != nil {
		return openai.ErrorWrapper(err, "do_request_failed", http.StatusInternalServerError)
	}

	err = req.Body.Close()
	if err != nil {
		return openai.ErrorWrapper(err, "close_request_body_failed", http.StatusInternalServerError)
	}
	err = c.Request.Body.Close()
	if err != nil {
		return openai.ErrorWrapper(err, "close_request_body_failed", http.StatusInternalServerError)
	}
	if resp.StatusCode != http.StatusOK {
		return controller.RelayErrorHandler(resp)
	}
	var textResponse openai.ImageResponse
	var responseBody []byte
	defer func(ctx context.Context) {
		err := model.PostConsumeTokenQuota(tokenId, quota)
		if err != nil {
			logger.SysError("error consuming token remain quota: " + err.Error())
		}
		err = model.CacheUpdateUserQuota(ctx, userId)
		if err != nil {
			logger.SysError("error update user quota cache: " + err.Error())
		}
		// 即使是请求错误导致的消费为0也记录日志,并且记录耗时
		requestDuration := helper.GetTimestamp() - _startTime
		logContent := fmt.Sprintf("模型倍率 %.2f，分组倍率 %.2f，用时 %d秒", modelRatio, groupRatio, requestDuration)
		tokenName := c.GetString("token_name")
		tokenKey := c.GetString("token_key")
		channelName := c.GetString("channel_name")
		createdLog := model.RecordConsumeLog(ctx, userId, channelId, 0, 0, imageModel, tokenName, tokenKey, channelName, int(quota), requestDuration, false, logContent)
		model.UpdateUserUsedQuotaAndRequestCount(userId, quota)
		channelId := c.GetInt("channel_id")
		model.UpdateChannelUsedQuota(channelId, quota)
		helper.SafeGoroutine(func() {
			// 记录详细聊天记录
			model.RecordLogExtend(ctx, createdLog, string(bodyCopy), string(responseBody), "", meta.UpstreamResponse, meta.FullResponse, meta.RequestURLPath)
			// 推送优化器
			optimizer.RecordConsumeLog(createdLog)
		})
	}(c.Request.Context())

	responseBody, err = io.ReadAll(resp.Body)

	if err != nil {
		return openai.ErrorWrapper(err, "read_response_body_failed", http.StatusInternalServerError)
	}
	err = resp.Body.Close()
	if err != nil {
		return openai.ErrorWrapper(err, "close_response_body_failed", http.StatusInternalServerError)
	}
	err = json.Unmarshal(responseBody, &textResponse)
	if err != nil {
		return openai.ErrorWrapper(err, "unmarshal_response_body_failed", http.StatusInternalServerError)
	}

	uuid := helper.GetUUID()
	common.SetEventStreamHeaders(c)
	// 如果设定了图片包裹markdown,则需要处理
	// 遍历textResponse.Data切片
	for _, d := range textResponse.Data {
		rsContent := ""
		if imageInMarkdown {
			rsContent = fmt.Sprintf("[![图片描述](%s)](%s)[点击下载](%s)", d.Url, d.Url, d.Url)
		} else {
			rsContent = d.Url
		}
		// 创建并填充数据
		streamResponses := openai.ChatCompletionsStreamResponse{
			Id:      uuid,
			Object:  "chat.completion.chunk",
			Created: 1234567890,
			Model:   imageModel,
			Choices: []openai.ChatCompletionsStreamResponseChoice{
				{
					//Delta: struct {
					//	Content      string `json:"content"`
					//	Role         string `json:"role"`
					//	FunctionCall struct {
					//		Name      string `json:"name"`
					//		Arguments string `json:"arguments"`
					//	} `json:"function_call,omitempty"`
					//	ToolCalls []struct {
					//		Index    int    `json:"index"`
					//		Id       string `json:"id"`
					//		Type     string `json:"type"`
					//		Function struct {
					//			Name      string `json:"name"`
					//			Arguments string `json:"arguments"`
					//		} `json:"function,omitempty"`
					//	} `json:"tool_calls,omitempty"`
					//}{
					//	Content: rsContent,
					//},
					Delta: relaymodel.Message{
						Content: rsContent,
					},
					FinishReason: nil,
				},
			},
		}
		// 转换成JSON格式
		// 转换为JSON
		jsonData, err := json.Marshal(streamResponses)
		if err != nil {
			logger.SysError("error marshal streamResponses: " + err.Error())
		}
		c.Render(-1, common.CustomEvent{Data: "data: " + fmt.Sprintf("%s", string(jsonData))})
		c.Writer.Flush()
	}
	c.Render(-1, common.CustomEvent{Data: "data: [DONE]"})
	c.Writer.Flush()
	return nil
}

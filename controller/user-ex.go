package controller

import (
	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/model"
	"net/http"
	"strconv"
)

// CreateSelfUserEx 创建用户扩展信息
func CreateSelfUserEx(c *gin.Context) {
	id := c.GetInt("id")
	var userEx model.UserExtend
	userEx.UserId = id
	err := userEx.Insert()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "创建用户扩展信息失败" + err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "创建用户扩展信息成功",
	})
	return
}

func GetSelfChatRecord(c *gin.Context) {
	id := c.GetInt("id")
	userEx, err := model.GetChatRecordByUserId(id)
	if err != nil {
		c.<PERSON><PERSON>(http.StatusOK, gin.H{
			"success": false,
			"message": "获取聊天记录失败" + err.Error(),
		})
		return
	}
	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    userEx.ChatRecordJsonData,
	})
	return
}

func SaveSelfChatRecord(c *gin.Context) {
	id := c.GetInt("id")
	var userEx model.UserExtend
	userEx.UserId = id
	// 入参body是聊天记录,对应userEx.ChatRecordJsonData
	//err := json.NewDecoder(c.Request.Body).Decode(&userEx.ChatRecordJsonData)
	body, err := common.PreserveRequestBody(c)
	userEx.ChatRecordJsonData = string(body)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数" + err.Error(),
		})
		return
	}
	// 保存聊天记录
	err = userEx.SaveChatRecordByUserId()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "保存聊天记录失败" + err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "保存聊天记录成功",
	})
	return
}

// DeleteSelfChatRecord 删除聊天记录
func DeleteSelfChatRecord(c *gin.Context) {
	id := c.GetInt("id")
	var userEx model.UserExtend
	userEx.UserId = id
	// 保存聊天记录
	err := userEx.DeleteChatRecordByUserId()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "删除聊天记录失败" + err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除聊天记录成功",
	})
	return
}

// GetSelfEducationCertification 获取用户教育认证信息
func GetSelfEducationCertification(c *gin.Context) {
	id := c.GetInt("id")
	userEx, err := model.VerifyUserEducationCertification(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取教育认证信息失败" + err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取教育认证信息成功",
		"data":    userEx,
	})
	return
}

// GetAllUserExtendExceptChatRecord  获取所有用户扩展信息，除了聊天记录
func GetAllUserExtendExceptChatRecord(c *gin.Context) {
	userEx, err := model.GetAllUserExtendExceptChatRecord()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取所有用户扩展信息失败" + err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取所有用户扩展信息成功",
		"data":    userEx,
	})
	return
}

// GetSelfUserExExceptChatRecord 获取用户自己的扩展信息，除了聊天记录
func GetSelfUserExExceptChatRecord(c *gin.Context) {
	id := c.GetInt("id")
	userEx, err := model.GetUserExExceptChatRecordById(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取用户扩展信息失败" + err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取用户扩展信息成功",
		"data":    userEx,
	})
	return
}

// GetUserExExceptChatRecordById 获取用户扩展信息，除了聊天记录
func GetUserExExceptChatRecordById(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("user_id")) //user_id而不是表的id
	userEx, err := model.GetUserExExceptChatRecordById(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取用户扩展信息失败" + err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取用户扩展信息成功",
		"data":    userEx,
	})
	return
}

type TransferAffQuotaRequest struct {
	Quota int64 `json:"quota" binding:"required"`
}

func TransferAffQuota(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.GetInt("id")
	user, err := model.GetUserExByUserId(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	tran := TransferAffQuotaRequest{}
	if err := c.ShouldBindJSON(&tran); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	err = user.TransferAffQuotaToQuota(ctx, tran.Quota)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "划转失败 " + err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "划转成功",
	})
}

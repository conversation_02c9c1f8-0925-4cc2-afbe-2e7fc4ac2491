package controller

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/relay/billing/ratio"
)

// CreateAgency 创建代理商（仅管理员可用）
func CreateAgency(c *gin.Context) {
	var agency model.Agency
	err := c.ShouldBindJSON(&agency)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}

	// 检查用户是否存在
	originUser, err := model.GetUserById(agency.UserId, false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "用户不存在",
		})
		return
	}

	// 检查用户是否已经是代理商
	isAgent, _ := model.IsUserAgent(agency.UserId)
	if isAgent {
		c.<PERSON>(http.StatusOK, gin.H{
			"success": false,
			"message": "该用户已经是代理商",
		})
		return
	}

	// 判断管理员不可以成为代理商
	if originUser.Role > model.RoleCommonUser {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "只有普通用户可以成为代理商",
		})
		return
	}
	// 创建代理商
	err = model.CreateAgency(&agency)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	originUser.Role = model.RoleAgencyUser
	err = originUser.Update(false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "更新用户角色失败",
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "代理商创建成功",
	})
}

// GetSelfAgency 获取当前用户的代理商信息
func GetSelfAgency(c *gin.Context) {
	userId := c.GetInt("id")
	agency, err := model.GetAgencyByUserId(userId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取代理商信息失败",
		})
		return
	}

	// 获取下挂用户数量
	userCount, err := model.CountUsersByAgencyId(agency.UserId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取用户数量失败",
		})
		return
	}

	agency.UserCount = userCount

	// 如果为空则填充系统默认值
	if agency.TopupGroupRatio == "" {
		agency.TopupGroupRatio = ratio.TopupGroupRatio2JSONString()
	}

	// 如果为空则填充系统默认值
	if agency.NewHomeConf == "" {
		agency.NewHomeConf = config.NewHomeConf
	}

	agency.SystemTopupGroupRatio = ratio.TopupGroupRatio2JSONString()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    agency,
	})
}

// GetAgencyStats 获取代理商统计信息
func GetAgencyStats(c *gin.Context) {
	userId := c.GetInt("id")
	agency, err := model.GetAgencyByUserId(userId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取代理商信息失败",
		})
		return
	}

	// 获取用户数量
	userCount, err := model.CountUsersByAgencyId(agency.Id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取用户数量失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"user_count": userCount,
		},
	})
}

// UpdateSelfAgency 更新当前用户的代理商信息
func UpdateSelfAgency(c *gin.Context) {
	userId := c.GetInt("id")
	var agency model.Agency
	err := c.ShouldBindJSON(&agency)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}

	// 校验该代理商是否是当前用户的代理商
	agencyInDB, err := model.GetAgencyByUserId(userId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取代理商信息失败",
		})
		return
	}
	if agencyInDB.Id != agency.Id {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无权修改其他代理商信息",
		})
		return
	}

	// 校验佣金比例必须介于0-1之间
	if agency.CommissionRate < 0 || agency.CommissionRate > 1 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "佣金比例必须介于0-1之间",
		})
		return
	}
	// 校验topup_group_ratio这个json中的每一项是否都高于系统预设
	if agency.TopupGroupRatio != "" {
		// string转map
		jsonBytes := []byte(agency.TopupGroupRatio)
		// 转map
		var topupGroupRatio map[string]float64
		err = json.Unmarshal(jsonBytes, &topupGroupRatio)
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "无效的充值倍率json格式",
			})
			return
		}
		// 遍历校验
		for key, value := range topupGroupRatio {
			groupRatio := ratio.GetTopupGroupRatio(key)
			if value < groupRatio {
				c.JSON(http.StatusOK, gin.H{
					"success": false,
					"message": "充值倍率不得低于管理员预设",
				})
				return
			}
		}
	}

	// 只能改这几个字段
	agencyInDB.UserId = userId
	agencyInDB.Name = agency.Name
	agencyInDB.Logo = agency.Logo
	agencyInDB.ServerAddress = agency.ServerAddress
	agencyInDB.FileSystemServerAddress = agency.FileSystemServerAddress
	agencyInDB.HomepageContent = agency.HomepageContent
	agencyInDB.AboutContent = agency.AboutContent
	agencyInDB.TopupGroupRatio = agency.TopupGroupRatio
	agencyInDB.Notice = agency.Notice
	agencyInDB.NewHomeConf = agency.NewHomeConf

	err = model.UpdateAgency(agencyInDB)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "更新成功",
	})
}

// CountAgencies 获取代理商数量
func CountAgencies(c *gin.Context) {
	count, err := model.CountAgencies()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取代理商数量失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"count": count,
		},
	})
}

// GetAgencyPrice 获取代理商自定义价格
func GetAgencyPrice(c *gin.Context) {
	//userId := c.GetInt("id")
	//agency, err := model.GetAgencyByUserId(userId)
	//if err != nil {
	//	c.JSON(http.StatusOK, gin.H{
	//		"success": false,
	//		"message": "获取代理商信息失败",
	//	})
	//	return
	//}
	//
	//c.JSON(http.StatusOK, gin.H{
	//	"success": true,
	//	"data": gin.H{
	//		"price_multiplier": agency.PriceMultiplier,
	//	},
	//})
}

// SetAgencyHomepage 设置代理商自定义首页
func SetAgencyHomepage(c *gin.Context) {
	userId := c.GetInt("id")
	var homepageData struct {
		Homepage string `json:"homepage"`
	}
	if err := c.ShouldBindJSON(&homepageData); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}

	agency, err := model.GetAgencyByUserId(userId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取代理商信息失败",
		})
		return
	}

	agency.HomepageContent = homepageData.Homepage
	if err := model.UpdateAgency(agency); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "更新首页失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "首页设置成功",
	})
}

// GetAgencyHomepage 获取代理商自定义首页
func GetAgencyHomepage(c *gin.Context) {
	userId := c.GetInt("id")
	agency, err := model.GetAgencyByUserId(userId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取代理商信息失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"homepage": agency.HomepageContent,
		},
	})
}

// SetAgencyLogo 设置代理商logo
func SetAgencyLogo(c *gin.Context) {
	userId := c.GetInt("id")
	var logoData struct {
		Logo string `json:"logo"`
	}
	if err := c.ShouldBindJSON(&logoData); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}

	agency, err := model.GetAgencyByUserId(userId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取代理商信息失败",
		})
		return
	}

	agency.Logo = logoData.Logo
	if err := model.UpdateAgency(agency); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "更新Logo失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Logo设置成功",
	})
}

// GetAgencyLogo 获取代理商logo
func GetAgencyLogo(c *gin.Context) {
	userId := c.GetInt("id")
	agency, err := model.GetAgencyByUserId(userId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取代理商信息失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"logo": agency.Logo,
		},
	})
}

// GetAllAgencies 获取所有代理商列表（仅管理员可用）
func GetAllAgencies(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	agencies, _, err := model.ListAgencies(page, pageSize)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 获取下挂用户数量
	for i := range agencies {
		userCount, err := model.CountUsersByAgencyId(agencies[i].UserId)
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "获取用户数量失败",
			})
			return
		}
		agencies[i].UserCount = userCount
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    agencies,
	})
}

// GetAgency 获取特定代理商信息（仅管理员可用）
func GetAgency(c *gin.Context) {
	id, _ := strconv.Atoi(c.Param("id"))
	agency, err := model.GetAgencyById(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取代理商信息失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    agency,
	})
}

// DeleteAgency 删除代理商（仅管理员可用）
func DeleteAgency(c *gin.Context) {
	id, _ := strconv.Atoi(c.Param("id"))

	// 删除代理商后，将用户角色设置为普通用户
	agency, err := model.GetAgencyById(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取代理商信息失败",
		})
		return
	}
	user, err := model.GetUserById(agency.UserId, false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取用户信息失败",
		})
		return
	}
	user.Role = model.RoleCommonUser
	err = user.Update(false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "更新用户角色失败",
		})
		return
	}

	err = model.DeleteAgency(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除成功",
	})
}

// UpdateAgency 更新代理商信息（仅管理员可用）
func UpdateAgency(c *gin.Context) {
	var agency model.Agency
	err := c.ShouldBindJSON(&agency)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}

	// 取path参数
	id, _ := strconv.Atoi(c.Param("id"))
	if id == 0 && agency.Id == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的ID",
		})
		return
	}
	// 校验佣金比例必须介于0-1之间
	if agency.CommissionRate < 0 || agency.CommissionRate > 1 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "佣金比例必须介于0-1之间",
		})
		return
	}
	// 校验topup_group_ratio这个json中的每一项是否都高于系统预设
	if agency.TopupGroupRatio != "" {
		// string转map
		jsonBytes := []byte(agency.TopupGroupRatio)
		// 转map
		var topupGroupRatio map[string]float64
		err = json.Unmarshal(jsonBytes, &topupGroupRatio)
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "无效的充值倍率json格式",
			})
			return
		}
		// 遍历校验
		for key, value := range topupGroupRatio {
			groupRatio := ratio.GetTopupGroupRatio(key)
			if value < groupRatio {
				c.JSON(http.StatusOK, gin.H{
					"success": false,
					"message": "充值倍率不得低于管理员预设",
				})
				return
			}
		}
	}

	if agency.Id == 0 {
		agency.Id = id
	}
	err = model.UpdateAgency(&agency)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "更新成功",
	})
}

// SetAgencyPrice 设置代理商自定义价格（仅限代理商）
func SetAgencyPrice(c *gin.Context) {
	userId := c.GetInt("id")

	var priceData struct {
		ModelName string  `json:"model_name"`
		Price     float64 `json:"price"`
	}

	if err := c.ShouldBindJSON(&priceData); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}

	agency, err := model.GetAgencyByUserId(userId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取代理商信息失败",
		})
		return
	}

	// 检查是否启用了自定义价格
	//if !agency.CustomPriceEnabled {
	//	c.JSON(http.StatusOK, gin.H{
	//		"success": false,
	//		"message": "未启用自定义价格功能",
	//	})
	//	return
	//}

	// 更新价格
	err = model.SetAgencyModelPrice(agency.Id, priceData.ModelName, priceData.Price)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "设置价格失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "价格设置成功",
	})
}

// SettleAgencyCommission 代理商佣金结算（仅管理员可用）
func SettleAgencyCommission(c *gin.Context) {
	id, _ := strconv.Atoi(c.Param("id"))

	var settleData struct {
		SettleAmount float64 `json:"settle_amount"`
		Note         string  `json:"note"`
	}

	err := c.ShouldBindJSON(&settleData)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}

	// 获取代理商信息
	agency, err := model.GetAgencyById(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取代理商信息失败",
		})
		return
	}

	// 检查结算金额是否有效
	if settleData.SettleAmount <= 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "结算金额必须大于0",
		})
		return
	}

	// 检查结算金额是否超过当前佣金收入
	if settleData.SettleAmount > agency.CommissionMoney {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "结算金额不能超过当前佣金收入",
		})
		return
	}

	// 记录结算前的佣金金额
	beforeCommission := agency.CommissionMoney

	// 使用原子操作扣减佣金，避免并发问题
	err = model.SettleAgencyCommission(id, settleData.SettleAmount)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "佣金结算失败: " + err.Error(),
		})
		return
	}

	// 重新获取更新后的代理商信息
	updatedAgency, err := model.GetAgencyById(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取更新后的代理商信息失败",
		})
		return
	}

	// 获取管理员用户信息
	ctx := c.Request.Context()
	adminUserId := c.GetInt("id")
	adminUser, err := model.GetUserById(adminUserId, false)
	adminUsername := "管理员"
	if err == nil {
		adminUsername = adminUser.Username
	}

	// 创建佣金结算记录
	settlementRecord := &model.AgencyCommissionSettlement{
		AgencyId:         agency.Id,
		AgencyUserId:     agency.UserId,
		AgencyName:       agency.Name,
		AdminUserId:      adminUserId,
		AdminUsername:    adminUsername,
		SettleAmount:     settleData.SettleAmount,
		BeforeCommission: beforeCommission,
		AfterCommission:  updatedAgency.CommissionMoney,
		Status:           model.SettlementStatusCompleted,
		Note:             settleData.Note,
	}

	err = model.CreateAgencyCommissionSettlement(settlementRecord)
	if err != nil {
		// 结算记录创建失败，但佣金已经扣减，记录错误日志
		model.RecordLog(ctx, adminUserId, model.LogTypeSystemErr,
			fmt.Sprintf("佣金结算成功但创建结算记录失败，代理商ID:%d，结算金额:%.2f，错误:%s",
				agency.Id, settleData.SettleAmount, err.Error()))
	}

	// 记录佣金结算日志
	// 将日志记录到代理商用户下，这样代理商可以在自己的日志中看到
	logContent := fmt.Sprintf("管理员 [%s] 为您进行佣金结算，结算金额：%.2f元，结算前佣金：%.2f元，结算后佣金：%.2f元",
		adminUsername, settleData.SettleAmount, beforeCommission, updatedAgency.CommissionMoney)

	if settleData.Note != "" {
		logContent += fmt.Sprintf("，备注：%s", settleData.Note)
	}

	// 记录到代理商用户的日志中
	model.RecordLog(ctx, agency.UserId, model.LogTypeManage, logContent)

	// 同时为管理员记录一条操作日志
	adminLogContent := fmt.Sprintf("为代理商 [%s] (ID:%d) 进行佣金结算，结算金额：%.2f元，结算前佣金：%.2f元，结算后佣金：%.2f元",
		agency.Name, agency.Id, settleData.SettleAmount, beforeCommission, updatedAgency.CommissionMoney)

	if settleData.Note != "" {
		adminLogContent += fmt.Sprintf("，备注：%s", settleData.Note)
	}

	model.RecordLog(ctx, adminUserId, model.LogTypeOperation, adminLogContent)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "佣金结算成功",
		"data": gin.H{
			"settled_amount":        settleData.SettleAmount,
			"remaining_commission":  updatedAgency.CommissionMoney,
			"historical_commission": updatedAgency.HistoryCommissionMoney,
		},
	})
}

// GetAgencyCommissionSettlements 获取代理商佣金结算记录（代理商用户可查看自己的记录）
func GetAgencyCommissionSettlements(c *gin.Context) {
	userId := c.GetInt("id")

	// 获取代理商信息
	agency, err := model.GetAgencyByUserId(userId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取代理商信息失败",
		})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	settlements, total, err := model.GetAgencyCommissionSettlements(agency.Id, page, pageSize)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取佣金结算记录失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    settlements,
		"total":   total,
	})
}

// GetAllAgencyCommissionSettlements 获取所有代理商的佣金结算记录（仅管理员可用）
func GetAllAgencyCommissionSettlements(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	agencyId, _ := strconv.Atoi(c.DefaultQuery("agency_id", "0"))
	adminUserId, _ := strconv.Atoi(c.DefaultQuery("admin_user_id", "0"))
	status, _ := strconv.Atoi(c.DefaultQuery("status", "0"))

	settlements, total, err := model.GetAllAgencyCommissionSettlements(page, pageSize, agencyId, adminUserId, status)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取佣金结算记录失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    settlements,
		"total":   total,
	})
}

// GetAgencyCommissionSettlementStats 获取代理商佣金结算统计（代理商用户可查看自己的统计）
func GetAgencyCommissionSettlementStats(c *gin.Context) {
	userId := c.GetInt("id")

	// 获取代理商信息
	agency, err := model.GetAgencyByUserId(userId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取代理商信息失败",
		})
		return
	}

	startTime, _ := strconv.ParseInt(c.DefaultQuery("start_time", "0"), 10, 64)
	endTime, _ := strconv.ParseInt(c.DefaultQuery("end_time", "0"), 10, 64)

	stats, err := model.GetAgencyCommissionSettlementStats(agency.Id, startTime, endTime)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取佣金结算统计失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// GetAdminSettlementStats 获取管理员结算统计（仅管理员可用）
func GetAdminSettlementStats(c *gin.Context) {
	adminUserId := c.GetInt("id")
	startTime, _ := strconv.ParseInt(c.DefaultQuery("start_time", "0"), 10, 64)
	endTime, _ := strconv.ParseInt(c.DefaultQuery("end_time", "0"), 10, 64)

	stats, err := model.GetAdminSettlementStats(adminUserId, startTime, endTime)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取管理员结算统计失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

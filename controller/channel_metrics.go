package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/model"
)

// GetChannelMetricsScores 获取渠道性能得分
func GetChannelMetricsScores(c *gin.Context) {
	timeRange := c.Query("time_range")
	if timeRange == "" {
		timeRange = "day" // 默认为日统计
	}

	modelName := c.Query("model")

	// 获取所有渠道的性能指标
	channelScores, err := model.GetAllChannelMetricsScores(timeRange, modelName)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取渠道性能指标失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"scores": channelScores,
		},
	})
}

// GetAvailableMetricsModels 获取所有有指标数据的模型列表
func GetAvailableMetricsModels(c *gin.Context) {
	models, err := model.GetAllMetricsModels()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取模型列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"models": models,
		},
	})
}

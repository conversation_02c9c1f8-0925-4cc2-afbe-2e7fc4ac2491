package controller

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common/client"
	"github.com/songquanpeng/one-api/common/logger"
)

// ProxyRequest 结构体定义代理请求的参数
type ProxyRequest struct {
	URL     string            `json:"url"`
	Method  string            `json:"method"`
	Headers map[string]string `json:"headers"`
	Data    interface{}       `json:"data"`
}

// ProxyAPI 处理代理请求，将请求转发到目标URL
func ProxyAPI(c *gin.Context) {
	// 验证用户权限 - 这已经由路由中的中间件JWTAuth和UserAuth处理
	// 用户必须已登录才能使用此API

	var proxyReq ProxyRequest
	err := c.ShouldBindJSON(&proxyReq)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	// 验证必要参数
	if proxyReq.URL == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "URL不能为空",
		})
		return
	}

	if proxyReq.Method == "" {
		proxyReq.Method = "GET" // 默认方法
	}

	// 准备请求体
	var reqBody io.Reader
	if proxyReq.Data != nil {
		jsonData, err := json.Marshal(proxyReq.Data)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "请求数据编码失败: " + err.Error(),
			})
			return
		}
		reqBody = bytes.NewBuffer(jsonData)
	}

	// 创建HTTP请求
	req, err := http.NewRequest(proxyReq.Method, proxyReq.URL, reqBody)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "创建请求失败: " + err.Error(),
		})
		return
	}

	// 设置请求头
	if proxyReq.Headers != nil {
		for key, value := range proxyReq.Headers {
			req.Header.Set(key, value)
		}
	}

	// 执行请求
	resp, err := client.HTTPClient.Do(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "请求失败: " + err.Error(),
		})
		return
	}
	defer resp.Body.Close()

	// 读取响应体
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "读取响应失败: " + err.Error(),
		})
		return
	}

	// 解析响应体为JSON
	var jsonResp interface{}
	err = json.Unmarshal(respBody, &jsonResp)
	if err != nil {
		// 如果响应不是JSON，直接返回原始响应
		logger.SysLog("代理响应不是有效的JSON格式: " + err.Error())
		c.Data(resp.StatusCode, resp.Header.Get("Content-Type"), respBody)
		return
	}

	// 返回响应
	c.JSON(http.StatusOK, jsonResp)
}

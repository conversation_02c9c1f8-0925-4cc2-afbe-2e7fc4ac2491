package controller

import (
	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common/client"
	"io"
	"net/http"
)

func ReverseProxy(c *gin.Context) {
	// 获取要反代的图片 URL
	imageURL := c.Query("url")

	// 发送请求获取图片数据
	resp, err := client.UserContentRequestHTTPClient.Get(imageURL)
	if err != nil {
		c.String(http.StatusInternalServerError, "Failed to fetch image")
		return
	}
	defer resp.Body.Close()

	// 设置响应头
	c.Header("Content-Type", resp.Header.Get("Content-Type"))
	c.Header("Content-Length", resp.Header.Get("Content-Length"))

	// 将图片数据写入响应
	io.Copy(c.Writer, resp.Body)
}

package controller

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/client"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/common/message"
	"github.com/songquanpeng/one-api/controller/optimizer"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/relay/adaptor/openai"
	"github.com/songquanpeng/one-api/relay/billing"
	billingratio "github.com/songquanpeng/one-api/relay/billing/ratio"
	"github.com/songquanpeng/one-api/relay/channeltype"
	"github.com/songquanpeng/one-api/relay/constant"
	"github.com/songquanpeng/one-api/relay/controller"
	relaycontroller "github.com/songquanpeng/one-api/relay/controller"
	"github.com/songquanpeng/one-api/relay/meta"
	relaymodel "github.com/songquanpeng/one-api/relay/model"
	"github.com/songquanpeng/one-api/relay/util"
)

func relayGPTGodTextHelper(c *gin.Context, relayMode int) *relaymodel.ErrorWithStatusCode {
	ctx := c.Request.Context()
	meta := meta.GetByContext(c)
	channelType := c.GetInt("channel")
	billingType := c.GetInt(ctxkey.BillingType)
	tokenId := c.GetInt("token_id")
	userId := c.GetInt("id")
	group := c.GetString("group")
	_startTime := helper.GetTimestamp()
	var textRequest relaymodel.GeneralOpenAIRequest
	err := common.UnmarshalBodyReusable(c, &textRequest)
	if err != nil {
		return openai.ErrorWrapper(err, "bind_request_body_failed", http.StatusBadRequest)
	}
	if relayMode == constant.RelayModeModerations && textRequest.Model == "" {
		textRequest.Model = "text-moderation-latest"
	}
	if relayMode == constant.RelayModeEmbeddings && textRequest.Model == "" {
		textRequest.Model = c.Param("model")
	}
	// request validation
	if textRequest.Model == "" {
		return openai.ErrorWrapper(errors.New("model is required"), "required_field_missing", http.StatusBadRequest)
	}
	switch relayMode {
	case constant.RelayModeCompletions:
		if textRequest.Prompt == "" {
			return openai.ErrorWrapper(errors.New("field prompt is required"), "required_field_missing", http.StatusBadRequest)
		}
	case constant.RelayModeChatCompletions:
		if textRequest.Messages == nil || len(textRequest.Messages) == 0 {
			return openai.ErrorWrapper(errors.New("field messages is required"), "required_field_missing", http.StatusBadRequest)
		}
	case constant.RelayModeEmbeddings:
	case constant.RelayModeModerations:
		if textRequest.Input == "" {
			return openai.ErrorWrapper(errors.New("field input is required"), "required_field_missing", http.StatusBadRequest)
		}
	case constant.RelayModeEdits:
		if textRequest.Instruction == "" {
			return openai.ErrorWrapper(errors.New("field instruction is required"), "required_field_missing", http.StatusBadRequest)
		}
	}
	// map model name
	isModelMapped := false
	meta.OriginModelName = textRequest.Model
	textRequest.Model, isModelMapped = util.GetMappedModelName(textRequest.Model, meta.ModelMapping, meta.ModelMappingArr)
	meta.ActualModelName = textRequest.Model
	baseURL := channeltype.ChannelBaseURLs[channelType]
	requestURL := c.Request.URL.String()
	// 用于保存详细聊天记录日志
	var detailPrompt = ""
	var detailCompletion = ""
	if c.GetString("base_url") != "" {
		baseURL = c.GetString("base_url")
	}
	fullRequestURL := util.GetFullRequestURL(baseURL, requestURL, channelType)
	var promptTokens int
	var completionTokens int
	switch relayMode {
	case constant.RelayModeGPTGodChatCompletions:
		if model.GetNewTikTokenBilling(userId) {
			promptTokens, _, _, err = openai.CountTokenMessagesNew(textRequest.Messages, textRequest.Model, meta)
		} else {
			promptTokens, _, _, err = openai.CountTokenMessages(textRequest.Messages, textRequest.Model, meta)
		}
	case constant.RelayModeChatCompletions:
		if model.GetNewTikTokenBilling(userId) {
			promptTokens, _, _, err = openai.CountTokenMessagesNew(textRequest.Messages, textRequest.Model, meta)
		} else {
			promptTokens, _, _, err = openai.CountTokenMessages(textRequest.Messages, textRequest.Model, meta)
		}
	case constant.RelayModeCompletions:
		promptTokens = lo.If(model.GetNewTikTokenBilling(userId), openai.CountTokenInputNew(textRequest.Prompt, textRequest.Model)).Else(openai.CountTokenInput(textRequest.Prompt, textRequest.Model))
	case constant.RelayModeModerations:
		promptTokens = lo.If(model.GetNewTikTokenBilling(userId), openai.CountTokenInputNew(textRequest.Input, textRequest.Model)).Else(openai.CountTokenInput(textRequest.Input, textRequest.Model))
	}
	if err != nil {
		return openai.ErrorWrapper(err, "count_token_messages_failed", http.StatusInternalServerError)
	}
	var modelFixedPrice float64
	var modelFixedPriceErr error
	if billingType == common.BillingTypeByCount {
		modelFixedPrice, modelFixedPriceErr = billingratio.GetModelFixedPrice(lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(textRequest.Model))
		if modelFixedPriceErr != nil {
			return openai.ErrorWrapper(modelFixedPriceErr, "model_fixed_price_not_config", http.StatusForbidden)
		}
	}
	// 替换用户个性费率
	userModelFixedPrice, ok, _, err := model.CacheGetUserModelFixedPrice(meta.UserId, lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(textRequest.Model))
	if ok {
		modelFixedPrice = userModelFixedPrice
	}
	meta.ModelFixedPrice = modelFixedPrice
	preConsumedTokens := config.PreConsumedQuota
	if textRequest.MaxTokens != 0 {
		preConsumedTokens = int64(promptTokens + textRequest.MaxTokens)
	}
	modelRatio := billingratio.GetModelRatio(lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(textRequest.Model), meta.ChannelType)
	// 获取用户个性化费率
	userModelRatio, ok, _, err := model.CacheGetUserModelRatio(meta.UserId, lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(textRequest.Model))
	if ok {
		modelRatio = userModelRatio
	}
	groupRatio := billingratio.GetGroupRatio(group)
	ratio := modelRatio * groupRatio
	if billingType == common.BillingTypeByCount {
		ratio = groupRatio
	}
	topupConvertRatio, userDiscount := billing.GetTopupConvertRatioByMeta(meta)
	preConsumedQuota := int64(float64(preConsumedTokens) * ratio * topupConvertRatio * userDiscount)
	if billingType == common.BillingTypeByCount {
		preConsumedQuota = int64(float64(modelFixedPrice) * 500000 * ratio * topupConvertRatio * userDiscount)
	}
	userQuota, quotaExpireTime, err := model.CacheGetUserQuotaAndExpireTime(ctx, userId)
	if err != nil {
		return openai.ErrorWrapper(err, "get_user_quota_failed", http.StatusInternalServerError)
	}
	if config.QuotaExpireEnabled && quotaExpireTime > 0 && quotaExpireTime < helper.GetTimestamp() {
		model.RecordSysLogToDBAndFile(c.Request.Context(), c.GetString(helper.RequestIdKey), model.LogTypeSystemErr, userId, 0, lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(textRequest.Model), "", "", "user quota expired", "尚未解析")
		return openai.ErrorWrapper(errors.New("user quota expired"), "user_quota_expired", http.StatusForbidden)
	}
	if userQuota <= 0 || userQuota-preConsumedQuota < 0 {
		return openai.ErrorWrapper(errors.New(fmt.Sprintf("user [%d] quota [%d] preConsumedQuota [%d] is not enough", meta.UserId, userQuota, preConsumedQuota)), "insufficient_user_quota", http.StatusForbidden)
	}
	if preConsumedQuota > 0 {
		err, isLowQuota := model.PreConsumeTokenQuota(ctx, tokenId, preConsumedQuota)
		if err != nil {
			return openai.ErrorWrapper(err, "pre_consume_token_quota_failed", http.StatusForbidden)
		}
		if isLowQuota {
			helper.SafeGoroutine(func() {
				// 发送余额预警通知（内部会检查2小时抑制机制）
				notificationSent := relaycontroller.SendBalanceWarningNotification(meta.UserId, userQuota)

				// 只有在用户通知发送成功时才通知管理员，避免重复通知
				if notificationSent {
					message.NotifyRootUser("额度即将用尽提醒", fmt.Sprintf("用户[%d]额度即将用尽，剩余额度为 %d $", meta.UserId, userQuota/500000))
				}
			})
		}
	}
	var requestBody io.Reader
	if isModelMapped {
		jsonStr, err := json.Marshal(textRequest)
		if err != nil {
			return openai.ErrorWrapper(err, "marshal_text_request_failed", http.StatusInternalServerError)
		}
		requestBody = bytes.NewBuffer(jsonStr)
	} else {
		requestBody = c.Request.Body
	}

	// 读取 Body 内容
	bodyBytes, _ := io.ReadAll(requestBody)
	detailPrompt = string(bodyBytes)
	// 根据配置排除请求字段
	detailPrompt, bodyBytes, err = util.ExcludeFields(c, detailPrompt, bodyBytes)
	detailPrompt, bodyBytes, err = util.AddExtraFields(c, detailPrompt, bodyBytes)
	if err != nil {
		return openai.ErrorWrapper(err, "exclude_fields_failed", http.StatusInternalServerError)
	}
	// 恢复原始的 Body
	requestBody = io.NopCloser(bytes.NewBuffer(bodyBytes))

	var req *http.Request
	var resp *http.Response
	isStream := textRequest.Stream

	req, err = http.NewRequest(c.Request.Method, fullRequestURL, requestBody)
	if err != nil {
		return openai.ErrorWrapper(err, "new_request_failed", http.StatusInternalServerError)
	}
	apiKey := c.Request.Header.Get("Authorization")
	apiKey = strings.TrimPrefix(apiKey, "Bearer ")
	req.Header.Set("Authorization", c.Request.Header.Get("Authorization"))
	if c.Request.Header.Get("OpenAI-Organization") != "" {
		req.Header.Set("OpenAI-Organization", c.Request.Header.Get("OpenAI-Organization"))
	}
	req.Header.Set("Content-Type", c.Request.Header.Get("Content-Type"))
	req.Header.Set("Accept", c.Request.Header.Get("Accept"))
	if isStream && c.Request.Header.Get("Accept") == "" {
		req.Header.Set("Accept", "text/event-stream")
	}
	//req.Header.Set("Connection", c.Request.Header.Get("Connection"))
	resp, err = client.HTTPClient.Do(req)
	if err != nil {
		return openai.ErrorWrapper(err, "do_request_failed", http.StatusInternalServerError)
	}
	err = req.Body.Close()
	if err != nil {
		return openai.ErrorWrapper(err, "close_request_body_failed", http.StatusInternalServerError)
	}
	err = c.Request.Body.Close()
	if err != nil {
		return openai.ErrorWrapper(err, "close_request_body_failed", http.StatusInternalServerError)
	}
	isStream = isStream || strings.HasPrefix(resp.Header.Get("Content-Type"), "text/event-stream")
	if resp.StatusCode != http.StatusOK {
		if preConsumedQuota != 0 {
			go func(ctx context.Context) {
				// return pre-consumed quota
				err := model.PostConsumeTokenQuota(tokenId, -preConsumedQuota)
				if err != nil {
					logger.Error(ctx, "error return pre-consumed quota: "+err.Error())
				}
			}(c.Request.Context())
		}
		return controller.RelayErrorHandler(resp)
	}

	var textResponse openai.TextResponse
	tokenName := c.GetString("token_name")
	tokenKey := c.GetString("token_key")
	channelId := c.GetInt("channel_id")
	channelName := c.GetString("channel_name")

	defer func(ctx context.Context) {
		// c.Writer.Flush()
		go func() {
			var quota int64
			completionRatio := billingratio.GetCompletionRatio(lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(textRequest.Model), meta.ChannelType)
			promptTokens = textResponse.Usage.PromptTokens
			completionTokens = textResponse.Usage.CompletionTokens
			quota = int64(math.Ceil((float64(promptTokens) + float64(completionTokens)*completionRatio) * ratio))
			if ratio*topupConvertRatio*userDiscount != 0 && quota <= 0 {
				quota = 1
			}
			totalTokens := promptTokens + completionTokens
			if totalTokens == 0 {
				// in this case, must be some error happened
				// we cannot just return, because we may have to return the pre-consumed quota
				quota = 0
			}
			if billingType == common.BillingTypeByCount {
				// 如果是按次计费
				quota = int64(float64(modelFixedPrice) * 500000 * ratio)
				if resp.StatusCode != http.StatusOK {
					// 上面已经有一个函数处理过预扣费的补回,所以这里不走下面的补回逻辑
					quota = 0
					return
				}
			}
			quota = int64(math.Ceil(float64(quota) * topupConvertRatio * userDiscount))
			quotaDelta := quota - preConsumedQuota
			err := model.PostConsumeTokenQuota(tokenId, quotaDelta)
			if err != nil {
				logger.Error(ctx, "error consuming token remain quota: "+err.Error())
			}
			err = model.CacheUpdateUserQuota(ctx, userId)
			if err != nil {
				logger.Error(ctx, "error update user quota cache: "+err.Error())
			}
			// 即使是请求错误导致的消费为0也记录日志,并且记录耗时
			requestDuration := helper.GetTimestamp() - _startTime
			logContent := fmt.Sprintf("模型倍率 %.2f，分组倍率 %.2f，充值转换率 %.2f，用户折扣率 %.2f，用时 %d秒", modelRatio, groupRatio, topupConvertRatio, userDiscount, requestDuration)
			if billingType == common.BillingTypeByCount {
				logContent = fmt.Sprintf("模型按次使用固定价格 %.6f，分组倍率 %.2f，充值转换率 %.2f，用户折扣率 %.2f，用时 %d秒", modelFixedPrice, groupRatio, topupConvertRatio, userDiscount, requestDuration)
			}
			model.UpdateUserUsedQuotaAndRequestCount(userId, quota)
			model.UpdateChannelUsedQuota(channelId, quota)
			createdLog := model.RecordConsumeLog(ctx, userId, channelId, promptTokens, completionTokens, lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(textRequest.Model), tokenName, tokenKey, channelName, int(quota), requestDuration, isStream, logContent)
			helper.SafeGoroutine(func() {
				// 记录详细聊天记录
				model.RecordLogExtend(ctx, createdLog, detailPrompt, detailCompletion, "", meta.UpstreamResponse, meta.FullResponse, meta.RequestURLPath)
				// 推送优化器
				optimizer.RecordConsumeLog(createdLog)
			})
		}()
	}(c.Request.Context())
	if isStream {
		err, responseText, _, _, _, _ := openai.StreamHandler(c, resp, meta, constant.RelayModeChatCompletions)
		if err != nil {
			return err
		}
		textResponse.Usage.PromptTokens = promptTokens
		textResponse.Usage.CompletionTokens = lo.If(model.GetNewTikTokenBilling(userId), openai.CountTokenTextNew(responseText, textRequest.Model)).Else(openai.CountTokenText(responseText, textRequest.Model))
		detailCompletion = responseText
		return nil
	} else {
		all, _ := io.ReadAll(resp.Body)
		detailCompletion = string(all)
		// 恢复原始的 Body
		resp.Body = io.NopCloser(bytes.NewBuffer(all))

		err, usage := openai.Handler(c, resp, promptTokens, textRequest.Model)
		if err != nil {
			return err
		}
		if usage != nil {
			textResponse.Usage = *usage
		}
		return nil
	}
}

package controller

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/smartwalle/xid"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/common/suixingpay"
	"github.com/songquanpeng/one-api/model"
	billingratio "github.com/songquanpeng/one-api/relay/billing/ratio"
	"gorm.io/gorm"
)

// 全局随行付客户端
var suixingpayClient *suixingpay.Client

// 存储当前的随行付配置
var currentSuixingpayConfig struct {
	OrgId              string
	PlatformPublicKey  string
	MerchantPrivateKey string
	MerchantNo         string
	Enabled            bool
	CallbackURL        string
}

// 上次检查配置的时间
var lastSuixingpayConfigCheckTime time.Time

// 在init函数中设置初始检查时间
func init() {
	lastSuixingpayConfigCheckTime = time.Now()
}

// 初始化随行付客户端
func initSuixingpayClient(forceInit ...bool) error {
	// 如果forceInit为true或客户端为nil，则初始化
	if (len(forceInit) > 0 && forceInit[0]) || suixingpayClient == nil {
		// 先关闭现有客户端
		if suixingpayClient != nil {
			suixingpayClient = nil
		}

		// 添加调试日志
		logger.SysLog(fmt.Sprintf("随行付配置检查: Enabled=%v, OrgId=%s, MerchantNo=%s, PlatformPublicKey长度=%d, MerchantPrivateKey长度=%d",
			config.SuixingpayEnabled, config.SuixingpayOrgId, config.SuixingpayMerchantNo,
			len(config.SuixingpayPlatformPublicKey), len(config.SuixingpayMerchantPrivateKey)))

		if !config.SuixingpayEnabled || config.SuixingpayOrgId == "" ||
			config.SuixingpayPlatformPublicKey == "" || config.SuixingpayMerchantPrivateKey == "" ||
			config.SuixingpayMerchantNo == "" {
			return fmt.Errorf("随行付聚合支付未启用或配置不完整")
		}

		// 初始化随行付客户端
		suixingpayClient = suixingpay.NewClient(
			config.SuixingpayOrgId,
			config.SuixingpayPlatformPublicKey,
			config.SuixingpayMerchantPrivateKey,
		)

		// 更新当前配置
		currentSuixingpayConfig.OrgId = config.SuixingpayOrgId
		currentSuixingpayConfig.PlatformPublicKey = config.SuixingpayPlatformPublicKey
		currentSuixingpayConfig.MerchantPrivateKey = config.SuixingpayMerchantPrivateKey
		currentSuixingpayConfig.MerchantNo = config.SuixingpayMerchantNo
		currentSuixingpayConfig.Enabled = config.SuixingpayEnabled
		currentSuixingpayConfig.CallbackURL = config.SuixingpayCallbackAddress

		lastSuixingpayConfigCheckTime = time.Now()
		return nil
	}

	return nil
}

// CheckSuixingpayConfigChanged 检查随行付配置是否更改
func CheckSuixingpayConfigChanged() bool {
	// 记录当前时间作为最后检查时间
	defer func() {
		lastSuixingpayConfigCheckTime = time.Now()
	}()

	// 如果启用状态改变，或者启用状态为true且其他配置发生变化，则需要重新初始化
	if currentSuixingpayConfig.Enabled != config.SuixingpayEnabled ||
		(config.SuixingpayEnabled && (currentSuixingpayConfig.OrgId != config.SuixingpayOrgId ||
			currentSuixingpayConfig.PlatformPublicKey != config.SuixingpayPlatformPublicKey ||
			currentSuixingpayConfig.MerchantPrivateKey != config.SuixingpayMerchantPrivateKey ||
			currentSuixingpayConfig.MerchantNo != config.SuixingpayMerchantNo ||
			currentSuixingpayConfig.CallbackURL != config.SuixingpayCallbackAddress)) {

		// 如果客户端已存在，则将其设为nil，下次使用时会重新初始化
		if suixingpayClient != nil {
			suixingpayClient = nil
			logger.SysLog("随行付配置发生变更，客户端将在下次使用时重新初始化")
		}

		// 更新配置缓存
		currentSuixingpayConfig.OrgId = config.SuixingpayOrgId
		currentSuixingpayConfig.PlatformPublicKey = config.SuixingpayPlatformPublicKey
		currentSuixingpayConfig.MerchantPrivateKey = config.SuixingpayMerchantPrivateKey
		currentSuixingpayConfig.MerchantNo = config.SuixingpayMerchantNo
		currentSuixingpayConfig.Enabled = config.SuixingpayEnabled
		currentSuixingpayConfig.CallbackURL = config.SuixingpayCallbackAddress

		return true
	}

	return false
}

// 检查配置是否需要更新（考虑时间因素）
func checkSuixingpayConfigIfNeeded() bool {
	// 如果距离上次检查超过5秒，则再次检查
	if time.Since(lastSuixingpayConfigCheckTime) > 5*time.Second {
		return CheckSuixingpayConfigChanged()
	}
	return false
}

// SuixingpayRequest 随行付聚合支付请求参数
type SuixingpayRequest struct {
	Amount      float64 `json:"amount"`       // 充值金额，单位美元
	Currency    string  `json:"currency"`     // 币种，可选值：USD，CNY，默认值：USD
	PaymentType string  `json:"payment_type"` // 支付类型：alipay, wxpay
}

// SuixingpayResponse 随行付聚合支付响应
type SuixingpayResponse struct {
	Success    bool   `json:"success"`
	Message    string `json:"message"`
	QRCode     string `json:"qr_code,omitempty"`  // 二维码链接（扫码支付）
	PayData    string `json:"pay_data,omitempty"` // 支付数据（JS支付）
	OutTradeNo string `json:"out_trade_no"`       // 商户订单号
}

// SuixingpayTradeStatus 随行付交易状态
type SuixingpayTradeStatus struct {
	Success    bool   `json:"success"`
	Message    string `json:"message"`
	Status     string `json:"status"`       // 交易状态
	TradeNo    string `json:"trade_no"`     // 商户订单号
	OutTradeNo string `json:"out_trade_no"` // 外部订单号
}

// SuixingpayPay 处理随行付聚合支付请求
func SuixingpayPay(c *gin.Context) {
	// 检查配置是否变更
	if checkSuixingpayConfigIfNeeded() {
		logger.SysLog("随行付配置已变更，将使用新配置初始化客户端")
	}

	// 初始化随行付客户端
	if suixingpayClient == nil {
		if err := initSuixingpayClient(); err != nil {
			logger.SysError(fmt.Sprintf("随行付客户端初始化失败: %s", err.Error()))
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "随行付客户端初始化失败: " + err.Error(),
			})
			return
		}
	}

	// 运行测试签名（用于对比PHP结果）
	fmt.Println("=== 开始测试签名 ===")
	suixingpayClient.TestSignature()
	fmt.Println("=== 测试签名结束 ===")

	userId := c.GetInt("id")
	user, err := model.GetUserById(userId, false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取用户信息失败: " + err.Error(),
		})
		return
	}

	var req SuixingpayRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	// 验证支付类型
	if req.PaymentType != "alipay" && req.PaymentType != "wxpay" {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "不支持的支付类型",
		})
		return
	}

	// 定义两个变量，充值美金额度和支付金额
	var addUSD float64
	var payCNY float64
	var payCNYOrigin float64

	// 根据选择的币种，获取支付金额或者充值额度
	// 如果是人民币，充值美金额度=req.Amount/单位美金价格，支付金额=req.Amount
	if req.Currency == "CNY" {
		addUSD = req.Amount / GetAmount(1, *user)
		payCNY = req.Amount
		payCNYOrigin = addUSD * GetAmountOrigin(1, *user)
	} else {
		// 如果是美金，充值美金额度=req.Amount，支付金额=req.Amount*单位美金价格
		addUSD = req.Amount
		payCNY = req.Amount * GetAmount(1, *user)
		payCNYOrigin = req.Amount * GetAmountOrigin(1, *user)
	}

	// 检查最小充值金额
	limit := billingratio.GetTopupGroupMinLimit(user.Group) // 单位美金
	if limit > 0 && addUSD < float64(limit) {
		c.JSON(200, gin.H{
			"success": false,
			"message": fmt.Sprintf("充值金额不能小于 %.2f 美元", float64(limit)),
		})
		return
	}

	// 生成商户订单号
	outTradeNo := fmt.Sprintf("%d", xid.Next())

	// 处理回调URL
	callbackURL := config.SuixingpayCallbackAddress
	if callbackURL == "" {
		// 如果未配置回调地址，使用服务器地址
		callbackURL = config.ServerAddress
	}

	// 确保回调URL以/api/suixingpay/callback结尾
	if !strings.HasSuffix(callbackURL, "/api/suixingpay/callback") {
		// 移除可能的尾部斜杠
		callbackURL = strings.TrimSuffix(callbackURL, "/")
		// 添加回调路径
		callbackURL = callbackURL + "/api/suixingpay/callback"
	}

	// 创建本地订单记录
	topUp := &model.TopUp{
		UserId:        userId,
		Amount:        addUSD,
		Money:         payCNY,
		MoneyOrigin:   payCNYOrigin,
		TradeNo:       outTradeNo,
		PaymentMethod: "suixingpay_" + req.PaymentType,
		CreateTime:    time.Now().Unix(),
		Status:        "pending",
	}
	err = topUp.Insert()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "创建订单记录失败: " + err.Error(),
		})
		return
	}

	// 构建支付请求参数
	var payType string
	switch req.PaymentType {
	case "alipay":
		payType = suixingpay.PayTypeAlipay
	case "wxpay":
		payType = suixingpay.PayTypeWechat
	}

	activeScanReq := suixingpay.ActiveScanRequest{
		Mno:       config.SuixingpayMerchantNo,
		OrdNo:     outTradeNo,
		Amt:       fmt.Sprintf("%.2f", payCNY),
		PayType:   payType,
		Subject:   fmt.Sprintf("充值 %.2f 美元", addUSD),
		TrmIp:     c.ClientIP(),
		NotifyUrl: callbackURL,
	}

	// 调用随行付API
	result, err := suixingpayClient.Submit("/order/activeScan", activeScanReq)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "创建支付订单失败: " + err.Error(),
		})
		return
	}

	// 检查业务状态码
	bizCode, ok := result["bizCode"].(string)
	if !ok || bizCode != suixingpay.BizCodeSuccess {
		bizMsg, _ := result["bizMsg"].(string)
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": fmt.Sprintf("[%s]%s", bizCode, bizMsg),
		})
		return
	}

	// 获取支付二维码
	payUrl, ok := result["payUrl"].(string)
	if !ok {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取支付二维码失败",
		})
		return
	}

	// 返回二维码
	c.JSON(http.StatusOK, SuixingpayResponse{
		Success:    true,
		Message:    "创建随行付聚合支付订单成功",
		QRCode:     payUrl,
		OutTradeNo: outTradeNo,
	})
}

// SuixingpayStatus 查询随行付聚合支付订单状态
func SuixingpayStatus(c *gin.Context) {
	outTradeNo := c.Query("out_trade_no")
	if outTradeNo == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "订单号不能为空",
		})
		return
	}

	// 查询本地订单
	topUp := model.GetTopUpByTradeNo(outTradeNo)
	if topUp == nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "订单不存在",
		})
		return
	}

	// 如果订单已经成功，直接返回成功
	if topUp.Status == "success" {
		c.JSON(http.StatusOK, SuixingpayTradeStatus{
			Success:    true,
			Message:    "支付成功",
			Status:     "TRADE_SUCCESS",
			TradeNo:    topUp.TradeNo,
			OutTradeNo: outTradeNo,
		})
		return
	}

	// 返回当前状态
	c.JSON(http.StatusOK, SuixingpayTradeStatus{
		Success:    true,
		Message:    "查询成功",
		Status:     strings.ToUpper(topUp.Status),
		TradeNo:    topUp.TradeNo,
		OutTradeNo: outTradeNo,
	})
}

// SuixingpayCallback 处理随行付聚合支付回调
func SuixingpayCallback(c *gin.Context) {
	// 检查配置是否变更
	if checkSuixingpayConfigIfNeeded() {
		logger.SysLog("随行付配置已变更，将使用新配置初始化客户端")
	}

	// 初始化随行付客户端
	if suixingpayClient == nil {
		if err := initSuixingpayClient(); err != nil {
			logger.SysError(fmt.Sprintf("随行付回调初始化客户端失败: %s", err.Error()))
			c.JSON(http.StatusOK, gin.H{"code": "fail", "msg": "初始化失败"})
			return
		}
	}

	// 读取请求体
	body, err := c.GetRawData()
	if err != nil {
		logger.SysError(fmt.Sprintf("随行付回调读取请求体失败: %s", err.Error()))
		c.JSON(http.StatusOK, gin.H{"code": "fail", "msg": "读取请求体失败"})
		return
	}

	// 解析通知数据
	var notifyData map[string]interface{}
	if err := json.Unmarshal(body, &notifyData); err != nil {
		logger.SysError(fmt.Sprintf("随行付回调解析数据失败: %s", err.Error()))
		c.JSON(http.StatusOK, gin.H{"code": "fail", "msg": "解析数据失败"})
		return
	}

	// 验证签名
	//if !suixingpayClient.VerifySign(notifyData) {
	//	logger.SysError("随行付回调验证签名失败")
	//	c.JSON(http.StatusOK, gin.H{"code": "fail", "msg": "签名错误"})
	//	return
	//}

	// 检查业务状态码
	bizCode, ok := notifyData["bizCode"].(string)
	if !ok || bizCode != suixingpay.BizCodeSuccess {
		bizMsg, _ := notifyData["bizMsg"].(string)
		logger.SysError(fmt.Sprintf("随行付回调业务状态错误: [%s]%s", bizCode, bizMsg))
		c.JSON(http.StatusOK, gin.H{"code": "fail", "msg": "状态错误"})
		return
	}

	// 获取订单号
	ordNo, ok := notifyData["ordNo"].(string)
	if !ok {
		logger.SysError("随行付回调缺少订单号")
		c.JSON(http.StatusOK, gin.H{"code": "fail", "msg": "缺少订单号"})
		return
	}

	// 使用事务处理订单
	err = model.DB.Transaction(func(tx *gorm.DB) error {
		// 锁定订单记录
		lockedTopUp, err := model.GetTopUpByTradeNoByTx(tx, ordNo)
		if err != nil {
			return fmt.Errorf("订单不存在: %v", err)
		}

		// 检查订单状态，避免重复处理
		if lockedTopUp.Status == "success" {
			return nil // 订单已处理，直接返回成功
		}

		// 更新订单状态
		lockedTopUp.Status = "success"
		if err := lockedTopUp.UpdateByTx(tx); err != nil {
			return fmt.Errorf("更新订单状态失败: %v", err)
		}

		// 增加用户余额
		if err := model.IncreaseUserQuotaAndRedisByTx(tx, lockedTopUp.UserId, int64(lockedTopUp.Amount*500000)); err != nil {
			return fmt.Errorf("增加用户余额失败: %v", err)
		}

		// 添加邀请返利
		if err := model.AddInviteBonusByAgency(c, tx, lockedTopUp, int(lockedTopUp.Amount*500000)); err != nil {
			return fmt.Errorf("邀请返利失败: %v", err)
		}

		// 更新余额有效期
		if config.QuotaExpireEnabled && config.QuotaExpireDays > 0 {
			if err := model.AddUserQuotaExpireByTx(tx, lockedTopUp.UserId, config.QuotaExpireDays); err != nil {
				return fmt.Errorf("更新用户余额过期时间失败: topUp:%v,err:%v", lockedTopUp, err)
			}
		}

		return nil
	})

	if err != nil {
		logger.SysError(fmt.Sprintf("随行付回调处理订单失败: %s", err.Error()))
		c.JSON(http.StatusOK, gin.H{"code": "fail", "msg": "处理失败"})
		return
	}

	// 记录日志
	sxfUuid, _ := notifyData["sxfUuid"].(string)
	buyerId, _ := notifyData["buyerId"].(string)
	transactionId, _ := notifyData["transactionId"].(string)

	topUp := model.GetTopUpByTradeNo(ordNo)
	if topUp != nil {
		topUpMsg := fmt.Sprintf("随行付聚合支付充值成功，充值金额：%.2f，支付金额：%.2f，随行付订单号：%s，买家ID：%s，第三方交易号：%s",
			topUp.Amount, topUp.Money, sxfUuid, buyerId, transactionId)
		model.RecordLog(c, topUp.UserId, model.LogTypeTopup, topUpMsg)
	}

	// 返回成功
	c.JSON(http.StatusOK, gin.H{"code": "success", "msg": "成功"})
}

package controller

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strconv"
	"time"

	epay "github.com/akl7777777/go-epay"
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/common/message"
	"github.com/songquanpeng/one-api/model"
	billingratio "github.com/songquanpeng/one-api/relay/billing/ratio"
)

type EpayRequest struct {
	Amount        int    `json:"amount"`         //充值金额，单位取决于currency字段
	PaymentMethod string `json:"payment_method"` //支付方式，可选值：alipay，wxpay
	Currency      string `json:"currency"`       //币种，可选值：USD，CNY，默认值：USD
	PaymentWay    int    `json:"payment_way"`    //支付通道，1：通道1，2：通道2，默认值：1
}

func GetEpayClient() *epay.Client {
	if config.PayAddress == "" || config.EpayId == "" || config.EpayKey == "" {
		return nil
	}
	withUrl, err := epay.NewClientWithUrl(&epay.Config{
		PartnerID: config.EpayId,
		Key:       config.EpayKey,
	}, config.PayAddress)
	if err != nil {
		return nil
	}
	return withUrl
}

func GetEpayClient2() *epay.Client {
	if config.PayAddress2 == "" || config.EpayId2 == "" || config.EpayKey2 == "" {
		return nil
	}
	withUrl, err := epay.NewClientWithUrl(&epay.Config{
		PartnerID: config.EpayId2,
		Key:       config.EpayKey2,
	}, config.PayAddress2)
	if err != nil {
		return nil
	}
	return withUrl
}

// GetAmount 获取当前用户所在分组单位美金价格
func GetAmount(count float64, user model.User) float64 {
	// 根据user个性化配置重算充值倍率
	userEx, _ := model.GetUserExByUserId(user.Id)
	if userEx != nil {
		customTopupRatio := userEx.GetTopupRatio()
		if customTopupRatio != 0 {
			return count * config.Price * customTopupRatio
		}
	}
	// 如果用户有代理商,则获取代理商配置的充值倍率
	if user.AgencyId > 0 {
		agency, _ := model.GetAgencyByUserId(user.AgencyId)
		if agency != nil && agency.TopupGroupRatio != "" {
			// 转json
			var topupGroupRatio map[string]float64
			err := json.Unmarshal([]byte(agency.TopupGroupRatio), &topupGroupRatio)
			if err != nil {
				logger.SysError(fmt.Sprintf("GetAmount json.Unmarshal err: %s", err))
			}
			if topupGroupRatio != nil {
				rsTopupGroupRatio := topupGroupRatio[user.Group]
				if rsTopupGroupRatio != 0 {
					return count * config.Price * rsTopupGroupRatio
				}
			}
		}
	}
	// 别问为什么用float64，问就是这么点钱没必要
	topupGroupRatio := billingratio.GetTopupGroupRatio(user.Group)
	if topupGroupRatio == 0 {
		topupGroupRatio = 1
	}
	amount := count * config.Price * topupGroupRatio
	return amount
}

// GetAmountOrigin 获取当前用户所在分组单位美金价格原始值 不根据user个性化配置重算充值倍率
func GetAmountOrigin(count float64, user model.User) float64 {
	// 别问为什么用float64，问就是这么点钱没必要
	topupGroupRatio := billingratio.GetTopupGroupRatio(user.Group)
	if topupGroupRatio == 0 {
		topupGroupRatio = 1
	}
	amount := count * config.Price * topupGroupRatio
	return amount
}

func GetAmountUSD(count float64, user model.User) float64 {
	topupGroupRatio := billingratio.GetTopupGroupRatio(user.Group)
	if topupGroupRatio == 0 {
		topupGroupRatio = 1
	}
	amount := count * topupGroupRatio
	return amount
}

// GetCallbackAddress 获取回调地址（不含后缀）
func GetCallbackAddress() string {
	if config.CustomEpayCallbackAddress == "" {
		return config.ServerAddress
	}
	return config.CustomEpayCallbackAddress
}
func GetCallbackAddress2() string {
	if config.CustomEpayCallbackAddress2 == "" {
		return config.ServerAddress
	}
	return config.CustomEpayCallbackAddress2
}

// RequestEpay 请求支付
func RequestEpay(c *gin.Context) {
	//1. 获取请求参数，校验参数
	var req EpayRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(200, gin.H{
			"success": false,
			"message": "参数错误",
			"data":    "",
		})
		return
	}

	// 如果未指定支付通道，默认使用通道1
	if req.PaymentWay == 0 {
		req.PaymentWay = 1
	}

	// 获取当前Referer
	referer := c.Request.Referer()
	paymentMethodValid := true
	//1.2 检验支付方式是否正确
	if req.PaymentMethod != "alipay" && req.PaymentMethod != "wxpay" && req.PaymentMethod != "card" &&
		req.PaymentMethod != "USDT_TRC20" && req.PaymentMethod != "EVM_ETH_ETH" && req.PaymentMethod != "stripe" &&
		req.PaymentMethod != "paypal" {
		c.JSON(200, gin.H{
			"success": false,
			"message": "unsupported payment method",
			"data":    "",
		})
		return
	}
	//1.2.2 检验支付方式是否被屏蔽（99：全部，1：仅支付宝，2：仅微信，3：仅QQ钱包（示例））
	if config.CustomAvailablePayMethods != 99 {
		switch req.PaymentMethod {
		case "alipay":
			if config.CustomAvailablePayMethods&config.Alipay == config.Alipay {
				paymentMethodValid = true
			} else if config.CustomAvailablePayMethods2&config.Alipay == config.Alipay {
				paymentMethodValid = true
			} else {
				paymentMethodValid = false
			}
		case "wxpay":
			if config.CustomAvailablePayMethods&config.WeChat == config.WeChat {
				paymentMethodValid = true
			} else if config.CustomAvailablePayMethods2&config.WeChat == config.WeChat {
				paymentMethodValid = true
			} else {
				paymentMethodValid = false
			}
		case "card":
			if config.CustomAvailablePayMethods&config.Card == config.Card {
				paymentMethodValid = true
			} else if config.CustomAvailablePayMethods2&config.Card == config.Card {
				paymentMethodValid = true
			} else {
				paymentMethodValid = false
			}

		case "stripe":
			if config.CustomAvailablePayMethods&config.Stripe == config.Stripe {
				paymentMethodValid = true
			} else if config.CustomAvailablePayMethods2&config.Stripe == config.Stripe {
				paymentMethodValid = true
			} else {
				paymentMethodValid = false
			}
		case "USDT_TRC20":
			if config.CustomAvailablePayMethods&config.UsdtTrc20 == config.UsdtTrc20 {
				paymentMethodValid = true
			} else if config.CustomAvailablePayMethods2&config.UsdtTrc20 == config.UsdtTrc20 {
				paymentMethodValid = true
			} else {
				paymentMethodValid = false
			}
		case "EVM_ETH_ETH":
			if config.CustomAvailablePayMethods&config.EvmEthEth == config.EvmEthEth {
				paymentMethodValid = true
			} else if config.CustomAvailablePayMethods2&config.EvmEthEth == config.EvmEthEth {
				paymentMethodValid = true
			} else {
				paymentMethodValid = false
			}
		case "paypal":
			if config.CustomAvailablePayMethods&config.PayPal == config.PayPal {
				paymentMethodValid = true
			} else if config.CustomAvailablePayMethods2&config.PayPal == config.PayPal {
				paymentMethodValid = true
			} else {
				paymentMethodValid = false
			}
		}
	}
	if !paymentMethodValid {
		c.JSON(200, gin.H{
			"success": false,
			"message": "unsupported payment method",
			"data":    "",
		})
		return
	}
	//1.3 检验币种是否正确
	if req.Currency != "USD" && req.Currency != "CNY" {
		c.JSON(200, gin.H{
			"success": false,
			"message": "unsupported currency",
			"data":    "",
		})
		return
	}
	//2. 校验用户是否存在
	id := c.GetInt("id")
	user, _ := model.CacheGetUserById(id, false)
	if user == nil {
		c.JSON(200, gin.H{
			"success": false,
			"message": "用户不存在",
			"data":    "",
		})
		return
	}
	//定义两个变量，充值美金额度和支付金额
	var addUSD float64
	var payCNY float64
	var payCNYOrigin float64
	//var payUSD float64
	//3. 根据选择的币种，获取支付金额或者充值额度
	//3.1 如果是人民币，充值美金额度=req.Amount/单位美金价格，支付金额=req.Amount
	if req.Currency == "CNY" {
		addUSD = float64(req.Amount) / GetAmount(1, *user)
		payCNY = float64(req.Amount)
		payCNYOrigin = addUSD * GetAmountOrigin(1, *user)
	}
	//3.2 如果是美金，充值美金额度=req.Amount，支付金额=req.Amount*单位美金价格
	if req.Currency == "USD" {
		addUSD = float64(req.Amount)
		payCNY = float64(req.Amount) * GetAmount(1, *user)
		payCNYOrigin = float64(req.Amount) * GetAmountOrigin(1, *user)
	}
	//4.校验最低、最高限度
	//4.1 校验最低限度
	limit := billingratio.GetTopupGroupMinLimit(user.Group) //单位美金
	if addUSD < float64(limit) {
		c.JSON(200, gin.H{
			"success": false,
			"message": fmt.Sprintf("充值金额不能小于 %d 美金", limit),
			"data":    "",
		})
		return
	}
	//4.2 校验最高限度
	if addUSD > config.MaxTopUpLimit {
		c.JSON(200, gin.H{
			"success": false,
			"message": fmt.Sprintf("充值金额不能大于 %.2f 美金", config.MaxTopUpLimit),
			"data":    "",
		})
		return
	}
	callBackAddress := lo.If(req.PaymentWay == 1, GetCallbackAddress()).Else(GetCallbackAddress2())
	returnUrl, _ := url.Parse(referer)
	notifyUrl, _ := url.Parse(callBackAddress + "/api/user/epay/notify")
	tradeNo := strconv.FormatInt(time.Now().Unix(), 10)
	payMoney := payCNY
	//if req.Currency == "USD" && req.PaymentMethod == "card" {
	//	// 信用卡支付，支付金额为美金
	//	payUSD = GetAmountUSD(float64(req.Amount), *user)
	//	if payUSD > 0 {
	//		payMoney = payUSD
	//	}
	//}
	//payMoney := amount

	//只有当用户选择PayPal支付并且支付金额小于此值时，会扣除此值
	payPalUsd := payMoney / config.CustomPayPalUsdRate
	if req.PaymentMethod == "paypal" && payPalUsd < config.PayPalMinimumFee {
		payPalUsd = config.PayPalMinimumFee
	}
	topUp := &model.TopUp{
		UserId:         id,
		AgencyId:       user.AgencyId,                    // 代理商id
		Amount:         addUSD,                           //充值额度
		Money:          payMoney,                         //支付金额
		MoneyOrigin:    payCNYOrigin,                     //支付金额（人民币,没打折之前应该支付的金额）
		MoneyUsdt:      payMoney / config.CustomUsdtRate, // 支付USDT
		MoneyEth:       payMoney / config.CustomEthRate,  // 支付ETH
		MoneyPayPalUsd: payPalUsd,                        // 支付PayPal
		PaymentMethod:  req.PaymentMethod,
		TradeNo:        "A" + tradeNo,
		CreateTime:     time.Now().Unix(),
		Status:         "pending",
	}
	err = topUp.Insert()
	if err != nil {
		c.JSON(200, gin.H{
			"success": false,
			"message": "创建订单失败",
			"data":    "",
		})
		return
	}
	// 根据paymentWay选择支付客户端
	client := lo.If(req.PaymentWay == 1, GetEpayClient()).Else(GetEpayClient2())
	if client == nil {
		c.JSON(200, gin.H{
			"success": false,
			"message": "未找到配置信息",
			"data":    "",
		})
		return
	}
	uri, params, err := client.Purchase(&epay.PurchaseArgs{
		Type:           epay.PurchaseType(req.PaymentMethod),
		ServiceTradeNo: "A" + tradeNo,
		Name:           "B" + tradeNo,
		Money: strconv.FormatFloat(lo.If(topUp.PaymentMethod == "USDT_TRC20", topUp.MoneyUsdt).
			ElseIf(topUp.PaymentMethod == "EVM_ETH_ETH", topUp.MoneyEth).
			ElseIf(topUp.PaymentMethod == "paypal", topUp.MoneyPayPalUsd).
			Else(payMoney), 'f', 2, 64),
		Device:    epay.PC,
		NotifyUrl: notifyUrl,
		ReturnUrl: returnUrl,
	})
	if err != nil {
		c.JSON(200, gin.H{
			"success": false,
			"message": "发起支付失败",
			"data":    "",
		})
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    params,
		"url":     uri,
	})
}

// EpayNotify 支付回调
func EpayNotify(c *gin.Context) {
	ctx := c.Request.Context()
	params := lo.Reduce(lo.Keys(c.Request.URL.Query()), func(r map[string]string, t string, i int) map[string]string {
		r[t] = c.Request.URL.Query().Get(t)
		return r
	}, map[string]string{})
	client := GetEpayClient()
	if client == nil {
		logger.SysError("支付回调失败 未找到配置信息")
		_, err := c.Writer.Write([]byte("fail"))
		if err != nil {
			logger.SysError(fmt.Sprintf("支付回调写入失败: %s", err.Error()))
		}
	}
	verifyInfo, err := client.Verify(params)
	if err == nil && verifyInfo.VerifyStatus {
		_, err := c.Writer.Write([]byte("success"))
		if err != nil {
			logger.SysError(fmt.Sprintf("支付回调写入失败: %s", err.Error()))
		}
	} else {
		_, err := c.Writer.Write([]byte("fail"))
		if err != nil {
			logger.SysError(fmt.Sprintf("支付回调写入失败: %s", err.Error()))
		}
	}

	if verifyInfo == nil {
		logger.SysError(fmt.Sprintf("支付回调失败: verifyInfo is nil"))
		return
	}

	if verifyInfo.TradeStatus == epay.StatusTradeSuccess {
		logger.SysLog(fmt.Sprintf("verifyInfo: %v", verifyInfo))
		if common.RedisEnabled {
			rdb := common.RDB
			lock := helper.NewRedisLock(rdb, fmt.Sprintf("lock:EpayNotify:%s", verifyInfo.ServiceTradeNo))
			if !lock.Lock(time.Second * 20) {
				logger.SysError(fmt.Sprintf("支付回调获取锁失败: %s", verifyInfo.ServiceTradeNo))
				return
			}
			defer lock.Unlock()
		}
		tx := model.DB.Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()
		topUp, err := model.GetTopUpByTradeNoByTx(tx, verifyInfo.ServiceTradeNo)
		if err != nil {
			logger.SysError(fmt.Sprintf("支付回调查询订单失败: %s", err.Error()))
			tx.Rollback()
			return
		}
		logger.SysLog(fmt.Sprintf("支付回调即将更新订单: %v", topUp))
		if topUp.Status == "pending" {
			topUp.Status = "success"
			err := topUp.UpdateByTx(tx)
			if err != nil {
				logger.SysError(fmt.Sprintf("支付回调更新订单失败: %s", err.Error()))
				tx.Rollback()
				return
			}
			err = model.IncreaseUserQuotaAndRedisByTx(tx, topUp.UserId, int64(topUp.Amount*500000))
			if err != nil {
				logger.SysError(fmt.Sprintf("支付回调更新用户失败: topUp:%v,err:%s", topUp, err))
				tx.Rollback()
				return
			}
			logger.SysError(fmt.Sprintf("支付回调更新用户成功 %v", topUp))
			err = model.AddInviteBonusByAgency(ctx, tx, topUp, int(topUp.Amount*500000))
			if err != nil {
				logger.SysError(fmt.Sprintf("支付回调邀请返利失败: %s", err.Error()))
				tx.Rollback()
				return
			}
			if config.QuotaExpireEnabled && config.QuotaExpireDays > 0 {
				err = model.AddUserQuotaExpireByTx(tx, topUp.UserId, config.QuotaExpireDays)
				if err != nil {
					logger.SysError(fmt.Sprintf("支付回调更新用户余额过期时间失败: topUp:%v,err:%s", topUp, err))
					tx.Rollback()
					return
				}
			}
			helper.SafeGoroutine(func() {
				recordTopUpMsg(ctx, topUp)
			})
		}
		tx.Commit()
		return
	} else {
		logger.SysError(fmt.Sprintf("支付异常回调: %v", verifyInfo))
	}

	// 校验支付渠道2
	client2 := GetEpayClient2()
	if client2 == nil {
		logger.SysError("支付2回调失败 未找到配置信息")
		_, err := c.Writer.Write([]byte("fail"))
		if err != nil {
			logger.SysError(fmt.Sprintf("支付2回调写入失败: %s", err.Error()))
		}
	}
	verifyInfo2, err := client2.Verify(params)
	if err == nil && verifyInfo2.VerifyStatus {
		_, err := c.Writer.Write([]byte("success"))
		if err != nil {
			logger.SysError(fmt.Sprintf("支付2回调写入失败: %s", err.Error()))
		}
	} else {
		_, err := c.Writer.Write([]byte("fail"))
		if err != nil {
			logger.SysError(fmt.Sprintf("支付2回调写入失败: %s", err.Error()))
		}
	}

	if verifyInfo2 == nil {
		logger.SysError(fmt.Sprintf("支付2回调失败: verifyInfo2 is nil"))
		return
	}

	if verifyInfo2.TradeStatus == epay.StatusTradeSuccess {
		logger.SysLog(fmt.Sprintf("verifyInfo2: %v", verifyInfo2))
		if common.RedisEnabled {
			rdb := common.RDB
			lock := helper.NewRedisLock(rdb, fmt.Sprintf("lock:EpayNotify:%s", verifyInfo2.ServiceTradeNo))
			if !lock.Lock(time.Second * 20) {
				logger.SysError(fmt.Sprintf("支付2回调获取锁失败: %s", verifyInfo2.ServiceTradeNo))
				return
			}
			defer lock.Unlock()
		}
		tx := model.DB.Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()
		topUp, err := model.GetTopUpByTradeNoByTx(tx, verifyInfo2.ServiceTradeNo)
		if err != nil {
			logger.SysError(fmt.Sprintf("支付2回调查询订单失败: %s", err.Error()))
			tx.Rollback()
			return
		}
		logger.SysLog(fmt.Sprintf("支付2回调即将更新订单: %v", topUp))
		if topUp.Status == "pending" {
			topUp.Status = "success"
			err := topUp.UpdateByTx(tx)
			if err != nil {
				logger.SysError(fmt.Sprintf("支付2回调更新订单失败: %s", err.Error()))
				tx.Rollback()
				return
			}
			err = model.IncreaseUserQuotaAndRedisByTx(tx, topUp.UserId, int64(topUp.Amount*500000))
			if err != nil {
				logger.SysError(fmt.Sprintf("支付2回调更新用户失败: topUp:%v,err:%s", topUp, err))
				tx.Rollback()
				return
			}
			logger.SysError(fmt.Sprintf("支付2回调更新用户成功 %v", topUp))
			err = model.AddInviteBonusByAgency(ctx, tx, topUp, int(topUp.Amount*500000))
			if err != nil {
				logger.SysError(fmt.Sprintf("支付2回调邀请返利失败: %s", err.Error()))
				tx.Rollback()
				return
			}
			if config.QuotaExpireEnabled && config.QuotaExpireDays > 0 {
				err = model.AddUserQuotaExpireByTx(tx, topUp.UserId, config.QuotaExpireDays)
				if err != nil {
					logger.SysError(fmt.Sprintf("支付2回调更新用户余额过期时间失败: topUp:%v,err:%s", topUp, err))
					tx.Rollback()
					return
				}
			}
			helper.SafeGoroutine(func() {
				recordTopUpMsg(ctx, topUp)
			})
		}
		tx.Commit()
		return
	} else {
		logger.SysError(fmt.Sprintf("支付2异常回调: %v", verifyInfo2))
	}

}

type AmountRequest struct {
	Amount   int    `json:"amount"`   //充值金额，单位取决于currency字段
	Currency string `json:"currency"` //币种，可选值：USD，CNY，默认值：USD
}

// RequestAmount 获取额度
func RequestAmount(c *gin.Context) {
	var req AmountRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(200, gin.H{
			"success": false,
			"message": "参数错误",
			"data":    "",
		})
		return
	}
	//检验币种是否正确
	if req.Currency != "USD" && req.Currency != "CNY" {
		c.JSON(200, gin.H{
			"success": false,
			"message": "unsupported currency",
			"data":    "",
		})
		return
	}
	if req.Amount <= 0 {
		c.JSON(200, gin.H{
			"success": false,
			"message": "充值金额不能小于0",
			"data":    "",
		})
		return
	}
	id := c.GetInt("id")
	user, _ := model.CacheGetUserById(id, false)
	everyUSD := GetAmount(1, *user)
	//根据选择的币种，获取支付金额或者充值额度
	if req.Currency == "CNY" {
		// 只有当用户选择PayPal支付并且支付金额小于此值时，会扣除此值
		payPalUsd := float64(req.Amount) / config.CustomPayPalUsdRate
		if payPalUsd < config.PayPalMinimumFee {
			payPalUsd = config.PayPalMinimumFee
		}
		c.JSON(200, gin.H{
			"success":         true,
			"message":         "",
			"data":            float64(req.Amount) / everyUSD,
			"data_usdt":       float64(req.Amount) / config.CustomUsdtRate,
			"data_paypal_usd": payPalUsd,
			"data_eth":        float64(req.Amount) / config.CustomEthRate,
		})
		return
	}
	if req.Currency == "USD" {
		// 只有当用户选择PayPal支付并且支付金额小于此值时，会扣除此值
		payPalUsd := (float64(req.Amount) * everyUSD) / config.CustomPayPalUsdRate
		if payPalUsd < config.PayPalMinimumFee {
			payPalUsd = config.PayPalMinimumFee
		}
		c.JSON(200, gin.H{
			"success":         true,
			"message":         "",
			"data":            float64(req.Amount) * everyUSD,
			"data_usdt":       (float64(req.Amount) * everyUSD) / config.CustomUsdtRate,
			"data_paypal_usd": payPalUsd,
			"data_eth":        (float64(req.Amount) * everyUSD) / config.CustomEthRate,
		})
		return
	}
}

func GetAvailableModel(c *gin.Context) {
	id := c.GetInt("id")
	user, _ := model.CacheGetUserById(id, false)
	if user == nil {
		c.JSON(200, gin.H{"message": "error", "data": "用户不存在"})
		return
	}

	var models []string
	// 如果配置了令牌自由切换
	if config.TokenGroupChangeEnabled {
		// 判断是否为管理员
		isAdmin := model.IsAdmin(id)
		// 获取所有可见的group
		groups, err := model.GetSelectableGroupsWithRatio(0, 500, "", "", user.Group, isAdmin)
		if err != nil {
			c.JSON(200, gin.H{"message": "error", "data": "获取用户组失败"})
			return
		}

		// 添加用户额外可见分组
		groups, err = model.AddUserExtraVisibleGroupsToGroupWithRatioList(id, user.Group, groups)
		if err != nil {
			c.JSON(200, gin.H{"message": "error", "data": "添加额外可见分组失败: " + err.Error()})
			return
		}

		// 遍历 group
		for _, group := range groups {
			// 获取当前group下面可用的模型
			groupModels := model.GetAvailableModelByGroup(group.Name)
			// 将模型添加到models中,并去重
			for _, m := range groupModels {
				if !contains(models, m) {
					models = append(models, m)
				}
			}
		}
	} else {
		models = model.GetAvailableModelByGroup(user.Group)
	}

	hasSearchSerperModel := model.HasSearchSerperModel()
	if hasSearchSerperModel {
		if !contains(models, "net-*") {
			models = append(models, "net-*")
		}
	}
	// 添加config.DynamicRouterModelMap
	for k := range config.DynamicRouterModelMap {
		if !contains(models, k) {
			models = append(models, k)
		}
	}
	c.JSON(200, gin.H{"success": true, "message": "", "data": models})
}

func GetAvailableModelsByGroups(c *gin.Context) {
	id := c.GetInt("id")
	user, _ := model.CacheGetUserById(id, false)
	if user == nil {
		c.JSON(200, gin.H{"success": false, "message": "用户不存在", "data": nil})
		return
	}

	userGroup, err := model.CacheGetGroupByName(user.Group)
	if err != nil {
		c.JSON(200, gin.H{"success": false, "message": "获取用户组失败", "data": nil})
		return
	}

	var groups []*model.GroupWithRatio
	if !config.TokenGroupChangeEnabled {
		// 如果没有开启模型自由切换，只返回当前用户组
		groups = []*model.GroupWithRatio{{
			Group:             userGroup,
			ConvertRatio:      1.0, // 当前组的转换率为1
			CurrentGroupRatio: userGroup.GroupRatio,
			CurrentTopupRatio: userGroup.TopupGroupRatio,
		}}
	} else {
		// 判断是否为管理员
		isAdmin := model.IsAdmin(id)
		// 获取所有可选的组及其转换率
		groups, err = model.GetSelectableGroupsWithRatio(0, 500, "", "", user.Group, isAdmin)
		if err != nil {
			c.JSON(200, gin.H{"success": false, "message": "获取用户组失败", "data": nil})
			return
		}

		// 如果用户当前分组不可见，添加到列表中
		if !userGroup.GetIsSelectable() {
			groups = append([]*model.GroupWithRatio{{
				Group:             userGroup,
				ConvertRatio:      1.0,
				CurrentGroupRatio: userGroup.GroupRatio,
				CurrentTopupRatio: userGroup.TopupGroupRatio,
			}}, groups...)
		}

		// 添加用户额外可见分组
		groups, err = model.AddUserExtraVisibleGroupsToGroupWithRatioList(id, user.Group, groups)
		if err != nil {
			c.JSON(200, gin.H{"success": false, "message": "添加额外可见分组失败: " + err.Error(), "data": nil})
			return
		}
	}

	result := make([]map[string]interface{}, 0)
	for _, group := range groups {
		models := model.GetAvailableModelByGroup(group.Name)

		// 添加网络搜索模型
		hasSearchSerperModel := model.HasSearchSerperModel()
		if hasSearchSerperModel && !contains(models, "net-*") {
			models = append(models, "net-*")
		}

		// 添加动态路由模型
		for k := range config.DynamicRouterModelMap {
			allowedRoles, exists := config.DynamicRouterModelRoles[k]
			if exists && len(allowedRoles) > 0 {
				if contains(allowedRoles, group.Name) && !contains(models, k) {
					models = append(models, k)
				}
			} else if !contains(models, k) {
				models = append(models, k)
			}
		}

		groupInfo := map[string]interface{}{
			"name":              group.Name,
			"displayName":       group.DisplayName,
			"models":            models,
			"convert_ratio":     group.ConvertRatio,
			"group_ratio":       group.GroupRatio,
			"topup_group_ratio": group.TopupGroupRatio,
		}

		result = append(result, groupInfo)
	}

	// 获取用户的初始令牌
	initialToken, err := model.GetUserInitialToken(id)
	initialTokenGroup := user.Group // 默认使用当前分组
	if err == nil && initialToken != nil && initialToken.Group != "" {
		initialTokenGroup = initialToken.Group // 如果有初始令牌，使用令牌的分组
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "",
		"data": map[string]interface{}{
			"groups":            result,
			"userGroup":         user.Group,
			"initialTokenGroup": initialTokenGroup,
			"currentGroupRatio": userGroup.GroupRatio,
			"currentTopupRatio": userGroup.TopupGroupRatio,
		},
	})
}

// 判断一个字符串是否在切片中
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// recordTopUpMsg
func recordTopUpMsg(ctx context.Context, topUp *model.TopUp) {
	// 获取用户当前余额 // 这里不走redis 因为金钱比较敏感,查询当前数据库真正的余额
	user, err := model.GetUserById(topUp.UserId, false)
	if err != nil {
		logger.SysError("获取用户信息失败: " + err.Error())
		return
	}
	beforeQuota := user.Quota
	afterQuota := beforeQuota + int64(topUp.Amount*500000)

	var topUpMsg string
	switch topUp.PaymentMethod {
	case "USDT_TRC20":
		topUpMsg = fmt.Sprintf("通过USDT充值 %v (充值前余额: %v → 充值后余额: %v)，USDT金额 %.2f，等价人民币金额 %.2f元",
			common.LogQuota(int64(topUp.Amount*500000)),
			common.LogQuota(beforeQuota),
			common.LogQuota(afterQuota),
			topUp.MoneyUsdt,
			topUp.Money)
	case "EVM_ETH_ETH":
		topUpMsg = fmt.Sprintf("通过ETH充值 %v (充值前余额: %v → 充值后余额: %v)，ETH金额 %.2f，等价人民币金额 %.2f元",
			common.LogQuota(int64(topUp.Amount*500000)),
			common.LogQuota(beforeQuota),
			common.LogQuota(afterQuota),
			topUp.MoneyEth,
			topUp.Money)
	case "paypal":
		topUpMsg = fmt.Sprintf("通过PayPal充值 %v (充值前余额: %v → 充值后余额: %v)，PayPal金额 %.2f，等价人民币金额 %.2f元",
			common.LogQuota(int64(topUp.Amount*500000)),
			common.LogQuota(beforeQuota),
			common.LogQuota(afterQuota),
			topUp.MoneyPayPalUsd,
			topUp.Money)
	default:
		topUpMsg = fmt.Sprintf("通过在线充值 %v (充值前余额: %v → 充值后余额: %v)，支付金额 %.2f元",
			common.LogQuota(int64(topUp.Amount*500000)),
			common.LogQuota(beforeQuota),
			common.LogQuota(afterQuota),
			topUp.Money)
	}

	model.RecordLog(ctx, topUp.UserId, model.LogTypeTopup, topUpMsg)
	message.NotifyRootUser(fmt.Sprintf("用户id[%d]在线充值[%f]元成功", topUp.UserId, topUp.Money), fmt.Sprintf("用户id[%d]在线充值[%f]元成功, %s", topUp.UserId, topUp.Money, topUpMsg))
}

// GetTopUpRecords 获取充值记录
func GetTopUpRecords(c *gin.Context) {
	// 检查是否有查看所有充值记录的权限
	role := c.GetInt("role")

	// 管理员及以上可以查看所有充值记录
	hasFullTopupPermission := role >= model.RoleAdminUser

	if !hasFullTopupPermission {
		// 没有权限查看所有充值记录，降级到查看用户自己的充值记录
		GetUserTopUpRecords(c)
		return
	}

	page, _ := strconv.Atoi(c.Query("page"))
	pageSize, _ := strconv.Atoi(c.Query("page_size"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 20
	}

	// 构建查询条件
	conditions := map[string]string{
		"username":       c.Query("username"),
		"amount":         c.Query("amount"),
		"payment_method": c.Query("payment_method"),
		"trade_no":       c.Query("trade_no"),
		"status":         c.Query("status"),
	}

	// 调用 model 层获取数据
	topUps, total, err := model.GetTopUpList(page, pageSize, conditions)
	if err != nil {
		c.JSON(200, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "",
		"data":    topUps,
		"total":   total,
	})
}

// GetUserTopUpRecords 获取用户自己的充值记录
func GetUserTopUpRecords(c *gin.Context) {
	page, _ := strconv.Atoi(c.Query("page"))
	pageSize, _ := strconv.Atoi(c.Query("page_size"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 20
	}

	userId := c.GetInt("id")

	// 获取充值记录列表
	var topUps []model.TopUp
	if err := model.DB.Where("user_id = ?", userId).
		Order("id DESC").
		Limit(pageSize).
		Offset((page - 1) * pageSize).
		Find(&topUps).Error; err != nil {
		c.JSON(200, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 获取总数
	var total int64
	if err := model.DB.Model(&model.TopUp{}).Where("user_id = ?", userId).Count(&total).Error; err != nil {
		c.JSON(200, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "",
		"data":    topUps,
		"total":   total,
	})
}

package controller

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/client"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/relay/adaptor/openai"
	"github.com/songquanpeng/one-api/relay/billing"
	billingratio "github.com/songquanpeng/one-api/relay/billing/ratio"
	"github.com/songquanpeng/one-api/relay/channeltype"
	"github.com/songquanpeng/one-api/relay/meta"
	relaymodel "github.com/songquanpeng/one-api/relay/model"
	"github.com/songquanpeng/one-api/relay/util"
)

func relayOpenaiSerperSearchTextHelper(c *gin.Context, relayMode int) *relaymodel.ErrorWithStatusCode {
	ctx := c.Request.Context()
	meta := meta.GetByContext(c)

	searchModel := "search-serper"

	tokenId := c.GetInt("token_id")
	channelType := c.GetInt("channel")
	channelId := c.GetInt("channel_id")
	userId := c.GetInt("id")
	group := c.GetString("group")
	_startTime := helper.GetTimestamp()

	var searchRequest relaymodel.GeneralOpenAIRequest
	err := common.UnmarshalBodyReusable(c, &searchRequest)
	if err != nil {
		return openai.ErrorWrapper(err, "bind_request_body_failed", http.StatusBadRequest)
	}

	if searchRequest.Model == "" {
		searchRequest.Model = searchModel
	}

	// 获取模型比率和组比率
	modelRatio := billingratio.GetModelRatio(searchRequest.Model, meta.ChannelType)
	groupRatio := billingratio.GetGroupRatio(group)
	ratio := modelRatio * groupRatio

	// 获取用户个性化费率
	userModelRatio, ok, _, err := model.CacheGetUserModelRatio(userId, searchRequest.Model)
	if ok {
		modelRatio = userModelRatio
		ratio = modelRatio * groupRatio
	}

	// 获取充值利差率
	topupConvertRatio, userDiscount := billing.GetTopupConvertRatioByMeta(meta)

	// 判断是否按次数计费
	var modelFixedPrice float64
	if meta.BillingType == common.BillingTypeByCount {
		modelFixedPrice, err = billingratio.GetModelFixedPrice(searchRequest.Model)
		if err != nil {
			return openai.ErrorWrapper(err, "model_fixed_price_not_config", http.StatusForbidden)
		}
		// 获取用户个性化固定价格
		userModelFixedPrice, ok, _, _ := model.CacheGetUserModelFixedPrice(userId, searchRequest.Model)
		if ok {
			modelFixedPrice = userModelFixedPrice
		}
		ratio = groupRatio // 按次数计费时，只使用组比率
	}

	// 预扣费逻辑
	var preConsumedQuota int64
	if meta.BillingType == common.BillingTypeByCount {
		preConsumedQuota = int64(modelFixedPrice * 500000 * ratio * topupConvertRatio * userDiscount)
	} else {
		preConsumedQuota = int64(float64(config.PreConsumedQuota) * ratio * topupConvertRatio * userDiscount)
	}

	if preConsumedQuota > 0 {
		err, _ := model.PreConsumeTokenQuota(ctx, tokenId, preConsumedQuota)
		if err != nil {
			return openai.ErrorWrapper(err, "pre_consume_token_quota_failed", http.StatusForbidden)
		}
	}

	searchRequest.Model, _ = util.GetMappedModelName(searchRequest.Model, meta.ModelMapping, meta.ModelMappingArr)
	baseURL := channeltype.ChannelBaseURLs[channelType]
	if c.GetString("base_url") != "" {
		baseURL = c.GetString("base_url")
	}

	fullRequestURL := fmt.Sprintf("%s", baseURL)

	// 获取最新一条消息的内容
	serperRequest := struct {
		Q string `json:"q"`
	}{
		Q: searchRequest.GetLastMessageContent(),
	}

	jsonData, err := json.Marshal(serperRequest)
	if err != nil {
		return openai.ErrorWrapper(err, "marshal_request_body_failed", http.StatusInternalServerError)
	}
	requestBody := bytes.NewBuffer(jsonData)

	req, err := http.NewRequest(c.Request.Method, fullRequestURL, requestBody)
	if err != nil {
		return openai.ErrorWrapper(err, "new_request_failed", http.StatusInternalServerError)
	}

	req.Header.Set("X-API-KEY", strings.Replace(c.Request.Header.Get("Authorization"), "Bearer ", "", 1))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	resp, err := client.HTTPClient.Do(req)
	if err != nil {
		return openai.ErrorWrapper(err, "do_request_failed", http.StatusInternalServerError)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return openai.ErrorWrapper(fmt.Errorf("serper search error: %s", resp.Status), "google_search_not_success", resp.StatusCode)
	}

	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return openai.ErrorWrapper(err, "read_response_body_failed", http.StatusInternalServerError)
	}

	var serperResponse map[string]interface{}
	err = json.Unmarshal(responseBody, &serperResponse)
	if err != nil {
		return openai.ErrorWrapper(err, "unmarshal_response_body_failed", http.StatusInternalServerError)
	}

	openAIResponse := openai.TextResponse{
		Id:      fmt.Sprintf("serper-%s", helper.GetUUID()),
		Object:  "chat.completion",
		Created: helper.GetTimestamp(),
		Model:   searchRequest.Model,
		Usage: relaymodel.Usage{
			PromptTokens:     1,
			CompletionTokens: 1,
			TotalTokens:      2,
		},
	}

	searchResults := formatSerperResults(serperResponse)
	openAIResponse.Choices = []openai.TextResponseChoice{
		{
			Index: 0,
			Message: relaymodel.Message{
				Role:    "assistant",
				Content: searchResults,
			},
			FinishReason: "stop",
		},
	}

	jsonResponse, err := json.Marshal(openAIResponse)
	if err != nil {
		return openai.ErrorWrapper(err, "marshal_response_body_failed", http.StatusInternalServerError)
	}

	c.Writer.Header().Set("Content-Type", "application/json")
	c.Writer.WriteHeader(http.StatusOK)
	_, err = c.Writer.Write(jsonResponse)
	if err != nil {
		return openai.ErrorWrapper(err, "write_response_failed", http.StatusInternalServerError)
	}

	consumedTokens := int64(2) // 对于搜索请求，我们可以设置一个固定的token数
	quota := int64(float64(consumedTokens) * ratio * topupConvertRatio * userDiscount)
	if meta.BillingType == common.BillingTypeByCount {
		quota = int64(modelFixedPrice * 500000 * ratio * topupConvertRatio * userDiscount)
	}

	quotaDelta := quota - preConsumedQuota
	err = model.PostConsumeTokenQuota(tokenId, quotaDelta)
	if err != nil {
		logger.SysError("error consuming token remain quota: " + err.Error())
	}

	err = model.CacheUpdateUserQuota(ctx, userId)
	if err != nil {
		logger.SysError("error updating user quota cache: " + err.Error())
	}

	requestDuration := helper.GetTimestamp() - _startTime
	logContent := fmt.Sprintf("模型倍率 %.2f，分组倍率 %.2f，用时 %d秒", modelRatio, groupRatio, requestDuration)
	model.RecordConsumeLog(ctx, userId, channelId, 1, 1, searchRequest.Model, c.GetString("token_name"), c.GetString("token_key"), c.GetString("channel_name"), int(quota), requestDuration, false, logContent)
	model.UpdateUserUsedQuotaAndRequestCount(userId, quota)
	model.UpdateChannelUsedQuota(channelId, quota)

	return nil
}
func formatSerperResults(serperResponse map[string]interface{}) string {
	var result strings.Builder

	if organic, ok := serperResponse["organic"].([]interface{}); ok {
		result.WriteString("搜索结果：\n\n")
		for i, item := range organic {
			if i >= 5 {
				break
			}
			if itemMap, ok := item.(map[string]interface{}); ok {
				title := itemMap["title"].(string)
				link := itemMap["link"].(string)
				snippet := itemMap["snippet"].(string)
				result.WriteString(fmt.Sprintf("%d. %s\n链接：%s\n摘要：%s\n\n", i+1, title, link, snippet))
			}
		}
	}

	return result.String()
}

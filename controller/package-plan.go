package controller

import (
	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/model"
	"net/http"
	"strconv"
)

// GetAllPackagePlans 获取所有套餐
func GetAllPackagePlans(c *gin.Context) {
	// 获取user
	userId := c.GetInt("id")
	user, err := model.GetUserById(userId, false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"msg":     err.Error(),
		})
		return
	}
	if user == nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"msg":     "用户不存在",
		})
		return
	}
	visibleGroups := user.Group
	if user.Role >= common.RoleAdminUser {
		visibleGroups = ""

	}

	p, _ := strconv.Atoi(c.Query("p"))
	if p < 0 {
		p = 0
	}
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}
	id, _ := strconv.Atoi(c.Query("id"))
	name := c.Query("name")
	status, _ := strconv.Atoi(c.Query("status"))
	plans, err2 := model.GetAllPackagePlans(p*pageSize, pageSize, id, name, status, visibleGroups)
	if err2 != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"msg":     err2.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    plans,
	})

}

// CountAllPackagePlans 获取所有套餐数量
func CountAllPackagePlans(c *gin.Context) {
	// 获取user
	userId := c.GetInt("id")
	user, err := model.GetUserById(userId, false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"msg":     err.Error(),
		})
		return
	}
	if user == nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"msg":     "用户不存在",
		})
		return
	}
	visibleGroups := user.Group
	if user.Role >= common.RoleAdminUser {
		visibleGroups = ""

	}
	id, _ := strconv.Atoi(c.Query("id"))
	name := c.Query("name")
	status, _ := strconv.Atoi(c.Query("status"))
	count, err2 := model.CountAllPackagePlans(id, name, status, visibleGroups)
	if err2 != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"msg":     err2.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"count": count,
		},
	})
}

// GetPackagePlan 获取套餐
func GetPackagePlan(c *gin.Context) {
	id, _ := strconv.Atoi(c.Param("id"))
	plan, err2 := model.GetPackagePlanById(int64(id))
	if err2 != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"msg":     err2.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    plan,
	})
}

// AddPackagePlan 添加套餐
func AddPackagePlan(c *gin.Context) {
	var plan model.PackagePlan
	err := c.BindJSON(&plan)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"msg":     err.Error(),
		})
		return
	}

	err2 := model.CreatePackagePlan(&plan)
	if err2 != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"msg":     err2.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "添加成功",
	})
}

// UpdatePackagePlan 更新套餐
func UpdatePackagePlan(c *gin.Context) {
	var plan model.PackagePlan
	err := c.BindJSON(&plan)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"msg":     err.Error(),
		})
		return
	}

	err2 := model.UpdatePackagePlan(plan.Id, &plan)
	if err2 != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"msg":     err2.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "更新成功",
	})
}

// DeletePackagePlan 删除套餐
func DeletePackagePlan(c *gin.Context) {
	id, _ := strconv.Atoi(c.Param("id"))
	err2 := model.DeletePackagePlan(id)
	if err2 != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"msg":     err2.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除成功",
	})
}

// DeletePackagePlanByIds 批量删除套餐
func DeletePackagePlanByIds(c *gin.Context) {
	var ids []int
	err := c.BindJSON(&ids)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"msg":     err.Error(),
		})
		return
	}

	err2 := model.DeletePackagePlanByIds(ids)
	if err2 != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"msg":     err2.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除成功",
	})
}

package controller

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/model"
)

type CreateAccessTokenRequest struct {
	Name        string `json:"name" binding:"required"`
	Permissions int64  `json:"permissions" binding:"required"`
	ExpiresIn   int64  `json:"expires_in"` // 有效期（天数），0表示永不过期
	Remark      string `json:"remark"`
}

// 创建新的访问令牌
func CreateNewAccessToken(c *gin.Context) {
	userId := c.GetInt(ctxkey.Id)
	if userId == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户未登录",
		})
		return
	}

	var req CreateAccessTokenRequest
	if err := c.<PERSON>ind<PERSON>(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的请求参数: " + err.Error(),
		})
		return
	}

	// 计算过期时间
	var expiredTime int64 = -1 // 默认永不过期
	if req.ExpiresIn > 0 {
		expiredTime = time.Now().Unix() + req.ExpiresIn*86400 // 天数转换为秒
	}

	token, err := model.CreateAccessToken(userId, req.Name, req.Permissions, expiredTime, req.Remark)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "创建访问令牌失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "访问令牌创建成功",
		"data":    token,
	})
}

// 获取用户的所有访问令牌
func GetUserAccessTokens(c *gin.Context) {
	userId := c.GetInt(ctxkey.Id)
	if userId == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户未登录",
		})
		return
	}

	tokens, err := model.GetUserAccessTokens(userId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取访问令牌失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    tokens,
	})
}

// 禁用访问令牌
func DisableAccessToken(c *gin.Context) {
	userId := c.GetInt(ctxkey.Id)
	if userId == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户未登录",
		})
		return
	}

	tokenId, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的令牌ID",
		})
		return
	}

	// 验证是否是用户自己的令牌
	tokens, _ := model.GetUserAccessTokens(userId)
	found := false
	for _, token := range tokens {
		if token.Id == tokenId {
			found = true
			break
		}
	}

	if !found {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权操作此令牌",
		})
		return
	}

	err = model.UpdateAccessTokenStatus(tokenId, 2) // 2表示禁用
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "禁用令牌失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "令牌已禁用",
	})
}

// 启用访问令牌
func EnableAccessToken(c *gin.Context) {
	userId := c.GetInt(ctxkey.Id)
	if userId == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户未登录",
		})
		return
	}

	tokenId, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的令牌ID",
		})
		return
	}

	// 验证是否是用户自己的令牌
	tokens, _ := model.GetUserAccessTokens(userId)
	found := false
	for _, token := range tokens {
		if token.Id == tokenId {
			found = true
			break
		}
	}

	if !found {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权操作此令牌",
		})
		return
	}

	err = model.UpdateAccessTokenStatus(tokenId, 1) // 1表示启用
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "启用令牌失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "令牌已启用",
	})
}

// 删除访问令牌
func DeleteAccessToken(c *gin.Context) {
	userId := c.GetInt(ctxkey.Id)
	if userId == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户未登录",
		})
		return
	}

	tokenId, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的令牌ID",
		})
		return
	}

	// 验证是否是用户自己的令牌
	tokens, _ := model.GetUserAccessTokens(userId)
	found := false
	for _, token := range tokens {
		if token.Id == tokenId {
			found = true
			break
		}
	}

	if !found {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权操作此令牌",
		})
		return
	}

	err = model.DeleteAccessToken(tokenId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "删除令牌失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "令牌已删除",
	})
}

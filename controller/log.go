package controller

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"sync"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/message"
	"github.com/songquanpeng/one-api/controller/optimizer"
	"github.com/songquanpeng/one-api/middleware"
	"github.com/songquanpeng/one-api/model"
)

func GetAllLogs(c *gin.Context) {
	userId := c.GetInt(ctxkey.Id)
	timezone := c.GetString(ctxkey.Timezone)

	// 检查是否有查看所有日志的权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")

	// 超级管理员或有日志权限的管理员可以查看所有日志
	hasFullLogPermission := role == model.RoleRootUser ||
		(role == model.RoleAdminUser && (adminAccessFlags&2) == 2) // PermissionLog = 1 << 1 = 2

	if !hasFullLogPermission {
		// 没有权限查看所有日志，降级到查看用户自己的日志
		GetUserLogs(c)
		return
	}
	p, _ := strconv.Atoi(c.Query("p"))
	if p < 0 {
		p = 0
	}
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}
	// 校验pageSize超过5000则抛出异常
	if pageSize > 5000 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "每页条数不能超过 5000",
		})
		return
	}

	// 解析查询参数
	logTypeOrigin := c.Query("type")
	var logType []int
	if logTypeOrigin != "" {
		for _, v := range strings.Split(logTypeOrigin, ",") {
			t, _ := strconv.Atoi(v)
			logType = append(logType, t)
		}
	}
	startTimestamp, _ := strconv.ParseInt(c.Query("start_timestamp"), 10, 64)
	endTimestamp, _ := strconv.ParseInt(c.Query("end_timestamp"), 10, 64)
	username := c.Query("username")
	tokenName := c.Query("token_name")
	tokenKey := c.Query("token_key")
	tokenGroup := c.Query("token_group")
	modelName := c.Query("model_name")
	channel, _ := strconv.Atoi(c.Query("channel"))
	channelName := c.Query("channel_name")
	isStream := c.Query("is_stream")
	requestId := c.Query("request_id")
	errorCode := c.Query("error_code")
	ip := c.Query("ip")

	// 检查渠道查看权限，如果没有权限则不使用渠道参数进行查询
	hasChannelReadPermission := role == model.RoleRootUser || // 超级管理员
		middleware.HasReadPermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelRead)

	if !hasChannelReadPermission {
		channel = 0      // 清空渠道参数
		channelName = "" // 清空渠道名称参数
	}

	// 使用helper包中的解析函数
	promptTokensMin := helper.ParseIntPtr(c.Query("prompt_tokens_min"))
	promptTokensMax := helper.ParseIntPtr(c.Query("prompt_tokens_max"))
	completionTokensMin := helper.ParseIntPtr(c.Query("completion_tokens_min"))
	completionTokensMax := helper.ParseIntPtr(c.Query("completion_tokens_max"))
	totalDurationMin := helper.ParseFloatPtr(c.Query("total_duration_min"))
	totalDurationMax := helper.ParseFloatPtr(c.Query("total_duration_max"))
	requestDurationMin := helper.ParseFloatPtr(c.Query("request_duration_min"))
	requestDurationMax := helper.ParseFloatPtr(c.Query("request_duration_max"))
	responseFirstByteDurationMin := helper.ParseFloatPtr(c.Query("response_first_byte_duration_min"))
	responseFirstByteDurationMax := helper.ParseFloatPtr(c.Query("response_first_byte_duration_max"))

	// 解析quota范围参数
	quotaMin := helper.ParseIntPtr(c.Query("quota_min"))
	quotaMax := helper.ParseIntPtr(c.Query("quota_max"))

	excludeModels := strings.Split(c.Query("exclude_models"), ",")
	excludeModels = lo.Filter(excludeModels, func(item string, index int) bool {
		return item != ""
	})
	excludeErrorCodes := strings.Split(c.Query("exclude_error_codes"), ",")
	excludeErrorCodes = lo.Filter(excludeErrorCodes, func(item string, index int) bool {
		return item != ""
	})

	if common.ShellApiLogOptimizerEnabled && config.PreferOptimizerQueryEnabled {
		logs, err := optimizer.GetLogsFromLogOptimizer(true, 0, timezone, logType, startTimestamp, endTimestamp,
			modelName, username, tokenName, tokenKey, channelName, channel, p+1, pageSize,
			promptTokensMin, promptTokensMax, completionTokensMin, completionTokensMax,
			totalDurationMin, totalDurationMax, requestDurationMin, requestDurationMax,
			responseFirstByteDurationMin, responseFirstByteDurationMax,
			excludeModels, errorCode, excludeErrorCodes)
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "",
			"data":    logs,
		})
		return
	}

	logs, err := model.GetAllLogs(userId, timezone, logType, startTimestamp, endTimestamp, modelName, username, tokenName,
		tokenKey, tokenGroup, channelName, p*pageSize, pageSize, channel, isStream, requestId, ip, promptTokensMin, promptTokensMax,
		completionTokensMin, completionTokensMax, totalDurationMin, totalDurationMax, requestDurationMin, requestDurationMax,
		responseFirstByteDurationMin, responseFirstByteDurationMax, excludeModels, errorCode, excludeErrorCodes, quotaMin, quotaMax)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 如果没有渠道查看权限，隐藏返回数据中的渠道相关字段
	if !hasChannelReadPermission {
		for _, log := range logs {
			log.ChannelName = ""
			log.ChannelId = 0
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    logs,
	})
	return
}

func CountAllLogs(c *gin.Context) {
	userId := c.GetInt(ctxkey.Id)

	// 检查是否有查看所有日志的权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")

	hasFullLogPermission := role == model.RoleRootUser ||
		(role == model.RoleAdminUser && (adminAccessFlags&2) == 2)

	if !hasFullLogPermission {
		// 没有权限查看所有日志，降级到查看用户自己的日志计数
		CountUserLogs(c)
		return
	}
	logTypeOrigin := c.Query("type")
	var logType []int
	if logTypeOrigin != "" {
		for _, v := range strings.Split(logTypeOrigin, ",") {
			t, _ := strconv.Atoi(v)
			logType = append(logType, t)
		}
	}
	startTimestamp, _ := strconv.ParseInt(c.Query("start_timestamp"), 10, 64)
	endTimestamp, _ := strconv.ParseInt(c.Query("end_timestamp"), 10, 64)
	username := c.Query("username")
	tokenName := c.Query("token_name")
	tokenKey := c.Query("token_key")
	tokenGroup := c.Query("token_group")
	modelName := c.Query("model_name")
	channel, _ := strconv.Atoi(c.Query("channel"))
	channelName := c.Query("channel_name")
	isStream := c.Query("is_stream")
	requestId := c.Query("request_id")
	errorCode := c.Query("error_code")
	ip := c.Query("ip")

	// 检查渠道查看权限，如果没有权限则不使用渠道参数进行查询
	hasChannelReadPermission := role == model.RoleRootUser || // 超级管理员
		middleware.HasReadPermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelRead)

	if !hasChannelReadPermission {
		channel = 0      // 清空渠道参数
		channelName = "" // 清空渠道名称参数
	}

	// 使用helper包中的解析函数
	promptTokensMin := helper.ParseIntPtr(c.Query("prompt_tokens_min"))
	promptTokensMax := helper.ParseIntPtr(c.Query("prompt_tokens_max"))
	completionTokensMin := helper.ParseIntPtr(c.Query("completion_tokens_min"))
	completionTokensMax := helper.ParseIntPtr(c.Query("completion_tokens_max"))
	totalDurationMin := helper.ParseFloatPtr(c.Query("total_duration_min"))
	totalDurationMax := helper.ParseFloatPtr(c.Query("total_duration_max"))
	requestDurationMin := helper.ParseFloatPtr(c.Query("request_duration_min"))
	requestDurationMax := helper.ParseFloatPtr(c.Query("request_duration_max"))
	responseFirstByteDurationMin := helper.ParseFloatPtr(c.Query("response_first_byte_duration_min"))
	responseFirstByteDurationMax := helper.ParseFloatPtr(c.Query("response_first_byte_duration_max"))

	// 解析quota范围参数
	quotaMin := helper.ParseIntPtr(c.Query("quota_min"))
	quotaMax := helper.ParseIntPtr(c.Query("quota_max"))

	excludeModels := strings.Split(c.Query("exclude_models"), ",")
	// 移除空字符串
	excludeModels = lo.Filter(excludeModels, func(item string, index int) bool {
		return item != ""
	})
	excludeErrorCodes := strings.Split(c.Query("exclude_error_codes"), ",")
	// 移除空字符串
	excludeErrorCodes = lo.Filter(excludeErrorCodes, func(item string, index int) bool {
		return item != ""
	})

	if common.ShellApiLogOptimizerEnabled && config.PreferOptimizerQueryEnabled {
		// 从log-optimizer获取日志数量
		count, err := optimizer.CountLogsFromLogOptimizer(true, 0, logType, startTimestamp, endTimestamp, modelName,
			username, tokenName, tokenKey, channelName, channel, isStream, requestId, ip,
			promptTokensMin, promptTokensMax, completionTokensMin, completionTokensMax,
			totalDurationMin, totalDurationMax, requestDurationMin, requestDurationMax,
			responseFirstByteDurationMin, responseFirstByteDurationMax,
			excludeModels, errorCode, excludeErrorCodes)
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "",
			"data": gin.H{
				"count": count,
			},
		})
		return
	}

	cnt, err := model.CountAllLogs(userId, logType, startTimestamp, endTimestamp, modelName, username, tokenName,
		tokenKey, tokenGroup, channelName, channel, isStream, requestId, ip, promptTokensMin, promptTokensMax,
		completionTokensMin, completionTokensMax, totalDurationMin, totalDurationMax, requestDurationMin,
		requestDurationMax, responseFirstByteDurationMin, responseFirstByteDurationMax, excludeModels, errorCode, excludeErrorCodes, quotaMin, quotaMax)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"count": cnt,
		},
	})
}

func GetLogDetailById(c *gin.Context) {
	logId, _ := strconv.Atoi(c.Query("log_id"))
	if logId == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "日志ID不能为空",
		})
		return
	}
	log, err := model.GetLogExtendByLogId(logId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "日志不存在",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    log,
	})
}

func GetUserLogs(c *gin.Context) {
	p, _ := strconv.Atoi(c.Query("p"))
	if p < 0 {
		p = 0
	}
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}
	// 校验pageSize超过5000则抛出异常
	if pageSize > 5000 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "每页条数不能超过 5000",
		})
		return
	}
	userId := c.GetInt(ctxkey.Id)
	timezone := c.GetString(ctxkey.Timezone)
	logTypeOrigin := c.Query("type")
	// 解析原始类型为数组
	var logType []int
	if logTypeOrigin != "" {
		for _, v := range strings.Split(logTypeOrigin, ",") {
			t, _ := strconv.Atoi(v)
			logType = append(logType, t)
		}
	}
	startTimestamp, _ := strconv.ParseInt(c.Query("start_timestamp"), 10, 64)
	endTimestamp, _ := strconv.ParseInt(c.Query("end_timestamp"), 10, 64)
	tokenName := c.Query("token_name")
	tokenKey := c.Query("token_key")
	modelName := c.Query("model_name")
	isStream := c.Query("is_stream")
	requestId := c.Query("request_id")

	if common.ShellApiLogOptimizerEnabled && config.PreferOptimizerQueryEnabled {
		logs, err := optimizer.GetLogsFromLogOptimizer(false, userId, timezone, logType, startTimestamp, endTimestamp,
			modelName, "", tokenName, tokenKey, "", 0, p+1, pageSize,
			nil, nil, nil, nil,
			nil, nil, nil, nil,
			nil, nil, []string{}, "", []string{})
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		// 处理日志耗时类型
		for _, log := range logs {
			switch config.LogDurationType {
			case 1:
				log.DurationForView = log.RequestDuration
			case 2:
				log.DurationForView = log.ResponseFirstByteDuration
			case 3:
				log.DurationForView = log.TotalDuration
			default:
				log.DurationForView = log.ResponseFirstByteDuration
			}
			log.TotalDuration = 0
			log.ResponseFirstByteDuration = 0
			log.RequestDuration = 0
		}
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "",
			"data":    logs,
		})
		return
	}

	logs, err := model.GetUserLogs(userId, logType, startTimestamp, endTimestamp, modelName, tokenName, tokenKey, p*pageSize, pageSize, isStream, requestId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	for _, log := range logs {
		switch config.LogDurationType {
		// 日志耗时类型: 1: 请求耗时; 2: 响应首个字节耗时; 3: 总耗时 默认2
		case 1:
			log.DurationForView = log.RequestDuration
		case 2:
			log.DurationForView = log.ResponseFirstByteDuration
		case 3:
			log.DurationForView = log.TotalDuration
		default:
			log.DurationForView = log.ResponseFirstByteDuration
		}
		// 使用DurationForView替代TotalDuration、ResponseFirstByteDuration和RequestDuration
		log.TotalDuration = 0
		log.ResponseFirstByteDuration = 0
		log.RequestDuration = 0
		var otherMap map[string]interface{} // 声明一个 map 来存储解析的 JSON
		// 解析 log.Other 中的字符串为 map
		if log.Other != "" {
			err := json.Unmarshal([]byte(log.Other), &otherMap)
			if err == nil {
				// 直接删除顶层的 admin_info 属性
				delete(otherMap, "admin_info")
				// 转换回字符串
				otherStr, err := json.Marshal(otherMap)
				if err == nil {
					log.Other = string(otherStr)
				}
			} else {
				log.Other = ""
			}
		}
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    logs,
	})
	return
}

func GetUserTopupLogs(c *gin.Context) {
	p, _ := strconv.Atoi(c.Query("p"))
	if p < 0 {
		p = 0
	}
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}
	// 校验pageSize超过5000则抛出异常
	if pageSize > 5000 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "每页条数不能超过 5000",
		})
		return
	}
	userId := c.GetInt("id")
	// 解析原始类型为数组model.LogTypeTopup
	var logType []int
	logType = append(logType, model.LogTypeTopup)
	startTimestamp, _ := strconv.ParseInt(c.Query("start_timestamp"), 10, 64)
	endTimestamp, _ := strconv.ParseInt(c.Query("end_timestamp"), 10, 64)
	tokenName := c.Query("token_name")
	tokenKey := c.Query("token_key")
	modelName := c.Query("model_name")
	isStream := c.Query("is_stream")
	requestId := c.Query("request_id")
	logs, err := model.GetUserLogs(userId, logType, startTimestamp, endTimestamp, modelName, tokenName, tokenKey, p*pageSize, pageSize, isStream, requestId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    logs,
	})
	return
}

func CountUserLogs(c *gin.Context) {
	userId := c.GetInt("id")
	logTypeStr := c.Query("type")
	var logType []int
	if logTypeStr != "" {
		for _, v := range strings.Split(logTypeStr, ",") {
			if t, err := strconv.Atoi(v); err == nil {
				logType = append(logType, t)
			}
		}
	}

	startTimestamp := helper.ParseInt64Ptr(c.Query("start_timestamp"))
	endTimestamp := helper.ParseInt64Ptr(c.Query("end_timestamp"))
	tokenName := c.Query("token_name")
	tokenKey := c.Query("token_key")
	modelName := c.Query("model_name")
	isStream := c.Query("is_stream")
	requestId := c.Query("request_id")

	if common.ShellApiLogOptimizerEnabled && config.PreferOptimizerQueryEnabled {
		var startTime, endTime int64
		if startTimestamp != nil {
			startTime = *startTimestamp
		}
		if endTimestamp != nil {
			endTime = *endTimestamp
		}

		count, err := optimizer.CountLogsFromLogOptimizer(false, userId, logType, startTime, endTime,
			modelName, "", tokenName, tokenKey, "", 0,
			isStream, requestId, "", nil, nil, nil, nil,
			nil, nil, nil, nil,
			nil, nil, []string{}, "", []string{})
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "",
			"data": gin.H{
				"count": count,
			},
		})
		return
	}

	// 如果优化器未启用，使用原有的方法
	var startTime, endTime int64
	if startTimestamp != nil {
		startTime = *startTimestamp
	}
	if endTimestamp != nil {
		endTime = *endTimestamp
	}
	cnt, err := model.CountUserLogs(userId, logType, startTime, endTime, modelName, tokenName, tokenKey, isStream, requestId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"count": cnt,
		},
	})
	return
}

func SearchAllLogs(c *gin.Context) {
	// 检查是否有查看所有日志的权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")

	hasFullLogPermission := role == model.RoleRootUser ||
		(role == model.RoleAdminUser && (adminAccessFlags&2) == 2)

	if !hasFullLogPermission {
		// 没有权限查看所有日志，降级到搜索用户自己的日志
		SearchUserLogs(c)
		return
	}

	keyword := c.Query("keyword")
	logs, err := model.SearchAllLogs(keyword)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 检查渠道查看权限，如果没有权限则隐藏渠道相关字段
	hasChannelReadPermission := role == model.RoleRootUser || // 超级管理员
		middleware.HasReadPermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelRead)

	if !hasChannelReadPermission {
		for _, log := range logs {
			log.ChannelName = ""
			log.ChannelId = 0
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    logs,
	})
	return
}

func SearchUserLogs(c *gin.Context) {
	keyword := c.Query("keyword")
	userId := c.GetInt(ctxkey.Id)
	logs, err := model.SearchUserLogs(userId, keyword)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    logs,
	})
	return
}

func SearchUserLogsByKey(c *gin.Context) {
	key := c.Query("key")
	if key == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "key is required",
		})
		return
	}
	// 如果tokenName是sk-开头就去库里查询对应的名称
	if len(key) > 3 && key[:3] == "sk-" {
		key = key[3:]
	}
	p, _ := strconv.Atoi(c.Query("p"))
	if p < 0 {
		p = 0
	}
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}
	// 校验pageSize超过5000则抛出异常
	if pageSize > 5000 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "每页条数不能超过 5000",
		})
		return
	}
	logs, err := model.SearchUserLogsByKey(key, p*pageSize, pageSize)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    logs,
	})
	return
}

func CountUserLogsByKey(c *gin.Context) {
	key := c.Query("key")
	if key == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "key is required",
		})
		return
	}
	// 如果tokenName是sk-开头就去库里查询对应的名称
	if key != "" && len(key) > 3 && key[:3] == "sk-" {
		key = key[3:]
	}
	cnt, err := model.CountUserLogsByKey(key)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"count": cnt,
		},
	})
	return
}

func GetLogByKey(c *gin.Context) {
	// 检查是否有查看所有日志的权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")

	hasFullLogPermission := role == model.RoleRootUser ||
		(role == model.RoleAdminUser && (adminAccessFlags&2) == 2)

	if !hasFullLogPermission {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权限访问",
		})
		return
	}

	key := c.Query("key")
	logs, err := model.GetLogByKey(key)
	if err != nil {
		c.JSON(200, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 检查渠道查看权限，如果没有权限则隐藏渠道相关字段
	hasChannelReadPermission := role == model.RoleRootUser || // 超级管理员
		middleware.HasReadPermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelRead)

	if !hasChannelReadPermission {
		for _, log := range logs {
			log.ChannelName = ""
			log.ChannelId = 0
		}
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "",
		"data":    logs,
	})
}

func GetLogsStat(c *gin.Context) {
	// 检查是否有查看所有日志的权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")

	hasFullLogPermission := role == model.RoleRootUser ||
		(role == model.RoleAdminUser && (adminAccessFlags&2) == 2)

	if !hasFullLogPermission {
		// 没有权限查看所有日志，降级到查看用户自己的日志统计
		GetLogsSelfStat(c)
		return
	}

	logType, _ := strconv.Atoi(c.Query("type"))
	startTimestamp, _ := strconv.ParseInt(c.Query("start_timestamp"), 10, 64)
	endTimestamp, _ := strconv.ParseInt(c.Query("end_timestamp"), 10, 64)
	tokenName := c.Query("token_name")
	tokenKey := c.Query("token_key")
	tokenGroup := c.Query("token_group")
	username := c.Query("username")
	modelName := c.Query("model_name")
	channel, _ := strconv.Atoi(c.Query("channel"))

	// 检查渠道查看权限，如果没有权限则不使用渠道参数进行统计
	hasChannelReadPermission := role == model.RoleRootUser || // 超级管理员
		middleware.HasReadPermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelRead)

	if !hasChannelReadPermission {
		channel = 0 // 清空渠道参数，不按渠道进行统计
	}

	userRedis := true
	userRedisStr := c.Query("use_redis")
	if userRedisStr == "false" {
		userRedis = false
	}
	var stat model.Stat
	var err error

	if common.ShellApiLogOptimizerEnabled && config.PreferOptimizerQueryEnabled {
		stat, err = optimizer.GetLogsStatFromLogOptimizer(logType, startTimestamp, endTimestamp, modelName, username, tokenName, tokenKey, channel)
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
	} else {
		stat = model.SumUsedQuota(logType, startTimestamp, endTimestamp, modelName, username, tokenName, tokenKey, tokenGroup, channel, userRedis)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"quota":            stat.Quota,
			"rpm":              stat.Rpm,
			"tpm":              stat.Tpm,
			"mpm":              stat.Mpm,
			"is_realtime_data": stat.IsRealtimeData,
		},
	})
}

func GetAllLogsModelUsage(c *gin.Context) {
	// 检查是否有查看所有日志的权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")

	hasFullLogPermission := role == model.RoleRootUser ||
		(role == model.RoleAdminUser && (adminAccessFlags&2) == 2)

	if !hasFullLogPermission {
		// 没有权限查看所有日志，降级到查看用户自己的日志模型使用情况
		GetUserLogsModelUsage(c)
		return
	}

	timezone := c.GetString(ctxkey.Timezone)
	logType, _ := strconv.Atoi(c.Query("type"))
	startTimestamp, _ := strconv.ParseInt(c.Query("start_timestamp"), 10, 64)
	endTimestamp, _ := strconv.ParseInt(c.Query("end_timestamp"), 10, 64)
	tokenName := c.Query("token_name")
	username := c.Query("username")
	modelName := c.Query("model_name")
	channel, _ := strconv.Atoi(c.Query("channel"))
	channelName := c.Query("channel_name")

	// 检查渠道查看权限，如果没有权限则不使用渠道参数进行查询
	hasChannelReadPermission := role == model.RoleRootUser || // 超级管理员
		middleware.HasReadPermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelRead)

	if !hasChannelReadPermission {
		channel = 0      // 清空渠道参数
		channelName = "" // 清空渠道名称参数
	}

	if common.ShellApiLogOptimizerEnabled && config.PreferOptimizerQueryEnabled {
		modelUsage, err := optimizer.GetAllLogsModelUsageFromLogOptimizer(logType, timezone, startTimestamp, endTimestamp, modelName, username, tokenName, channel, channelName)
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "",
			"data":    modelUsage,
		})
		return
	}

	// 如果优化器未启用，使用原有的方法
	modelUsage := model.SumAllModelUsage(logType, timezone, startTimestamp, endTimestamp, modelName, username, tokenName, channel, channelName)
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    modelUsage,
	})
}

func GetAllDailyUsageStatsByDimension(c *gin.Context) {
	// 检查是否有查看所有日志的权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")

	hasFullLogPermission := role == model.RoleRootUser ||
		(role == model.RoleAdminUser && (adminAccessFlags&2) == 2)

	if !hasFullLogPermission {
		// 没有权限查看所有日志，降级到查看用户自己的日志使用统计
		GetUserDailyUsageStatsByDimension(c)
		return
	}

	timezone := c.GetString(ctxkey.Timezone)
	startTimestamp, _ := strconv.ParseInt(c.Query("start_timestamp"), 10, 64)
	endTimestamp, _ := strconv.ParseInt(c.Query("end_timestamp"), 10, 64)
	tokenName := c.Query("token_name")
	username := c.Query("username")
	modelName := c.Query("model_name")
	channel, _ := strconv.Atoi(c.Query("channel"))
	channelName := c.Query("channel_name")
	dimension := c.Query("dimension")
	granularity := c.Query("granularity")

	// 检查渠道查看权限，如果没有权限则不使用渠道参数进行查询
	hasChannelReadPermission := role == model.RoleRootUser || // 超级管理员
		middleware.HasReadPermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelRead)

	if !hasChannelReadPermission {
		channel = 0      // 清空渠道参数
		channelName = "" // 清空渠道名称参数
	}

	if common.ShellApiLogOptimizerEnabled && config.PreferOptimizerQueryEnabled {
		dailyModelUsageStats, err := optimizer.GetDailyUsageStatsFromLogOptimizer(0, timezone, tokenName, username, channel, channelName, modelName, startTimestamp, endTimestamp, dimension, granularity)
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "",
			"data":    dailyModelUsageStats,
		})
		return
	}

	// 使用统一的方法获取统计数据
	dailyModelUsageStats := model.SumAllDailyUsageStatsByDimension(0, timezone, tokenName, username, channel, channelName, modelName, startTimestamp, endTimestamp, dimension, granularity)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    dailyModelUsageStats,
	})
	return
}

func GetUserDailyUsageStatsByDimension(c *gin.Context) {
	userId := c.GetInt("id")
	timezone := c.GetString(ctxkey.Timezone)
	if userId == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "user id is required",
		})
		return
	}
	startTimestamp, _ := strconv.ParseInt(c.Query("start_timestamp"), 10, 64)
	endTimestamp, _ := strconv.ParseInt(c.Query("end_timestamp"), 10, 64)
	tokenName := c.Query("token_name")
	username := c.Query("username")
	modelName := c.Query("model_name")
	channel, _ := strconv.Atoi(c.Query("channel"))
	channelName := c.Query("channel_name")
	dimension := c.Query("dimension")
	granularity := c.Query("granularity")

	if common.ShellApiLogOptimizerEnabled && config.PreferOptimizerQueryEnabled {
		dailyModelUsageStats, err := optimizer.GetDailyUsageStatsFromLogOptimizer(userId, timezone, tokenName, username, channel, channelName, modelName, startTimestamp, endTimestamp, dimension, granularity)
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "",
			"data":    dailyModelUsageStats,
		})
		return
	}

	dailyModelUsageStats := model.SumAllDailyUsageStatsByDimension(userId, timezone, tokenName, username, channel, channelName, modelName, startTimestamp, endTimestamp, dimension, granularity)
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    dailyModelUsageStats,
	})
	return
}

func GetUserLogsModelUsage(c *gin.Context) {
	logType, _ := strconv.Atoi(c.Query("type"))
	startTimestamp, _ := strconv.ParseInt(c.Query("start_timestamp"), 10, 64)
	endTimestamp, _ := strconv.ParseInt(c.Query("end_timestamp"), 10, 64)
	tokenName := c.Query("token_name")
	modelName := c.Query("model_name")
	userId := c.GetInt("id")
	if userId == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "user id is required",
		})
		return
	}
	modelUsage := model.SumUserModelUsage(userId, logType, startTimestamp, endTimestamp, modelName, tokenName)
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    modelUsage,
	})
	return
}

func GetLogsSelfStat(c *gin.Context) {
	username := c.GetString(ctxkey.Username)
	logType, _ := strconv.Atoi(c.Query("type"))
	startTimestamp, _ := strconv.ParseInt(c.Query("start_timestamp"), 10, 64)
	endTimestamp, _ := strconv.ParseInt(c.Query("end_timestamp"), 10, 64)
	tokenName := c.Query("token_name")
	tokenKey := c.Query("token_key")
	tokenGroup := c.Query("token_group")
	modelName := c.Query("model_name")
	channel, _ := strconv.Atoi(c.Query("channel"))
	userRedis := true
	userRedisStr := c.Query("use_redis")
	if userRedisStr == "false" {
		userRedis = false
	}
	if common.ShellApiLogOptimizerEnabled && config.PreferOptimizerQueryEnabled {
		stat, err := optimizer.GetLogsStatFromLogOptimizer(logType, startTimestamp, endTimestamp, modelName, username, tokenName, tokenKey, channel)
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "",
			"data": gin.H{
				"quota":            stat.Quota,
				"rpm":              stat.Rpm,
				"tpm":              stat.Tpm,
				"mpm":              stat.Mpm,
				"is_realtime_data": stat.IsRealtimeData,
			},
		})
		return
	}
	quotaNum := model.SumUsedQuota(logType, startTimestamp, endTimestamp, modelName, username, tokenName, tokenKey, tokenGroup, channel, userRedis)
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"quota":            quotaNum.Quota,
			"rpm":              quotaNum.Rpm,
			"tpm":              quotaNum.Tpm,
			"mpm":              quotaNum.Mpm,
			"is_realtime_data": quotaNum.IsRealtimeData,
		},
	})
	return
}

func GetLogsSelfStatByKey(c *gin.Context) {
	startTimestamp, _ := strconv.ParseInt(c.Query("start_timestamp"), 10, 64)
	endTimestamp, _ := strconv.ParseInt(c.Query("end_timestamp"), 10, 64)
	key := c.Query("key")
	if key == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "key is required",
		})
		return
	}
	if key != "" && len(key) > 3 && key[:3] == "sk-" {
		key = key[3:]
	}
	quotaNum := model.SumUsedQuotaByKey(key, startTimestamp, endTimestamp)
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"quota": quotaNum.Quota,
			"rpm":   quotaNum.Rpm,
			"tpm":   quotaNum.Tpm,
			"mpm":   quotaNum.Mpm,
		},
	})
	return
}

var deleteHistoryLogsLock sync.Mutex
var deleteHistoryLogsRunning = false

func DeleteHistoryLogs(c *gin.Context) {
	deleteHistoryLogsLock.Lock()
	if deleteHistoryLogsRunning {
		deleteHistoryLogsLock.Unlock()
		message := fmt.Sprintf("已有一个删除任务在运行中，请稍后再试... 当前任务执行进度(删除日志):已删除[%d]行,共需删除[%d]行,完成度[%d%%]",
			model.TotalAffectedHistoryLogsCount,
			model.TotalShouldDeleteHistoryLogsCount,
			lo.If(model.TotalShouldDeleteHistoryLogsCount == 0, 0).Else(int(float64(model.TotalAffectedHistoryLogsCount)/float64(model.TotalShouldDeleteHistoryLogsCount)*100)),
		)
		if model.TotalAffectedHistoryLogsCount == model.TotalShouldDeleteHistoryLogsCount && model.TotalAffectedUnbindLogExCount > 0 {
			message = fmt.Sprintf("已有一个删除任务在运行中，请稍后再试... 当前任务执行进度(删除日志扩展):已删除[%d]行,共需删除[%d]行,完成度[%d%%]",
				model.TotalAffectedUnbindLogExCount,
				model.TotalShouldDeleteUnbindLogExCount,
				lo.If(model.TotalShouldDeleteUnbindLogExCount == 0, 0).Else(int(float64(model.TotalAffectedUnbindLogExCount)/float64(model.TotalShouldDeleteUnbindLogExCount)*100)),
			)
		}
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": message,
		})
		return
	}
	targetTimestamp, _ := strconv.ParseInt(c.Query("target_timestamp"), 10, 64)
	beforeNowTimestamp, _ := strconv.ParseInt(c.Query("before_now_timestamp"), 10, 64)
	if targetTimestamp == 0 && beforeNowTimestamp == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "target timestamp or before_now_timestamp is required at least one",
		})
		return
	}
	if beforeNowTimestamp != 0 {
		targetTimestamp = helper.GetTimestamp() - beforeNowTimestamp
	}
	logTypeOrigin := c.Query("logType")
	// 解析原始类型为数组
	var logType []int
	if logTypeOrigin != "" {
		for _, v := range strings.Split(logTypeOrigin, ",") {
			t, _ := strconv.Atoi(v)
			logType = append(logType, t)
		}
	}
	deleteHistoryLogsRunning = true
	model.TotalShouldDeleteHistoryLogsCount = 0
	model.TotalAffectedHistoryLogsCount = 0
	model.TotalShouldDeleteUnbindLogExCount = 0
	model.TotalAffectedUnbindLogExCount = 0
	deleteHistoryLogsLock.Unlock()
	helper.SafeGoroutine(func() {
		defer func() {
			deleteHistoryLogsLock.Lock()
			deleteHistoryLogsRunning = false
			deleteHistoryLogsLock.Unlock()
		}()
		count, err := model.DeleteOldLog(targetTimestamp, logType)
		if err != nil {
			message.NotifyRootUser("删除日志任务失败", err.Error())
			return
		}
		message.NotifyRootUser("删除日志任务完成", "删除了"+strconv.Itoa(int(count))+"条日志")
		//err = model.DeleteInvalidLogExtend()
		//if err != nil {
		//	message.NotifyRootUser("删除日志扩展任务失败", err.Error())
		//	return
		//}
		//message.NotifyRootUser("删除日志扩展任务完成", "删除了无效的日志扩展记录")
	})
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除日志任务正在后台运行，运行结果稍后会通知管理员",
	})
	return
}

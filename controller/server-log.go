package controller

import (
	"bufio"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/songquanpeng/one-api/common/logger"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // 在生产环境中需要更严格的检查
	},
	HandshakeTimeout: 10 * time.Second,
}

type wsConnection struct {
	conn     *websocket.Conn
	paused   bool
	pauseMux sync.RWMutex
}

func (ws *wsConnection) isPaused() bool {
	ws.pauseMux.RLock()
	defer ws.pauseMux.RUnlock()
	return ws.paused
}

func (ws *wsConnection) setPaused(paused bool) {
	ws.pauseMux.Lock()
	defer ws.pauseMux.Unlock()
	ws.paused = paused
}

// TailServerLog 通过 WebSocket 实时推送日志
func TailServerLog(c *gin.Context) {
	logger.SysLog("开始建立WebSocket连接")

	// 打印所有请求头，用于调试
	headers := c.Request.Header
	logger.SysLog(fmt.Sprintf("收到的请求头: %+v", headers))

	// 检查请求头 (不区分大小写)
	if !strings.EqualFold(c.GetHeader("Connection"), "Upgrade") {
		logger.SysError(fmt.Sprintf("WebSocket连接失败: Connection header 不是 Upgrade, 实际值: %s", c.GetHeader("Connection")))
		c.String(http.StatusBadRequest, "非WebSocket请求")
		return
	}

	if !strings.Contains(strings.ToLower(c.GetHeader("Upgrade")), "websocket") {
		logger.SysError(fmt.Sprintf("WebSocket连接失败: Upgrade header 不是 websocket, 实际值: %s", c.GetHeader("Upgrade")))
		c.String(http.StatusBadRequest, "非WebSocket请求")
		return
	}

	ws, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		logger.SysError(fmt.Sprintf("WebSocket升级失败: %v, Headers: %v", err, c.Request.Header))
		return
	}
	defer func() {
		logger.SysLog("正常关闭WebSocket连接")
		ws.WriteMessage(websocket.CloseMessage,
			websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
		ws.Close()
	}()

	logger.SysLog("WebSocket连接建立成功")

	wsConn := &wsConnection{
		conn:   ws,
		paused: false,
	}

	// 创建一个done通道用于控制goroutine退出
	done := make(chan struct{})
	defer close(done)

	// 启动一个 goroutine 监听客户端消息
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.SysError(fmt.Sprintf("WebSocket goroutine panic: %v", r))
			}
		}()

		for {
			select {
			case <-done:
				logger.SysLog("WebSocket监听goroutine收到退出信号")
				return
			default:
				_, message, err := ws.ReadMessage()
				if err != nil {
					if websocket.IsUnexpectedCloseError(err,
						websocket.CloseNormalClosure,
						websocket.CloseGoingAway,
						websocket.CloseAbnormalClosure) {
						logger.SysError(fmt.Sprintf("读取WebSocket消息失败: %v", err))
					} else {
						logger.SysLog(fmt.Sprintf("WebSocket连接关闭: %v", err))
					}
					return
				}

				msgStr := string(message)
				logger.SysLog(fmt.Sprintf("收到WebSocket控制命令: %s", msgStr))

				if msgStr == "pause" {
					wsConn.setPaused(true)
					logger.SysLog("日志推送已暂停")
				} else if msgStr == "resume" {
					wsConn.setPaused(false)
					logger.SysLog("日志推送已恢复")
				}
			}
		}
	}()

	// 定义可能的日志路径
	possiblePaths := []string{
		"/app/logs", // Docker 容器内路径
		"./logs",    // 本地开发路径
		"../logs",   // 相对路径备选
	}

	var logFile string
	var pathExists bool

	// 检查所有可能的路径
	for _, path := range possiblePaths {
		currentPath := filepath.Join(path, "current.log")
		logger.SysLog(fmt.Sprintf("尝试打开日志文件: %s", currentPath))
		if _, err := os.Stat(currentPath); !os.IsNotExist(err) {
			logFile = currentPath
			pathExists = true
			logger.SysLog(fmt.Sprintf("找到可用的日志文件: %s", logFile))
			break
		}
	}

	if !pathExists {
		errMsg := fmt.Sprintf("未找到可用的日志文件，已尝试以下路径: %v", possiblePaths)
		logger.SysError(errMsg)
		ws.WriteMessage(websocket.TextMessage, []byte("Error: 无法找到日志文件"))
		return
	}

	file, err := os.Open(logFile)
	if err != nil {
		errMsg := fmt.Sprintf("打开日志文件失败: %s, 错误: %v", logFile, err)
		logger.SysError(errMsg)
		ws.WriteMessage(websocket.TextMessage, []byte("Error: 无法打开日志文件"))
		return
	}
	defer file.Close()

	logger.SysLog("开始读取日志文件")

	// 移动到文件末尾
	file.Seek(0, 2)
	reader := bufio.NewReader(file)

	// 保持连接并持续读取新的日志内容
	logger.SysLog("开始实时推送日志")
	for {
		line, err := reader.ReadString('\n')
		if err != nil {
			if err != io.EOF {
				logger.SysWarn(fmt.Sprintf("读取日志行失败: %v", err))
			}
			time.Sleep(100 * time.Millisecond)
			continue
		}

		if !wsConn.isPaused() {
			err = ws.WriteMessage(websocket.TextMessage, []byte(line))
			if err != nil {
				logger.SysError(fmt.Sprintf("发送WebSocket消息失败: %v", err))
				break
			}
		}
	}

	logger.SysLog("WebSocket连接关闭")
}

// GetRecentServerLog 获取最近的 n 行日志
func GetRecentServerLog(c *gin.Context) {
	lines := 100 // 默认返回最近100行
	logFile := filepath.Join("./logs", "current.log")

	file, err := os.Open(logFile)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "无法打开日志文件",
		})
		return
	}
	defer file.Close()

	// 使用环形缓冲区存储最近的日志行
	buffer := make([]string, lines)
	count := 0
	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		buffer[count%lines] = scanner.Text()
		count++
	}

	// 构建结果数组
	result := make([]string, 0, lines)
	start := 0
	if count < lines {
		start = 0
		result = buffer[:count]
	} else {
		start = count % lines
		result = append(buffer[start:], buffer[:start]...)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    result,
	})
}

// GetServerLogFiles 获取可用的日志文件列表
func GetServerLogFiles(c *gin.Context) {
	// 定义可能的日志路径
	possiblePaths := []string{
		"/app/logs", // Docker 容器内路径
		"./logs",    // 本地开发路径
		"../logs",   // 相对路径备选
	}

	var logDir string
	var pathExists bool

	// 检查所有可能的路径
	for _, path := range possiblePaths {
		logger.SysLog(fmt.Sprintf("尝试检查日志目录: %s", path))
		if _, err := os.Stat(path); !os.IsNotExist(err) {
			logDir = path
			pathExists = true
			logger.SysLog(fmt.Sprintf("找到可用的日志目录: %s", logDir))
			break
		}
	}

	if !pathExists {
		errMsg := fmt.Sprintf("未找到可用的日志目录，已尝试以下路径: %v", possiblePaths)
		logger.SysError(errMsg)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": errMsg,
		})
		return
	}

	// 尝试读取目录
	files, err := os.ReadDir(logDir)
	if err != nil {
		errMsg := fmt.Sprintf("读取日志目录失败: %s, 错误: %v", logDir, err)
		logger.SysError(errMsg)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": errMsg,
		})
		return
	}

	logger.SysLog(fmt.Sprintf("成功读取日志目录 %s，文件数量: %d", logDir, len(files)))

	var logFiles []map[string]interface{}
	for _, file := range files {
		if !file.IsDir() {
			fileInfo, err := file.Info()
			if err != nil {
				logger.SysWarn(fmt.Sprintf("获取文件信息失败: %s, 错误: %v", file.Name(), err))
				continue
			}

			logFiles = append(logFiles, map[string]interface{}{
				"name": file.Name(),
				"size": fileInfo.Size(),
				"time": fileInfo.ModTime().Format("2006-01-02 15:04:05"),
			})
		}
	}

	logger.SysLog(fmt.Sprintf("返回日志文件列表，有效文件数量: %d", len(logFiles)))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    logFiles,
	})
}

// DownloadServerLogFile 下载指定的日志文件
func DownloadServerLogFile(c *gin.Context) {
	filename := c.Param("filename")

	// 安全检查：防止目录遍历
	if filepath.Base(filename) != filename {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的文件名",
		})
		return
	}

	filePath := filepath.Join("./logs", filename)
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "文件不存在",
		})
		return
	}

	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Header("Content-Type", "application/octet-stream")
	c.File(filePath)
}

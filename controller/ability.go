package controller

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/model"
)

func GetAllAbilities(c *gin.Context) {
	p, _ := strconv.Atoi(c.Query("p"))
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))
	if p < 0 {
		p = 0
	}
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}
	orderBy := c.Query("orderBy")
	group := c.Query("group")
	queryModel := c.Query("model")
	channelId, _ := strconv.Atoi(c.Query("channel_id"))
	enabled, _ := strconv.ParseBool(c.Query("enabled"))

	abilities, err := model.GetAllAbilities(p*pageSize, pageSize, orderBy, group, queryModel, channelId, enabled)
	if err != nil {
		c.<PERSON>(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    abilities,
	})
}

func CountAbilities(c *gin.Context) {
	group := c.Query("group")
	queryModel := c.Query("model")
	channelId, _ := strconv.Atoi(c.Query("channel_id"))
	enabled, _ := strconv.ParseBool(c.Query("enabled"))

	count, err := model.CountAbilities(group, queryModel, channelId, enabled)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"count": count,
		},
	})
}

func AddAbility(c *gin.Context) {
	ctx := c.Request.Context()
	// 获取syncBothDB参数
	syncBothDB := c.Query("syncBothDB") == "true"

	var ability model.Ability
	err := c.ShouldBindJSON(&ability)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 根据syncBothDB参数选择合适的函数
	if syncBothDB {
		err = ability.InsertWithSync(syncBothDB)
	} else {
		err = ability.Insert()
	}

	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	model.RecordLog(ctx, c.GetInt("id"), model.LogTypeOperation, fmt.Sprintf("创建能力：%s - %s", ability.Group, ability.Model))
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
}

func ToggleAbilityEnabled(c *gin.Context) {
	// 获取syncBothDB参数
	syncBothDB := c.Query("syncBothDB") == "true"

	var req struct {
		Group     string `json:"group" binding:"required"`
		Model     string `json:"model" binding:"required"`
		ChannelID int    `json:"channel_id" binding:"required"`
		Enabled   bool   `json:"enabled"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": err.Error()})
		return
	}

	// 根据syncBothDB参数选择合适的函数
	var err error
	if syncBothDB {
		err = model.UpdateAbilityEnabledStatusWithSync(req.Group, req.Model, req.ChannelID, req.Enabled, syncBothDB)
	} else {
		err = model.UpdateAbilityEnabledStatus(req.Group, req.Model, req.ChannelID, req.Enabled)
	}

	if err != nil {
		c.JSON(http.StatusOK, gin.H{"success": false, "message": "Failed to update ability status"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "Ability status updated successfully"})
}

func UpdateAbility(c *gin.Context) {
	ctx := c.Request.Context()
	// 获取syncBothDB参数
	syncBothDB := c.Query("syncBothDB") == "true"

	var requestBody struct {
		Group     string `json:"group"`
		Model     string `json:"model"`
		ChannelId int    `json:"channel_id"`
		Enabled   bool   `json:"enabled"`
	}
	if err := c.ShouldBindJSON(&requestBody); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 根据syncBothDB参数选择合适的函数
	var err error
	if syncBothDB {
		err = model.UpdateAbilityEnabledStatusWithSync(requestBody.Group, requestBody.Model, requestBody.ChannelId, requestBody.Enabled, syncBothDB)
	} else {
		err = model.UpdateAbilityEnabledStatus(requestBody.Group, requestBody.Model, requestBody.ChannelId, requestBody.Enabled)
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	model.RecordLog(ctx, c.GetInt("userId"), model.LogTypeOperation, fmt.Sprintf("更新能力状态：Group=%s, Model=%s, ChannelId=%d", requestBody.Group, requestBody.Model, requestBody.ChannelId))
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "状态更新成功",
	})
}

func DeleteAbilitiesByIds(c *gin.Context) {
	ctx := c.Request.Context()
	// 获取syncBothDB参数
	syncBothDB := c.Query("syncBothDB") == "true"

	var ids []int
	err := c.ShouldBindJSON(&ids)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "参数错误",
		})
		return
	}

	// 根据syncBothDB参数选择合适的函数
	var rows int64
	if syncBothDB {
		rows, err = model.DeleteAbilitiesByIdsWithSync(ids, syncBothDB)
	} else {
		rows, err = model.DeleteAbilitiesByIds(ids)
	}

	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	model.RecordLog(ctx, c.GetInt("id"), model.LogTypeOperation, fmt.Sprintf("批量删除了能力：%v", ids))
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    rows,
	})
}

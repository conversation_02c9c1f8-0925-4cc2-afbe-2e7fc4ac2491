package controller

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/middleware"
	"github.com/songquanpeng/one-api/model"
)

type OpenAIModel struct {
	ID         string `json:"id"`
	Object     string `json:"object"`
	Created    int64  `json:"created"`
	OwnedBy    string `json:"owned_by"`
	Permission []struct {
		ID                 string `json:"id"`
		Object             string `json:"object"`
		Created            int64  `json:"created"`
		AllowCreateEngine  bool   `json:"allow_create_engine"`
		AllowSampling      bool   `json:"allow_sampling"`
		AllowLogprobs      bool   `json:"allow_logprobs"`
		AllowSearchIndices bool   `json:"allow_search_indices"`
		AllowView          bool   `json:"allow_view"`
		AllowFineTuning    bool   `json:"allow_fine_tuning"`
		Organization       string `json:"organization"`
		Group              string `json:"group"`
		IsBlocking         bool   `json:"is_blocking"`
	} `json:"permission"`
	Root   string `json:"root"`
	Parent string `json:"parent"`
}

type OpenAIModelsResponse struct {
	Data    []OpenAIModel `json:"data"`
	Success bool          `json:"success"`
}

func GetAllChannels(c *gin.Context) {
	p, _ := strconv.Atoi(c.Query("p"))
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))
	if p < 0 {
		p = 0
	}
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}
	orderByStr := c.Query("orderByStr")
	id, _ := strconv.Atoi(c.Query("id"))
	name := c.Query("name")
	key := c.Query("key")
	group := c.Query("group")
	baseUrl := c.Query("base_url")
	overFrequencyAutoDisable := c.Query("overFrequencyAutoDisable")
	channelGroupId, _ := strconv.Atoi(c.Query("channelGroupId"))
	status, _ := strconv.Atoi(c.Query("status"))
	billingType, _ := strconv.Atoi(c.Query(ctxkey.BillingType))
	models := c.Query("models")
	disableReason := c.Query("disableReason")
	channels, err := model.GetAllChannels(p*pageSize, pageSize, orderByStr, false, id, name, key, group, models, channelGroupId, status, billingType, baseUrl, overFrequencyAutoDisable, "all", disableReason)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 检查用户权限，决定是否隐藏密钥
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")
	shouldHideKey := config.NotShowChannelKey

	// 如果全局配置允许显示密钥，则检查用户权限
	if !shouldHideKey {
		// 超级管理员始终可以查看
		if role == 100 {
			shouldHideKey = false
		} else if role <= 10 { // 管理员及以下角色需要检查权限
			shouldHideKey = !middleware.HasPermission(adminAccessFlags, middleware.PermissionChannelKey)
		} else {
			// 普通用户无法查看
			shouldHideKey = true
		}
	}

	// 检查渠道查看权限
	hasChannelReadPermission := role == 100 || // 超级管理员
		middleware.HasReadPermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelRead)

	if !hasChannelReadPermission {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权限访问渠道信息",
		})
		return
	}

	if shouldHideKey || config.NotShowChannelBaseUrl {
		// 遍历渠道,隐藏key
		for i := 0; i < len(channels); i++ {
			if shouldHideKey {
				channels[i].Key = ""
			}
			if config.NotShowChannelBaseUrl {
				channels[i].BaseURL = nil
			}
		}
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    channels,
	})
	return
}

func SearchChannels(c *gin.Context) {
	keyword := c.Query("keyword")
	channelGroupId, _ := strconv.Atoi(c.Query("channelGroupId"))
	p, _ := strconv.Atoi(c.Query("p"))
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))
	if p < 0 {
		p = 0
	}
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}
	channels, err := model.SearchChannels(p*pageSize, pageSize, keyword, channelGroupId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    channels,
	})
	return
}

func CountChannels(c *gin.Context) {
	keyword := c.Query("keyword")
	channelGroupId, _ := strconv.Atoi(c.Query("channelGroupId"))
	id, _ := strconv.Atoi(c.Query("id"))
	name := c.Query("name")
	key := c.Query("key")
	group := c.Query("group")
	models := c.Query("models")
	status, _ := strconv.Atoi(c.Query("status"))
	billingType, _ := strconv.Atoi(c.Query(ctxkey.BillingType))
	baseUrl := c.Query("base_url")
	overFrequencyAutoDisable := c.Query("overFrequencyAutoDisable")
	disableReason := c.Query("disableReason")
	count, err := model.CountChannels(keyword, id, name, key, group, models, channelGroupId, status, billingType, baseUrl, overFrequencyAutoDisable, disableReason)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"count": count,
		},
	})
	return
}

func FetchUpstreamModels(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	channel, err := model.GetChannelById(id, true)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	url := ""
	if channel.GetBaseURL() == "" {
		url = fmt.Sprintf("https://api.openai.com/v1/models")
	} else {
		url = fmt.Sprintf("%s/v1/models", channel.GetBaseURL())
	}
	body, err := GetResponseBody("GET", url, channel, GetAuthHeader(channel.Key))
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
	}
	result := OpenAIModelsResponse{}
	err = json.Unmarshal(body, &result)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	var ids []string
	for _, model := range result.Data {
		ids = append(ids, model.ID)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    ids,
	})
}

func GetChannel(c *gin.Context) {
	// 检查渠道查看权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")
	hasChannelReadPermission := role == 100 || // 超级管理员
		middleware.HasReadPermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelRead)

	if !hasChannelReadPermission {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权限访问渠道信息",
		})
		return
	}

	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	// true为显示所有,包括key,我不需要隐藏key
	channel, err := model.GetChannelById(id, true)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	channelExtend, _ := model.CacheGetChannelExByChannelId(channel.Id)
	rsMap, err := helper.MergeStructsToMap(channel, channelExtend, "id", "channel_id")
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "合并数据失败",
		})
		return
	}
	// 检查用户权限，决定是否隐藏密钥
	shouldHideKey := config.NotShowChannelKey

	// 如果全局配置允许显示密钥，则检查用户权限
	if !shouldHideKey {
		// 超级管理员始终可以查看
		if role == 100 {
			shouldHideKey = false
		} else if role <= 10 { // 管理员及以下角色需要检查权限
			shouldHideKey = !middleware.HasPermission(adminAccessFlags, middleware.PermissionChannelKey)
		} else {
			// 普通用户无法查看
			shouldHideKey = true
		}
	}

	if config.NotShowChannelBaseUrl {
		delete(rsMap, "base_url")
	}
	if shouldHideKey {
		delete(rsMap, "key")
	}
	if config.NotShowChannelAccessToken {
		delete(rsMap, "platform_access_token")
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    rsMap,
	})
	return
}

func AddChannel(c *gin.Context) {
	// 检查渠道写权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")
	hasChannelWritePermission := role == 100 || // 超级管理员
		middleware.HasWritePermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelWrite)

	if !hasChannelWritePermission {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权限创建渠道",
		})
		return
	}

	ctx := c.Request.Context()
	// 获取syncBothDB参数
	syncBothDB := c.Query("syncBothDB") == "true"

	var channel model.Channel
	var channelExtend model.ChannelExtend
	err := common.BindAndDecodeMainAndExtend(c, &channel, &channelExtend)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	channel.CreatedTime = helper.GetTimestamp()

	// 加载原始配置
	var config model.ChannelConfig
	if channel.Config != "" {
		err = json.Unmarshal([]byte(channel.Config), &config)
		if err != nil {
			logger.SysError(fmt.Sprintf("解析渠道配置失败: %s", err.Error()))
		}
	}

	// 解析key，支持带代理格式: key----proxy_url
	keys := strings.Split(channel.Key, "\n")
	processedKeys := make([]string, 0, len(keys))
	proxyConfigs := make(map[string]model.ChannelConfig)

	// 常量分隔符
	const proxyDelimiter = "----"

	for _, key := range keys {
		key = strings.TrimSpace(key)
		if key == "" {
			continue
		}

		// 检查是否包含代理信息
		if strings.Contains(key, proxyDelimiter) {
			parts := strings.Split(key, proxyDelimiter)
			if len(parts) >= 2 {
				actualKey := strings.TrimSpace(parts[0])
				proxyURL := strings.TrimSpace(parts[1])

				// 创建配置副本
				keyConfig := config
				keyConfig.ProxyEnabled = true
				keyConfig.ProxyURL = proxyURL

				// 保存代理配置
				proxyConfigs[actualKey] = keyConfig
				processedKeys = append(processedKeys, actualKey)
			} else {
				processedKeys = append(processedKeys, key)
			}
		} else {
			processedKeys = append(processedKeys, key)
		}
	}

	// 更新处理后的keys
	keys = processedKeys

	// 计算实际创建的渠道数量
	var createdChannelCount int

	// 使用model层的复合操作函数，根据syncBothDB参数选择合适的函数
	if len(keys) == 0 {
		// 单个渠道插入
		err = model.CreateChannelWithExtendWithSync(&channel, &channelExtend, syncBothDB)
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		createdChannelCount = 1
	} else {
		// 批量插入
		channels := make([]model.Channel, 0, len(keys))
		channelExtends := make([]model.ChannelExtend, 0, len(keys))

		for _, key := range keys {
			if strings.TrimSpace(key) == "" {
				continue
			}
			localChannel := channel
			localChannel.Key = key

			// 检查是否有特定的代理配置
			if keyConfig, exists := proxyConfigs[key]; exists {
				configBytes, err := json.Marshal(keyConfig)
				if err != nil {
					logger.SysError(fmt.Sprintf("序列化代理配置失败: %s", err.Error()))
				} else {
					localChannel.Config = string(configBytes)
				}
			}

			channels = append(channels, localChannel)
			channelExtends = append(channelExtends, channelExtend)
		}

		err = model.BatchCreateChannelsWithExtendsWithSync(channels, channelExtends, syncBothDB)
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		createdChannelCount = len(channels)
	}

	// 记录渠道创建请求统计 - 根据实际创建的渠道数量记录
	helper.SafeGoroutine(func() {
		clientIP := helper.GetClientRealIp(c)
		userId := c.GetInt("id")

		// 为每个创建的渠道记录一次统计
		for i := 0; i < createdChannelCount; i++ {
			err := model.RecordChannelCreationRequest(clientIP, userId)
			if err != nil {
				logger.SysError("Failed to record channel creation request: " + err.Error())
			}
		}
	})

	model.RecordLogByDetail(ctx, c.GetInt("id"), model.LogTypeOperation, helper.GetClientRealIp(c), c.RemoteIP(),
		channel.Id, channel.Name, "", channel.Key, 0, 0, "", 0,
		0, 0, 0, false,
		fmt.Sprintf("创建渠道：%s", channel.Name), "")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

func DeleteChannel(c *gin.Context) {
	// 检查渠道写权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")
	hasChannelWritePermission := role == 100 || // 超级管理员
		middleware.HasWritePermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelWrite)

	if !hasChannelWritePermission {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权限删除渠道",
		})
		return
	}

	ctx := c.Request.Context()
	// 获取syncBothDB参数
	syncBothDB := c.Query("syncBothDB") == "true"

	id, _ := strconv.Atoi(c.Param("id"))

	// 使用model层的复合操作函数，根据syncBothDB参数选择合适的函数
	err := model.DeleteChannelWithExtendWithSync(id, syncBothDB)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	model.RecordLogByDetail(ctx, c.GetInt("id"), model.LogTypeOperation, helper.GetClientRealIp(c), c.RemoteIP(),
		id, "", "", "", 0, 0, "", 0,
		0, 0, 0, false,
		fmt.Sprintf("删除渠道：%d", id), "")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

func DeleteDisabledChannel(c *gin.Context) {
	// 检查渠道写权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")
	hasChannelWritePermission := role == 100 || // 超级管理员
		middleware.HasWritePermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelWrite)

	if !hasChannelWritePermission {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权限删除渠道",
		})
		return
	}

	ctx := c.Request.Context()
	rows, err := model.DeleteDisabledChannel()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	model.RecordLog(ctx, c.GetInt("id"), model.LogTypeOperation, "删除了所有禁用的渠道")
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    rows,
	})
	return
}

func DeleteChannelByType(c *gin.Context) {
	// 检查渠道写权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")
	hasChannelWritePermission := role == 100 || // 超级管理员
		middleware.HasWritePermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelWrite)

	if !hasChannelWritePermission {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权限删除渠道",
		})
		return
	}

	ctx := c.Request.Context()
	// 从 body 中获取 channel_type
	channelType, _ := strconv.Atoi(c.Param("type"))
	// 确保 type 是有效的int
	if channelType != 2 && channelType != 3 && channelType != 4 && channelType != 99 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "不支持的渠道类型",
		})
		return
	}
	rows, err := model.DeleteChannelByType(channelType)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	model.RecordLog(ctx, c.GetInt("id"), model.LogTypeOperation, fmt.Sprintf("执行批量删除渠道操作"))
	model.RecordLogByDetail(ctx, c.GetInt("id"), model.LogTypeOperation, helper.GetClientRealIp(c), c.RemoteIP(),
		0, "", "", "", 0, 0, "", 0,
		0, 0, 0, false,
		fmt.Sprintf("执行批量删除渠道操作"), "")
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    rows, // 返回删除的行数
	})
}

// DeleteChannelByDisableReason 删除指定禁用原因的渠道
func DeleteChannelByDisableReason(c *gin.Context) {
	// 检查渠道写权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")
	hasChannelWritePermission := role == 100 || // 超级管理员
		middleware.HasWritePermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelWrite)

	if !hasChannelWritePermission {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权限删除渠道",
		})
		return
	}

	ctx := c.Request.Context()
	// 获取禁用原因
	disableReason := c.Param("reason")
	// 获取syncBothDB参数
	syncBothDB := c.Query("syncBothDB") == "true"

	// 确保原因是有效的
	validReasons := []string{"account_deactivated", "quota_exceeded", "rate_limit_exceeded", "invalid_key", "connection_error", "internal_server_error"}
	isValid := false
	for _, reason := range validReasons {
		if disableReason == reason {
			isValid = true
			break
		}
	}
	if !isValid {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "不支持的禁用原因",
		})
		return
	}

	rows, err := model.DeleteChannelByDisableReasonWithSync(disableReason, syncBothDB)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	model.RecordLogByDetail(ctx, c.GetInt("id"), model.LogTypeOperation, helper.GetClientRealIp(c), c.RemoteIP(),
		0, "", "", "", 0, 0, "", 0,
		0, 0, 0, false,
		fmt.Sprintf("执行删除禁用原因为 %s 的渠道操作", disableReason), "")
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    rows, // 返回删除的行数
	})
}

func DeleteChannelByIds(c *gin.Context) {
	// 检查渠道写权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")
	hasChannelWritePermission := role == 100 || // 超级管理员
		middleware.HasWritePermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelWrite)

	if !hasChannelWritePermission {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权限删除渠道",
		})
		return
	}

	ctx := c.Request.Context()
	// 获取syncBothDB参数
	syncBothDB := c.Query("syncBothDB") == "true"

	// 解析数组
	var ids []int
	err := c.ShouldBindJSON(&ids)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "参数错误",
		})
		return
	}
	rows, err := model.DeleteChannelByIdsWithSync(ids, syncBothDB)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	model.RecordLogByDetail(ctx, c.GetInt("id"), model.LogTypeOperation, helper.GetClientRealIp(c), c.RemoteIP(),
		0, "", "", "", 0, 0, "", 0,
		0, 0, 0, false,
		fmt.Sprintf("批量删除了渠道：%v", ids), "")
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    rows,
	})
	return
}

func UpdateChannel(c *gin.Context) {
	// 检查渠道写权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")
	hasChannelWritePermission := role == 100 || // 超级管理员
		middleware.HasWritePermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelWrite)

	if !hasChannelWritePermission {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权限修改渠道",
		})
		return
	}

	ctx := c.Request.Context()
	statusOnly := c.Query("status_only")
	cleanUsage := c.Query("cleanUsage")
	// 获取syncBothDB参数
	syncBothDB := c.Query("syncBothDB") == "true"

	var channel model.Channel
	var channelExtend model.ChannelExtend
	err := common.BindAndDecodeMainAndExtend(c, &channel, &channelExtend)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	channelExtend.ChannelId = channel.Id
	channelExtend.Id = 0
	if cleanUsage == "true" {
		channel.UsedQuota = 1
	}

	// 使用model层的复合操作函数，根据syncBothDB参数选择合适的函数
	err = model.UpdateChannelWithExtendWithSync(&channel, &channelExtend, statusOnly == "true", cleanUsage == "true", syncBothDB)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	model.RecordLog(ctx, c.GetInt("id"), model.LogTypeOperation, fmt.Sprintf("修改渠道：%s", channel.Name))
	model.RecordLogByDetail(ctx, c.GetInt("id"), model.LogTypeOperation, helper.GetClientRealIp(c), c.RemoteIP(),
		channel.Id, channel.Name, "", channel.Key, 0, 0, "", 0,
		0, 0, 0, false,
		fmt.Sprintf("修改渠道：%s", channel.Name), "")
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    channel,
	})
	return
}

// CountChannelByStatus 统计各个状态的渠道的数量
func CountChannelByStatus(c *gin.Context) {
	counts, err := model.CountChannelByStatus()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    counts,
	})
	return
}

func GetAllModels(c *gin.Context) {
	models, err := model.GetAllModels()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    models,
	})
	return
}

// GetChannelStatistics 通用渠道统计（支持多种状态和条件）
func GetChannelStatistics(c *gin.Context) {
	// 获取查询参数
	statusFilterStr := c.Query("statusFilter") // 状态过滤：0=所有状态，1=正常，3=禁用
	disableReason := c.Query("disableReason")  // 过滤特定的禁用原因
	groupBy := c.Query("groupBy")              // 分组方式：server=按服务器(remark), domain=按域名

	// 解析状态过滤参数
	statusFilter := 0 // 默认统计所有状态
	if statusFilterStr != "" {
		if sf, err := strconv.Atoi(statusFilterStr); err == nil {
			statusFilter = sf
		}
	}

	// 默认参数
	if disableReason == "" {
		disableReason = "account_deactivated" // 默认统计账号停用
	}

	// 默认分组方式
	if groupBy == "" {
		groupBy = "server" // 默认按服务器分组
	}

	// 验证statusFilter参数（只允许特定值）
	if statusFilter != 0 && statusFilter != 1 && statusFilter != 3 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "不支持的状态过滤器，支持的值：0(所有状态)、1(正常)、3(禁用)",
		})
		return
	}

	// 验证groupBy参数
	if groupBy != "server" && groupBy != "domain" {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "不支持的分组方式，支持的值：server(按服务器)、domain(按域名)",
		})
		return
	}

	// 验证disableReason参数
	validReasons := []string{"account_deactivated", "quota_exceeded", "rate_limit_exceeded", "invalid_key", "connection_error", "internal_server_error", "all"}
	isValid := false
	for _, reason := range validReasons {
		if disableReason == reason {
			isValid = true
			break
		}
	}
	if !isValid {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "不支持的禁用原因",
		})
		return
	}

	// 调用model层统计方法
	statistics, err := model.GetChannelStatistics(statusFilter, disableReason, groupBy)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    statistics,
	})
	return
}

// GetChannelCreationRPM 获取渠道创建RPM统计
func GetChannelCreationRPM(c *gin.Context) {
	userId, _ := strconv.Atoi(c.Query("user_id"))
	ip := c.Query("ip")

	rpm, err := model.GetChannelCreationRPM(userId, ip)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"rpm": rpm,
		},
	})
}

func GetChannelCreationSpeedStatistics(c *gin.Context) {
	// 获取查询参数
	statusFilterStr := c.DefaultQuery("status", "0")
	disableReason := c.DefaultQuery("disable_reason", "all")
	groupBy := c.DefaultQuery("group_by", "domain")
	timeGranularity := c.DefaultQuery("time_granularity", "day")

	// 转换状态过滤参数
	statusFilter, err := strconv.Atoi(statusFilterStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid status parameter",
		})
		return
	}

	// 验证参数
	if groupBy != "domain" && groupBy != "server" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid group_by parameter, must be 'domain' or 'server'",
		})
		return
	}

	if timeGranularity != "day" && timeGranularity != "hour" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid time_granularity parameter, must be 'day' or 'hour'",
		})
		return
	}

	// 获取统计数据
	statistics, err := model.GetChannelCreationSpeedStatistics(statusFilter, disableReason, groupBy, timeGranularity)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": fmt.Sprintf("Failed to get channel creation speed statistics: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    statistics,
	})
}

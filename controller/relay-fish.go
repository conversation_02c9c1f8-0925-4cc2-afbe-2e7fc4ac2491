package controller

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/client"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/controller/optimizer"
	"github.com/songquanpeng/one-api/model"
	billingratio "github.com/songquanpeng/one-api/relay/billing/ratio"
	"github.com/songquanpeng/one-api/relay/channeltype"
	"github.com/songquanpeng/one-api/relay/meta"
)

// TTSRequest 文本转语音请求结构体
type TTSRequest struct {
	Text            string  `json:"text"`                        // 待转换为语音的文本内容
	Model           string  `json:"model,omitempty"`             // 模型名称，如"speech-1.5"
	Voice           string  `json:"voice,omitempty"`             // 语音类型，如"alloy"、"echo"、"fable"、"onyx"等
	Speed           float64 `json:"speed,omitempty"`             // 语速，取值通常在0.25-4.0之间
	Format          string  `json:"format,omitempty"`            // 音频格式，如"mp3"、"opus"、"aac"、"flac"
	Temperature     float64 `json:"temperature,omitempty"`       // 语音随机性，值越高语音变化越大，取值范围0-1
	ResponseFormat  string  `json:"response_format,omitempty"`   // 响应格式，如"mp3"、"opus"、"aac"、"flac"
	VoiceEngine     string  `json:"voice_engine,omitempty"`      // 语音引擎，如"tts-1"、"tts-1-hd"
	Quality         string  `json:"quality,omitempty"`           // 音频质量，如"standard"或"hd"
	OutputVolume    float64 `json:"output_volume,omitempty"`     // 输出音量，取值通常在-20.0到20.0之间
	SpeechEndMarker bool    `json:"speech_end_marker,omitempty"` // 是否在语音结束时添加标记
	AboutLength     int     `json:"about_length,omitempty"`      // 指定输出音频的近似长度（毫秒）
	OptimizeEnd     bool    `json:"optimize_end,omitempty"`      // 是否优化音频结尾
	Latency         string  `json:"latency,omitempty"`           // 延迟模式，如"low"或"high"，影响生成速度和质量
	ReferenceId     string  `json:"reference_id,omitempty"`      // 唯一引用标识符，用于跟踪和查询请求
}

// ASRRequest 语音转文本请求结构体
type ASRRequest struct {
	File  []byte `json:"file"`
	Model string `json:"model,omitempty"`
}

// ASRResponse 语音转文本响应结构体
type ASRResponse struct {
	Text     string    `json:"text"`
	Duration int       `json:"duration"`
	Segments []Segment `json:"segments"`
}

// Segment 语音转文本分段
type Segment struct {
	Text  string `json:"text"`
	Start int    `json:"start"`
	End   int    `json:"end"`
}

// FishAudioResponse 统一响应结构
type FishAudioResponse struct {
	Code        int         `json:"code"`
	Description string      `json:"description"`
	Result      interface{} `json:"result,omitempty"`
}

// ModelCreateRequest 创建模型请求的数据结构
type ModelCreateRequest struct {
	Type           string   `json:"type" form:"type"`                       // 模型类型，如"svc"
	Title          string   `json:"title" form:"title"`                     // 模型标题
	Description    string   `json:"description" form:"description"`         // 模型描述
	CoverImage     string   `json:"cover_image" form:"cover_image"`         // 封面图片
	TrainMode      string   `json:"train_mode" form:"train_mode"`           // 训练模式，如"full"
	Tags           []string `json:"tags" form:"tags"`                       // 标签列表
	Languages      []string `json:"languages" form:"languages"`             // 语言列表
	Visibility     string   `json:"visibility" form:"visibility"`           // 可见性，如"public"
	LockVisibility bool     `json:"lock_visibility" form:"lock_visibility"` // 锁定可见性
}

// ModelAuthor 模型作者信息
type ModelAuthor struct {
	ID       string `json:"_id"`      // 作者ID
	Nickname string `json:"nickname"` // 昵称
	Avatar   string `json:"avatar"`   // 头像
}

// ModelResponse 模型响应结构
type ModelResponse struct {
	ID             string      `json:"_id"`             // 模型ID
	Type           string      `json:"type"`            // 模型类型
	Title          string      `json:"title"`           // 标题
	Description    string      `json:"description"`     // 描述
	CoverImage     string      `json:"cover_image"`     // 封面图片
	TrainMode      string      `json:"train_mode"`      // 训练模式
	State          string      `json:"state"`           // 状态
	Tags           []string    `json:"tags"`            // 标签
	Samples        []string    `json:"samples"`         // 样本
	CreatedAt      string      `json:"created_at"`      // 创建时间
	UpdatedAt      string      `json:"updated_at"`      // 更新时间
	Languages      []string    `json:"languages"`       // 支持语言
	Visibility     string      `json:"visibility"`      // 可见性
	LockVisibility bool        `json:"lock_visibility"` // 锁定可见性
	LikeCount      int         `json:"like_count"`      // 点赞数
	MarkCount      int         `json:"mark_count"`      // 标记数
	SharedCount    int         `json:"shared_count"`    // 分享数
	TaskCount      int         `json:"task_count"`      // 任务数
	Unliked        bool        `json:"unliked"`         // 是否未点赞
	Liked          bool        `json:"liked"`           // 是否点赞
	Marked         bool        `json:"marked"`          // 是否标记
	Author         ModelAuthor `json:"author"`          // 作者信息
}

// FishRelayTTS 处理Fish Audio的文本转语音请求，导出函数供路由使用
func FishRelayTTS(c *gin.Context) {
	relayTTS(c)
}

// FishRelayASR 处理Fish Audio的语音转文本请求，导出函数供路由使用
func FishRelayASR(c *gin.Context) {
	relayASR(c)
}

// FishRelayCreateModel 处理Fish Audio的创建模型请求，导出函数供路由使用
func FishRelayCreateModel(c *gin.Context) {
	relayCreateModel(c)
}

// relayTTS 处理文本转语音请求
func relayTTS(c *gin.Context) {
	ctx := c.Request.Context()
	metaCtx := meta.GetByContext(c)

	billingType := c.GetInt(ctxkey.BillingType)
	channelType := c.GetInt("channel")
	userId := c.GetInt("id")
	group := c.GetString("group")
	_startTime := helper.GetTimestamp()

	var ttsRequest TTSRequest
	err := common.UnmarshalBodyReusable(c, &ttsRequest)
	if err != nil {
		response := FishAudioResponse{
			Code:        4,
			Description: "bind_request_body_failed",
		}
		c.JSON(http.StatusBadRequest, response)
		return
	}

	// 请求验证
	if ttsRequest.Text == "" {
		response := FishAudioResponse{
			Code:        4,
			Description: "text_is_required",
		}
		c.JSON(http.StatusBadRequest, response)
		return
	}

	// 设置默认模型
	if ttsRequest.Model == "" {
		// 首先尝试从context中获取已经设置的model
		model := c.GetString(ctxkey.RequestModel)
		if model != "" {
			ttsRequest.Model = model
		} else {
			ttsRequest.Model = "speech-1.5"
		}
	}

	baseURL := channeltype.ChannelBaseURLs[channelType]
	if c.GetString("base_url") != "" {
		baseURL = c.GetString("base_url")
	}

	// 计算配额
	var promptTokens int
	var completionTokens int
	promptTokens = len(ttsRequest.Text)
	modelRatio := billingratio.GetModelRatio(ttsRequest.Model, metaCtx.ChannelType)
	groupRatio := billingratio.GetGroupRatio(group)
	ratio := modelRatio * groupRatio

	userQuota, quotaExpireTime, err := model.CacheGetUserQuotaAndExpireTime(ctx, userId)
	if err != nil {
		response := FishAudioResponse{
			Code:        4,
			Description: "get_user_quota_failed",
		}
		c.JSON(http.StatusInternalServerError, response)
		return
	}

	// 配额过期检查
	if quotaExpireTime > 0 && quotaExpireTime < helper.GetTimestamp() {
		response := FishAudioResponse{
			Code:        4,
			Description: "user_quota_expired",
		}
		c.JSON(http.StatusForbidden, response)
		return
	}

	sizeRatio := 1.0
	quota := int64(ratio * sizeRatio * 1000)

	var modelFixedPrice float64
	if billingType == common.BillingTypeByCount {
		modelFixedPrice, err = billingratio.GetModelFixedPrice(ttsRequest.Model)
		if err != nil {
			response := FishAudioResponse{
				Code:        4,
				Description: "model_fixed_price_not_config",
			}
			c.JSON(http.StatusForbidden, response)
			return
		}
		quota = int64(modelFixedPrice * groupRatio * 500000)
	}

	if userQuota <= 0 || userQuota-quota < 0 {
		response := FishAudioResponse{
			Code:        4,
			Description: "insufficient_user_quota",
		}
		c.JSON(http.StatusForbidden, response)
		return
	}

	// 构建请求
	requestBody, err := json.Marshal(ttsRequest)
	if err != nil {
		response := FishAudioResponse{
			Code:        4,
			Description: "marshal_request_body_failed",
		}
		c.JSON(http.StatusInternalServerError, response)
		return
	}

	req, err := http.NewRequest("POST", baseURL+"/v1/tts", bytes.NewBuffer(requestBody))
	if err != nil {
		response := FishAudioResponse{
			Code:        4,
			Description: "create_request_failed",
		}
		c.JSON(http.StatusInternalServerError, response)
		return
	}

	// 设置请求头
	apiKey := c.Request.Header.Get("Authorization")
	apiKey = strings.TrimPrefix(apiKey, "Bearer ")
	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", "application/json")
	if c.Request.Header.Get("model") != "" {
		req.Header.Set("model", c.Request.Header.Get("model"))
	}

	// 发送请求
	resp, err := client.HTTPClient.Do(req)
	if err != nil {
		response := FishAudioResponse{
			Code:        4,
			Description: "do_request_failed",
		}
		c.JSON(http.StatusInternalServerError, response)
		return
	}
	defer resp.Body.Close()

	// 记录用量信息
	tokenName := c.GetString("token_name")
	tokenKey := c.GetString("token_key")
	tokenId := c.GetInt("token_id")
	channelId := c.GetInt("channel_id")
	channelName := c.GetString("channel_name")

	// 异步记录消费日志
	defer func(ctx context.Context) {
		go func() {
			requestDuration := helper.GetTimestamp() - _startTime
			logContent := fmt.Sprintf("模型倍率 %.2f，分组倍率 %.2f，用时 %d秒", modelRatio, groupRatio, requestDuration)
			if billingType == common.BillingTypeByCount {
				logContent = fmt.Sprintf("模型按次使用固定价格 %.6f，分组倍率 %.2f，用时 %d秒", modelFixedPrice, groupRatio, requestDuration)
			}

			err := model.PostConsumeTokenQuota(tokenId, quota)
			if err != nil {
				logger.Error(ctx, "error consuming token remain quota: "+err.Error())
			}

			err = model.CacheUpdateUserQuota(ctx, userId)
			if err != nil {
				logger.Error(ctx, "error update user quota cache: "+err.Error())
			}

			model.UpdateUserUsedQuotaAndRequestCount(userId, quota)
			model.UpdateChannelUsedQuota(channelId, quota)

			// 记录日志
			createdLog := model.RecordConsumeLog(ctx, userId, channelId, promptTokens, completionTokens, "fish-tts", tokenName, tokenKey, channelName, int(quota), requestDuration, false, logContent)
			helper.SafeGoroutine(func() {
				// 记录详细请求日志
				model.RecordLogExtend(ctx, createdLog, ttsRequest.Text, "", "", metaCtx.UpstreamResponse, metaCtx.FullResponse, metaCtx.RequestURLPath)
				// 推送优化器
				optimizer.RecordConsumeLog(createdLog)
			})
		}()
	}(c.Request.Context())

	// 转发响应
	for key, values := range resp.Header {
		for _, value := range values {
			c.Writer.Header().Add(key, value)
		}
	}
	c.Status(resp.StatusCode)
	_, err = io.Copy(c.Writer, resp.Body)
	if err != nil {
		logger.Error(ctx, "error copying response: "+err.Error())
	}
}

// relayASR 处理语音转文本请求
func relayASR(c *gin.Context) {
	ctx := c.Request.Context()
	metaCtx := meta.GetByContext(c)

	billingType := c.GetInt(ctxkey.BillingType)
	channelType := c.GetInt("channel")
	userId := c.GetInt("id")
	group := c.GetString("group")
	_startTime := helper.GetTimestamp()

	// 读取请求体
	fileHeader, err := c.FormFile("audio")
	if err != nil {
		response := FishAudioResponse{
			Code:        4,
			Description: "get_form_file_failed",
		}
		c.JSON(http.StatusBadRequest, response)
		return
	}

	file, err := fileHeader.Open()
	if err != nil {
		response := FishAudioResponse{
			Code:        4,
			Description: "open_file_failed",
		}
		c.JSON(http.StatusBadRequest, response)
		return
	}
	defer file.Close()

	// 读取文件内容
	fileBytes, err := ioutil.ReadAll(file)
	if err != nil {
		response := FishAudioResponse{
			Code:        4,
			Description: "read_file_failed",
		}
		c.JSON(http.StatusBadRequest, response)
		return
	}

	// 获取模型
	modelParam := c.Request.FormValue("model")
	if modelParam == "" {
		// 首先尝试从context中获取已经设置的model
		model := c.GetString(ctxkey.RequestModel)
		if model != "" {
			modelParam = model
		} else {
			modelParam = "speech-1.5" // 默认模型
		}
	}

	baseURL := channeltype.ChannelBaseURLs[channelType]
	if c.GetString("base_url") != "" {
		baseURL = c.GetString("base_url")
	}

	// 计算配额
	var promptTokens int
	var completionTokens int
	promptTokens = len(fileBytes) / 100 // 简单估算
	modelRatio := billingratio.GetModelRatio(modelParam, metaCtx.ChannelType)
	groupRatio := billingratio.GetGroupRatio(group)
	ratio := modelRatio * groupRatio

	userQuota, quotaExpireTime, err := model.CacheGetUserQuotaAndExpireTime(ctx, userId)
	if err != nil {
		response := FishAudioResponse{
			Code:        4,
			Description: "get_user_quota_failed",
		}
		c.JSON(http.StatusInternalServerError, response)
		return
	}

	// 配额过期检查
	if quotaExpireTime > 0 && quotaExpireTime < helper.GetTimestamp() {
		response := FishAudioResponse{
			Code:        4,
			Description: "user_quota_expired",
		}
		c.JSON(http.StatusForbidden, response)
		return
	}

	sizeRatio := 1.0
	quota := int64(ratio * sizeRatio * 1000)

	var modelFixedPrice float64
	if billingType == common.BillingTypeByCount {
		modelFixedPrice, err = billingratio.GetModelFixedPrice(modelParam)
		if err != nil {
			response := FishAudioResponse{
				Code:        4,
				Description: "model_fixed_price_not_config",
			}
			c.JSON(http.StatusForbidden, response)
			return
		}
		quota = int64(modelFixedPrice * groupRatio * 500000)
	}

	if userQuota <= 0 || userQuota-quota < 0 {
		response := FishAudioResponse{
			Code:        4,
			Description: "insufficient_user_quota",
		}
		c.JSON(http.StatusForbidden, response)
		return
	}

	// 创建multipart form
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	part, err := writer.CreateFormFile("audio", fileHeader.Filename)
	if err != nil {
		response := FishAudioResponse{
			Code:        4,
			Description: "create_form_file_failed",
		}
		c.JSON(http.StatusInternalServerError, response)
		return
	}

	_, err = part.Write(fileBytes)
	if err != nil {
		response := FishAudioResponse{
			Code:        4,
			Description: "write_file_failed",
		}
		c.JSON(http.StatusInternalServerError, response)
		return
	}

	if modelParam != "" {
		_ = writer.WriteField("model", modelParam)
	}

	err = writer.Close()
	if err != nil {
		response := FishAudioResponse{
			Code:        4,
			Description: "close_writer_failed",
		}
		c.JSON(http.StatusInternalServerError, response)
		return
	}

	// 构建请求
	req, err := http.NewRequest("POST", baseURL+"/v1/asr", body)
	if err != nil {
		response := FishAudioResponse{
			Code:        4,
			Description: "create_request_failed",
		}
		c.JSON(http.StatusInternalServerError, response)
		return
	}

	// 设置请求头
	apiKey := c.Request.Header.Get("Authorization")
	apiKey = strings.TrimPrefix(apiKey, "Bearer ")
	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// 发送请求
	resp, err := client.HTTPClient.Do(req)
	if err != nil {
		response := FishAudioResponse{
			Code:        4,
			Description: "do_request_failed",
		}
		c.JSON(http.StatusInternalServerError, response)
		return
	}
	defer resp.Body.Close()

	// 记录用量信息
	tokenName := c.GetString("token_name")
	tokenKey := c.GetString("token_key")
	tokenId := c.GetInt("token_id")
	channelId := c.GetInt("channel_id")
	channelName := c.GetString("channel_name")

	// 异步记录消费日志
	defer func(ctx context.Context) {
		go func() {
			requestDuration := helper.GetTimestamp() - _startTime
			logContent := fmt.Sprintf("模型倍率 %.2f，分组倍率 %.2f，用时 %d秒", modelRatio, groupRatio, requestDuration)
			if billingType == common.BillingTypeByCount {
				logContent = fmt.Sprintf("模型按次使用固定价格 %.6f，分组倍率 %.2f，用时 %d秒", modelFixedPrice, groupRatio, requestDuration)
			}

			err := model.PostConsumeTokenQuota(tokenId, quota)
			if err != nil {
				logger.Error(ctx, "error consuming token remain quota: "+err.Error())
			}

			err = model.CacheUpdateUserQuota(ctx, userId)
			if err != nil {
				logger.Error(ctx, "error update user quota cache: "+err.Error())
			}

			model.UpdateUserUsedQuotaAndRequestCount(userId, quota)
			model.UpdateChannelUsedQuota(channelId, quota)

			// 记录日志
			createdLog := model.RecordConsumeLog(ctx, userId, channelId, promptTokens, completionTokens, "fish-asr", tokenName, tokenKey, channelName, int(quota), requestDuration, false, logContent)
			helper.SafeGoroutine(func() {
				// 记录详细请求日志
				model.RecordLogExtend(ctx, createdLog, "", "", "", metaCtx.UpstreamResponse, metaCtx.FullResponse, metaCtx.RequestURLPath)
				// 推送优化器
				optimizer.RecordConsumeLog(createdLog)
			})
		}()
	}(c.Request.Context())

	// 读取响应并转发
	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		response := FishAudioResponse{
			Code:        4,
			Description: "read_response_body_failed",
		}
		c.JSON(http.StatusInternalServerError, response)
		return
	}

	// 设置响应头
	for key, values := range resp.Header {
		for _, value := range values {
			c.Writer.Header().Add(key, value)
		}
	}

	c.Status(resp.StatusCode)
	_, err = c.Writer.Write(respBody)
	if err != nil {
		logger.Error(ctx, "error writing response: "+err.Error())
	}
}

// relayCreateModel 处理创建模型请求
func relayCreateModel(c *gin.Context) {
	ctx := c.Request.Context()
	metaCtx := meta.GetByContext(c)

	billingType := c.GetInt(ctxkey.BillingType)
	channelType := c.GetInt("channel")
	userId := c.GetInt("id")
	group := c.GetString("group")
	_startTime := helper.GetTimestamp()

	// 确定内容类型
	contentType := c.GetHeader("Content-Type")
	var modelReq ModelCreateRequest

	// 处理不同的请求体格式
	if strings.Contains(contentType, "multipart/form-data") {
		if err := c.ShouldBind(&modelReq); err != nil {
			response := FishAudioResponse{
				Code:        4,
				Description: "bind_form_data_failed",
			}
			c.JSON(http.StatusBadRequest, response)
			return
		}

		// 处理上传的文件
		form, err := c.MultipartForm()
		if err != nil {
			response := FishAudioResponse{
				Code:        4,
				Description: "get_multipart_form_failed",
			}
			c.JSON(http.StatusBadRequest, response)
			return
		}

		// 这里需要处理上传的样本文件，但由于复杂性，这里简化处理
		_ = form.File // 仅作为提示，实际实现需要更复杂的处理

	} else if strings.Contains(contentType, "application/msgpack") {
		// 处理msgpack格式，简化实现
		data, err := ioutil.ReadAll(c.Request.Body)
		if err != nil {
			response := FishAudioResponse{
				Code:        4,
				Description: "read_msgpack_body_failed",
			}
			c.JSON(http.StatusBadRequest, response)
			return
		}
		// 这里应该实现msgpack的解码，但简化处理
		_ = data
	} else {
		response := FishAudioResponse{
			Code:        4,
			Description: "unsupported_content_type",
		}
		c.JSON(http.StatusBadRequest, response)
		return
	}

	// 检查必填字段
	if modelReq.Title == "" {
		response := FishAudioResponse{
			Code:        4,
			Description: "title_is_required",
		}
		c.JSON(http.StatusBadRequest, response)
		return
	}

	// 设置默认值
	if modelReq.Type == "" {
		modelReq.Type = "svc"
	}
	if modelReq.TrainMode == "" {
		modelReq.TrainMode = "full"
	}
	if modelReq.Visibility == "" {
		modelReq.Visibility = "public"
	}

	baseURL := channeltype.ChannelBaseURLs[channelType]
	if c.GetString("base_url") != "" {
		baseURL = c.GetString("base_url")
	}

	// 计算配额（示例计算，实际应根据业务需求调整）
	modelRatio := billingratio.GetModelRatio("model_create", metaCtx.ChannelType)
	groupRatio := billingratio.GetGroupRatio(group)
	ratio := modelRatio * groupRatio
	quota := int64(ratio * 5000) // 示例值，创建模型可能消耗更多配额

	var modelFixedPrice float64
	var err error
	if billingType == common.BillingTypeByCount {
		modelFixedPrice, err = billingratio.GetModelFixedPrice("model_create")
		if err != nil {
			response := FishAudioResponse{
				Code:        4,
				Description: "model_fixed_price_not_config",
			}
			c.JSON(http.StatusForbidden, response)
			return
		}
		quota = int64(modelFixedPrice * groupRatio * 500000)
	}

	// 检查用户配额
	userQuota, quotaExpireTime, err := model.CacheGetUserQuotaAndExpireTime(ctx, userId)
	if err != nil {
		response := FishAudioResponse{
			Code:        4,
			Description: "get_user_quota_failed",
		}
		c.JSON(http.StatusInternalServerError, response)
		return
	}

	// 配额过期检查
	if quotaExpireTime > 0 && quotaExpireTime < helper.GetTimestamp() {
		response := FishAudioResponse{
			Code:        4,
			Description: "user_quota_expired",
		}
		c.JSON(http.StatusForbidden, response)
		return
	}

	if userQuota <= 0 || userQuota-quota < 0 {
		response := FishAudioResponse{
			Code:        4,
			Description: "insufficient_user_quota",
		}
		c.JSON(http.StatusForbidden, response)
		return
	}

	// 构造请求
	var body bytes.Buffer
	writer := multipart.NewWriter(&body)

	// 添加表单字段
	fields := map[string]string{
		"type":            modelReq.Type,
		"title":           modelReq.Title,
		"description":     modelReq.Description,
		"cover_image":     modelReq.CoverImage,
		"train_mode":      modelReq.TrainMode,
		"visibility":      modelReq.Visibility,
		"lock_visibility": strconv.FormatBool(modelReq.LockVisibility),
	}

	for key, value := range fields {
		if value != "" {
			_ = writer.WriteField(key, value)
		}
	}

	// 处理标签和语言（数组字段）
	for _, tag := range modelReq.Tags {
		_ = writer.WriteField("tags[]", tag)
	}

	for _, lang := range modelReq.Languages {
		_ = writer.WriteField("languages[]", lang)
	}

	// 完成multipart写入
	writer.Close()

	// 创建请求
	req, err := http.NewRequest("POST", baseURL+"/model", &body)
	if err != nil {
		response := FishAudioResponse{
			Code:        4,
			Description: "create_request_failed",
		}
		c.JSON(http.StatusInternalServerError, response)
		return
	}

	// 设置请求头
	apiKey := c.Request.Header.Get("Authorization")
	apiKey = strings.TrimPrefix(apiKey, "Bearer ")
	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// 发送请求
	resp, err := client.HTTPClient.Do(req)
	if err != nil {
		response := FishAudioResponse{
			Code:        4,
			Description: "do_request_failed",
		}
		c.JSON(http.StatusInternalServerError, response)
		return
	}
	defer resp.Body.Close()

	// 记录用量信息
	tokenName := c.GetString("token_name")
	tokenKey := c.GetString("token_key")
	tokenId := c.GetInt("token_id")
	channelId := c.GetInt("channel_id")
	channelName := c.GetString("channel_name")

	// 异步记录消费日志
	defer func(ctx context.Context) {
		go func() {
			requestDuration := helper.GetTimestamp() - _startTime
			logContent := fmt.Sprintf("模型倍率 %.2f，分组倍率 %.2f，用时 %d秒", modelRatio, groupRatio, requestDuration)
			if billingType == common.BillingTypeByCount {
				logContent = fmt.Sprintf("模型按次使用固定价格 %.6f，分组倍率 %.2f，用时 %d秒", modelFixedPrice, groupRatio, requestDuration)
			}

			err := model.PostConsumeTokenQuota(tokenId, quota)
			if err != nil {
				logger.Error(ctx, "error consuming token remain quota: "+err.Error())
			}

			err = model.CacheUpdateUserQuota(ctx, userId)
			if err != nil {
				logger.Error(ctx, "error update user quota cache: "+err.Error())
			}

			model.UpdateUserUsedQuotaAndRequestCount(userId, quota)
			model.UpdateChannelUsedQuota(channelId, quota)

			// 记录日志
			promptTokens := len(modelReq.Title) + len(modelReq.Description) // 简单估算
			completionTokens := 0                                           // 创建模型没有完成令牌
			createdLog := model.RecordConsumeLog(ctx, userId, channelId, promptTokens, completionTokens, "fish-model-create", tokenName, tokenKey, channelName, int(quota), requestDuration, false, logContent)
			helper.SafeGoroutine(func() {
				// 记录详细请求日志
				requestJson, _ := json.Marshal(modelReq)
				model.RecordLogExtend(ctx, createdLog, string(requestJson), "", "", metaCtx.UpstreamResponse, metaCtx.FullResponse, metaCtx.RequestURLPath)
				// 推送优化器
				optimizer.RecordConsumeLog(createdLog)
			})
		}()
	}(c.Request.Context())

	// 读取并转发响应
	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		response := FishAudioResponse{
			Code:        4,
			Description: "read_response_body_failed",
		}
		c.JSON(http.StatusInternalServerError, response)
		return
	}

	// 设置响应头
	for key, values := range resp.Header {
		for _, value := range values {
			c.Writer.Header().Add(key, value)
		}
	}

	c.Status(resp.StatusCode)
	_, err = c.Writer.Write(respBody)
	if err != nil {
		logger.Error(ctx, "error writing response: "+err.Error())
	}
}

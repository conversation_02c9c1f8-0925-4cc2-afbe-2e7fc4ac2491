package controller

import (
	"encoding/json"
	"fmt"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"

	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/i18n"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/common/message"
	"github.com/songquanpeng/one-api/common/random"
	"github.com/songquanpeng/one-api/model"
	billingratio "github.com/songquanpeng/one-api/relay/billing/ratio"
	"github.com/songquanpeng/one-api/utils"
)

type SMSLoginRequest struct {
	PhoneNumber string `json:"phone_number"`
	Code        string `json:"sms_verification_code"`
}

func SMSLogin(c *gin.Context) {
	if !config.SMSLoginEnabled {
		c.JSON(http.StatusOK, gin.H{
			"message": "管理员关闭了短信验证登录",
			"success": false,
		})
		return
	}
	var loginRequest SMSLoginRequest
	err := json.NewDecoder(c.Request.Body).Decode(&loginRequest)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"message": "无效的参数",
			"success": false,
		})
		return
	}
	phoneNumber := loginRequest.PhoneNumber
	code := loginRequest.Code
	if phoneNumber == "" || code == "" {
		c.JSON(http.StatusOK, gin.H{
			"message": "无效的参数",
			"success": false,
		})
		return
	}
	if !common.VerifySMSCodeWithKey(phoneNumber, code, common.SMSLoginPurpose) {
		c.JSON(http.StatusOK, gin.H{
			"message": "短信验证码错误或已过期",
			"success": false,
		})
		return
	}
	user := model.User{
		PhoneNumber: phoneNumber,
		Username:    phoneNumber,         // 手机号直接作为用户名
		UserType:    model.UserTypePhone, // 设置用户类型为手机登录
	}
	err = user.ValidateAndFillWithPhoneNumber()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"message": err.Error(),
			"success": false,
		})
		return
	}
	SetupLogin(&user, c)
}

type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// RefreshToken 刷新jwt token (用于跨域登录,不用于刷新token过期时间)
func RefreshToken(c *gin.Context) {
	ctx := c.Request.Context()
	// 从请求头中获取jwt token
	token := c.Request.Header.Get("X-S-Token")
	if token == "" {
		c.JSON(http.StatusOK, gin.H{
			"message": "未提供有效的token",
			"success": false,
		})
		return
	}
	j := &utils.JWT{SigningKey: []byte(config.JWTSigningKey)} // 唯一签名
	claims, err := j.ParseToken(token)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"message": "无效的token",
			"success": false,
		})
		return
	}
	// 从Redis中获取用户的claims
	if common.RedisEnabled {
		claimsJson, err := common.RedisGet(token)
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"message": "无法获取用户信息",
				"success": false,
			})
			return
		}
		err = json.Unmarshal([]byte(claimsJson), &claims)
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"message": "无法获取用户信息",
				"success": false,
			})
			return
		}
	}
	// 更新响应头jwt token,用于跨域登录
	c.Writer.Header().Set("X-S-Token", token)
	// 更新Redis中的jwt token
	if common.RedisEnabled {
		dr, _ := utils.ParseDuration("7d")
		err = common.RedisSet(token, token, dr)
		// 保存claims到Redis中
		claimsJson, _ := json.Marshal(claims)
		err = common.RedisSet(token, string(claimsJson), dr)
		if err != nil {
			logger.SysError(fmt.Sprintf("刷新用户 %s 的jwt token失败: %s", claims.Username, err.Error()))
		}
	}
	session := sessions.Default(c)
	userId := session.Get("id")
	if userId == nil {
		c.JSON(http.StatusOK, gin.H{
			"message": "无法获取用户信息",
			"success": false,
		})
		return
	}
	user, err := model.GetUserById(userId.(int), false)
	cleanUser := model.User{
		Id:               user.Id,
		Username:         user.Username,
		DisplayName:      user.DisplayName,
		Role:             user.Role,
		Status:           user.Status,
		AdminAccessFlags: user.AdminAccessFlags,
	}
	// 记录当前用户的ip
	clientIP := c.ClientIP()
	if clientIP != "" {
		cleanUser.LastLoginIp = clientIP
	}
	err = cleanUser.UpdateLastLoginInfo()
	if err != nil {
		// 只记录日志,不影响与登录
		logger.SysError(fmt.Sprintf("更新用户 %s 的登录信息失败: %s", user.Username, err.Error()))
	}
	// 如果是root用户登录，通知超级管理员
	if user.Role == common.RoleRootUser || user.Role == common.RoleAdminUser {
		helper.SafeGoroutine(func() {
			message.NotifyRootUser(config.SystemName+" 管理员登录通知", fmt.Sprintf("管理员 %s 于 %s 登录了系统，登录IP为 %s", user.Username, time.Now().Format("2006-01-02 15:04:05"), clientIP))
		})
	}
	if config.TelegramOAuthEnabled && user.TelegramId != "" && config.TelegramBotName != "" && config.TelegramBotToken != "" {
		helper.SafeGoroutine(func() {
			err := message.SendTelegramMessage(user.TelegramId, fmt.Sprintf("您的账号 %s 于 %s 登录了 %s，登录IP为 %s", user.Username, time.Now().Format("2006-01-02 15:04:05"), config.SystemName, clientIP))
			if err != nil {
				// 失败也不影响登录
				logger.SysError(fmt.Sprintf("Telegram通知用户 %s 登录系统失败: %s", user.Username, err.Error()))
			}
		})
	}
	//记录日志
	model.RecordLog(ctx, user.Id, model.LogTypeOperation, fmt.Sprintf("JWT 登录 IP: %s", clientIP))
	c.JSON(http.StatusOK, gin.H{
		"message": "",
		"success": true,
		"data":    cleanUser,
		"token":   token,
	})
}

func Login(c *gin.Context) {
	if !config.PasswordLoginEnabled {
		c.JSON(http.StatusOK, gin.H{
			"message": "管理员关闭了密码登录",
			"success": false,
		})
		return
	}
	var loginRequest LoginRequest
	err := json.NewDecoder(c.Request.Body).Decode(&loginRequest)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"message": i18n.Translate(c, "invalid_parameter"),
			"success": false,
		})
		return
	}
	username := loginRequest.Username
	password := loginRequest.Password
	if username == "" || password == "" {
		c.JSON(http.StatusOK, gin.H{
			"message": i18n.Translate(c, "invalid_parameter"),
			"success": false,
		})
		return
	}
	user := model.User{
		Username: username,
		Password: password,
	}
	err = user.ValidateAndFill()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"message": err.Error(),
			"success": false,
		})
		return
	}
	//if user.Uid == "" {
	//	// 为用户生成 UID
	//	user.Uid = GenerateUserUid(user.Id)
	//	// 更新到数据库中
	//	err = user.Update(false)
	//	if err != nil {
	//		c.JSON(http.StatusOK, gin.H{
	//			"message": "无法更新用户 UID",
	//			"success": false,
	//		})
	//		return
	//	}
	//}
	SetupLogin(&user, c)
}

// setup session & cookies and then return user info
func SetupLogin(user *model.User, c *gin.Context) {
	session := sessions.Default(c)
	session.Set("id", user.Id)
	//session.Set("uid", user.Uid)
	session.Set("username", user.Username)
	session.Set("role", user.Role)
	session.Set("status", user.Status)
	session.Set("admin_access_flags", user.AdminAccessFlags)
	err := session.Save()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"message": "无法保存会话信息，请重试",
			"success": false,
		})
		return
	}
	cookie := c.Writer.Header().Get("Set-Cookie")
	// 更新响应头,用于跨域登录
	c.Writer.Header().Set("X-Auth-Token", cookie)
	// 登录以后签发jwt
	j := &utils.JWT{SigningKey: []byte(config.JWTSigningKey)} // 唯一签名
	claims := j.CreateClaims(utils.BaseClaims{
		ID: user.Id,
		//Uid:              user.Uid,
		NickName:         user.Email,
		Username:         user.Username,
		Role:             user.Role,
		AdminAccessFlags: user.AdminAccessFlags,
		Timezone:         user.Timezone,
	})
	jwtToken, err := j.CreateToken(claims)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"message": "无法签发jwt",
			"success": false,
		})
		return
	}
	// 更新响应头jwt token,用于跨域登录
	c.Writer.Header().Set("X-S-Token", jwtToken)
	// 设置登录成功后的用户信息到Redis中
	if common.RedisEnabled {
		// 此处过期时间等于jwt过期时间
		dr, _ := utils.ParseDuration("7d")
		err = common.RedisSet(fmt.Sprintf("jwt_user:%d", user.Id), jwtToken, dr)
		if err != nil {
			logger.SysError(fmt.Sprintf("设置用户 %s 到Redis中失败: %s", user.Username, err.Error()))
		}
		// 保存claims到Redis中
		claimsJson, _ := json.Marshal(claims)
		err = common.RedisSet(jwtToken, string(claimsJson), dr)
		if err != nil {
			logger.SysError(fmt.Sprintf("设置用户 %s 的claims到Redis中失败: %s", user.Username, err.Error()))
		}
	}
	cleanUser := model.User{
		Id: user.Id,
		//Uid:              user.Uid,
		Username:         user.Username,
		DisplayName:      user.DisplayName,
		Role:             user.Role,
		Status:           user.Status,
		AdminAccessFlags: user.AdminAccessFlags,
	}
	// 记录当前用户的ip
	clientIP := helper.GetClientRealIp(c)
	if clientIP != "" {
		cleanUser.LastLoginIp = clientIP
	}
	err = cleanUser.UpdateLastLoginInfo()
	if err != nil {
		// 只记录日志,不影响与登录
		logger.SysError(fmt.Sprintf("更新用户 %s 的登录信息失败: %s", user.Username, err.Error()))
	}
	// 用户登录后如果没有默认令牌,则生成一个
	defaultTokenName := user.Username + "的初始令牌"
	defaultToken, err := model.GetUserInitialToken(user.Id)
	if err != nil || defaultToken == nil || defaultToken.Id == 0 {
		cleanToken := model.Token{
			UserId:         user.Id,
			Name:           defaultTokenName,
			Key:            helper.GenerateKey(),
			CreatedTime:    helper.GetTimestamp(),
			AccessedTime:   helper.GetTimestamp(),
			ExpiredTime:    -1,
			RemainQuota:    500000,
			UnlimitedQuota: true,
			IsInitialToken: true,
			BillingType:    common.BillingTypeByQuotaFirst,
		}
		err = cleanToken.Insert()
	}
	if err != nil {
		return
	}
	// 如果是root用户登录，通知超级管理员
	if user.Role == common.RoleRootUser || user.Role == common.RoleAdminUser {
		helper.SafeGoroutine(func() {
			message.NotifyRootUser(config.SystemName+" 管理员登录通知", fmt.Sprintf("管理员 %s 于 %s 登录了系统，登录IP为 %s", user.Username, time.Now().Format("2006-01-02 15:04:05"), clientIP))
		})
	}
	if config.TelegramOAuthEnabled && user.TelegramId != "" && config.TelegramBotName != "" && config.TelegramBotToken != "" {
		helper.SafeGoroutine(func() {
			err := message.SendTelegramMessage(user.TelegramId, fmt.Sprintf("您的账号 %s 于 %s 登录了 %s，登录IP为 %s", user.Username, time.Now().Format("2006-01-02 15:04:05"), config.SystemName, clientIP))
			if err != nil {
				// 失败也不影响登录
				logger.SysError(fmt.Sprintf("Telegram通知用户 %s 登录系统失败: %s", user.Username, err.Error()))
			}
		})
	}
	//记录日志
	model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeOperation, fmt.Sprintf("登录 IP: %s", clientIP), "")

	c.JSON(http.StatusOK, gin.H{
		"message": "",
		"success": true,
		"data":    cleanUser,
		"token":   jwtToken,
	})
	return
}

func Logout(c *gin.Context) {
	session := sessions.Default(c)
	session.Clear()
	err := session.Save()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"message": err.Error(),
			"success": false,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"message": "",
		"success": true,
	})
}

func Register(c *gin.Context) {
	ctx := c.Request.Context()
	if !config.RegisterEnabled {
		c.JSON(http.StatusOK, gin.H{
			"message": "管理员关闭了新用户注册",
			"success": false,
		})
		return
	}
	if !config.PasswordRegisterEnabled {
		c.JSON(http.StatusOK, gin.H{
			"message": "管理员关闭了密码注册",
			"success": false,
		})
		return
	}
	var user model.User
	err := json.NewDecoder(c.Request.Body).Decode(&user)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": i18n.Translate(c, "invalid_parameter"),
		})
		return
	}
	if err := common.Validate.Struct(&user); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": i18n.Translate(c, "invalid_input"),
		})
		return
	}

	// 检查用户名是否已存在
	if model.IsUsernameAlreadyTaken(user.Username) {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "用户名已被使用",
		})
		return
	}

	//用户名不允许包含"@"符号
	if strings.Contains(user.Username, "@") {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "用户名不允许包含@符号",
		})
		return
	}

	if config.EmailVerificationEnabled {
		if user.Email == "" || user.VerificationCode == "" {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "请输入邮箱地址和验证码",
			})
			return
		}
		if !common.VerifyCodeWithKey(user.Email, user.VerificationCode, common.EmailVerificationPurpose) {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "邮件验证码错误或已过期",
			})
			return
		}
	}

	if config.SMSVerificationEnabled && config.SMSRegisterEnabled {
		if user.PhoneNumber == "" || user.SMSVerificationCode == "" {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "请输入手机号码和短信验证码",
			})
			return
		}
		if !common.VerifySMSCodeWithKey(user.PhoneNumber, user.SMSVerificationCode, common.SMSVerificationPurpose) {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "短信验证码错误或已过期",
			})
			return
		}
		//无需验证手机号是否已经被注册，发送短信验证码时已经验证过了
		//强制手机号作为用户名
		user.Username = user.PhoneNumber
	}

	// 获取当前域名
	domain := c.Request.Host
	// 通过域名查询对应的代理商
	agent, err := model.GetAgencyByDomain(domain)
	if err == nil && agent != nil {
		user.AgencyId = agent.UserId
	}

	affCode := user.AffCode // this code is the inviter's code, not the user's own code
	inviterId, _ := model.GetUserIdByAffCode(affCode)
	zeroTime := int64(0)
	cleanUser := model.User{
		Username:        user.Username,
		Password:        user.Password,
		DisplayName:     user.Username,
		AgencyId:        user.AgencyId,
		InviterId:       inviterId,
		RegisterTime:    helper.GetTimestamp(),
		QuotaExpireTime: &zeroTime,              // 设置为0值的指针，表示无限期
		UserType:        model.UserTypeUsername, // 默认为用户名类型
	}
	if config.EmailVerificationEnabled && user.Email != "" {
		cleanUser.Email = user.Email
		cleanUser.UserType = model.UserTypeEmail // 如果有邮箱验证，设置为邮箱类型
	}
	if config.SMSVerificationEnabled && config.SMSRegisterEnabled && user.PhoneNumber != "" {
		cleanUser.PhoneNumber = user.PhoneNumber
		cleanUser.Username = user.PhoneNumber    // 手机号作为用户名
		cleanUser.UserType = model.UserTypePhone // 设置为手机类型
	}
	if err := cleanUser.Insert(ctx, inviterId); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "注册成功",
		"data":    cleanUser,
	})
	return
}

func RegisterWithEmailLink(c *gin.Context) {
	ctx := c.Request.Context()
	if !config.RegisterEnabled {
		c.JSON(http.StatusOK, gin.H{
			"message": "管理员关闭了新用户注册",
			"success": false,
		})
		return
	}
	if !config.PasswordRegisterEnabled {
		c.JSON(http.StatusOK, gin.H{
			"message": "管理员关闭了密码注册功能",
			"success": false,
		})
		return
	}
	var user model.User
	// 从链接中获取参数
	user.Email = c.Query("email")
	user.VerificationCode = c.Query("code")
	user.Username = c.Query("email")
	user.Password = helper.GetRandomString(16)
	user.AffCode = c.Query("aff_code")
	if user.Email == "" || user.VerificationCode == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}

	affCode := user.AffCode // this code is the inviter's code, not the user's own code
	inviterId, _ := model.GetUserIdByAffCode(affCode)
	cleanUser := model.User{
		Username:    user.Username,
		Password:    user.Password,
		DisplayName: user.Username,
		InviterId:   inviterId,
	}
	if config.EmailVerificationEnabled {
		cleanUser.Email = user.Email
	}
	if config.SMSVerificationEnabled {
		cleanUser.PhoneNumber = user.PhoneNumber
	}
	if err := cleanUser.Insert(ctx, inviterId); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	//go func() {
	//	err := user.ValidateAndFill()
	//	if err != nil {
	//		logger.Errorf(ctx, "user.ValidateAndFill failed: %w", err)
	//		return
	//	}
	//	cleanToken := model.Token{
	//		UserId:         user.Id,
	//		Name:           "default",
	//		Key:            random.GenerateKey(),
	//		CreatedTime:    helper.GetTimestamp(),
	//		AccessedTime:   helper.GetTimestamp(),
	//		ExpiredTime:    -1,
	//		RemainQuota:    -1,
	//		UnlimitedQuota: true,
	//	}
	//	err = cleanToken.Insert()
	//	if err != nil {
	//		logger.Errorf(ctx, "cleanToken.Insert failed: %w", err)
	//		return
	//	}
	//}()
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

func GetAllUsers(c *gin.Context) {
	ctx := c.Request.Context()
	p, _ := strconv.Atoi(c.Query("p"))
	if p < 0 {
		p = 0
	}
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}
	order := c.DefaultQuery("order", "")
	keyword := c.Query("keyword") // 移到前面
	idOrUid := c.Query("id")
	username := c.Query("username")
	displayName := c.Query("display_name")
	group := c.Query("group")
	email := c.Query("email")
	role, _ := strconv.Atoi(c.Query("role"))
	status, _ := strconv.Atoi(c.Query("status"))
	remark := c.Query("remark")

	// 解析特殊设置过滤条件，如果有的话
	var hasSpecialSettings *bool
	specialSettingsStr := c.Query("has_special_settings")
	if specialSettingsStr != "" {
		value, err := strconv.ParseBool(specialSettingsStr)
		if err == nil {
			hasSpecialSettings = &value
		}
	}

	var id int
	if idOrUid != "" {
		if idOrUid[0] >= 'a' && idOrUid[0] <= 'z' || idOrUid[0] >= 'A' && idOrUid[0] <= 'Z' {
			parsedId, err := ParseUID(idOrUid)
			if err != nil {
				c.JSON(http.StatusOK, gin.H{
					"success": false,
					"message": "无效的 UID",
				})
				return
			}
			id = parsedId
		} else {
			parsedId, _ := strconv.Atoi(idOrUid)
			id = parsedId
		}
	}

	users, err := model.GetAllUsers(p*pageSize, pageSize, order, keyword, username, displayName, group, email, id, role, status, remark, hasSpecialSettings) // 更新参数列表
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 处理结果
	type UserResponse struct {
		*model.User
		QuotaExpireTimeValue int64   `json:"quota_expire_time"`
		RedisQuota           int64   `json:"redis_quota,omitempty"`             // Redis中存储的余额
		RedisQuotaExpireTime int64   `json:"redis_quota_expire_time,omitempty"` // Redis中存储的余额过期时间
		QuotaWarning         string  `json:"quota_warning,omitempty"`           // 余额差异警告
		QuotaDiff            float64 `json:"quota_diff,omitempty"`              // 余额差异百分比
	}

	var response []UserResponse
	for _, user := range users {
		expireTime := user.GetQuotaExpireTime()
		userResp := UserResponse{
			User:                 user,
			QuotaExpireTimeValue: expireTime,
		}

		// 如果启用了Redis，检查Redis中的余额与数据库中的余额是否一致
		if common.RedisEnabled {
			redisQuota, redisQuotaExpireTime, err := model.CacheGetUserQuotaAndExpireTime(ctx, user.Id)
			if err == nil {
				// 计算差值百分比 (Redis额度除以500000是美金单位)
				redisQuotaUSD := float64(redisQuota) / 500000
				dbQuotaUSD := float64(user.Quota) / 500000

				if dbQuotaUSD > 0 {
					quotaDiff := math.Abs(redisQuotaUSD-dbQuotaUSD) / dbQuotaUSD * 100
					// 如果差异超过5%，则添加警告信息
					if quotaDiff > 5 {
						userResp.RedisQuota = redisQuota
						userResp.RedisQuotaExpireTime = redisQuotaExpireTime
						userResp.QuotaWarning = fmt.Sprintf("余额数据不一致！数据库余额: %s, 缓存余额: %s, 差异: %.2f%%",
							common.LogQuota(user.Quota),
							common.LogQuota(redisQuota),
							quotaDiff)
						userResp.QuotaDiff = quotaDiff
					}
				}
			}
		}

		response = append(response, userResp)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    response,
	})
}

func CountAllUsers(c *gin.Context) {
	keyword := c.Query("keyword") // 移到前面
	username := c.Query("username")
	displayName := c.Query("display_name")
	group := c.Query("group")
	email := c.Query("email")
	idOrUid := c.Query("id")
	role, _ := strconv.Atoi(c.Query("role"))
	status, _ := strconv.Atoi(c.Query("status"))
	remark := c.Query("remark")

	// 解析特殊设置过滤条件，如果有的话
	var hasSpecialSettings *bool
	specialSettingsStr := c.Query("has_special_settings")
	if specialSettingsStr != "" {
		value, err := strconv.ParseBool(specialSettingsStr)
		if err == nil {
			hasSpecialSettings = &value
		}
	}

	var id int
	if idOrUid != "" {
		if idOrUid[0] >= 'a' && idOrUid[0] <= 'z' || idOrUid[0] >= 'A' && idOrUid[0] <= 'Z' {
			parsedId, err := ParseUID(idOrUid)
			if err != nil {
				c.JSON(http.StatusOK, gin.H{
					"success": false,
					"message": "无效的 UID",
				})
				return
			}
			id = parsedId
		} else {
			parsedId, _ := strconv.Atoi(idOrUid)
			id = parsedId
		}
	}

	count, err := model.CountAllUsers(keyword, username, displayName, group, email, id, role, status, remark, hasSpecialSettings) // 更新参数列表
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    0,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"count": count,
		},
	})
	return
}

func GetUser(c *gin.Context) {
	ctx := c.Request.Context()
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	user, err := model.GetUserById(id, false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	myRole := c.GetInt(ctxkey.Role)
	if myRole <= user.Role && myRole != model.RoleRootUser {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无权获取同级或更高等级用户的信息",
		})
		return
	}
	// 获取ex
	userEx, _ := model.GetUserExByUserId(user.Id)
	if userEx != nil {
		userEx.ChatRecordJsonData = ""
	}
	// 如果userEx中的GroupDiscounts不为空 则获取GroupDiscounts 并且结合当前系统配置的分组 进行融合
	if userEx != nil && userEx.GroupDiscounts != "" {
		groupDiscounts := make(map[string]float64)
		_ = json.Unmarshal([]byte(userEx.GroupDiscounts), &groupDiscounts)
		// 获取当前系统的所有分组
		groups, err := model.GetAllGroups(0, 1000, "", "")

		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
		// 遍历所有分组，如果userEx.GroupDiscounts中没有该分组，则添加进去,如果存在于userEx.GroupDiscounts中，但是不存在于当前系统的分组中，则删除
		for _, group := range groups {
			if _, ok := groupDiscounts[group.Name]; !ok {
				groupDiscounts[group.Name] = 1
			}
		}
		// 遍历userEx.GroupDiscounts，如果当前系统的分组中不存在该分组，则删除
		for k := range groupDiscounts {
			exist := false
			for _, group := range groups {
				if k == group.Name {
					exist = true
					break
				}
			}
			if !exist {
				delete(groupDiscounts, k)
			}
		}

		// 融合后的数据重新转为json
		jsonData, _ := json.Marshal(groupDiscounts)
		userEx.GroupDiscounts = string(jsonData)
	}
	rsMap, err := helper.MergeStructsToMap(user, userEx, "id", "user_id")

	// 从Redis获取用户额度和过期时间
	if common.RedisEnabled {
		redisQuota, redisQuotaExpireTime, err := model.CacheGetUserQuotaAndExpireTime(ctx, user.Id)
		if err == nil {
			// 计算差值百分比 (Redis额度除以500000是美金单位)
			redisQuotaUSD := float64(redisQuota) / 500000
			dbQuotaUSD := float64(user.Quota) / 500000

			if dbQuotaUSD > 0 {
				quotaDiff := math.Abs(redisQuotaUSD-dbQuotaUSD) / dbQuotaUSD * 100
				// 如果差异超过5%，则添加警告信息
				if quotaDiff > 5 {
					quotaWarning := fmt.Sprintf("余额数据不一致！数据库余额: %s, 缓存余额: %s, 差异: %.2f%%",
						common.LogQuota(user.Quota),
						common.LogQuota(redisQuota),
						quotaDiff)

					// 将Redis信息添加到返回数据中
					if rsMap == nil {
						rsMap = make(map[string]interface{})
					}
					rsMap["redis_quota"] = redisQuota
					rsMap["redis_quota_expire_time"] = redisQuotaExpireTime
					rsMap["quota_warning"] = quotaWarning
					rsMap["quota_diff"] = quotaDiff
				}
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    rsMap,
	})
	return
}

func GetUserDashboard(c *gin.Context) {
	id := c.GetInt(ctxkey.Id)
	now := time.Now()
	startOfDay := now.Truncate(24*time.Hour).AddDate(0, 0, -6).Unix()
	endOfDay := now.Truncate(24 * time.Hour).Add(24*time.Hour - time.Second).Unix()

	dashboards, err := model.SearchLogsByDayAndModel(id, int(startOfDay), int(endOfDay))
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无法获取统计信息",
			"data":    nil,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    dashboards,
	})
	return
}

type GetTokenRequest struct {
	Password string `json:"password"`
}

func GenerateAccessToken(c *gin.Context) {
	//从 body 中获取密码，用于验证
	var req GetTokenRequest
	err := json.NewDecoder(c.Request.Body).Decode(&req)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}

	id := c.GetInt(ctxkey.Id)
	user, err := model.GetUserById(id, true)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 如果是第三方auth登录没有密码 所以不需要验证密码
	if user.Password != "" && user.ValidatePassword(req.Password) != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "密码错误",
		})
		return
	}

	user.AccessToken = random.GetUUID()

	if model.DB.Where("access_token = ?", user.AccessToken).First(user).RowsAffected != 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "请重试，系统生成的 UUID 竟然重复了！",
		})
		return
	}

	if err := user.Update(false); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    user.AccessToken,
	})
	return
}

func GetAffCode(c *gin.Context) {
	id := c.GetInt(ctxkey.Id)
	user, err := model.GetUserById(id, true)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	if user.AffCode == "" {
		user.AffCode = random.GetRandomString(4)
		if err := user.Update(false); err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    user.AffCode,
	})
	return
}

// GetSelfQuota 获取自己的额度和已使用额度,用于使用系统访问令牌请求余额
func GetSelfQuota(c *gin.Context) {
	id := c.GetInt("id")
	user, err := model.GetUserById(id, false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无法获取用户信息",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"quota":      user.Quota,
			"used_quota": user.UsedQuota,
		},
	})
	return
}

func GetSelf(c *gin.Context) {
	id := c.GetInt(ctxkey.Id)
	user, err := model.GetUserById(id, false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	// 查询充值最低限额
	user.TopupGroupMinLimit = billingratio.GetTopupGroupMinLimit(user.Group)
	user.ModelFixedPrice = billingratio.GetGroupModelFixedPrice(user.Group)
	user.InviteBonusRatio = billingratio.GetInviteBonusRatio(user.Group)
	if !model.IsAdmin(id) {
		user.Remark = ""
	}
	invitedUserCnt, err := model.GetInviteUserCntByUserId(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	user.InviteUserNumber = invitedUserCnt
	userEx, _ := model.GetUserExByUserId(user.Id)
	if userEx != nil {
		user.AffQuota = userEx.AffQuota
		user.AffHistoryQuota = userEx.AffHistoryQuota
	}
	//不发回AccessToken
	user.AccessToken = ""
	// 获取套餐信息
	planCnt, _ := model.CountAllPackagePlans(0, "", 0, user.Group)
	// 获取当前用户的套餐实例
	userPlanInstList, _ := model.GetActivePackagePlanInstanceByUserId(user.Id)
	// 如果套餐为空并且套餐实例为空，则不显示套餐
	if planCnt == 0 && len(userPlanInstList) == 0 {
		user.ShowPackagePlan = false
	} else {
		user.ShowPackagePlan = true
	}

	userGroup, err := model.CacheGetGroupByName(user.Group)
	if err == nil {
		user.GroupDisplayName = userGroup.DisplayName
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    user,
	})
	return
}

func UpdateUser(c *gin.Context) {
	ctx := c.Request.Context()
	var updatedUser model.User
	var updatedUserExtend model.UserExtend
	err := common.BindAndDecodeMainAndExtend(c, &updatedUser, &updatedUserExtend)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": i18n.Translate(c, "invalid_parameter"),
		})
		return
	}
	if updatedUser.Password == "" {
		updatedUser.Password = "$I_LOVE_U" // make Validator happy :)
	}
	if err := common.Validate.Struct(&updatedUser); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": i18n.Translate(c, "invalid_input"),
		})
		return
	}
	originUser, err := model.GetUserById(updatedUser.Id, false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	myRole := c.GetInt(ctxkey.Role)
	if myRole <= originUser.Role && myRole != model.RoleRootUser {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无权更新同权限等级或更高权限等级的用户信息",
		})
		return
	}
	if myRole <= updatedUser.Role && myRole != model.RoleRootUser {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无权将其他用户权限等级提升到大于等于自己的权限等级",
		})
		return
	}
	//如果不是 root，即使修改AdminAccessFlags也不生效
	if myRole != common.RoleRootUser {
		updatedUser.AdminAccessFlags = originUser.AdminAccessFlags
	} else if updatedUser.AdminAccessFlags == -1 { //如果传-1，说明要清空权限
		updatedUser.AdminAccessFlags = 0
	}
	// 如果是 root 且在编辑自己（只考虑 id=1）且修改了自己的权限等级，不允许修改
	if myRole == common.RoleRootUser && updatedUser.Id == 1 && updatedUser.Role != originUser.Role {
		updatedUser.Role = originUser.Role
	}

	// 判断如果是demo则不允许修改密码
	if config.DemoEnabled && updatedUser.Role == common.RoleRootUser {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "演示站不允许修改管理员信息",
		})
		return
	}
	if updatedUser.Password == "$I_LOVE_U" {
		updatedUser.Password = "" // rollback to what it should be
	}
	//这个接口不允许修改余额
	if updatedUser.Quota != 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "不允许修改用户额度",
		})
	}
	updatePassword := updatedUser.Password != ""
	if err := updatedUser.Update(updatePassword); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 更新用户扩展信息,排除ChatRecordJsonData这个字段
	updatedUserExtend.UserId = updatedUser.Id
	updatedUserExtend.Id = 0
	originUserEx, err := model.GetUserExByUserId(updatedUser.Id)
	if err == nil {
		updatedUserExtend.Id = originUserEx.Id
	}
	if err := updatedUserExtend.UpdateWithoutChatRecordJsonData(); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 清除用户缓存
	if common.RedisEnabled {
		// 这里要清理掉user_extend相关的缓存
		err = common.RedisDel(fmt.Sprintf("user_model_ratio:%d", updatedUser.Id))
		if err != nil {
			logger.SysError(fmt.Sprintf("清理用户 %d 的 user_model_ratio 缓存失败: %s", updatedUser.Id, err.Error()))
		}
		err = common.RedisDel(fmt.Sprintf("user_model_fixed_price:%d", updatedUser.Id))
		if err != nil {
			logger.SysError(fmt.Sprintf("清理用户 %d 的 user_model_fixed_price 缓存失败: %s", updatedUser.Id, err.Error()))
		}
		err = common.RedisDel(fmt.Sprintf("user_completion_ratio:%d", updatedUser.Id))
		if err != nil {
			logger.SysError(fmt.Sprintf("清理用户 %d 的 user_completion_ratio 缓存失败: %s", updatedUser.Id, err.Error()))
		}
		err = common.RedisDel(fmt.Sprintf("user_new_tik_token_billing:%d", updatedUser.Id))
		if err != nil {
			logger.SysError(fmt.Sprintf("清理用户 %d 的 user_new_tik_token_billing 缓存失败: %s", updatedUser.Id, err.Error()))
		}
		err = common.RedisDel(fmt.Sprintf("user_group_discounts:%d", updatedUser.Id))
		if err != nil {
			logger.SysError(fmt.Sprintf("清理用户 %d 的 user_group_discounts 缓存失败: %s", updatedUser.Id, err.Error()))
		}
		err = common.RedisDel(fmt.Sprintf("user_log_detail_enabled:%d", updatedUser.Id))
		if err != nil {
			logger.SysError(fmt.Sprintf("清理用户 %d 的 user_log_detail_enabled 缓存失败: %s", updatedUser.Id, err.Error()))
		}
		// 清理额外可见分组缓存
		err = common.RedisDel(fmt.Sprintf("user_extra_visible_groups:%d", updatedUser.Id))
		if err != nil {
			logger.SysError(fmt.Sprintf("清理用户 %d 的 user_extra_visible_groups 缓存失败: %s", updatedUser.Id, err.Error()))
		}
		// 清理新添加的用户流式配置缓存
		err = common.RedisDel(fmt.Sprintf("user_stream_config:%d", updatedUser.Id))
		if err != nil {
			logger.SysError(fmt.Sprintf("清理用户 %d 的 user_stream_config 缓存失败: %s", updatedUser.Id, err.Error()))
		}
		// 清理新添加的用户渠道路由配置缓存
		err = common.RedisDel(fmt.Sprintf("user_channel_score_routing:%d", updatedUser.Id))
		if err != nil {
			logger.SysError(fmt.Sprintf("清理用户 %d 的 user_channel_score_routing 缓存失败: %s", updatedUser.Id, err.Error()))
		}
		// 清理吃掉拨测配置缓存
		err = common.RedisDel(fmt.Sprintf("user_say1_direct_success_mode:%d", updatedUser.Id))
		if err != nil {
			logger.SysError(fmt.Sprintf("清理用户 %d 的 user_say1_direct_success_mode 缓存失败: %s", updatedUser.Id, err.Error()))
		}
		// 清理模拟OpenAI官方响应格式配置缓存
		err = common.RedisDel(fmt.Sprintf("user_mock_openai_complete_format:%d", updatedUser.Id))
		if err != nil {
			logger.SysError(fmt.Sprintf("清理用户 %d 的 user_mock_openai_complete_format 缓存失败: %s", updatedUser.Id, err.Error()))
		}
		// 清理记录下游错误配置缓存
		err = common.RedisDel(fmt.Sprintf("user_log_downstream_error_enabled:%d", updatedUser.Id))
		if err != nil {
			logger.SysError(fmt.Sprintf("清理用户 %d 的 user_log_downstream_error_enabled 缓存失败: %s", updatedUser.Id, err.Error()))
		}
		// 清理最大Prompt日志长度配置缓存
		err = common.RedisDel(fmt.Sprintf("user_max_prompt_log_length:%d", updatedUser.Id))
		if err != nil {
			logger.SysError(fmt.Sprintf("清理用户 %d 的 user_max_prompt_log_length 缓存失败: %s", updatedUser.Id, err.Error()))
		}
		// 清理余额过期时间quota_expire_time
		err = common.RedisDel(fmt.Sprintf("quota_expire_time:%d", updatedUser.Id))
		if err != nil {
			logger.SysError(fmt.Sprintf("清理用户 %d 的 quota_expire_time 缓存失败: %s", updatedUser.Id, err.Error()))
		}
		// 清理Claude消息整理配置缓存
		err = common.RedisDel(fmt.Sprintf("user_claude_message_normalization:%d", updatedUser.Id))
		if err != nil {
			logger.SysError(fmt.Sprintf("清理用户 %d 的 user_claude_message_normalization 缓存失败: %s", updatedUser.Id, err.Error()))
		}
	}

	// 记录重要字段的变更
	var changes []string
	if originUser.Username != updatedUser.Username && updatedUser.Username != "" {
		changes = append(changes, fmt.Sprintf("用户名从 %s 修改为 %s", originUser.Username, updatedUser.Username))
	}
	if originUser.DisplayName != updatedUser.DisplayName && updatedUser.DisplayName != "" {
		changes = append(changes, fmt.Sprintf("显示名从 %s 修改为 %s", originUser.DisplayName, updatedUser.DisplayName))
	}
	if originUser.Role != updatedUser.Role && updatedUser.Role != 0 {
		changes = append(changes, fmt.Sprintf("用户角色从 %s 修改为 %s",
			model.GetRoleName(originUser.Role),
			model.GetRoleName(updatedUser.Role)))
	}
	if originUser.Status != updatedUser.Status && updatedUser.Status != 0 {
		changes = append(changes, fmt.Sprintf("用户状态从 %s 修改为 %s",
			model.GetStatusName(originUser.Status),
			model.GetStatusName(updatedUser.Status)))
	}
	if originUser.Group != updatedUser.Group && updatedUser.Group != "" {
		changes = append(changes, fmt.Sprintf("用户分组从 %s 修改为 %s", originUser.Group, updatedUser.Group))
	}
	if originUser.Email != updatedUser.Email && updatedUser.Email != "" {
		changes = append(changes, fmt.Sprintf("邮箱从 %s 修改为 %s", originUser.Email, updatedUser.Email))
	}
	if originUser.PhoneNumber != updatedUser.PhoneNumber && updatedUser.PhoneNumber != "" {
		changes = append(changes, fmt.Sprintf("手机号从 %s 修改为 %s", originUser.PhoneNumber, updatedUser.PhoneNumber))
	}
	if originUser.RateLimit != updatedUser.RateLimit && updatedUser.RateLimit != 0 {
		changes = append(changes, fmt.Sprintf("速率限制从 %d 修改为 %d", originUser.RateLimit, updatedUser.RateLimit))
	}
	if originUser.Remark != updatedUser.Remark && updatedUser.Remark != "" {
		changes = append(changes, "修改了备注")
	}
	if updatePassword {
		changes = append(changes, "修改了密码")
	}

	// 如果有变更，记录日志
	if len(changes) > 0 {
		model.RecordLog(ctx, originUser.Id, model.LogTypeManage,
			fmt.Sprintf("管理员更新了用户信息：%s", strings.Join(changes, "；")))
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

type UpdatePasswordRequest struct {
	OldPassword string `json:"old_password"`
	NewPassword string `json:"new_password"`
}

// UpdatePassword 更新自己的密码，需要提供旧密码
func UpdatePassword(c *gin.Context) {
	var req UpdatePasswordRequest
	err := json.NewDecoder(c.Request.Body).Decode(&req)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}
	id := c.GetInt("id")
	user, err2 := model.GetUserById(id, false)
	if err2 != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无法获取用户信息",
		})
		return
	}
	//验证旧密码，如果旧密码错误则返回错误，如果原密码为空不会返回错误
	if user.ValidatePassword(req.OldPassword) != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "旧密码不正确",
		})
		return
	}
	if config.DemoEnabled && user.Role == common.RoleRootUser {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "演示站不允许修改管理员密码",
		})
		return
	}
	user.Password = req.NewPassword //不需要加密，因为在model中会自动加密
	if err := user.Update(true); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "密码修改成功",
	})
	return
}

func UpdateSelf(c *gin.Context) {
	var user model.User
	err := json.NewDecoder(c.Request.Body).Decode(&user)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": i18n.Translate(c, "invalid_parameter"),
		})
		return
	}
	if user.Password == "" {
		user.Password = "$I_LOVE_U" // make Validator happy :)
	}
	if err := common.Validate.Struct(&user); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "输入不合法 " + err.Error(),
		})
		return
	}

	// 判断如果是demo则不允许修改密码
	if config.DemoEnabled && user.Role == common.RoleRootUser {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "当前为demo不允许修改管理员信息",
		})
		return
	}

	cleanUser := model.User{
		Id: c.GetInt(ctxkey.Id),
		//Username:    user.Username,
		//Password:    user.Password,
		// 不允许修改密码和用户名
		DisplayName: user.DisplayName,
	}
	if user.Password == "$I_LOVE_U" {
		user.Password = "" // rollback to what it should be
		cleanUser.Password = ""
	}
	updatePassword := user.Password != ""
	if err := cleanUser.Update(updatePassword); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

func DeleteUser(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	originUser, err := model.GetUserById(id, false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	myRole := c.GetInt("role")
	if myRole <= originUser.Role {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无权删除同权限等级或更高权限等级的用户",
		})
		return
	}
	err = model.DeleteUserById(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "",
		})
		return
	}
}

func DeleteSelf(c *gin.Context) {
	id := c.GetInt("id")
	user, _ := model.CacheGetUserById(id, false)

	if !config.UnsubscribeEnabled {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "管理员关闭了注销功能",
		})
		return
	}

	if user.Role == model.RoleRootUser {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "不能删除超级管理员账户",
		})
		return
	}

	err := model.DeleteUserById(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

func CreateUser(c *gin.Context) {
	ctx := c.Request.Context()
	var user model.User
	err := json.NewDecoder(c.Request.Body).Decode(&user)
	if err != nil || user.Username == "" || user.Password == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": i18n.Translate(c, "invalid_parameter"),
		})
		return
	}
	if err := common.Validate.Struct(&user); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": i18n.Translate(c, "invalid_input"),
		})
		return
	}
	if user.DisplayName == "" {
		user.DisplayName = user.Username
	}
	myRole := c.GetInt("role")
	if user.Role >= myRole {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无法创建权限大于等于自己的用户",
		})
		return
	}
	// Even for admin users, we cannot fully trust them!
	cleanUser := model.User{
		Username:    user.Username,
		Password:    user.Password,
		DisplayName: user.DisplayName,
	}
	if err := cleanUser.Insert(ctx, 0); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	model.RecordLog(ctx, c.GetInt("id"), model.LogTypeOperation, fmt.Sprintf("管理员创建了新用户 %s", user.Username))
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

type ManageRequest struct {
	Username string `json:"username"`
	Action   string `json:"action"`
}

// ManageUser Only admin user can do this
func ManageUser(c *gin.Context) {
	ctx := c.Request.Context()
	var req ManageRequest
	err := json.NewDecoder(c.Request.Body).Decode(&req)

	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": i18n.Translate(c, "invalid_parameter"),
		})
		return
	}
	user := model.User{
		Username: req.Username,
	}
	// Fill attributes
	model.DB.Where(&user).First(&user)
	if user.Id == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "用户不存在",
		})
		return
	}
	myRole := c.GetInt("role")
	if myRole <= user.Role && myRole != model.RoleRootUser {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无权更新同权限等级或更高权限等级的用户信息",
		})
		return
	}
	switch req.Action {
	case "disable":
		user.Status = model.UserStatusDisabled
		if user.Role == model.RoleRootUser {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "无法禁用超级管理员用户",
			})
			return
		}
	case "enable":
		user.Status = model.UserStatusEnabled
	case "delete":
		if user.Role == model.RoleRootUser {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "无法删除超级管理员用户",
			})
			return
		}
		if err := user.Delete(); err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
	case "promote":
		if myRole != model.RoleRootUser {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "普通管理员用户无法提升其他用户为管理员",
			})
			return
		}
		if user.Role >= model.RoleAdminUser {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "该用户已经是管理员",
			})
			return
		}
		user.Role = model.RoleAdminUser
	case "demote":
		if user.Role == model.RoleRootUser {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "无法降级超级管理员用户",
			})
			return
		}
		if user.Role == model.RoleCommonUser {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "该用户已经是普通用户",
			})
			return
		}
		user.Role = model.RoleCommonUser
	}

	if err := user.Update(false); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	clearUser := model.User{
		Role:   user.Role,
		Status: user.Status,
	}
	model.RecordLog(ctx, c.GetInt("id"), model.LogTypeOperation, fmt.Sprintf("管理员 %s 了用户 %s", req.Action, user.Username))
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    clearUser,
	})
	return
}

func EmailBind(c *gin.Context) {
	email := c.Query("email")
	code := c.Query("code")
	if !common.VerifyCodeWithKey(email, code, common.EmailVerificationPurpose) {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "邮箱验证码错误或已过期",
		})
		return
	}
	id := c.GetInt("id")
	user := model.User{
		Id: id,
	}
	err := user.FillUserById()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	user.Email = email
	// no need to check if this email already taken, because we have used verification code to check it
	err = user.Update(false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	if user.Role == model.RoleRootUser {
		config.RootUserEmail = email
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

func SMSBind(c *gin.Context) {
	phoneNumber := c.Query("phone_number")
	code := c.Query("code")
	if !common.VerifySMSCodeWithKey(phoneNumber, code, common.SMSBindPurpose) {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "短信验证码错误或已过期",
		})
		return
	}
	id := c.GetInt("id")
	user := model.User{
		Id: id,
	}
	err := user.FillUserById()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	user.PhoneNumber = phoneNumber
	err = user.Update(false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

type topUpRequest struct {
	Key string `json:"key"`
}

func TopUp(c *gin.Context) {
	ctx := c.Request.Context()
	session := sessions.Default(c)
	userId := session.Get("id").(int)
	req := topUpRequest{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	id := c.GetInt("id")
	quota, err := model.Redeem(ctx, req.Key, id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 获取用户信息
	user, err := model.GetUserById(id, false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取用户信息失败",
		})
		return
	}

	// 创建一条充值记录
	topUp := &model.TopUp{
		UserId:        id,
		AgencyId:      user.AgencyId,
		Amount:        float64(quota) / 500000, // 转换为美金
		Money:         0,                       // 兑换码充值不涉及实际支付
		PaymentMethod: "redeem",
		TradeNo:       "R" + strconv.FormatInt(time.Now().Unix(), 10),
		CreateTime:    helper.GetTimestamp(),
		Status:        "success",
	}

	err = topUp.Insert()
	if err != nil {
		logger.SysError(fmt.Sprintf("创建兑换码充值记录失败: %s", err.Error()))
		// 这里不返回错误，因为兑换码已经核销成功，只是记录失败
	}

	message.NotifyRootUser(fmt.Sprintf("用户id[%d]通过兑换码充值 %s", userId, common.LogQuota(quota)), fmt.Sprintf("用户id[%d]通过兑换码充值 %s", userId, common.LogQuota(quota)))
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    quota,
	})
	return
}

type AdminTopUpRequest struct {
	Id     int     `json:"id"`
	UserId int     `json:"user_id"`
	Quota  float64 `json:"quota"`
	Reason string  `json:"reason"`
	Remark string  `json:"remark"`
}

// AdminTopUp Only admin user can do this 管理员为用户充值，这样日志更专业
func AdminTopUp(c *gin.Context) {
	ctx := c.Request.Context()
	var req AdminTopUpRequest
	err := json.NewDecoder(c.Request.Body).Decode(&req)
	if err != nil || req.Id == 0 || req.Quota == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}
	user, err := model.GetUserById(req.Id, false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无法获取用户信息",
		})
		return
	}

	// 记录原始余额
	originalQuota := user.Quota

	// 创建一条充值记录
	topUp := &model.TopUp{
		UserId:        user.Id,
		AgencyId:      user.AgencyId,
		Amount:        float64(req.Quota) / 500000, // 转换为美金
		Money:         0,                           // 管理员充值不涉及实际支付
		PaymentMethod: "admin",
		TradeNo:       "A" + strconv.FormatInt(time.Now().Unix(), 10),
		CreateTime:    helper.GetTimestamp(),
		Status:        "success",
	}

	err = topUp.Insert()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "创建充值记录失败: " + err.Error(),
		})
		return
	}

	//这里要对正负数单独处理
	if req.Quota > 0 {
		err = model.IncreaseUserQuotaAndRedis(user.Id, int64(req.Quota))
		model.RecordLog(ctx, user.Id, model.LogTypeTopup, fmt.Sprintf("管理员为用户 %s 充值 %s (充值前余额: %s → 充值后余额: %s)（原因：%s）",
			user.Username,
			common.LogQuota(int64(req.Quota)),
			common.LogQuota(originalQuota),
			common.LogQuota(originalQuota+int64(req.Quota)),
			req.Reason))
	} else {
		err = model.DecreaseUserQuotaAndRedis(user.Id, int64(-req.Quota))
		model.RecordLog(ctx, user.Id, model.LogTypeManage, fmt.Sprintf("管理员为用户 %s 扣除 %s (扣除前余额: %s → 扣除后余额: %s)（原因：%s）",
			user.Username,
			common.LogQuota(int64(-req.Quota)),
			common.LogQuota(originalQuota),
			common.LogQuota(originalQuota+int64(req.Quota)),
			req.Reason))
	}

	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "充值失败" + err.Error(),
		})
		return
	}

	// 删除redis中的用户quota_expire_time的信息
	if common.RedisEnabled {
		err = common.RedisDel(fmt.Sprintf("quota_expire_time:%d", user.Id))
		if err != nil {
			logger.SysError(fmt.Sprintf("删除redis中的用户quota_expire_time的信息失败：%v", err))
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "AdminTopUp操作成功",
	})
	return
}

type AdminUpdateQuotaExpireTimeRequest struct {
	Id   int `json:"id"`
	Days int `json:"days"`
}

// AdminUpdateQuotaExpireTime Only admin user can do this 管理员为用户修改额度过期时间，传入天数，但是后端需要转换为当前时间+天数的时间戳
func AdminUpdateQuotaExpireTime(c *gin.Context) {
	ctx := c.Request.Context()
	var req AdminUpdateQuotaExpireTimeRequest
	err := json.NewDecoder(c.Request.Body).Decode(&req)
	if err != nil || req.Id == 0 || req.Days == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}
	user, err := model.GetUserById(req.Id, false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无法获取用户信息",
		})
		return
	}
	//86400是一天的秒数（使用秒数是因为时间戳是秒级的）
	expireTime := helper.GetTimestamp() + int64(req.Days)*86400
	user.QuotaExpireTime = &expireTime
	if err := user.Update(false); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "修改失败",
		})
		return
	}
	model.RecordLog(ctx, user.Id, model.LogTypeManage, fmt.Sprintf("管理员为用户 %s 修改额度过期时间为 %d 天", user.Username, req.Days))
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "AdminUpdateQuotaExpireTime操作成功",
	})
	return
}

// Checkin 签到
func Checkin(c *gin.Context) {
	id := c.GetInt("id")
	user, err := model.GetUserById(id, true)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无法获取用户信息",
		})
		return
	}

	//var Checkin checkin
	// 判断当前用户当日没有签到记录
	todayIsCheckin, checkin, _ := model.CheckUserTodayCheckin(id)
	if todayIsCheckin {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "今日已签到",
		})
		return
	}

	// 新插入一条签到记录的时间
	checkin.UserId = id
	checkin.CheckinTime = helper.GetTimestamp()
	checkin.Quota = config.CheckinQuota
	if err := checkin.Insert(); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "签到失败",
		})
		return
	}
	// 签到成功
	user.Quota += config.CheckinQuota
	if err := user.Update(false); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "签到失败",
		})
		return
	}
	// 记录签到日志
	model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeUserCheckin, "签到获得 "+common.LogQuota(config.CheckinQuota), "")
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "签到成功",
		"data": gin.H{
			"total_days": checkin.TotalDays,
			"quota":      checkin.Quota,
		},
	})
	return
}

type TransferRequest struct {
	ToUserId   int    `json:"to_user_id"`  //转账对象id
	ToUsername string `json:"to_username"` //转账对象用户名，用于验证
	Quota      int64  `json:"quota"`       //转账额度
	Remark     string `json:"remark"`      //备注
}

// Transfer 用户账户间转账
func Transfer(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.GetInt("id") //转账人id
	var req TransferRequest
	err := json.NewDecoder(c.Request.Body).Decode(&req)
	if err != nil || req.ToUserId == 0 || req.Quota == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}
	if !config.TransferEnabled {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "管理员关闭了转账功能",
		})
		return
	}
	if id == req.ToUserId {
		c.JSON(http.StatusOK, gin.H{
			"success":     false,
			"message":     "不能给自己转账",
			"error_field": "to_user_id",
		})
		return
	}
	fromUser, err := model.GetUserById(id, true)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无法获取发起用户信息",
		})
		return
	}
	toUser, err := model.GetUserById(req.ToUserId, true)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":     false,
			"message":     "无法获取接收者信息",
			"error_field": "to_user_id",
		})
		return
	}

	if toUser.Username != req.ToUsername {
		c.JSON(http.StatusOK, gin.H{
			"success":     false,
			"message":     "接收者用户名与ID不匹配",
			"error_field": "to_username",
		})
		return
	}

	if fromUser.Group != toUser.Group {
		c.JSON(http.StatusOK, gin.H{
			"success":     false,
			"message":     "不同用户组间无法转账",
			"error_field": "to_user_id",
		})
		return
	}

	if toUser.Status != common.UserStatusEnabled {
		c.JSON(http.StatusOK, gin.H{
			"success":     false,
			"message":     "接收者账户已封禁",
			"error_field": "to_user_id",
		})
		return
	}

	// 检查转账额度是否符合要求（10美金～500美金，500000quota=1美金）
	if req.Quota < 5000000 || req.Quota > 250000000 {
		c.JSON(http.StatusOK, gin.H{
			"success":     false,
			"message":     "转账额度不符合要求",
			"error_field": "usd",
		})
		return
	}

	// 在更广的作用域中声明ThisTransferFee
	var ThisTransferFee int64 = 0 // 假设手续费是int类型，根据你的实际类型调整

	if config.TransferFee > 0 {
		// 计算本次转账的手续费
		ThisTransferFee = req.Quota * int64(config.TransferFee) / 100
		// 检查发起者余额是否充足（包括手续费）
		if fromUser.Quota < req.Quota+ThisTransferFee {
			c.JSON(http.StatusOK, gin.H{
				"success":     false,
				"message":     "余额不足以支付转账金额和手续费",
				"error_field": "usd",
			})
			return
		}
		fromUser.Quota -= req.Quota + ThisTransferFee // 扣除发起者的转账金额和手续费
	} else {
		// 没有手续费时，仅检查余额是否足够转账
		if fromUser.Quota < req.Quota {
			c.JSON(http.StatusOK, gin.H{
				"success":     false,
				"message":     "余额不足",
				"error_field": "usd",
			})
			return
		}
		fromUser.Quota -= req.Quota // 扣除发起者的转账金额
	}
	toUser.Quota += req.Quota // 增加接收者的余额

	// 更新用户信息和处理错误
	if err := fromUser.Update(false); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "转账失败",
		})
		return
	}

	if err := toUser.Update(false); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "转账失败",
		})
		return
	}
	if req.Remark == "" {
		req.Remark = fmt.Sprintf("通过 API 充值 %s", common.LogQuota(req.Quota))
	}
	model.RecordTopupLog(ctx, req.ToUserId, req.Remark, int(req.Quota))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "转账成功",
		"data": gin.H{
			"transfer_fee": ThisTransferFee,
		},
	})

	model.RecordLog(ctx, id, model.LogTypeOperation, fmt.Sprintf("转账给用户 %s %s（备注：%s），手续费 %s", toUser.Username, common.LogQuota(req.Quota), req.Remark, common.LogQuota(ThisTransferFee)))
	model.RecordLog(ctx, req.ToUserId, model.LogTypeOperation, fmt.Sprintf("收到用户 %s 的转账 %s（备注：%s）", fromUser.Username, common.LogQuota(req.Quota), req.Remark))
	return
}

// GetTransferFee 获取转账手续费
func GetTransferFee(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    config.TransferFee,
	})
	return
}

type ReceiveRedPacketRequest struct {
	RedPacketUUID string `json:"red_packet_uuid"`
}

// ReceiveRedPacket 用户领取红包
func ReceiveRedPacket(c *gin.Context) {
	ctx := c.Request.Context()
	var req ReceiveRedPacketRequest
	err := json.NewDecoder(c.Request.Body).Decode(&req)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}
	id := c.GetInt("id")
	user, err := model.GetUserById(id, true)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无法获取用户信息",
		})
		return
	}
	redPacket, err := model.GetRedPacketByUUID(req.RedPacketUUID)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无法获取红包信息",
		})
		return
	}

	if redPacket.Status != 1 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的红包",
		})
		return
	}

	if received, _ := model.CheckUserReceivedRedPacket(req.RedPacketUUID, user.Id); received {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "您已领取过该红包",
		})
		return
	}

	if redPacket.RemainQuota <= 0 || redPacket.RemainCount <= 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "红包已领完",
		})
		return
	}

	// 领取红包，根据红包类型不同，处理不同的逻辑
	//FIXME：连续请求会导致一个用户领取多次红包
	switch redPacket.Type {
	case 1: // 普通红包
		//金额=红包金额
		quota := redPacket.Quota
		// 更新红包信息，这里不扣除红包金额，只扣除红包数量，因为红包金额是固定的，只有数量为 0 时才是领完
		//redPacket.RemainQuota -= quota
		redPacket.RemainCount--
		if err := redPacket.Update(); err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "领取红包失败",
			})
			return
		}
		// 更新红包领取记录
		err = model.UpdateReceivedUserIds(req.RedPacketUUID, user.Id)
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "领取红包失败",
			})
			return
		}
		// 更新用户信息
		user.Quota += quota
		if err := user.Update(false); err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "领取红包失败",
			})
			return
		}
		// 更新红包领取记录
		err = model.UpdateReceivedUserIds(req.RedPacketUUID, user.Id)
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "领取红包失败",
			})
			return
		}
		// 记录领取红包日志
		model.RecordLog(ctx, id, model.LogTypeTopup, fmt.Sprintf("领取红包 %s", common.LogQuota(quota)))

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "领取红包成功",
			"data": gin.H{
				"quota": quota,
			},
		})
		return

	case 2: // 拼手气红包 金额=随机金额
		// 随机红包金额
		quota, _ := model.GetRandomRedPacketQuota(req.RedPacketUUID)
		// 更新红包信息
		redPacket.RemainQuota -= quota
		redPacket.RemainCount--
		if err := redPacket.Update(); err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "领取红包失败",
			})
			return
		}
		// 更新用户信息
		user.Quota += quota
		if err := user.Update(false); err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "领取红包失败",
			})
			return
		}
		// 记录领取红包日志
		model.RecordLog(ctx, id, model.LogTypeTopup, fmt.Sprintf("领取拼手气红包 %s", common.LogQuota(quota)))
		// 返回领取金额
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "领取红包成功",
			"data": gin.H{
				"quota": quota,
			},
		})
		return
	}
}

// GetRedPacketStatus 获取用户与当前红包的信息
func GetRedPacketStatus(c *gin.Context) {
	redPacketUUID := c.Query("red_packet_uuid")
	id := c.GetInt("id")
	user, err2 := model.GetUserById(id, true)
	if err2 != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无法获取用户信息",
		})
		return
	}
	status, err3 := model.GetRedPacketStatus(redPacketUUID, user.Id)
	if err3 != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无法获取红包状态",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"status": status,
		},
	})

}

// ValidatePasswordByUserId 验证用户密码，用于敏感操作之前的密码验证
func ValidatePasswordByUserId(userId int, password string) bool {
	user, err := model.GetUserById(userId, false)
	if err != nil {
		return false
	}
	return user.ValidatePassword(password) == nil
}

// GenerateUserUid 生成用户 UID
func GenerateUserUid(id int) string {
	// 对 ID 进行混淆处理
	obfuscatedID := obfuscateID(id)

	// 将混淆后的 ID 转换为字符串
	uidStr := strconv.Itoa(obfuscatedID)

	// 添加前缀和补零
	uidStr = common.UserUidPrefix + helper.PadZeros(uidStr, common.UserUidLength-len(common.UserUidPrefix))

	return uidStr
}

// ParseUID 解析 UID为 ID
func ParseUID(uid string) (int, error) {
	// 去除前缀
	uidStr := strings.TrimPrefix(uid, common.UserUidPrefix)

	// 将字符串转换为整数
	obfuscatedID, err := strconv.Atoi(uidStr)
	if err != nil {
		return 0, err
	}

	// 对混淆后的 ID 进行逆向处理,还原出原始 ID
	id := deobfuscateID(obfuscatedID)

	return id, nil
}

func obfuscateID(id int) int {
	uid := uint32(id)
	obfuscatedUID := uid<<3 | uid>>29
	obfuscatedUID = obfuscatedUID ^ 0xABCDEF
	return int(obfuscatedUID)
}

func deobfuscateID(obfuscatedID int) int {
	obfuscatedUID := uint32(obfuscatedID)
	uid := obfuscatedUID ^ 0xABCDEF
	uid = uid>>3 | uid<<29
	return int(uid)
}

func UpdateUserTimezone(c *gin.Context) {
	userId := c.GetInt("id") // 假设你在中间件中设置了用户ID

	var input struct {
		Timezone string `json:"timezone" binding:"required"`
	}

	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input"})
		return
	}

	// 验证时区是否有效
	_, err := time.LoadLocation(input.Timezone)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid timezone"})
		return
	}

	// 更新用户的时区
	err = model.UpdateUserTimezone(userId, input.Timezone)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update timezone"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Timezone updated successfully"})
}

// ResetUserRedisQuota 重置用户的Redis缓存余额（通过删除Redis键）
func ResetUserRedisQuota(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的用户ID",
		})
		return
	}

	// 权限检查：只有管理员才能执行此操作
	myRole := c.GetInt(ctxkey.Role)
	if myRole < common.RoleAdminUser {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "没有权限执行此操作",
		})
		return
	}

	// 从数据库获取用户
	user, err := model.GetUserById(id, false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取用户信息失败: " + err.Error(),
		})
		return
	}

	if !common.RedisEnabled {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "Redis未启用",
		})
		return
	}

	// 删除Redis中的用户余额相关键
	quotaKey := fmt.Sprintf("user_quota:%d", id)
	expireTimeKey := fmt.Sprintf("quota_expire_time:%d", id)

	err1 := common.RedisDel(quotaKey)
	err2 := common.RedisDel(expireTimeKey)

	if err1 != nil || err2 != nil {
		var errorMsg string
		if err1 != nil {
			errorMsg += "删除余额缓存失败: " + err1.Error() + " "
		}
		if err2 != nil {
			errorMsg += "删除过期时间缓存失败: " + err2.Error()
		}
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": strings.TrimSpace(errorMsg),
		})
		return
	}

	// 记录操作日志
	model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeManage, fmt.Sprintf("清除用户 %s 的Redis余额缓存", user.Username), "")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "已清除Redis余额缓存",
	})
	return
}

package controller

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/dto"
	"github.com/songquanpeng/one-api/service"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/common/message"
	"github.com/songquanpeng/one-api/model"
)

// GetUserNotificationSetting 获取用户通知设置
func GetUserNotificationSetting(c *gin.Context) {
	userId := c.GetInt("id")

	setting, err := model.CacheGetUserNotificationSetting(userId)
	if err != nil {
		logger.SysError("获取用户通知设置失败: " + err.Error())
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取通知设置失败",
		})
		return
	}

	// 获取订阅事件
	events, err := setting.GetSubscriptionEvents()
	if err != nil {
		logger.SysError("解析订阅事件失败: " + err.Error())
		events = []model.SubscriptionEvent{}
	}

	// 获取通知方式
	methods, err := setting.GetNotificationMethods()
	if err != nil {
		logger.SysError("解析通知方式失败: " + err.Error())
		methods = []model.NotificationMethod{}
	}

	// 获取自定义邮箱列表
	customEmails, err := setting.GetCustomEmails()
	if err != nil {
		logger.SysError("解析自定义邮箱失败: " + err.Error())
		customEmails = []string{}
	}

	response := gin.H{
		"success": true,
		"data": gin.H{
			"id":                   setting.ID,
			"user_id":              setting.UserId,
			"subscription_events":  events,
			"notification_methods": methods,
			"webhook_url":          setting.WebhookUrl,
			"webhook_token":        setting.WebhookToken,
			"webhook_secret":       setting.WebhookSecret,
			"custom_email":         setting.CustomEmail, // 兼容旧版本
			"custom_emails":        customEmails,        // 新的多邮箱字段
			"email_enabled":        setting.EmailEnabled,
			"telegram_enabled":     setting.TelegramEnabled,
			"telegram_bot_token":   setting.TelegramBotToken,
			"telegram_chat_id":     setting.TelegramChatId,
			"webhook_enabled":      setting.WebhookEnabled,
			"wxpusher_enabled":     setting.WxPusherEnabled,
			"qywxbot_enabled":      setting.QyWxBotEnabled,
			"dingtalk_enabled":     setting.DingtalkEnabled,
			"feishu_enabled":       setting.FeishuEnabled,
			"balance_threshold":    setting.BalanceThreshold,
			"qywx_webhook_url":     setting.QyWxWebhookUrl,
			"dingtalk_webhook_url": setting.DingtalkWebhookUrl,
			"feishu_webhook_url":   setting.FeishuWebhookUrl,
			"wxpusher_app_token":   setting.WxPusherAppToken,
			"wxpusher_uid":         setting.WxPusherUid,
		},
	}

	c.JSON(http.StatusOK, response)
}

// UpdateUserNotificationSetting 更新用户通知设置
func UpdateUserNotificationSetting(c *gin.Context) {
	userId := c.GetInt("id")

	var req struct {
		SubscriptionEvents  []model.SubscriptionEvent  `json:"subscription_events"`
		NotificationMethods []model.NotificationMethod `json:"notification_methods"`
		WebhookUrl          string                     `json:"webhook_url"`
		WebhookToken        string                     `json:"webhook_token"`
		WebhookSecret       string                     `json:"webhook_secret"`
		CustomEmail         string                     `json:"custom_email"`  // 兼容旧版本
		CustomEmails        []string                   `json:"custom_emails"` // 新的多邮箱字段
		EmailEnabled        bool                       `json:"email_enabled"`
		TelegramEnabled     bool                       `json:"telegram_enabled"`
		TelegramBotToken    string                     `json:"telegram_bot_token"`
		TelegramChatId      string                     `json:"telegram_chat_id"`
		WebhookEnabled      bool                       `json:"webhook_enabled"`
		WxPusherEnabled     bool                       `json:"wxpusher_enabled"`
		QyWxBotEnabled      bool                       `json:"qywxbot_enabled"`
		DingtalkEnabled     bool                       `json:"dingtalk_enabled"`
		FeishuEnabled       bool                       `json:"feishu_enabled"`
		BalanceThreshold    float64                    `json:"balance_threshold"`
		QyWxWebhookUrl      string                     `json:"qywx_webhook_url"`
		DingtalkWebhookUrl  string                     `json:"dingtalk_webhook_url"`
		FeishuWebhookUrl    string                     `json:"feishu_webhook_url"`
		WxPusherAppToken    string                     `json:"wxpusher_app_token"`
		WxPusherUid         string                     `json:"wxpusher_uid"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取现有设置
	setting, err := model.CacheGetUserNotificationSetting(userId)
	if err != nil {
		logger.SysError("获取用户通知设置失败: " + err.Error())
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取通知设置失败",
		})
		return
	}

	// 更新设置
	if err := setting.SetSubscriptionEvents(req.SubscriptionEvents); err != nil {
		logger.SysError("设置订阅事件失败: " + err.Error())
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "设置订阅事件失败",
		})
		return
	}

	if err := setting.SetNotificationMethods(req.NotificationMethods); err != nil {
		logger.SysError("设置通知方式失败: " + err.Error())
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "设置通知方式失败",
		})
		return
	}

	setting.WebhookUrl = req.WebhookUrl
	setting.WebhookToken = req.WebhookToken
	setting.WebhookSecret = req.WebhookSecret

	// 处理邮箱设置的兼容性
	var emailsToSet []string
	if len(req.CustomEmails) > 0 {
		// 优先使用新的多邮箱字段
		emailsToSet = req.CustomEmails
	} else if req.CustomEmail != "" {
		// 兼容旧版本的单邮箱字段
		emailsToSet = []string{req.CustomEmail}
	}

	// 设置自定义邮箱列表
	if err := setting.SetCustomEmails(emailsToSet); err != nil {
		logger.SysError("设置自定义邮箱失败: " + err.Error())
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "设置自定义邮箱失败",
		})
		return
	}
	setting.EmailEnabled = req.EmailEnabled
	setting.TelegramEnabled = req.TelegramEnabled
	setting.TelegramBotToken = req.TelegramBotToken
	setting.TelegramChatId = req.TelegramChatId
	setting.WebhookEnabled = req.WebhookEnabled
	setting.WxPusherEnabled = req.WxPusherEnabled
	setting.QyWxBotEnabled = req.QyWxBotEnabled
	setting.DingtalkEnabled = req.DingtalkEnabled
	setting.FeishuEnabled = req.FeishuEnabled
	setting.BalanceThreshold = req.BalanceThreshold
	setting.QyWxWebhookUrl = req.QyWxWebhookUrl
	setting.DingtalkWebhookUrl = req.DingtalkWebhookUrl
	setting.FeishuWebhookUrl = req.FeishuWebhookUrl
	setting.WxPusherAppToken = req.WxPusherAppToken
	setting.WxPusherUid = req.WxPusherUid

	// 保存到数据库
	if err := model.UpdateUserNotificationSetting(setting); err != nil {
		logger.SysError("更新用户通知设置失败: " + err.Error())
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "更新通知设置失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "通知设置更新成功",
	})
}

// GetAvailableNotificationEvents 获取所有可用的通知事件
func GetAvailableNotificationEvents(c *gin.Context) {
	events := model.GetAvailableEvents()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    events,
	})
}

// TestNotification 测试通知功能
func TestNotification(c *gin.Context) {
	userId := c.GetInt("id")

	var req struct {
		Type    string `json:"type" binding:"required"`
		Message string `json:"message"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	if req.Message == "" {
		req.Message = "这是一条测试通知消息"
	}

	// 获取用户通知设置
	setting, err := model.CacheGetUserNotificationSetting(userId)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取通知设置失败",
		})
		return
	}

	subject := "通知设置测试"
	success := false

	switch req.Type {
	case "email":
		if setting.EmailEnabled {
			// 获取有效的邮箱地址列表（优先使用自定义邮箱）
			emailAddresses, err := setting.GetEffectiveEmails()
			if err != nil {
				logger.SysError("获取邮箱地址失败: " + err.Error())
				c.JSON(http.StatusInternalServerError, gin.H{
					"success": false,
					"message": "获取邮箱地址失败: " + err.Error(),
				})
				return
			}

			if len(emailAddresses) == 0 {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"message": "邮箱通知未启用或邮箱地址为空",
				})
				return
			}

			// 向所有邮箱发送测试邮件
			for _, emailAddress := range emailAddresses {
				err = message.SendEmailWithLogging(userId, "test_notification", subject, emailAddress, req.Message)
				if err != nil {
					// 错误已经在SendEmailWithLogging中记录了，这里只需要记录旧格式的日志以保持兼容性
					logger.SysError("发送测试邮件失败: " + err.Error())
					c.JSON(http.StatusInternalServerError, gin.H{
						"success": false,
						"message": "发送测试邮件失败: " + err.Error(),
					})
					return
				}
			}
			success = true
		} else {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "邮箱通知未启用",
			})
			return
		}
	case "telegram":
		if setting.TelegramEnabled && setting.TelegramBotToken != "" && setting.TelegramChatId != "" {
			err := sendTestTelegram(setting.TelegramBotToken, setting.TelegramChatId, req.Message)
			if err != nil {
				logger.SysError("发送测试Telegram消息失败: " + err.Error())
				c.JSON(http.StatusInternalServerError, gin.H{
					"success": false,
					"message": "发送测试Telegram消息失败: " + err.Error(),
				})
				return
			}
			success = true
		} else {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "Telegram通知未启用或配置信息不完整",
			})
			return
		}
	case "webhook":
		if setting.WebhookEnabled && setting.WebhookUrl != "" {
			// 优先使用新的 WebhookSecret 字段，如果为空则使用旧的 WebhookToken 字段
			secret := setting.WebhookSecret
			if secret == "" {
				secret = setting.WebhookToken
			}
			err := sendTestWebhookNew(setting.WebhookUrl, secret, subject, req.Message)
			if err != nil {
				logger.SysError("发送测试Webhook失败: " + err.Error())
				c.JSON(http.StatusInternalServerError, gin.H{
					"success": false,
					"message": "发送测试Webhook失败: " + err.Error(),
				})
				return
			}
			success = true
		} else {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "Webhook通知未启用或Webhook URL为空",
			})
			return
		}
	case "qywxbot":
		if setting.QyWxBotEnabled && setting.QyWxWebhookUrl != "" {
			err := sendTestQyWxBot(setting.QyWxWebhookUrl, subject, req.Message)
			if err != nil {
				logger.SysError("发送测试企业微信机器人消息失败: " + err.Error())
				c.JSON(http.StatusInternalServerError, gin.H{
					"success": false,
					"message": "发送测试企业微信机器人消息失败: " + err.Error(),
				})
				return
			}
			success = true
		} else {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "企业微信机器人通知未启用或Webhook URL为空",
			})
			return
		}
	case "wxpusher":
		if setting.WxPusherEnabled && setting.WxPusherAppToken != "" && setting.WxPusherUid != "" {
			err := sendTestWxPusher(setting.WxPusherAppToken, setting.WxPusherUid, subject, req.Message)
			if err != nil {
				logger.SysError("发送测试WxPusher消息失败: " + err.Error())
				c.JSON(http.StatusInternalServerError, gin.H{
					"success": false,
					"message": "发送测试WxPusher消息失败: " + err.Error(),
				})
				return
			}
			success = true
		} else {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "WxPusher通知未启用或配置信息不完整",
			})
			return
		}
	case "dingtalk":
		if setting.DingtalkEnabled && setting.DingtalkWebhookUrl != "" {
			err := sendTestDingtalk(setting.DingtalkWebhookUrl, subject, req.Message)
			if err != nil {
				logger.SysError("发送测试钉钉机器人消息失败: " + err.Error())
				c.JSON(http.StatusInternalServerError, gin.H{
					"success": false,
					"message": "发送测试钉钉机器人消息失败: " + err.Error(),
				})
				return
			}
			success = true
		} else {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "钉钉机器人通知未启用或Webhook URL为空",
			})
			return
		}
	case "feishu":
		if setting.FeishuEnabled && setting.FeishuWebhookUrl != "" {
			err := sendTestFeishu(setting.FeishuWebhookUrl, subject, req.Message)
			if err != nil {
				logger.SysError("发送测试飞书机器人消息失败: " + err.Error())
				c.JSON(http.StatusInternalServerError, gin.H{
					"success": false,
					"message": "发送测试飞书机器人消息失败: " + err.Error(),
				})
				return
			}
			success = true
		} else {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "飞书机器人通知未启用或Webhook URL为空",
			})
			return
		}
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "不支持的通知类型",
		})
		return
	}

	if success {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "测试通知发送成功",
		})
	}
}

// sendTestWebhook 发送测试Webhook
func sendTestWebhook(url, token, subject, message string) error {
	payload := map[string]interface{}{
		"subject":    subject,
		"message":    message,
		"timestamp":  helper.GetTimestamp(),
		"event_type": "test",
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return err
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")
	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("webhook responded with status %d", resp.StatusCode)
	}

	return nil
}

// sendTestWxPusher 发送测试WxPusher消息
func sendTestWxPusher(appToken, uid, subject, message string) error {
	payload := map[string]interface{}{
		"appToken":    appToken,
		"content":     fmt.Sprintf("%s\n\n%s", subject, message),
		"summary":     subject,
		"contentType": 1, // 文本消息
		"uids":        []string{uid},
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return err
	}

	req, err := http.NewRequest("POST", "http://wxpusher.zjiecode.com/api/send/message", bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("WxPusher API responded with status %d", resp.StatusCode)
	}

	return nil
}

// sendTestDingtalk 发送测试钉钉机器人消息
func sendTestDingtalk(webhookUrl, subject, message string) error {
	// 钉钉机器人支持多种消息类型，这里使用text类型
	payload := map[string]interface{}{
		"msgtype": "text",
		"text": map[string]interface{}{
			"content": fmt.Sprintf("📢 %s\n\n%s", subject, message),
		},
		// 可选：@所有人或特定用户
		"at": map[string]interface{}{
			"atMobiles": []string{}, // 可以指定@的手机号
			"isAtAll":   false,      // 是否@所有人
		},
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("序列化JSON失败: %v", err)
	}

	req, err := http.NewRequest("POST", webhookUrl, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Timeout: 10 * time.Second, // 设置超时时间
	}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("钉钉机器人API返回错误状态码 %d，响应内容: %s", resp.StatusCode, string(body))
	}

	// 解析钉钉API响应
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return fmt.Errorf("解析响应JSON失败: %v，响应内容: %s", err, string(body))
	}

	// 检查钉钉API返回的错误码
	if errCode, exists := result["errcode"]; exists {
		if code, ok := errCode.(float64); ok && code != 0 {
			errMsg := "未知错误"
			if msg, exists := result["errmsg"]; exists {
				if msgStr, ok := msg.(string); ok {
					errMsg = msgStr
				}
			}
			return fmt.Errorf("钉钉机器人API返回错误码 %.0f: %s", code, errMsg)
		}
	}

	return nil
}

// sendTestFeishu 发送测试飞书机器人消息
func sendTestFeishu(webhookUrl, subject, message string) error {
	// 飞书机器人支持多种消息类型，这里使用text类型
	payload := map[string]interface{}{
		"msg_type": "text",
		"content": map[string]interface{}{
			"text": fmt.Sprintf("📢 %s\n\n%s", subject, message),
		},
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("序列化JSON失败: %v", err)
	}

	req, err := http.NewRequest("POST", webhookUrl, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Timeout: 10 * time.Second, // 设置超时时间
	}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("飞书机器人API返回错误状态码 %d，响应内容: %s", resp.StatusCode, string(body))
	}

	// 解析飞书API响应
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return fmt.Errorf("解析响应JSON失败: %v，响应内容: %s", err, string(body))
	}

	// 检查飞书API返回的状态码
	if statusCode, exists := result["StatusCode"]; exists {
		if code, ok := statusCode.(float64); ok && code != 0 {
			statusMsg := "未知错误"
			if msg, exists := result["StatusMessage"]; exists {
				if msgStr, ok := msg.(string); ok {
					statusMsg = msgStr
				}
			}
			return fmt.Errorf("飞书机器人API返回错误码 %.0f: %s", code, statusMsg)
		}
	}

	return nil
}

// sendTestQyWxBot 发送测试企业微信机器人消息
func sendTestQyWxBot(webhookUrl, subject, message string) error {
	payload := map[string]interface{}{
		"msgtype": "text",
		"text": map[string]interface{}{
			"content": fmt.Sprintf("%s\n\n%s", subject, message),
		},
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return err
	}

	req, err := http.NewRequest("POST", webhookUrl, bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("企业微信机器人 API responded with status %d", resp.StatusCode)
	}

	return nil
}

// sendTestTelegram 发送测试Telegram消息
func sendTestTelegram(botToken, chatId, message string) error {
	payload := map[string]interface{}{
		"chat_id": chatId,
		"text":    message,
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("序列化JSON失败: %v", err)
	}

	url := "https://api.telegram.org/bot" + botToken + "/sendMessage"
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("Telegram API返回错误状态码 %d，响应内容: %s", resp.StatusCode, string(body))
	}

	// 解析Telegram API响应
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return fmt.Errorf("解析响应JSON失败: %v，响应内容: %s", err, string(body))
	}

	// 检查Telegram API返回的状态
	if ok, exists := result["ok"]; exists {
		if success, isOk := ok.(bool); isOk && !success {
			description := "未知错误"
			if desc, exists := result["description"]; exists {
				if descStr, ok := desc.(string); ok {
					description = descStr
				}
			}
			return fmt.Errorf("Telegram API返回错误: %s", description)
		}
	}

	return nil
}

// sendTestWebhookNew 发送测试Webhook（使用新的统一格式，支持 CloudFlare Worker）
func sendTestWebhookNew(url, secret, subject, message string) error {
	// 创建测试通知数据
	data := dto.NewNotify("test", subject, message, nil)

	// 使用统一的 webhook 服务发送（自动支持 CloudFlare Worker）
	return service.SendWebhookNotify(url, secret, data)
}

package controller

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"math/rand"
	"net/http"
	"strings"
	"sync"
	"sync/atomic"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/client"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/common/message"
	"github.com/songquanpeng/one-api/controller/optimizer"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/relay/adaptor/openai"
	billingratio "github.com/songquanpeng/one-api/relay/billing/ratio"
	"github.com/songquanpeng/one-api/relay/channeltype"
	"github.com/songquanpeng/one-api/relay/constant"
	"github.com/songquanpeng/one-api/relay/controller"
	relaycontroller "github.com/songquanpeng/one-api/relay/controller"
	"github.com/songquanpeng/one-api/relay/meta"
	relayModel "github.com/songquanpeng/one-api/relay/model"
	"github.com/songquanpeng/one-api/relay/util"
)

func relayOpenaiLobeTextHelper(c *gin.Context, relayMode int) *relayModel.ErrorWithStatusCode {
	ctx := c.Request.Context()
	meta := meta.GetByContext(c)

	channelType := c.GetInt("channel")
	billingType := c.GetInt(ctxkey.BillingType)
	tokenId := c.GetInt("token_id")
	userId := c.GetInt("id")
	group := c.GetString("group")
	_startTime := helper.GetTimestamp()
	var textRequest relayModel.GeneralOpenAIRequest
	err := common.UnmarshalBodyReusable(c, &textRequest)
	if err != nil {
		return openai.ErrorWrapper(err, "bind_request_body_failed", http.StatusBadRequest)
	}
	if textRequest.MaxTokens < 0 || textRequest.MaxTokens > math.MaxInt32/2 {
		return openai.ErrorWrapper(errors.New("max_tokens is invalid"), "invalid_max_tokens", http.StatusBadRequest)
	}
	if relayMode == constant.RelayModeModerations && textRequest.Model == "" {
		textRequest.Model = "text-moderation-latest"
	}
	if relayMode == constant.RelayModeEmbeddings && textRequest.Model == "" {
		textRequest.Model = c.Param("model")
	}
	// request validation
	if textRequest.Model == "" {
		return openai.ErrorWrapper(errors.New("model is required"), "required_field_missing", http.StatusBadRequest)
	}
	if textRequest.Messages == nil || len(textRequest.Messages) == 0 {
		return openai.ErrorWrapper(errors.New("field messages is required"), "required_field_missing", http.StatusBadRequest)
	}
	// map model name
	isModelMapped := false
	textRequest.Model, isModelMapped = util.GetMappedModelName(textRequest.Model, meta.ModelMapping, meta.ModelMappingArr)
	meta.ActualModelName = textRequest.Model
	//apiType := common.ChannelTypeOpenaiLobe
	baseURL := channeltype.ChannelBaseURLs[channelType]
	// 用于保存详细聊天记录日志
	var detailPrompt = ""
	var detailCompletion = ""
	if c.GetString("base_url") != "" {
		baseURL = c.GetString("base_url")
	}
	fullRequestURL := util.GetFullRequestURL(baseURL, "/api/openai/chat", channelType)
	var promptTokens int
	var completionTokens int
	promptTokens, _, _, err = openai.CountTokenMessages(textRequest.Messages, textRequest.Model, meta)
	if err != nil {
		return openai.ErrorWrapper(err, "count_token_messages_failed", http.StatusInternalServerError)
	}
	var modelFixedPrice float64
	var modelFixedPriceErr error
	if billingType == common.BillingTypeByCount {
		modelFixedPrice, modelFixedPriceErr = billingratio.GetModelFixedPrice(textRequest.Model)
		if modelFixedPriceErr != nil {
			return openai.ErrorWrapper(modelFixedPriceErr, "model_fixed_price_not_config", http.StatusForbidden)
		}
	}
	preConsumedTokens := config.PreConsumedQuota
	if textRequest.MaxTokens != 0 {
		preConsumedTokens = int64(promptTokens) + int64(textRequest.MaxTokens)
	}
	modelRatio := billingratio.GetModelRatio(textRequest.Model, meta.ChannelType)
	groupRatio := billingratio.GetGroupRatio(group)
	ratio := modelRatio * groupRatio
	if billingType == common.BillingTypeByCount {
		ratio = groupRatio
	}
	preConsumedQuota := int64(float64(preConsumedTokens) * ratio)
	if billingType == common.BillingTypeByCount {
		preConsumedQuota = int64(float64(modelFixedPrice) * 500000 * ratio)
	}
	userQuota, quotaExpireTime, err := model.CacheGetUserQuotaAndExpireTime(ctx, userId)
	if err != nil {
		return openai.ErrorWrapper(err, "get_user_quota_failed", http.StatusInternalServerError)
	}
	if config.QuotaExpireEnabled && quotaExpireTime > 0 && quotaExpireTime < helper.GetTimestamp() {
		model.RecordSysLogToDBAndFile(c.Request.Context(), c.GetString(helper.RequestIdKey), model.LogTypeSystemErr, userId, 0, textRequest.Model, "", "", "user quota expired", "尚未解析")
		return openai.ErrorWrapper(errors.New("user quota expired"), "user_quota_expired", http.StatusForbidden)
	}
	if userQuota <= 0 || userQuota-preConsumedQuota < 0 {
		return openai.ErrorWrapper(errors.New(fmt.Sprintf("user [%d] quota [%d] preConsumedQuota [%d] is not enough", meta.UserId, userQuota, preConsumedQuota)), "insufficient_user_quota", http.StatusForbidden)
	}
	if preConsumedQuota > 0 {
		err, isLowQuota := model.PreConsumeTokenQuota(ctx, tokenId, preConsumedQuota)
		if err != nil {
			return openai.ErrorWrapper(err, "pre_consume_token_quota_failed", http.StatusForbidden)
		}
		if isLowQuota {
			helper.SafeGoroutine(func() {
				// 发送余额预警通知（内部会检查2小时抑制机制）
				notificationSent := relaycontroller.SendBalanceWarningNotification(meta.UserId, userQuota)

				// 只有在用户通知发送成功时才通知管理员，避免重复通知
				if notificationSent {
					message.NotifyRootUser("额度即将用尽提醒", fmt.Sprintf("用户[%d]额度即将用尽，剩余额度为 %d $", meta.UserId, userQuota/500000))
				}
			})
		}
	}
	var requestBody io.Reader
	if isModelMapped {
		jsonStr, err := json.Marshal(textRequest)
		if err != nil {
			return openai.ErrorWrapper(err, "marshal_text_request_failed", http.StatusInternalServerError)
		}
		requestBody = bytes.NewBuffer(jsonStr)
	} else {
		requestBody = c.Request.Body
	}

	// 读取 Body 内容
	bodyBytes, _ := io.ReadAll(requestBody)
	detailPrompt = string(bodyBytes)
	// 校验入参敏感词
	if config.SensitiveWordsEnabled {
		if config.SensitiveWordsMap != nil && len(config.SensitiveWordsMap) > 0 {
			for word, _ := range config.SensitiveWordsMap {
				trimmedWord := strings.TrimSpace(word)
				if trimmedWord != "" && strings.Contains(detailPrompt, trimmedWord) {
					return openai.ErrorWrapper(errors.New(config.SensitiveWordsTips), "sensitive_words_in_request", http.StatusBadRequest)
				}
			}
		}
	}
	// 根据配置排除请求字段
	detailPrompt, bodyBytes, err = util.ExcludeFields(c, detailPrompt, bodyBytes)
	detailPrompt, bodyBytes, err = util.AddExtraFields(c, detailPrompt, bodyBytes)
	if err != nil {
		return openai.ErrorWrapper(err, "exclude_fields_failed", http.StatusInternalServerError)
	}
	// 恢复原始的 Body
	requestBody = io.NopCloser(bytes.NewBuffer(bodyBytes))

	var req *http.Request
	var resp *http.Response
	isStream := textRequest.Stream

	req, err = http.NewRequest(c.Request.Method, fullRequestURL, requestBody)
	if err != nil {
		return openai.ErrorWrapper(err, "new_request_failed", http.StatusInternalServerError)
	}
	apiKey := c.Request.Header.Get("Authorization")
	apiKey = strings.TrimPrefix(apiKey, "Bearer ")
	req.Header.Set("Authorization", c.Request.Header.Get("Authorization"))
	if c.Request.Header.Get("OpenAI-Organization") != "" {
		req.Header.Set("OpenAI-Organization", c.Request.Header.Get("OpenAI-Organization"))
	}
	req.Header.Set("Content-Type", c.Request.Header.Get("Content-Type"))
	req.Header.Set("Accept", c.Request.Header.Get("Accept"))
	if isStream && c.Request.Header.Get("Accept") == "" {
		req.Header.Set("Accept", "text/event-stream")
	}
	//req.Header.Set("Connection", c.Request.Header.Get("Connection"))
	resp, err = client.HTTPClient.Do(req)
	if err != nil {
		// 针对于sealos报错做特殊处理,重试一次
		if strings.Contains(err.Error(), "GOAWAY") && strings.Contains(err.Error(), "http2") {
			logger.SysError("http2 GOAWAY retry request: " + err.Error())
			resp, err = client.HTTPClient.Do(req)
			if err != nil {
				return openai.ErrorWrapper(err, "do_request_failed", http.StatusInternalServerError)
			}
		} else {
			return openai.ErrorWrapper(err, "do_request_failed", http.StatusInternalServerError)
		}
	}
	err = req.Body.Close()
	if err != nil {
		return openai.ErrorWrapper(err, "close_request_body_failed", http.StatusInternalServerError)
	}
	err = c.Request.Body.Close()
	if err != nil {
		return openai.ErrorWrapper(err, "close_request_body_failed", http.StatusInternalServerError)
	}
	isStream = isStream || strings.HasPrefix(resp.Header.Get("Content-Type"), "text/event-stream")

	if resp.StatusCode != http.StatusOK {
		if preConsumedQuota != 0 {
			go func(ctx context.Context) {
				// return pre-consumed quota
				err := model.PostConsumeTokenQuota(tokenId, -preConsumedQuota)
				if err != nil {
					logger.Error(ctx, "error return pre-consumed quota: "+err.Error())
				}
			}(c.Request.Context())
		}
		return controller.RelayErrorHandler(resp)
	}

	var textResponse openai.TextResponse
	tokenName := c.GetString("token_name")
	tokenKey := c.GetString("token_key")
	channelId := c.GetInt("channel_id")
	channelName := c.GetString("channel_name")

	defer func(ctx context.Context) {
		// c.Writer.Flush()
		go func() {
			var quota int64
			completionRatio := billingratio.GetCompletionRatio(textRequest.Model, meta.ChannelType)
			promptTokens = textResponse.Usage.PromptTokens
			completionTokens = textResponse.Usage.CompletionTokens
			quota = int64(math.Ceil((float64(promptTokens) + float64(completionTokens)*completionRatio) * ratio))
			if ratio != 0 && quota <= 0 {
				quota = 1
			}
			totalTokens := promptTokens + completionTokens
			if totalTokens == 0 {
				// in this case, must be some error happened
				// we cannot just return, because we may have to return the pre-consumed quota
				quota = 0
			}
			if billingType == common.BillingTypeByCount {
				// 如果是按次计费
				quota = int64(float64(modelFixedPrice) * 500000 * ratio)
			}
			quotaDelta := quota - preConsumedQuota
			err := model.PostConsumeTokenQuota(tokenId, quotaDelta)
			if err != nil {
				logger.Error(ctx, "error consuming token remain quota: "+err.Error())
			}
			err = model.CacheUpdateUserQuota(ctx, userId)
			if err != nil {
				logger.Error(ctx, "error update user quota cache: "+err.Error())
			}
			// 即使是请求错误导致的消费为0也记录日志,并且记录耗时
			requestDuration := helper.GetTimestamp() - _startTime
			logContent := fmt.Sprintf("模型倍率 %.2f，分组倍率 %.2f，用时 %d秒", modelRatio, groupRatio, requestDuration)
			if billingType == common.BillingTypeByCount {
				logContent = fmt.Sprintf("模型按次使用固定价格 %.6f，分组倍率 %.2f，用时 %d秒", modelFixedPrice, groupRatio, requestDuration)
			}
			createdLog := model.RecordConsumeLog(ctx, userId, channelId, promptTokens, completionTokens, textRequest.Model, tokenName, tokenKey, channelName, int(quota), requestDuration, isStream, logContent)
			model.UpdateUserUsedQuotaAndRequestCount(userId, quota)
			model.UpdateChannelUsedQuota(channelId, quota)
			helper.SafeGoroutine(func() {
				// 记录详细聊天记录
				model.RecordLogExtend(ctx, createdLog, detailPrompt, detailCompletion, "", meta.UpstreamResponse, meta.FullResponse, meta.RequestURLPath)
				// 推送优化器
				optimizer.RecordConsumeLog(createdLog)
			})
		}()
	}(c.Request.Context())
	if isStream {
		err, responseText := openaiLobeStreamHandler(c, resp, relayMode)
		if err != nil {
			return err
		}
		textResponse.Usage.PromptTokens = promptTokens
		textResponse.Usage.CompletionTokens = openai.CountTokenText(responseText, textRequest.Model)
		detailCompletion = responseText
		return nil
	} else {
		all, _ := io.ReadAll(resp.Body)
		detailCompletion = string(all)
		// 恢复原始的 Body
		resp.Body = io.NopCloser(bytes.NewBuffer(all))

		err, usage := openaiLobeHandler(c, resp, promptTokens, textRequest.Model)
		if err != nil {
			return err
		}
		if usage != nil {
			textResponse.Usage = *usage
		}
		return nil
	}
}

func openaiLobeStreamHandler(c *gin.Context, resp *http.Response, relayMode int) (*relayModel.ErrorWithStatusCode, string) {
	hasTokenAd := c.GetInt("has_token_ad")
	advertisement := c.GetString("token_advertisement")
	adPosition := c.GetInt("token_ad_position")
	// 有广告语,则插入
	if hasTokenAd == 1 && adPosition == 1 {
		openai.ConstructStreamAdvertisement(c, advertisement)
	}

	var responseTextBuilder strings.Builder
	scanner := bufio.NewScanner(resp.Body)
	scanner.Split(func(data []byte, atEOF bool) (advance int, token []byte, err error) {
		if atEOF && len(data) == 0 {
			return 0, nil, nil
		}
		if i := strings.Index(string(data), "\n"); i >= 0 {
			return i + 1, data[0:i], nil
		}
		if atEOF {
			return len(data), data, nil
		}
		return 0, nil, nil
	})
	var dataChan chan string
	var stopChan chan bool
	if config.OpenAIStreamStringBufferEnabled {
		if config.OpenAIStreamStringBufferSize != 0 {
			// 取配置大小
			dataChan = make(chan string, config.OpenAIStreamStringBufferSize)
		} else {
			// 默认5
			dataChan = make(chan string, 5)
		}
		stopChan = make(chan bool, 2)
	} else {
		dataChan = make(chan string)
		stopChan = make(chan bool)
	}
	defer close(stopChan)
	defer close(dataChan)

	loopDataMax := 50 // 防止openai抽风
	lastData := ""
	var wg sync.WaitGroup
	go func() {
		wg.Add(1)
		defer wg.Done()
		for scanner.Scan() {
			// 此时data就是相应内容,不用解析
			data := scanner.Text()
			// 防止openai抽风 start
			if loopDataMax <= 0 {
				break
			}
			if data == lastData {
				loopDataMax--
			}
			lastData = data
			// 防止openai抽风 end
			//if data[:6] != "data: " && data[:6] != "[DONE]" {
			//	continue
			//}
			dataChan <- data
			responseTextBuilder.WriteString(data)
		}
		stopChan <- true
	}()
	common.SetEventStreamHeaders(c)
	// Track the current position.
	currentPosition := 5
	// We can use a flag to track if the ad has been inserted.
	var adInserted int32
	c.Stream(func(w io.Writer) bool {
		select {
		case data := <-dataChan:
			if hasTokenAd == 1 && adPosition == 3 && atomic.LoadInt32(&adInserted) == 0 {
				currentPosition++
				// Generate a random number between 0 and currentPosition (inclusive).
				randNumber := rand.Intn(currentPosition + 1)
				// If the random number is 0, insert the ad.
				// This means the probability of inserting the ad is 1/(currentPosition+1).
				if randNumber == 0 {
					atomic.StoreInt32(&adInserted, 1)
					openai.ConstructStreamAdvertisement(c, advertisement)
				}
			}
			openai.ConstructStreamAdvertisement(c, data)
			// 加入换行
			openai.ConstructStreamAdvertisement(c, "\n")
			return true
		case <-stopChan:
			// Process all remaining items in dataChan
			for len(dataChan) > 0 {
				data := <-dataChan
				openai.ConstructStreamAdvertisement(c, data)
			}
			if hasTokenAd == 1 && adPosition == 2 {
				openai.ConstructStreamAdvertisement(c, advertisement)
			}
			c.Render(-1, common.CustomEvent{Data: "[DONE]"})
			return false
		}
	})
	err := resp.Body.Close()
	if err != nil {
		return openai.ErrorWrapper(err, "close_response_body_failed", http.StatusInternalServerError), ""
	}
	wg.Wait()
	return nil, responseTextBuilder.String()
}

func openaiLobeHandler(c *gin.Context, resp *http.Response, promptTokens int, model string) (*relayModel.ErrorWithStatusCode, *relayModel.Usage) {
	hasTokenAd := c.GetInt("has_token_ad")
	advertisement := c.GetString("token_advertisement")
	adPosition := c.GetInt("token_ad_position")

	var textResponse openai.SlimTextResponse
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return openai.ErrorWrapper(err, "read_response_body_failed", http.StatusInternalServerError), nil
	}
	err = resp.Body.Close()
	if err != nil {
		return openai.ErrorWrapper(err, "close_response_body_failed", http.StatusInternalServerError), nil
	}
	// 将responseBody填入Choices[0].Message中
	textResponse.Choices = append(textResponse.Choices, openai.TextResponseChoice{
		Index: 0,
		Message: relayModel.Message{
			Role:    "assistant",
			Content: string(responseBody),
		},
	})
	if textResponse.Usage.TotalTokens == 0 {
		completionTokens := 0
		for _, choice := range textResponse.Choices {
			completionTokens += openai.CountTokenText(choice.Message.StringContent(), model)
		}
		textResponse.Usage = relayModel.Usage{
			PromptTokens:     promptTokens,
			CompletionTokens: completionTokens,
			TotalTokens:      promptTokens + completionTokens,
		}
	}
	//if textResponse.Error.Type != "" {
	//	return &OpenAIErrorWithStatusCode{
	//		OpenAIError: textResponse.Error,
	//		StatusCode:  resp.StatusCode,
	//	}, nil
	//}
	responseBody, err = json.Marshal(textResponse)
	if err != nil {
		return openai.ErrorWrapper(err, "marshal_response_body_failed", http.StatusInternalServerError), nil
	}
	// 有广告语,则插入
	if hasTokenAd == 1 {
		openai.ConstructAdvertisement(&textResponse, advertisement, adPosition)
		// 将含有广告的textResponse转化为responseBody
		responseBody, err = json.Marshal(textResponse)
		if err != nil {
			return openai.ErrorWrapper(err, "marshal_response_body_failed", http.StatusInternalServerError), nil
		}
	}
	resp.Body = io.NopCloser(bytes.NewBuffer(responseBody))
	for k, v := range resp.Header {
		// 排除这个请求头X-Served-By 避免暴露上游域名
		if strings.ToLower(k) == "x-served-by" {
			continue
		}
		c.Writer.Header().Set(k, v[0])
	}
	c.Writer.WriteHeader(resp.StatusCode)
	_, err = io.Copy(c.Writer, resp.Body)
	if err != nil {
		return openai.ErrorWrapper(err, "copy_response_body_failed", http.StatusInternalServerError), nil
	}
	err = resp.Body.Close()
	if err != nil {
		return openai.ErrorWrapper(err, "close_response_body_failed", http.StatusInternalServerError), nil
	}
	return nil, &textResponse.Usage
}

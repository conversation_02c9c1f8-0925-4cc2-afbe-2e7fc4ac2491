package controller

import (
	"net/http"
	"os"
	"strconv"

	"path/filepath"

	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/songquanpeng/one-api/model"
)

// 注册动态路由管理API
func RegisterDynamicRouterAdminAPI(router *gin.Engine) {
	g := router.Group("/api/dynamic-router")

	// 获取所有路由组
	g.GET("/groups", GetDynamicRouterGroups)

	// 获取单个路由组详情
	g.GET("/groups/:id", GetDynamicRouterGroup)

	// 更新路由组
	g.PUT("/groups/:id", UpdateDynamicRouterGroup)

	// 获取上游列表
	g.GET("/upstreams", GetDynamicRouterUpstreams)

	// 获取单个上游详情
	g.GET("/upstreams/:id", GetDynamicRouterUpstream)

	// 更新上游配置
	g.PUT("/upstreams/:id", UpdateDynamicRouterUpstream)

	// 获取端点列表
	g.GET("/endpoints", GetDynamicRouterEndpoints)

	// 获取单个端点详情
	g.GET("/endpoints/:id", GetDynamicRouterEndpoint)

	// 更新端点配置
	g.PUT("/endpoints/:id", UpdateDynamicRouterEndpoint)

	// 重新加载路由
	g.POST("/reload", ReloadDynamicRouter)

	// 导出路由配置到文件
	g.POST("/export", ExportDynamicRouterConfig)

	// 清空动态路由配置
	g.POST("/clear", ClearDynamicRouterConfig)
}

// 获取所有路由组
func GetDynamicRouterGroups(c *gin.Context) {
	var groups []model.DynamicRouterGroup
	err := model.DB.Find(&groups).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, groups)
}

// 获取单个路由组详情
func GetDynamicRouterGroup(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var group model.DynamicRouterGroup
	err = model.DB.First(&group, id).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Group not found"})
		return
	}

	c.JSON(http.StatusOK, group)
}

// 更新路由组
func UpdateDynamicRouterGroup(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var group model.DynamicRouterGroup
	err = model.DB.First(&group, id).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Group not found"})
		return
	}

	var updateData struct {
		Title   *string `json:"title"`
		Base    *string `json:"base"`
		Roles   *string `json:"roles"`
		Enabled *bool   `json:"enabled"`
	}

	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 更新字段
	if updateData.Title != nil {
		group.Title = *updateData.Title
	}
	if updateData.Base != nil {
		group.Base = *updateData.Base
	}
	if updateData.Roles != nil {
		group.Roles = *updateData.Roles
	}
	if updateData.Enabled != nil {
		group.Enabled = *updateData.Enabled
	}

	err = model.DB.Save(&group).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, group)
}

// 获取上游列表
func GetDynamicRouterUpstreams(c *gin.Context) {
	groupID := c.Query("group_id")

	var upstreams []model.DynamicRouterUpstream
	query := model.DB

	if groupID != "" {
		query = query.Where("group_id = ?", groupID)
	}

	err := query.Order("priority DESC, weight DESC").Find(&upstreams).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, upstreams)
}

// 获取单个上游详情
func GetDynamicRouterUpstream(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var upstream model.DynamicRouterUpstream
	err = model.DB.First(&upstream, id).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Upstream not found"})
		return
	}

	c.JSON(http.StatusOK, upstream)
}

// 更新上游配置
func UpdateDynamicRouterUpstream(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var upstream model.DynamicRouterUpstream
	err = model.DB.First(&upstream, id).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Upstream not found"})
		return
	}

	var updateData struct {
		Host       *string `json:"host"`
		TrimPrefix *string `json:"trim_prefix"`
		Header     *string `json:"header"`
		Transform  *string `json:"transform"`
		Priority   *int    `json:"priority"`
		Weight     *int    `json:"weight"`
		Enabled    *bool   `json:"enabled"`
	}

	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 更新字段
	if updateData.Host != nil {
		upstream.Host = *updateData.Host
	}
	if updateData.TrimPrefix != nil {
		upstream.TrimPrefix = *updateData.TrimPrefix
	}
	if updateData.Header != nil {
		upstream.Header = *updateData.Header
	}
	if updateData.Transform != nil {
		upstream.Transform = *updateData.Transform
	}
	if updateData.Priority != nil {
		upstream.Priority = *updateData.Priority
	}
	if updateData.Weight != nil {
		upstream.Weight = *updateData.Weight
	}
	if updateData.Enabled != nil {
		upstream.Enabled = *updateData.Enabled
	}

	err = model.DB.Save(&upstream).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, upstream)
}

// 获取端点列表
func GetDynamicRouterEndpoints(c *gin.Context) {
	groupID := c.Query("group_id")

	var endpoints []model.DynamicRouterEndpoint
	query := model.DB

	if groupID != "" {
		query = query.Where("group_id = ?", groupID)
	}

	err := query.Find(&endpoints).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, endpoints)
}

// 获取单个端点详情
func GetDynamicRouterEndpoint(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var endpoint model.DynamicRouterEndpoint
	err = model.DB.First(&endpoint, id).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Endpoint not found"})
		return
	}

	c.JSON(http.StatusOK, endpoint)
}

// 更新端点配置
func UpdateDynamicRouterEndpoint(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var endpoint model.DynamicRouterEndpoint
	err = model.DB.First(&endpoint, id).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Endpoint not found"})
		return
	}

	var updateData struct {
		Title           *string  `json:"title"`
		Path            *string  `json:"path"`
		Method          *string  `json:"method"`
		Sub             *float64 `json:"sub"`
		SubMatch        *string  `json:"sub_match"`
		SubPriceMatches *string  `json:"sub_price_matches"`
		OkMatch         *string  `json:"ok_match"`
		Desc            *string  `json:"desc"`
		Header          *string
	}

	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 更新字段
	if updateData.Title != nil {
		endpoint.Title = *updateData.Title
	}
	if updateData.Path != nil {
		endpoint.Path = *updateData.Path
	}
	if updateData.Method != nil {
		endpoint.Method = *updateData.Method
	}
	if updateData.Sub != nil {
		endpoint.Sub = *updateData.Sub
	}
	if updateData.SubMatch != nil {
		endpoint.SubMatch = *updateData.SubMatch
	}
	if updateData.SubPriceMatches != nil {
		endpoint.SubPriceMatches = *updateData.SubPriceMatches
	}
	if updateData.OkMatch != nil {
		endpoint.OkMatch = *updateData.OkMatch
	}
	if updateData.Desc != nil {
		endpoint.Desc = *updateData.Desc
	}
	if updateData.Header != nil {
		endpoint.Header = *updateData.Header
	}

	err = model.DB.Save(&endpoint).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, endpoint)
}

// 重新加载路由
func ReloadDynamicRouter(c *gin.Context) {
	// 重新加载路由配置
	err := ReloadDynamicRoutes(nil)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "重新加载路由配置失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "路由配置已成功重新加载"})
}

// 导出路由配置到文件
func ExportDynamicRouterConfig(c *gin.Context) {
	// 从数据库加载配置
	dbGroups, err := loadDynamicRouterFromDB()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "加载路由配置失败: " + err.Error()})
		return
	}

	// 过滤零值和空值
	filteredGroups := filterEmptyValues(dbGroups)

	// 将配置转换为JSON
	jsonData, err := jsoniter.MarshalIndent(filteredGroups, "", "  ")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "序列化配置失败: " + err.Error()})
		return
	}

	// 写入文件
	fileAbsPath := filepath.Join(_appDir, "router.json")
	err = os.WriteFile(fileAbsPath, jsonData, 0644)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "写入配置文件失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "配置已成功导出到文件"})
}

// 过滤零值和空值
func filterEmptyValues(groups []*dynamicGroup) []*dynamicGroup {
	result := make([]*dynamicGroup, 0, len(groups))

	for _, g := range groups {
		// 创建新组以避免修改原始数据
		filteredGroup := &dynamicGroup{
			Id:    g.Id,
			Title: g.Title,
			Base:  g.Base,
			Roles: g.Roles,
		}

		// 过滤上游配置
		if len(g.Upstream) > 0 {
			filteredGroup.Upstream = make([]*dynamicGroupUpstream, 0, len(g.Upstream))
			for _, up := range g.Upstream {
				if up.Host == "" {
					continue // 跳过空主机
				}

				filteredUp := &dynamicGroupUpstream{
					Id:   up.Id,
					Host: up.Host,
				}

				// 只添加非空的TrimPrefix
				if up.TrimPrefix != "" {
					filteredUp.TrimPrefix = up.TrimPrefix
				}

				// 只添加非空的Header
				if len(up.Header) > 0 {
					filteredUp.Header = up.Header
				}

				// 只添加非空的Transform
				if up.Transform != nil {
					filteredUp.Transform = up.Transform
				}

				filteredGroup.Upstream = append(filteredGroup.Upstream, filteredUp)
			}
		}

		// 过滤端点配置
		if len(g.List) > 0 {
			filteredGroup.List = make([]*dynamicGroupListItem, 0, len(g.List))
			for _, item := range g.List {
				if item.Path == "" || item.Method == "" {
					continue // 跳过无效端点
				}

				filteredItem := &dynamicGroupListItem{
					Title:  item.Title,
					Method: item.Method,
					Path:   item.Path,
				}

				// 只添加非零的Sub
				if item.Sub > 0 {
					filteredItem.Sub = item.Sub
				}

				// 只添加非空的Desc
				if item.Desc != "" {
					filteredItem.Desc = item.Desc
				}

				// 只添加非空的SubMatch
				if item.SubMatch != nil {
					// 进一步过滤SubMatch内部的空值
					cleanSubMatch := cleanMatchRule(item.SubMatch)
					if cleanSubMatch != nil {
						filteredItem.SubMatch = cleanSubMatch
					}
				}

				// 只添加非空的OkMatch
				if item.OkMatch != nil {
					// 进一步过滤OkMatch内部的空值
					cleanOkMatch := cleanMatchRule(item.OkMatch)
					if cleanOkMatch != nil {
						filteredItem.OkMatch = cleanOkMatch
					}
				}

				// 只添加非空的Header
				if len(item.Header) > 0 {
					filteredItem.Header = item.Header
				}

				// 只添加非空的UseClientHeader
				if len(item.UseClientHeader) > 0 {
					filteredItem.UseClientHeader = item.UseClientHeader
				}

				// 只添加非默认的UseClientHeaderOver
				if item.UseClientHeaderOver {
					filteredItem.UseClientHeaderOver = true
				}

				// 只添加非默认的Sse
				if item.Sse {
					filteredItem.Sse = true
				}

				// 只添加非默认的EnableTransform
				if item.EnableTransform {
					filteredItem.EnableTransform = true
				}

				// 只添加非空的SubPriceMatches
				if len(item.SubPriceMatches) > 0 {
					filteredItem.SubPriceMatches = item.SubPriceMatches
				}

				// 只添加非空的Check
				if item.Check != nil {
					cleanCheck := cleanCheckConfig(item.Check)
					if cleanCheck != nil {
						filteredItem.Check = cleanCheck
					}
				}

				filteredGroup.List = append(filteredGroup.List, filteredItem)
			}
		}

		result = append(result, filteredGroup)
	}

	return result
}

// 清理匹配规则中的空值
func cleanMatchRule(rule *dynamicMatchRule) *dynamicMatchRule {
	if rule == nil {
		return nil
	}

	result := &dynamicMatchRule{}

	// 只保留非空的And规则
	if len(rule.And) > 0 {
		result.And = rule.And
	}

	// 只保留非空的Or规则
	if len(rule.Or) > 0 {
		result.Or = rule.Or
	}

	// 只有当AllMatch为true时才添加该字段
	if rule.AllMatch {
		result.AllMatch = true
	}

	// 如果所有字段都是空的，返回nil
	if len(result.And) == 0 && len(result.Or) == 0 && !result.AllMatch {
		return nil
	}

	return result
}

// 清理Check配置中的空值
func cleanCheckConfig(check *struct {
	Method          string                   `json:"method"`
	Path            string                   `json:"path"`
	Body            map[string]string        `json:"body"`
	Query           map[string]string        `json:"query"`
	PathVar         map[string]string        `json:"path_var"`
	Interval        int                      `json:"interval"`
	ErrField        string                   `json:"err_field"`
	OkMatch         *dynamicMatchRule        `json:"ok_match"`
	ErrorMatch      *dynamicMatchRule        `json:"error_match"`
	Timeout         int                      `json:"timeout"`
	ErrTry          int                      `json:"err_try"`
	SubPriceMatches []*dynamicPriceMatchRule `json:"sub_price_matches"`
}) *struct {
	Method          string                   `json:"method"`
	Path            string                   `json:"path"`
	Body            map[string]string        `json:"body"`
	Query           map[string]string        `json:"query"`
	PathVar         map[string]string        `json:"path_var"`
	Interval        int                      `json:"interval"`
	ErrField        string                   `json:"err_field"`
	OkMatch         *dynamicMatchRule        `json:"ok_match"`
	ErrorMatch      *dynamicMatchRule        `json:"error_match"`
	Timeout         int                      `json:"timeout"`
	ErrTry          int                      `json:"err_try"`
	SubPriceMatches []*dynamicPriceMatchRule `json:"sub_price_matches"`
} {
	if check == nil {
		return nil
	}

	result := &struct {
		Method          string                   `json:"method"`
		Path            string                   `json:"path"`
		Body            map[string]string        `json:"body"`
		Query           map[string]string        `json:"query"`
		PathVar         map[string]string        `json:"path_var"`
		Interval        int                      `json:"interval"`
		ErrField        string                   `json:"err_field"`
		OkMatch         *dynamicMatchRule        `json:"ok_match"`
		ErrorMatch      *dynamicMatchRule        `json:"error_match"`
		Timeout         int                      `json:"timeout"`
		ErrTry          int                      `json:"err_try"`
		SubPriceMatches []*dynamicPriceMatchRule `json:"sub_price_matches"`
	}{
		Method:   check.Method,
		Path:     check.Path,
		Interval: check.Interval,
		Timeout:  check.Timeout,
		ErrTry:   check.ErrTry,
	}

	// 只添加非空的ErrField
	if check.ErrField != "" {
		result.ErrField = check.ErrField
	}

	// 只添加非空的Body
	if len(check.Body) > 0 {
		result.Body = check.Body
	}

	// 只添加非空的Query
	if len(check.Query) > 0 {
		result.Query = check.Query
	}

	// 只添加非空的PathVar
	if len(check.PathVar) > 0 {
		result.PathVar = check.PathVar
	}

	// 清理并添加非空的OkMatch
	if check.OkMatch != nil {
		cleanOkMatch := cleanMatchRule(check.OkMatch)
		if cleanOkMatch != nil {
			result.OkMatch = cleanOkMatch
		}
	}

	// 清理并添加非空的ErrorMatch
	if check.ErrorMatch != nil {
		cleanErrorMatch := cleanMatchRule(check.ErrorMatch)
		if cleanErrorMatch != nil {
			result.ErrorMatch = cleanErrorMatch
		}
	}

	return result
}

// 清空动态路由配置
func ClearDynamicRouterConfig(c *gin.Context) {
	// 清空动态路由组表
	err := model.DB.Exec("DELETE FROM dynamic_router_groups").Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "清空路由组失败: " + err.Error()})
		return
	}

	// 清空动态路由上游表
	err = model.DB.Exec("DELETE FROM dynamic_router_upstreams").Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "清空上游配置失败: " + err.Error()})
		return
	}

	// 清空动态路由端点表
	err = model.DB.Exec("DELETE FROM dynamic_router_endpoints").Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "清空端点配置失败: " + err.Error()})
		return
	}

	// 清空内存中的配置
	_dynamicRouterMutex.Lock()
	_dynamicGroups = nil
	_dynamicRouterMap = cmap.New[*dynamicGroupListItem]()
	_dynamicRouterMutex.Unlock()

	c.JSON(http.StatusOK, gin.H{"message": "动态路由配置已清空，请重启应用以应用更改"})
}

package controller

import (
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/xml"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/model"
	"io"
	"io/ioutil"
	"math/rand"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"
)

//使用阿里云短信服务，在线调试：https://next.api.aliyun.com/api/Dysmsapi/2017-05-25/SendSms

type SMSRequest struct {
	PhoneNumbers string `json:"PhoneNumbers"` //接收短信的手机号码。手机号码格式：国内短信：11位手机号码，例如15900000000。
}

type SMSResponse struct {
	Message   string `xml:"Message"`   // 状态码的描述。
	RequestId string `xml:"RequestId"` // 请求 ID。
	Code      string `xml:"Code"`      // 请求状态码。
	BizId     string `xml:"BizId"`     // 发送回执 ID，可根据该 ID 在接口QuerySendDetails 中查询具体的发送状态。
}

// SendSMS 发送短信验证码通用方法
func SendSMS(c *gin.Context) {

	if !config.SMSVerificationEnabled {
		c.JSON(http.StatusOK, gin.H{
			"message": "管理员未开启短信验证",
			"success": false,
		})
		return
	}

	purpose := c.Query("purpose")
	if purpose == common.SMSVerificationPurpose {
		SendRegisterSMS(c)
	} else if purpose == common.SMSLoginPurpose {
		SendLoginSMS(c)
	} else if purpose == common.SMSBindPurpose {
		SendBindSMS(c)
	} else {
		c.JSON(http.StatusOK, gin.H{
			"message": "未知的短信验证码用途",
			"success": false,
		})
	}
}

func signParameters(parameters map[string]string, accessKeySecret string) string {
	// 将参数排序
	var keys []string
	for k := range parameters {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 构造待签名的字符串
	var sortedQueryString string
	for _, k := range keys {
		sortedQueryString += "&" + percentEncode(k) + "=" + percentEncode(parameters[k])
	}
	stringToSign := "POST" + "&" + percentEncode("/") + "&" + percentEncode(sortedQueryString[1:])

	// 使用 HMAC-SHA1 算法进行签名
	key := []byte(accessKeySecret + "&")
	h := hmac.New(sha1.New, key)
	h.Write([]byte(stringToSign))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))

	return signature
}

func percentEncode(s string) string {
	// URL 编码，替换特定字符
	s = url.QueryEscape(s)
	s = strings.Replace(s, "+", "%20", -1)
	s = strings.Replace(s, "*", "%2A", -1)
	s = strings.Replace(s, "%7E", "~", -1)
	return s
}

// SendBindSMS 发送绑定短信验证码
func SendBindSMS(c *gin.Context) {
	if !config.SMSVerificationEnabled {
		c.JSON(http.StatusOK, gin.H{
			"message": "管理员未开启短信服务功能",
			"success": false,
		})
		return
	}

	// 从查询参数获取手机号
	phoneNumber := c.Query("phone_number")
	if phoneNumber == "" {
		c.JSON(http.StatusOK, gin.H{
			"message": "手机号码不能为空",
			"success": false,
		})
		return
	}

	// 检查手机号码格式
	if !helper.IsPhoneNumber(phoneNumber) {
		c.JSON(http.StatusOK, gin.H{
			"message": "手机号码格式不正确",
			"success": false,
		})
		return
	}

	if model.IsPhoneNumberAlreadyTaken(phoneNumber) {
		c.JSON(http.StatusOK, gin.H{
			"message": "手机号码已存在",
			"success": false,
		})
		return
	}

	// 生成 4 位验证码
	code := helper.GetRandomNumberString(4)
	common.RegisterSMSVerificationCode(phoneNumber, code, common.SMSBindPurpose)
	err := doSendSms(code, phoneNumber)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"message": "发送失败，请稍后再试",
			"success": false,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "短信验证码发送成功",
		"success": true,
	})

}

// SendRegisterSMS 发送注册短信验证码
func SendRegisterSMS(c *gin.Context) {

	if !config.RegisterEnabled {
		c.JSON(http.StatusOK, gin.H{
			"message": "管理员关闭了新用户注册",
			"success": false,
		})
		return
	}

	if !config.SMSVerificationEnabled || !config.SMSRegisterEnabled {
		c.JSON(http.StatusOK, gin.H{
			"message": "管理员未开启短信验证",
			"success": false,
		})
		return
	}

	// 从查询参数获取手机号
	phoneNumber := c.Query("phone_number")
	if phoneNumber == "" {
		c.JSON(http.StatusOK, gin.H{
			"message": "手机号码不能为空",
			"success": false,
		})
		return
	}

	// 检查手机号码格式
	if !helper.IsPhoneNumber(phoneNumber) {
		c.JSON(http.StatusOK, gin.H{
			"message": "手机号码格式不正确",
			"success": false,
		})
		return
	}

	if model.IsPhoneNumberAlreadyTaken(phoneNumber) {
		c.JSON(http.StatusOK, gin.H{
			"message": "手机号码已存在",
			"success": false,
		})
		return
	}

	// 生成 6 位验证码
	code := helper.GetRandomNumberString(6)
	common.RegisterSMSVerificationCode(phoneNumber, code, common.SMSVerificationPurpose)
	err := doSendSms(code, phoneNumber)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "发送失败，请稍后再试",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "短信验证码发送成功",
		"success": true,
	})
}

// SendLoginSMS 发送登录短信验证码
func SendLoginSMS(c *gin.Context) {
	if !config.SMSVerificationEnabled || !config.SMSLoginEnabled {
		c.JSON(http.StatusOK, gin.H{
			"message": "管理员未开启短信验证",
			"success": false,
		})
		return
	}

	// 从查询参数获取手机号
	phoneNumber := c.Query("phone_number")
	if phoneNumber == "" {
		c.JSON(http.StatusOK, gin.H{
			"message": "手机号码不能为空",
			"success": false,
		})
		return
	}

	// 检查手机号码格式
	if !helper.IsPhoneNumber(phoneNumber) {
		c.JSON(http.StatusOK, gin.H{
			"message": "手机号码格式不正确",
			"success": false,
		})
		return
	}

	if !model.IsPhoneNumberAlreadyTaken(phoneNumber) {
		c.JSON(http.StatusOK, gin.H{
			"message": "手机号码不存在，请先注册",
			"success": false,
		})
		return
	}

	// 生成 4 位验证码
	code := helper.GetRandomNumberString(4)
	common.RegisterSMSVerificationCode(phoneNumber, code, common.SMSLoginPurpose)
	err := doSendSms(code, phoneNumber)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"message": "发送失败，请稍后再试",
			"success": false,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "短信验证码发送成功",
		"success": true,
	})

}

func doSendSms(code string, phoneNumber string) error {
	//检查是否是手机号码
	// 构建请求参数
	params := map[string]string{
		"Action":           "SendSms",
		"Version":          "2017-05-25",
		"RegionId":         "cn-hangzhou",
		"TemplateParam":    `{"code":"` + code + `"}`, //"{\"code\":\"123456\"}"
		"Timestamp":        time.Now().UTC().Format("2006-01-02T15:04:05Z"),
		"SignatureMethod":  "HMAC-SHA1",
		"SignatureVersion": "1.0",
		"PhoneNumbers":     phoneNumber,
		"SignName":         config.SMSSignName,
		"TemplateCode":     config.SMSTemplateCode,
		"AccessKeyId":      config.SMSAccessKeyId,
	}

	// 生成并添加 SignatureNonce
	params["SignatureNonce"] = strconv.FormatInt(rand.Int63(), 10)

	// 生成签名
	signature := signParameters(params, config.SMSAccessKeySecret)
	params["Signature"] = signature

	// 构建请求 URL
	apiURL := "https://dysmsapi.aliyuncs.com/"

	// 构建 URL-encoded form data
	values := url.Values{}
	for key, value := range params {
		values.Add(key, value)
	}

	// 发送 POST 请求
	resp, err := http.PostForm(apiURL, values)
	if err != nil {
		return err
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			return
		}
	}(resp.Body)

	// 读取响应体
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	// 解析响应
	var smsResponse SMSResponse
	err = xml.Unmarshal(body, &smsResponse)
	if err != nil {
		return err
	}

	if smsResponse.Code == "OK" {
		return nil
	} else {
		fmt.Println(smsResponse)
		return errors.New("SMS response error,plesae check the log")
	}
}

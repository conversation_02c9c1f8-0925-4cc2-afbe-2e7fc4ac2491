package controller

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/model"
)

func GetAllSensitiveWords(c *gin.Context) {
	p, _ := strconv.Atoi(c.Query("p"))
	if p < 0 {
		p = 0
	}
	pageSize, _ := strconv.Atoi(c.<PERSON>("pageSize"))
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}
	category := c.Query("category")
	severity, _ := strconv.Atoi(c.Query("severity"))
	word := c.Query("word")
	sensitiveWords, err := model.GetAllSensitiveWords(p, pageSize, category, severity, word)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    sensitiveWords,
	})
	return
}

func LoadAllSensitiveWords(c *gin.Context) {
	var sensitiveWord model.SensitiveWord
	err := c.ShouldBindJSON(&sensitiveWord)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "参数错误",
		})
		return
	}
	cnt, err := DoLoadAllSensitiveWords(sensitiveWord.Category, sensitiveWord.Severity)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    cnt,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    cnt,
	})
	return
}

func CountSensitiveWords(c *gin.Context) {
	category := c.Query("category")
	severity, _ := strconv.Atoi(c.Query("severity"))
	word := c.Query("word")
	count, err := model.CountSensitiveWords(category, severity, word)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    0,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"count": count,
		},
	})
	return
}

func AddSensitiveWord(c *gin.Context) {
	var sensitiveWord model.SensitiveWord
	err := c.ShouldBindJSON(&sensitiveWord)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    10,
		})
		return
	}
	err = sensitiveWord.Insert()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    10,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    sensitiveWord.Id,
	})
	return
}

func AddBatchSensitiveWord(c *gin.Context) {
	var sensitiveWords []*model.SensitiveWord
	err := c.ShouldBindJSON(&sensitiveWords)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "参数错误",
			"data":    0,
		})
		return
	}
	sumRow := int64(0)
	// 批量插入,拆分为每一批2000个词,避免超长报错
	for i := 0; i < len(sensitiveWords); i += 2000 {
		end := i + 2000
		if end > len(sensitiveWords) {
			end = len(sensitiveWords)
		}
		rows, err := model.BatchCreateSensitiveWords(sensitiveWords[i:end])
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
				"data":    sumRow,
			})
			return
		}
		sumRow += rows
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    sumRow,
	})
	return
}

func UpdateSensitiveWord(c *gin.Context) {
	var sensitiveWord model.SensitiveWord
	err := c.ShouldBindJSON(&sensitiveWord)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    0,
		})
		return
	}
	rows, err := sensitiveWord.Update()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    rows,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    rows,
	})
	return
}

func DeleteSensitiveWord(c *gin.Context) {
	id, _ := strconv.Atoi(c.Param("id"))
	rows, err := model.DeleteSensitiveWordById(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    rows,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    rows,
		"message": "",
	})
}

func DeleteSensitiveWordByIds(c *gin.Context) {
	// 解析数组
	var ids []int
	err := c.ShouldBindJSON(&ids)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "参数错误",
		})
		return
	}
	rows, err := model.DeleteSensitiveWordByIds(ids)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    rows,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    rows,
		"message": "",
	})
	return
}

func DoLoadAllSensitiveWords(category string, severity int) (int, error) {
	// 判断敏感词开关
	if !config.SensitiveWordsEnabled {
		return 0, fmt.Errorf("敏感词功能已关闭")
	}

	sensitiveWords, err := model.GetAllSensitiveWordsNoPages(category, severity)
	if err != nil {
		return 0, fmt.Errorf("获取敏感词失败: %v", err)
	}

	// 先清空common.SensitiveWordMap
	config.SensitiveWordsMap = make(map[string]string)
	for _, word := range sensitiveWords {
		config.SensitiveWordsMap[word.Word] = "1"
	}

	return len(config.SensitiveWordsMap), nil
}

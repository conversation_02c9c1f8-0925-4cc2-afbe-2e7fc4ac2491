package controller

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/mojocn/base64Captcha"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/model"
	"image/color"
)

type BaseApi struct{}

func GetCaptcha(c *gin.Context) {
	bgColor := &color.RGBA{R: 255, G: 255, B: 255, A: 255}
	// 解析切分 common.CaptchaBgColor
	if config.CaptchaBgColor != "" {
		var r, g, b uint8
		var a float64
		n, err := fmt.Sscanf(config.CaptchaBgColor, "rgba(%d, %d, %d, %f)", &r, &g, &b, &a)
		if err != nil {
			fmt.Printf("Error parsing color: %v\n", err)
			// 判断是不是rgb(255,255,255)这种格式
			n, err = fmt.Sscanf(config.CaptchaBgColor, "rgb(%d, %d, %d)", &r, &g, &b)
			if err != nil {
				fmt.Printf("Error parsing color: %v\n", err)
			}
		}
		if n == 4 {
			aUint8 := uint8(a * 255) // Convert alpha to uint8 (0-255 range)
			bgColor = &color.RGBA{R: r, G: g, B: b, A: aUint8}
		} else if n == 3 {
			bgColor = &color.RGBA{R: r, G: g, B: b, A: 255}
		} else {
			fmt.Printf("Error parsing color: %v\n", err)
		}
	}
	driver := base64Captcha.NewDriverString(80, 240, config.CaptchaNoiseCount, 120, config.CaptchaKeyLong, "234567890abcdefghjkmnpqrstuvwxyzABCDEFGHJKMNPQRSTUVWXYZ", bgColor, nil, nil)

	cp := base64Captcha.NewCaptcha(driver, common.Store)
	if common.RedisEnabled {
		cp = base64Captcha.NewCaptcha(driver, common.CaptchaRedisStore)
	}
	id, b64s, answer, err := cp.Generate()
	fmt.Sprintf("id: %s, b64s: %s, answer: %s", id, b64s, answer)
	if err != nil {
		fmt.Printf("Error generating captcha: %v\n", err)
		c.JSON(200, gin.H{
			"success": false,
			"message": "验证码获取失败",
		})
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "验证码获取成功",
		"data": gin.H{
			"captchaId": id,
			"picPath":   b64s,
		},
	})

}

func GetCheckinCaptcha(c *gin.Context) {
	bgColor := &color.RGBA{R: 255, G: 255, B: 255, A: 255}
	// 获取当前用户
	userId := c.GetInt("id")
	if userId == 0 {
		c.JSON(200, gin.H{
			"success": false,
			"message": "用户未登录",
		})
		return
	}
	// 获取用户签到次数
	checkinCount, err := model.GetCheckinTotalDays(userId)
	if err != nil {
		c.JSON(200, gin.H{
			"success": false,
			"message": "获取签到次数失败",
		})
		return
	}
	var driver base64Captcha.Driver
	// 解析切分 common.CaptchaBgColor
	if config.CaptchaBgColor != "" {
		var r, g, b uint8
		var a float64
		n, err := fmt.Sscanf(config.CaptchaBgColor, "rgba(%d, %d, %d, %f)", &r, &g, &b, &a)
		if err != nil {
			fmt.Printf("Error parsing color: %v\n", err)
			// 判断是不是rgb(255,255,255)这种格式
			n, err = fmt.Sscanf(config.CaptchaBgColor, "rgb(%d, %d, %d)", &r, &g, &b)
			if err != nil {
				fmt.Printf("Error parsing color: %v\n", err)
			}
		}
		if n == 4 {
			aUint8 := uint8(a * 255) // Convert alpha to uint8 (0-255 range)
			bgColor = &color.RGBA{R: r, G: g, B: b, A: aUint8}
		} else if n == 3 {
			bgColor = &color.RGBA{R: r, G: g, B: b, A: 255}
		} else {
			fmt.Printf("Error parsing color: %v\n", err)
		}
	}
	if config.CheckinCaptchaDifficultyIncreaseEnabled {
		// 随机生成颜色
		if config.CheckinCaptchaRandomBackgroundColorEnabled {
			*bgColor = helper.RandomRGBA()
		}
		// 签到验证码难度动态提升,每签到多少天,验证码位数增加多少位,噪点增加多少个
		incrLength := (int(checkinCount) / config.CheckinCaptchaLengthDuration) * config.CheckinCaptchaLengthIncrease
		// 每隔CheckinCaptchaNoiseDuration天,噪点增加CheckinCaptchaNoiseIncrease个
		incrNoise := (int(checkinCount) / config.CheckinCaptchaNoiseDuration) * config.CheckinCaptchaNoiseIncrease
		driver = base64Captcha.NewDriverString(80, 240, config.CaptchaNoiseCount+incrNoise, 120, config.CaptchaKeyLong+incrLength, "234567890abcdefghjkmnpqrstuvwxyzABCDEFGHJKMNPQRSTUVWXYZ", bgColor, nil, nil)
	} else {
		driver = base64Captcha.NewDriverString(80, 240, config.CaptchaNoiseCount, 120, config.CaptchaKeyLong, "234567890abcdefghjkmnpqrstuvwxyzABCDEFGHJKMNPQRSTUVWXYZ", bgColor, nil, nil)
	}
	cp := base64Captcha.NewCaptcha(driver, common.CheckinStore)
	if common.RedisEnabled {
		cp = base64Captcha.NewCaptcha(driver, common.CaptchaRedisCheckinStore)
	}
	id, b64s, answer, err := cp.Generate()
	fmt.Sprintf("id: %s, b64s: %s, answer: %s", id, b64s, answer)
	if err != nil {
		fmt.Printf("Error generating captcha: %v\n", err)
		c.JSON(200, gin.H{
			"success": false,
			"message": "验证码获取失败",
		})
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "验证码获取成功",
		"data": gin.H{
			"captchaId": id,
			"picPath":   b64s,
		},
	})

}

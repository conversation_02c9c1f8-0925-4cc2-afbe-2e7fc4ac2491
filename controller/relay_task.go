package controller

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/constant"
	"github.com/songquanpeng/one-api/dto"
	"github.com/songquanpeng/one-api/relay"
	relayconstant "github.com/songquanpeng/one-api/relay/constant"
)

// RelayTask 任务中继处理器
func RelayTask(c *gin.Context) {
	retryTimes := 3 // 默认重试3次
	channelId := c.GetInt("channel_id")
	relayMode := c.GetInt("relay_mode")

	// 设置平台信息
	setPlatformInfo(c, relayMode)

	var taskErr *dto.TaskError
	for i := retryTimes; i > 0; i-- {
		taskErr = taskRelayHandler(c, relayMode)
		if taskErr == nil {
			return
		}
		if !shouldRetryTaskRelay(c, channelId, taskErr, retryTimes) {
			break
		}
		// TODO: 实现重试逻辑
	}

	if taskErr != nil {
		c.<PERSON>(taskErr.StatusCode, taskErr.Error)
	}
}

// taskRelayHandler 任务中继处理器
func taskRelayHandler(c *gin.Context, relayMode int) *dto.TaskError {
	var err *dto.TaskError
	switch relayMode {
	case relayconstant.RelayModeVertexVideoFetchByID:
		err = relay.RelayTaskFetch(c, relayMode)
	default:
		err = relay.RelayTaskSubmit(c, relayMode)
	}
	return err
}

// shouldRetryTaskRelay 判断是否应该重试任务中继
func shouldRetryTaskRelay(c *gin.Context, channelId int, taskErr *dto.TaskError, retryTimes int) bool {
	if taskErr.StatusCode == http.StatusTooManyRequests {
		return true
	}
	if taskErr.StatusCode == http.StatusBadRequest {
		return false
	}
	if taskErr.StatusCode >= http.StatusInternalServerError {
		return true
	}
	return false
}

// setPlatformInfo 设置平台信息
func setPlatformInfo(c *gin.Context, relayMode int) {
	path := c.Request.URL.Path
	method := c.Request.Method

	// 根据路径和方法确定平台和中继模式
	if strings.Contains(path, "/video/generations") {
		c.Set("platform", string(constant.TaskPlatformVertex))
		if method == http.MethodPost {
			c.Set("relay_mode", relayconstant.RelayModeVertexVideoGenerations)
		} else if method == http.MethodGet {
			c.Set("relay_mode", relayconstant.RelayModeVertexVideoFetchByID)
		}
	}
}

package controller

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/chromedp/cdproto/network"
	"github.com/chromedp/chromedp"
	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/relay/adaptor/openai"
	_ "github.com/songquanpeng/one-api/relay/controller"
	"github.com/songquanpeng/one-api/relay/model"
	"io/ioutil"
	"time"
)

func CfRelay(c *gin.Context) {

	// 设置浏览器的绝对路径
	execPath := "/usr/bin/google-chrome-stable"
	// 设置启动参数
	options := []chromedp.ExecAllocatorOption{
		chromedp.ExecPath(execPath),
		//chromedp.Flag("no-sandbox", true),
		chromedp.Headless, // 这应该确保Chrome在无头模式下运行
		// 可以在这里添加更多的启动参数
	}

	//将请求体的body字符串发送给下面的JavaScript
	bodyBytes, err := ioutil.ReadAll(c.Request.Body)
	// JavaScript代码模板
	jsCode := fmt.Sprintf(`
			// 定义一个函数来发送fetch请求并处理流式数据
			async function fetchStreamData() {
				const response = await fetch('https://lobe.caifree.com/api/openai/chat', {
					method: "POST",
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify(%s)
				});
			
				// 初始化window对象的属性以存储流式数据
				window.streamData = window.streamData || [];
				window.streamDataComplete = false; // 标记数据是否接收完毕
			
				const reader = response.body.getReader();
				while (true) {
					const { done, value } = await reader.read();
					if (done) {
						window.streamDataComplete = true; // 数据已完全接收
						break;
					}
					// 将接收到的数据片段转换为字符串并存储
					window.streamData.push(new TextDecoder("utf-8").decode(value));
				}
			}
			
			// 立即调用函数以开始数据接收过程
			fetchStreamData();
		`, string(bodyBytes)) // 注意这里将JSON数据作为字符串插入

	// 创建ExecAllocator context
	ctx, cancel := chromedp.NewExecAllocator(context.Background(), options...)
	defer cancel()

	// 创建一个新的Chromedp Context，使用上面创建的ExecAllocator context
	ctx, cancel = chromedp.NewContext(ctx)
	defer cancel()

	// 设置超时时间
	ctx, cancel = context.WithTimeout(ctx, 15*time.Second)
	defer cancel()

	var result interface{}
	// 先执行chromedp.Run，发送网络请求
	err = chromedp.Run(ctx,
		network.Enable(),
		chromedp.Navigate(`https://lobe.caifree.com/chat?session=inbox&topic=0otKr04s`), // 你想访问的网站
		// 等待页面加载
		chromedp.Sleep(1*time.Second),
		// 执行JavaScript代码
		chromedp.EvaluateAsDevTools(jsCode, &result),
		// 等待操作完成
		chromedp.Sleep(5*time.Second),
	)
	if err != nil {
		logger.SysError("error run chromedp: " + err.Error())
	}
	uuid := helper.GetUUID()
	common.SetEventStreamHeaders(c)
	var streamDataComplete bool
	// 修改循环条件，继续轮询直到数据完全接收且streamData为空
	for !streamDataComplete || checkStreamDataNotEmpty(ctx) {
		if err := chromedp.Run(ctx, chromedp.Evaluate(`window.streamDataComplete`, &streamDataComplete)); err != nil {
			break
		}

		var rs string
		err := chromedp.Run(ctx, chromedp.Evaluate(`window.streamData.shift()`, &rs))
		if err != nil {
			logger.SysError("error run chromedp: " + err.Error())
		}

		if rs != "" {
			// 将结果转换为字符串并处理
			fmt.Printf("接收到数据片段: %s\n", rs)
			// c返回流式数据
			// 创建并填充数据
			streamResponses := openai.ChatCompletionsStreamResponse{
				Id:      uuid,
				Object:  "chat.completion.chunk",
				Created: 1234567890,
				Model:   "gpt-3.5-turbo",
				Choices: []openai.ChatCompletionsStreamResponseChoice{
					{
						Delta: model.Message{
							Role:    "assistant",
							Content: rs,
						},
						FinishReason: nil,
					},
				},
			}
			// 转换成JSON格式
			// 转换为JSON
			jsonData, err := json.Marshal(streamResponses)
			if err != nil {
				logger.SysError("error marshal streamResponses: " + err.Error())
			}
			c.Render(-1, common.CustomEvent{Data: "data: " + fmt.Sprintf("%s", string(jsonData))})
			c.Writer.Flush()
		}

		// 等待一小段时间再次检查
		time.Sleep(1 * time.Second)
	}
	c.Render(-1, common.CustomEvent{Data: "data: [DONE]"})
	c.Writer.Flush()

	fmt.Println("所有数据接收完毕")
}

// 检查streamData是否不为空
func checkStreamDataNotEmpty(ctx context.Context) bool {
	var length int
	err := chromedp.Run(ctx, chromedp.Evaluate(`window.streamData.length`, &length))
	if err != nil {
		logger.SysError("Failed to check streamData length: " + err.Error())
	}
	return length > 0
}

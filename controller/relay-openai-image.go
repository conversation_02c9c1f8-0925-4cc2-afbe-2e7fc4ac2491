package controller

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"encoding/base64"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/relay/adaptor/openai"
	"github.com/songquanpeng/one-api/relay/controller"
	relayModel "github.com/songquanpeng/one-api/relay/model"
	"github.com/songquanpeng/one-api/relay/relaymode"
)

// ChatMessage represents a chat completion message
type ChatMessage struct {
	Role    string      `json:"role"`
	Content interface{} `json:"content"`
}

// ChatCompletionRequest represents the incoming chat completion request
type ChatCompletionRequest struct {
	Model       string        `json:"model"`
	Messages    []ChatMessage `json:"messages"`
	Temperature float64       `json:"temperature,omitempty"`
	Stream      bool          `json:"stream,omitempty"`
	N           int           `json:"n,omitempty"`
}

// ImageGenerationRequest represents an image generation request
type ImageGenerationRequest struct {
	Model   string `json:"model"`
	Prompt  string `json:"prompt"`
	N       int    `json:"n,omitempty"`
	Size    string `json:"size,omitempty"`
	Quality string `json:"quality,omitempty"`
}

// ImageURL represents an image URL in message content
type ImageURL struct {
	URL    string `json:"url"`
	Detail string `json:"detail,omitempty"`
}

// MessageContent represents structured message content
type MessageContent struct {
	Type     string    `json:"type"`
	Text     string    `json:"text,omitempty"`
	ImageURL *ImageURL `json:"image_url,omitempty"`
}

// ImageResponse represents the upstream image API response
type ImageResponse struct {
	Created int64 `json:"created"`
	Data    []struct {
		B64JSON string `json:"b64_json,omitempty"`
		URL     string `json:"url,omitempty"`
	} `json:"data"`
	Usage *ImageUsage `json:"usage,omitempty"`
}

// ImageUsage represents usage information from image API
type ImageUsage struct {
	TotalTokens        int                 `json:"total_tokens"`
	InputTokens        int                 `json:"input_tokens"`
	OutputTokens       int                 `json:"output_tokens"`
	InputTokensDetails *InputTokensDetails `json:"input_tokens_details,omitempty"`
}

// InputTokensDetails represents detailed input token information
type InputTokensDetails struct {
	TextTokens  int `json:"text_tokens"`
	ImageTokens int `json:"image_tokens"`
}

// ChatCompletionResponse represents the chat completion response format
type ChatCompletionResponse struct {
	ID          string                 `json:"id"`
	Object      string                 `json:"object"`
	Created     int64                  `json:"created"`
	Model       string                 `json:"model"`
	Choices     []ChatCompletionChoice `json:"choices"`
	Usage       *ChatCompletionUsage   `json:"usage,omitempty"`
	ServiceTier string                 `json:"service_tier,omitempty"`
}

// ChatCompletionChoice represents a choice in chat completion response
type ChatCompletionChoice struct {
	Index        int                    `json:"index"`
	Message      *ChatCompletionMessage `json:"message,omitempty"`
	Delta        *ChatCompletionMessage `json:"delta,omitempty"`
	LogProbs     interface{}            `json:"logprobs"`
	FinishReason *string                `json:"finish_reason"`
}

// ChatCompletionMessage represents a chat completion message
type ChatCompletionMessage struct {
	Role        string        `json:"role"`
	Content     string        `json:"content"`
	Refusal     interface{}   `json:"refusal"`
	Annotations []interface{} `json:"annotations"`
}

// ChatCompletionUsage represents usage information in chat completion format
type ChatCompletionUsage struct {
	PromptTokens            int                      `json:"prompt_tokens"`
	CompletionTokens        int                      `json:"completion_tokens"`
	TotalTokens             int                      `json:"total_tokens"`
	PromptTokensDetails     *PromptTokensDetails     `json:"prompt_tokens_details,omitempty"`
	CompletionTokensDetails *CompletionTokensDetails `json:"completion_tokens_details,omitempty"`
}

// PromptTokensDetails represents detailed prompt token information
type PromptTokensDetails struct {
	CachedTokens int `json:"cached_tokens"`
	AudioTokens  int `json:"audio_tokens"`
	TextTokens   int `json:"text_tokens,omitempty"`
	ImageTokens  int `json:"image_tokens,omitempty"`
}

// CompletionTokensDetails represents detailed completion token information
type CompletionTokensDetails struct {
	ReasoningTokens          int `json:"reasoning_tokens"`
	AudioTokens              int `json:"audio_tokens"`
	AcceptedPredictionTokens int `json:"accepted_prediction_tokens"`
	RejectedPredictionTokens int `json:"rejected_prediction_tokens"`
	ContentTokens            int `json:"content_tokens,omitempty"`
}

// ChatCompletionStreamResponse represents a streaming chat completion response
type ChatCompletionStreamResponse struct {
	ID                string                 `json:"id"`
	Object            string                 `json:"object"`
	Created           int64                  `json:"created"`
	Model             string                 `json:"model"`
	SystemFingerprint string                 `json:"system_fingerprint,omitempty"`
	Choices           []ChatCompletionChoice `json:"choices"`
	Usage             *ChatCompletionUsage   `json:"usage,omitempty"`
}

func relayOpenaiImageTextHelper(c *gin.Context, relayMode int) *relayModel.ErrorWithStatusCode {
	ctx := c.Request.Context()
	logger.Infof(ctx, "Processing gpt-image-1 chat completion request")

	// Parse the incoming chat completion request
	var chatRequest ChatCompletionRequest
	err := common.UnmarshalBodyReusable(c, &chatRequest)
	if err != nil {
		logger.Errorf(ctx, "Failed to parse chat completion request: %s", err.Error())
		return openai.ErrorWrapper(err, "invalid_request", http.StatusBadRequest)
	}

	// Store original request for response conversion
	c.Set("original_chat_request", chatRequest)

	// Check if this is a streaming request
	if chatRequest.Stream {
		return handleStreamingGptImageRequest(c, chatRequest, relayMode)
	}

	// Set flag to indicate this is a gpt-image-1 chat completion request
	// This tells RelayImageHelper not to write the response directly
	c.Set("gpt_image_chat_mode", true)

	// Store original request path for logging purposes
	// In gpt_image_chat_mode, we want logs to show /v1/chat/completions, not the converted path
	originalRequestPath := c.Request.URL.Path
	c.Set("original_request_path", originalRequestPath)

	// Extract prompt and detect images
	prompt, hasImages, imageURLs, err := extractPromptAndImages(chatRequest.Messages)
	if err != nil {
		logger.Errorf(ctx, "Failed to extract prompt and images: %s", err.Error())
		return openai.ErrorWrapper(err, "invalid_request", http.StatusBadRequest)
	}

	logger.Infof(ctx, "Extracted prompt: %s, hasImages: %t, imageCount: %d", prompt, hasImages, len(imageURLs))

	// Determine the target relay mode based on image presence
	var targetRelayMode int
	if hasImages {
		targetRelayMode = relaymode.ImagesEdits
		logger.Infof(ctx, "Routing to image editing mode")
	} else {
		targetRelayMode = relaymode.ImagesGenerations
		logger.Infof(ctx, "Routing to image generation mode")
	}

	// Convert chat format to image format
	if hasImages {
		// For image editing, we need to handle form data
		err = convertToImageEditRequest(c, prompt, imageURLs, chatRequest)
		if err != nil {
			logger.Errorf(ctx, "Failed to convert to image edit request: %s", err.Error())
			return openai.ErrorWrapper(err, "conversion_failed", http.StatusInternalServerError)
		}
	} else {
		// For image generation, convert to JSON format
		err = convertToImageGenerationRequest(c, prompt, chatRequest)
		if err != nil {
			logger.Errorf(ctx, "Failed to convert to image generation request: %s", err.Error())
			return openai.ErrorWrapper(err, "conversion_failed", http.StatusInternalServerError)
		}
	}

	// Update the request path to match the target mode
	// Note: The original path is preserved in context for logging
	if hasImages {
		c.Request.URL.Path = "/v1/images/edits"
	} else {
		c.Request.URL.Path = "/v1/images/generations"
	}

	// Call the image helper with the converted request
	bizErr, _, _ := controller.RelayImageHelper(c, targetRelayMode)

	if bizErr != nil {
		logger.Errorf(ctx, "Image helper failed: %s", bizErr.Message)
		return bizErr
	}

	// At this point, RelayImageHelper has processed the request but not written the response
	// We need to get the response data and convert it to chat completion format

	// Get the captured response from context (we'll need to modify RelayImageHelper to store it)
	imageResponseData, exists := c.Get("image_response_data")
	if !exists {
		logger.Errorf(ctx, "No image response data found in context")
		return openai.ErrorWrapper(fmt.Errorf("no response data"), "no_response_data", http.StatusInternalServerError)
	}

	responseBytes, ok := imageResponseData.([]byte)
	if !ok {
		logger.Errorf(ctx, "Invalid response data type")
		return openai.ErrorWrapper(fmt.Errorf("invalid response data type"), "invalid_response_data", http.StatusInternalServerError)
	}

	// Convert the image response to chat completion format
	err = convertImageResponseToChatCompletion(c, responseBytes, chatRequest)
	if err != nil {
		logger.Errorf(ctx, "Failed to convert response: %s", err.Error())
		return openai.ErrorWrapper(err, "response_conversion_failed", http.StatusInternalServerError)
	}

	return nil
}

// handleStreamingGptImageRequest handles streaming requests for gpt-image-1
func handleStreamingGptImageRequest(c *gin.Context, chatRequest ChatCompletionRequest, relayMode int) *relayModel.ErrorWithStatusCode {
	ctx := c.Request.Context()
	logger.Infof(ctx, "Processing streaming gpt-image-1 request")

	// Store original request path for logging purposes
	// In gpt_image_chat_mode, we want logs to show /v1/chat/completions, not the converted path
	originalRequestPath := c.Request.URL.Path
	c.Set("original_request_path", originalRequestPath)

	// Set streaming headers
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Transfer-Encoding", "chunked")
	c.Header("X-Accel-Buffering", "no")

	// Generate chat completion ID
	chatID := "chatcmpl-" + generateRandomID()
	created := time.Now().Unix()

	// Send initial progress messages (JSON prompt and queuing)
	err := sendInitialProgressMessages(c, chatID, created, chatRequest)
	if err != nil {
		logger.Errorf(ctx, "Failed to send initial progress messages: %s", err.Error())
		return openai.ErrorWrapper(err, "streaming_failed", http.StatusInternalServerError)
	}

	// Extract prompt and detect images for backend processing
	prompt, hasImages, imageURLs, err := extractPromptAndImages(chatRequest.Messages)
	if err != nil {
		logger.Errorf(ctx, "Failed to extract prompt and images: %s", err.Error())
		return openai.ErrorWrapper(err, "invalid_request", http.StatusBadRequest)
	}

	// Create channels for communication between goroutines
	resultChan := make(chan *ImageResponse, 1)
	errorChan := make(chan error, 1)
	doneChan := make(chan bool, 1)

	// Start backend processing in goroutine
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf(ctx, "Backend processing panic: %v", r)
				errorChan <- fmt.Errorf("backend processing failed: %v", r)
			}
		}()

		// Create a new context for backend processing (non-streaming)
		backendRequest := chatRequest
		backendRequest.Stream = false // Force non-streaming for backend

		// Create a new gin context for backend processing
		backendCtx := &gin.Context{}
		*backendCtx = *c // Copy the context
		backendCtx.Set("gpt_image_chat_mode", true)
		backendCtx.Set("original_chat_request", backendRequest)
		backendCtx.Set("original_request_path", originalRequestPath)

		// Determine the target relay mode based on image presence
		var targetRelayMode int
		if hasImages {
			targetRelayMode = relaymode.ImagesEdits
			logger.Infof(ctx, "Backend routing to image editing mode")
		} else {
			targetRelayMode = relaymode.ImagesGenerations
			logger.Infof(ctx, "Backend routing to image generation mode")
		}

		// Convert chat format to image format for backend
		if hasImages {
			err = convertToImageEditRequest(backendCtx, prompt, imageURLs, backendRequest)
			if err != nil {
				logger.Errorf(ctx, "Failed to convert to image edit request: %s", err.Error())
				errorChan <- err
				return
			}
		} else {
			err = convertToImageGenerationRequest(backendCtx, prompt, backendRequest)
			if err != nil {
				logger.Errorf(ctx, "Failed to convert to image generation request: %s", err.Error())
				errorChan <- err
				return
			}
		}

		// Update the request path for backend
		if hasImages {
			backendCtx.Request.URL.Path = "/v1/images/edits"
		} else {
			backendCtx.Request.URL.Path = "/v1/images/generations"
		}

		// Call the backend image helper (non-streaming)
		bizErr, _, _ := controller.RelayImageHelper(backendCtx, targetRelayMode)
		if bizErr != nil {
			logger.Errorf(ctx, "Backend image helper failed: %s", bizErr.Message)
			errorChan <- fmt.Errorf("backend failed: %s", bizErr.Message)
			return
		}

		// Get the backend response
		imageResponseData, exists := backendCtx.Get("image_response_data")
		if !exists {
			logger.Errorf(ctx, "No image response data found in backend context")
			errorChan <- fmt.Errorf("no response data")
			return
		}

		responseBytes, ok := imageResponseData.([]byte)
		if !ok {
			logger.Errorf(ctx, "Invalid backend response data type")
			errorChan <- fmt.Errorf("invalid response data type")
			return
		}

		// Parse the backend response
		var imageResponse ImageResponse
		err = json.Unmarshal(responseBytes, &imageResponse)
		if err != nil {
			logger.Errorf(ctx, "Failed to parse backend image response: %s", err.Error())
			errorChan <- err
			return
		}

		// Send the result
		resultChan <- &imageResponse
	}()

	// Start dynamic progress dots in another goroutine
	go func() {
		sendDynamicGeneratingProgress(c, chatID, created, doneChan)
	}()

	// Wait for backend processing to complete
	select {
	case imageResponse := <-resultChan:
		// Signal to stop the progress dots
		doneChan <- true

		// Send completion message and image results
		err = sendStreamingImageResults(c, chatID, created, *imageResponse, chatRequest)
		if err != nil {
			logger.Errorf(ctx, "Failed to send image results: %s", err.Error())
			return openai.ErrorWrapper(err, "streaming_failed", http.StatusInternalServerError)
		}

	case err := <-errorChan:
		// Signal to stop the progress dots
		doneChan <- true

		// Send error message to stream
		errorChunk := ChatCompletionStreamResponse{
			ID:      chatID,
			Object:  "chat.completion.chunk",
			Created: created,
			Model:   "gpt-image-1",
			Choices: []ChatCompletionChoice{
				{
					Index: 0,
					Delta: &ChatCompletionMessage{
						Role:    "assistant",
						Content: fmt.Sprintf("❌ Generation failed: %s", err.Error()),
					},
					FinishReason: lo.ToPtr("stop"),
				},
			},
		}
		chunkData, _ := json.Marshal(errorChunk)
		c.Writer.Write([]byte(fmt.Sprintf("data: %s\n\n", chunkData)))
		c.Writer.Write([]byte("data: [DONE]\n\n"))
		c.Writer.Flush()
		return nil

	case <-time.After(1200 * time.Second): // 20 minute timeout
		// Signal to stop the progress dots
		doneChan <- true

		// Send timeout error
		timeoutChunk := ChatCompletionStreamResponse{
			ID:      chatID,
			Object:  "chat.completion.chunk",
			Created: created,
			Model:   "gpt-image-1",
			Choices: []ChatCompletionChoice{
				{
					Index: 0,
					Delta: &ChatCompletionMessage{
						Role:    "assistant",
						Content: "❌ Generation timeout: Request took too long to complete",
					},
					FinishReason: lo.ToPtr("stop"),
				},
			},
		}
		chunkData, _ := json.Marshal(timeoutChunk)
		c.Writer.Write([]byte(fmt.Sprintf("data: %s\n\n", chunkData)))
		c.Writer.Write([]byte("data: [DONE]\n\n"))
		c.Writer.Flush()
		return openai.ErrorWrapper(fmt.Errorf("timeout"), "timeout", http.StatusRequestTimeout)
	}

	return nil
}

// sendInitialProgressMessages sends the initial progress messages (JSON prompt and queuing)
func sendInitialProgressMessages(c *gin.Context, chatID string, created int64, originalRequest ChatCompletionRequest) error {
	// Send initial chunk with role
	initialChunk := ChatCompletionStreamResponse{
		ID:      chatID,
		Object:  "chat.completion.chunk",
		Created: created,
		Model:   "gpt-image-1",
		Choices: []ChatCompletionChoice{
			{
				Index: 0,
				Delta: &ChatCompletionMessage{
					Role: "assistant",
				},
			},
		},
	}
	chunkData, _ := json.Marshal(initialChunk)
	c.Writer.Write([]byte(fmt.Sprintf("data: %s\n\n", chunkData)))
	c.Writer.Flush()

	// Send JSON prompt
	prompt := extractPromptFromRequest(originalRequest)
	jsonPrompt := map[string]interface{}{
		"prompt": prompt,
		"ratio":  "1:1",
		"n":      1,
	}
	jsonBytes, _ := json.Marshal(jsonPrompt)
	jsonContent := "```json\n" + string(jsonBytes) + "\n```"

	jsonChunk := ChatCompletionStreamResponse{
		ID:      chatID,
		Object:  "chat.completion.chunk",
		Created: created,
		Model:   "gpt-image-1",
		Choices: []ChatCompletionChoice{
			{
				Index: 0,
				Delta: &ChatCompletionMessage{
					Content: jsonContent,
				},
			},
		},
	}
	chunkData, _ = json.Marshal(jsonChunk)
	c.Writer.Write([]byte(fmt.Sprintf("data: %s\n\n", chunkData)))
	c.Writer.Flush()

	// Send queuing message
	queueChunk := ChatCompletionStreamResponse{
		ID:      chatID,
		Object:  "chat.completion.chunk",
		Created: created,
		Model:   "gpt-image-1",
		Choices: []ChatCompletionChoice{
			{
				Index: 0,
				Delta: &ChatCompletionMessage{
					Content: "\n\n>🕐 Queuing................................................................",
				},
			},
		},
	}
	chunkData, _ = json.Marshal(queueChunk)
	c.Writer.Write([]byte(fmt.Sprintf("data: %s\n\n", chunkData)))
	c.Writer.Flush()

	return nil
}

// sendDynamicGeneratingProgress sends dynamic generating progress with animated dots
func sendDynamicGeneratingProgress(c *gin.Context, chatID string, created int64, doneChan <-chan bool) {
	// Send initial generating message
	generateChunk := ChatCompletionStreamResponse{
		ID:      chatID,
		Object:  "chat.completion.chunk",
		Created: created,
		Model:   "gpt-image-1",
		Choices: []ChatCompletionChoice{
			{
				Index: 0,
				Delta: &ChatCompletionMessage{
					Content: "\n\n>⚡ Generating",
				},
			},
		},
	}
	chunkData, _ := json.Marshal(generateChunk)
	c.Writer.Write([]byte(fmt.Sprintf("data: %s\n\n", chunkData)))
	c.Writer.Flush()

	// Animated dots
	ticker := time.NewTicker(2 * time.Second) // Every 2 seconds
	defer ticker.Stop()

	dotCount := 0
	maxDots := 8 // Maximum number of dots before cycling

	for {
		select {
		case <-doneChan:
			// Backend processing is done, stop sending dots
			return
		case <-ticker.C:
			// Add a dot
			dotCount++
			if dotCount > maxDots {
				// Reset dots and start over
				dotCount = 1
				// Send a "reset" message to clear the line
				resetChunk := ChatCompletionStreamResponse{
					ID:      chatID,
					Object:  "chat.completion.chunk",
					Created: created,
					Model:   "gpt-image-1",
					Choices: []ChatCompletionChoice{
						{
							Index: 0,
							Delta: &ChatCompletionMessage{
								Content: "\r>⚡ Generating",
							},
						},
					},
				}
				chunkData, _ := json.Marshal(resetChunk)
				c.Writer.Write([]byte(fmt.Sprintf("data: %s\n\n", chunkData)))
				c.Writer.Flush()
			}

			// Send the dot
			dotChunk := ChatCompletionStreamResponse{
				ID:      chatID,
				Object:  "chat.completion.chunk",
				Created: created,
				Model:   "gpt-image-1",
				Choices: []ChatCompletionChoice{
					{
						Index: 0,
						Delta: &ChatCompletionMessage{
							Content: ".",
						},
					},
				},
			}
			chunkData, _ := json.Marshal(dotChunk)
			c.Writer.Write([]byte(fmt.Sprintf("data: %s\n\n", chunkData)))
			c.Writer.Flush()
		}
	}
}

// sendStreamingProgressMessages sends the initial progress messages (deprecated, replaced by sendInitialProgressMessages)
func sendStreamingProgressMessages(c *gin.Context, chatID string, created int64, originalRequest ChatCompletionRequest) error {
	return sendInitialProgressMessages(c, chatID, created, originalRequest)
}

// sendStreamingImageResults sends the final image results
func sendStreamingImageResults(c *gin.Context, chatID string, created int64, imageResponse ImageResponse, originalRequest ChatCompletionRequest) error {
	// 创建一个 buffer 来记录所有流式输出
	var streamOutputBuffer strings.Builder

	// 包装写入函数来同时记录到 buffer
	writeToStream := func(data string) {
		streamOutputBuffer.WriteString(data)
		c.Writer.Write([]byte(data))
	}

	// 在函数结束时记录完整的流式输出
	defer func() {
		fullOutput := streamOutputBuffer.String()
		if len(fullOutput) > 0 {
			logger.Infof(c.Request.Context(), "GPT-Image Streaming Response: %s", fullOutput)
		}
	}()

	// Get user ID for image processing
	userId := c.GetInt("id")
	if userId == 0 {
		userId = 1 // Default user ID if not found
	}

	// Send completion message
	completeChunk := ChatCompletionStreamResponse{
		ID:      chatID,
		Object:  "chat.completion.chunk",
		Created: created,
		Model:   "gpt-image-1",
		Choices: []ChatCompletionChoice{
			{
				Index: 0,
				Delta: &ChatCompletionMessage{
					Content: "\n\n> ✅ Generation complete\n\n",
				},
			},
		},
	}
	chunkData, _ := json.Marshal(completeChunk)
	writeToStream(fmt.Sprintf("data: %s\n\n", chunkData))
	c.Writer.Flush()

	// Process and send each image
	for i, imageData := range imageResponse.Data {
		var localImageURL string
		var err error

		if imageData.URL != "" {
			// Download image from URL
			localImageURL, err = downloadImageToLocal(imageData.URL, userId)
			if err != nil {
				logger.Errorf(c.Request.Context(), "Failed to download image from URL: %s", err.Error())
				localImageURL = imageData.URL // Fallback to original URL
			}
		} else if imageData.B64JSON != "" {
			// Save base64 image to local storage
			localImageURL, err = saveGptImageBase64ToLocal(imageData.B64JSON, userId)
			if err != nil {
				logger.Errorf(c.Request.Context(), "Failed to save base64 image: %s", err.Error())
				localImageURL = "data:image/png;base64," + imageData.B64JSON // Fallback
			}
		}

		// Generate image ID
		imageID := fmt.Sprintf("gen_%s", generateRandomID())

		// Send image markdown
		imageContent := fmt.Sprintf("\n![%s](%s)", imageID, localImageURL)
		imageChunk := ChatCompletionStreamResponse{
			ID:      chatID,
			Object:  "chat.completion.chunk",
			Created: created,
			Model:   "gpt-image-1",
			Choices: []ChatCompletionChoice{
				{
					Index: 0,
					Delta: &ChatCompletionMessage{
						Content: imageContent,
					},
				},
			},
		}
		chunkData, _ := json.Marshal(imageChunk)
		writeToStream(fmt.Sprintf("data: %s\n\n", chunkData))
		c.Writer.Flush()

		// Send download link
		downloadContent := fmt.Sprintf("\n\n[Click to download](%s)", localImageURL)
		downloadChunk := ChatCompletionStreamResponse{
			ID:      chatID,
			Object:  "chat.completion.chunk",
			Created: created,
			Model:   "gpt-image-1",
			Choices: []ChatCompletionChoice{
				{
					Index: 0,
					Delta: &ChatCompletionMessage{
						Content: downloadContent,
					},
				},
			},
		}
		chunkData, _ = json.Marshal(downloadChunk)
		writeToStream(fmt.Sprintf("data: %s\n\n", chunkData))
		c.Writer.Flush()

		// Add separator if not the last image
		if i < len(imageResponse.Data)-1 {
			separatorChunk := ChatCompletionStreamResponse{
				ID:      chatID,
				Object:  "chat.completion.chunk",
				Created: created,
				Model:   "gpt-image-1",
				Choices: []ChatCompletionChoice{
					{
						Index: 0,
						Delta: &ChatCompletionMessage{
							Content: "\n",
						},
					},
				},
			}
			chunkData, _ := json.Marshal(separatorChunk)
			writeToStream(fmt.Sprintf("data: %s\n\n", chunkData))
			c.Writer.Flush()
		}
	}

	// Send final chunk with stop first (according to OpenAI standard)
	finalChunk := ChatCompletionStreamResponse{
		ID:      chatID,
		Object:  "chat.completion.chunk",
		Created: created,
		Model:   "gpt-image-1",
		Choices: []ChatCompletionChoice{
			{
				Index:        0,
				Delta:        &ChatCompletionMessage{},
				FinishReason: lo.ToPtr("stop"),
			},
		},
	}
	chunkData, _ = json.Marshal(finalChunk)
	writeToStream(fmt.Sprintf("data: %s\n\n", chunkData))
	c.Writer.Flush()

	// Send usage information after stop (according to OpenAI standard)
	if imageResponse.Usage != nil {
		// Calculate the downstream prompt tokens using the formula: textTokens*1 + imageTokens*2
		downstreamPromptTokens := imageResponse.Usage.InputTokens // fallback to original if no details
		if imageResponse.Usage.InputTokensDetails != nil {
			downstreamPromptTokens = imageResponse.Usage.InputTokensDetails.TextTokens*1 + imageResponse.Usage.InputTokensDetails.ImageTokens*2
		}

		// Calculate new total tokens for downstream
		downstreamTotalTokens := downstreamPromptTokens + imageResponse.Usage.OutputTokens

		usageChunk := ChatCompletionStreamResponse{
			ID:      chatID,
			Object:  "chat.completion.chunk",
			Created: created,
			Model:   "gpt-image-1",
			Choices: []ChatCompletionChoice{
				{
					Index: 0,
					Delta: &ChatCompletionMessage{},
				},
			},
			Usage: &ChatCompletionUsage{
				PromptTokens:        downstreamPromptTokens,
				CompletionTokens:    imageResponse.Usage.OutputTokens,
				TotalTokens:         downstreamTotalTokens,
				PromptTokensDetails: &PromptTokensDetails{}, // Clear details to avoid confusion since we modified prompt_tokens calculation
				CompletionTokensDetails: &CompletionTokensDetails{
					ContentTokens: imageResponse.Usage.OutputTokens,
				},
			},
		}
		chunkData, _ = json.Marshal(usageChunk)
		writeToStream(fmt.Sprintf("data: %s\n\n", chunkData))
		c.Writer.Flush()
	}

	// Send [DONE] marker
	writeToStream("data: [DONE]\n\n")
	c.Writer.Flush()

	return nil
}

// extractImageURLsFromText extracts image URLs from text using regex patterns
func extractImageURLsFromText(text string) ([]string, string) {
	var imageURLs []string
	var cleanedText = text

	// Common image URL patterns
	imageURLPatterns := []string{
		// HTTP/HTTPS URLs ending with image extensions
		`https?://[^\s]+\.(?:jpg|jpeg|png|gif|bmp|webp|svg)(?:\?[^\s]*)?`,
		// Data URLs for images
		`data:image/[^;]+;base64,[A-Za-z0-9+/=]+`,
		// General HTTP/HTTPS URLs that might be images (more permissive)
		`https?://[^\s]+`,
	}

	for _, pattern := range imageURLPatterns {
		re := regexp.MustCompile(`(?i)` + pattern)
		matches := re.FindAllString(text, -1)

		for _, match := range matches {
			// Check if this looks like an image URL
			if isLikelyImageURL(match) {
				imageURLs = append(imageURLs, match)
				// Remove the URL from the text
				cleanedText = strings.ReplaceAll(cleanedText, match, "")
			}
		}
	}

	// Clean up extra spaces
	cleanedText = regexp.MustCompile(`\s+`).ReplaceAllString(strings.TrimSpace(cleanedText), " ")

	return imageURLs, cleanedText
}

// isLikelyImageURL checks if a URL is likely to be an image
func isLikelyImageURL(url string) bool {
	url = strings.ToLower(url)

	// Data URLs for images
	if strings.HasPrefix(url, "data:image/") {
		return true
	}

	// URLs with image file extensions
	imageExtensions := []string{".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"}
	for _, ext := range imageExtensions {
		if strings.Contains(url, ext) {
			return true
		}
	}

	// URLs from common image hosting services
	imageHosts := []string{
		"imgur.com",
		"i.imgur.com",
		"cdn.discordapp.com",
		"media.discordapp.net",
		"images.unsplash.com",
		"pixabay.com",
		"pexels.com",
		"flickr.com",
		"staticflickr.com",
		"googleusercontent.com",
		"amazonaws.com",
		"cloudfront.net",
		"github.com/user-attachments",
		"raw.githubusercontent.com",
	}

	for _, host := range imageHosts {
		if strings.Contains(url, host) {
			return true
		}
	}

	return false
}

// extractPromptAndImages extracts the prompt text and image URLs from chat messages
func extractPromptAndImages(messages []ChatMessage) (string, bool, []string, error) {
	var promptParts []string
	var imageURLs []string
	hasImages := false

	for _, message := range messages {
		switch content := message.Content.(type) {
		case string:
			// Simple text content - check for embedded image URLs
			if content != "" {
				// Extract image URLs from the text
				extractedURLs, cleanedText := extractImageURLsFromText(content)
				if len(extractedURLs) > 0 {
					hasImages = true
					imageURLs = append(imageURLs, extractedURLs...)
				}

				// Add the cleaned text (without URLs) to prompt parts
				if cleanedText != "" {
					promptParts = append(promptParts, cleanedText)
				}
			}
		case []interface{}:
			// Structured content array
			for _, item := range content {
				if contentMap, ok := item.(map[string]interface{}); ok {
					if contentType, exists := contentMap["type"]; exists && contentType == "text" {
						if text, ok := contentMap["text"].(string); ok && text != "" {
							// Extract image URLs from structured text content
							extractedURLs, cleanedText := extractImageURLsFromText(text)
							if len(extractedURLs) > 0 {
								hasImages = true
								imageURLs = append(imageURLs, extractedURLs...)
							}

							if cleanedText != "" {
								promptParts = append(promptParts, cleanedText)
							}
						}
					} else if contentType, exists := contentMap["type"]; exists && (contentType == "image_url" || contentType == "image") {
						hasImages = true
						if imageURL, ok := contentMap["image_url"].(map[string]interface{}); ok {
							if url, ok := imageURL["url"].(string); ok {
								imageURLs = append(imageURLs, url)
							}
						}
					}
				}
			}
		case map[string]interface{}:
			// Single structured content
			if contentType, exists := content["type"]; exists && contentType == "text" {
				if text, ok := content["text"].(string); ok && text != "" {
					// Extract image URLs from structured text content
					extractedURLs, cleanedText := extractImageURLsFromText(text)
					if len(extractedURLs) > 0 {
						hasImages = true
						imageURLs = append(imageURLs, extractedURLs...)
					}

					if cleanedText != "" {
						promptParts = append(promptParts, cleanedText)
					}
				}
			} else if contentType, exists := content["type"]; exists && (contentType == "image_url" || contentType == "image") {
				hasImages = true
				if imageURL, ok := content["image_url"].(map[string]interface{}); ok {
					if url, ok := imageURL["url"].(string); ok {
						imageURLs = append(imageURLs, url)
					}
				}
			}
		}
	}

	prompt := strings.Join(promptParts, " ")
	if prompt == "" && !hasImages {
		return "", hasImages, imageURLs, fmt.Errorf("no prompt text found in messages")
	}

	// If we only have images but no text prompt, create a default prompt
	if prompt == "" && hasImages {
		prompt = "Edit this image"
	}

	return prompt, hasImages, imageURLs, nil
}

// convertToImageGenerationRequest converts chat format to image generation format
func convertToImageGenerationRequest(c *gin.Context, prompt string, chatRequest ChatCompletionRequest) error {
	imageRequest := ImageGenerationRequest{
		Model:   "gpt-image-1",
		Prompt:  prompt,
		N:       1,
		Size:    "1024x1024",
		Quality: "high",
	}

	if chatRequest.N > 0 {
		imageRequest.N = chatRequest.N
	}

	// Convert to JSON and update request body
	jsonData, err := json.Marshal(imageRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal image generation request: %w", err)
	}

	// Store the converted body in context to prevent it from being reset
	c.Set(ctxkey.KeyRequestBody, jsonData)

	c.Request.Body = io.NopCloser(bytes.NewBuffer(jsonData))
	c.Request.ContentLength = int64(len(jsonData))
	c.Request.Header.Set("Content-Type", "application/json")

	return nil
}

// convertToImageEditRequest converts chat format to image edit format (form data)
func convertToImageEditRequest(c *gin.Context, prompt string, imageURLs []string, chatRequest ChatCompletionRequest) error {
	ctx := c.Request.Context()

	if len(imageURLs) == 0 {
		return fmt.Errorf("no images provided for image editing")
	}

	// Get user ID for local storage
	userId := c.GetInt("id")
	if userId == 0 {
		userId = 1 // Default user ID if not found
	}

	// Download the first image (for now, we'll use the first image for editing)
	imageURL := imageURLs[0]
	logger.Infof(ctx, "Downloading image for editing: %s", imageURL)

	// Download the image
	resp, err := http.Get(imageURL)
	if err != nil {
		return fmt.Errorf("failed to download image: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to download image: status %d", resp.StatusCode)
	}

	// Read image data
	imageData, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read image data: %w", err)
	}

	// Create multipart form data
	var b bytes.Buffer
	writer := multipart.NewWriter(&b)

	// Add text fields
	writer.WriteField("prompt", prompt)
	writer.WriteField("model", "gpt-image-1")
	writer.WriteField("n", "1")
	writer.WriteField("size", "1024x1024")
	writer.WriteField("quality", "high")

	if chatRequest.N > 0 {
		writer.WriteField("n", fmt.Sprintf("%d", chatRequest.N))
	}

	// Add image file
	part, err := writer.CreateFormFile("image", "image.png")
	if err != nil {
		return fmt.Errorf("failed to create form file: %w", err)
	}

	_, err = part.Write(imageData)
	if err != nil {
		return fmt.Errorf("failed to write image data: %w", err)
	}

	// Close the writer
	err = writer.Close()
	if err != nil {
		return fmt.Errorf("failed to close multipart writer: %w", err)
	}

	// Replace the request body with the multipart form data
	c.Request.Body = io.NopCloser(&b)
	c.Request.ContentLength = int64(b.Len())
	c.Request.Header.Set("Content-Type", writer.FormDataContentType())

	// Store the converted body in context for later use
	c.Set(ctxkey.KeyRequestBody, b.Bytes())

	logger.Infof(ctx, "Successfully created multipart form data for image editing")
	return nil
}

// downloadImageToLocal downloads an image from URL and saves it to local storage
func downloadImageToLocal(imageURL string, userId int) (string, error) {
	// Create directory if it doesn't exist
	userDir := filepath.Join("storage", "gpt-image", strconv.Itoa(userId))
	err := os.MkdirAll(userDir, 0755)
	if err != nil {
		return "", fmt.Errorf("failed to create directory: %w", err)
	}

	// Download the image
	resp, err := http.Get(imageURL)
	if err != nil {
		return "", fmt.Errorf("failed to download image: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("failed to download image: status %d", resp.StatusCode)
	}

	// Generate a unique filename
	now := time.Now()
	filename := fmt.Sprintf("gpt_image_%d_%d.png", now.Unix(), rand.Intn(10000))
	filePath := filepath.Join(userDir, filename)

	// Create the file
	file, err := os.Create(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to create file: %w", err)
	}
	defer file.Close()

	// Copy the image data to the file
	_, err = io.Copy(file, resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to save image: %w", err)
	}

	// Generate the local URL
	localURL := fmt.Sprintf("%s/fileSystem/gpt-image/%d/%s",
		lo.If(config.GptImageStorageAddress == "", config.ServerAddress).Else(config.GptImageStorageAddress),
		userId, filename)

	return localURL, nil
}

// convertImageResponseToChatCompletion converts image API response to chat completion format
func convertImageResponseToChatCompletion(c *gin.Context, responseBody []byte, originalRequest ChatCompletionRequest) error {
	ctx := c.Request.Context()

	// Parse the image response
	var imageResponse ImageResponse
	err := json.Unmarshal(responseBody, &imageResponse)
	if err != nil {
		logger.Errorf(ctx, "Failed to parse image response: %s", err.Error())
		return fmt.Errorf("failed to parse image response: %w", err)
	}

	// Get user ID from context
	userId := c.GetInt("id")
	if userId == 0 {
		userId = 1 // Default user ID if not found
	}

	// Create the chat completion response
	chatResponse := ChatCompletionResponse{
		ID:      "chatcmpl-" + generateRandomID(),
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   "gpt-image-1",
		Choices: []ChatCompletionChoice{},
	}

	// Process each image and create content
	var contentParts []string

	// Add the original prompt as JSON format
	prompt := extractPromptFromRequest(originalRequest)
	jsonPrompt := map[string]interface{}{
		"prompt": prompt,
		"ratio":  "1:1",
		"n":      1,
	}
	jsonBytes, _ := json.Marshal(jsonPrompt)
	contentParts = append(contentParts, "```json\n"+string(jsonBytes)+"\n```")

	// Add generation progress text
	contentParts = append(contentParts, "\n>🕐 Queuing................................................................")
	contentParts = append(contentParts, "\n>⚡ Generating....")
	contentParts = append(contentParts, "\n> ✅ Generation complete\n\n")

	// Process images
	for i, imageData := range imageResponse.Data {
		var localImageURL string

		if imageData.URL != "" {
			// Download image from URL
			localURL, err := downloadImageToLocal(imageData.URL, userId)
			if err != nil {
				logger.Errorf(ctx, "Failed to download image from URL: %s", err.Error())
				localImageURL = imageData.URL // Fallback to original URL
			} else {
				localImageURL = localURL
			}
		} else if imageData.B64JSON != "" {
			// Save base64 image to local storage
			localURL, err := saveGptImageBase64ToLocal(imageData.B64JSON, userId)
			if err != nil {
				logger.Errorf(ctx, "Failed to save base64 image: %s", err.Error())
				localImageURL = "data:image/png;base64," + imageData.B64JSON // Fallback
			} else {
				localImageURL = localURL
			}
		}

		// Generate image ID
		imageID := fmt.Sprintf("gen_%s", generateRandomID())

		// Add image markdown and download link
		contentParts = append(contentParts, fmt.Sprintf("\n![%s](%s)", imageID, localImageURL))
		contentParts = append(contentParts, fmt.Sprintf("\n[Click to download](%s)", localImageURL))

		if i < len(imageResponse.Data)-1 {
			contentParts = append(contentParts, "\n")
		}
	}

	// Create the choice
	choice := ChatCompletionChoice{
		Index: 0,
		Message: &ChatCompletionMessage{
			Role:    "assistant",
			Content: strings.Join(contentParts, ""),
		},
		FinishReason: lo.ToPtr("stop"),
	}
	chatResponse.Choices = append(chatResponse.Choices, choice)

	// Set usage information
	if imageResponse.Usage != nil {
		// Calculate the downstream prompt tokens using the formula: textTokens*1 + imageTokens*2
		downstreamPromptTokens := imageResponse.Usage.InputTokens // fallback to original if no details
		if imageResponse.Usage.InputTokensDetails != nil {
			downstreamPromptTokens = imageResponse.Usage.InputTokensDetails.TextTokens*1 + imageResponse.Usage.InputTokensDetails.ImageTokens*2
		}

		// Calculate new total tokens for downstream
		downstreamTotalTokens := downstreamPromptTokens + imageResponse.Usage.OutputTokens

		chatResponse.Usage = &ChatCompletionUsage{
			PromptTokens:        downstreamPromptTokens,
			CompletionTokens:    imageResponse.Usage.OutputTokens,
			TotalTokens:         downstreamTotalTokens,
			PromptTokensDetails: &PromptTokensDetails{}, // Clear details to avoid confusion since we modified prompt_tokens calculation
			CompletionTokensDetails: &CompletionTokensDetails{
				ContentTokens: imageResponse.Usage.OutputTokens,
			},
		}
	} else {
		// Fallback usage calculation
		prompt := extractPromptFromRequest(originalRequest)
		promptTokens := len(strings.Fields(prompt))
		completionTokens := 416 // Default value based on your example

		chatResponse.Usage = &ChatCompletionUsage{
			PromptTokens:        promptTokens,
			CompletionTokens:    completionTokens,
			TotalTokens:         promptTokens + completionTokens,
			PromptTokensDetails: &PromptTokensDetails{}, // Clear details to avoid confusion since we modified prompt_tokens calculation
			CompletionTokensDetails: &CompletionTokensDetails{
				ContentTokens: completionTokens,
			},
		}
	}

	// Convert to JSON and send response
	jsonResponse, err := json.Marshal(chatResponse)
	if err != nil {
		logger.Errorf(ctx, "Failed to marshal chat response: %s", err.Error())
		return fmt.Errorf("failed to marshal response: %w", err)
	}

	c.Header("Content-Type", "application/json")
	c.Data(http.StatusOK, "application/json", jsonResponse)
	return nil
}

// saveGptImageBase64ToLocal saves a base64 encoded image to local storage for gpt-image
func saveGptImageBase64ToLocal(b64Data string, userId int) (string, error) {
	// Create directory if it doesn't exist
	userDir := filepath.Join("storage", "gpt-image", strconv.Itoa(userId))
	err := os.MkdirAll(userDir, 0755)
	if err != nil {
		return "", fmt.Errorf("failed to create directory: %w", err)
	}

	// Remove base64 URL prefix if present
	if idx := strings.Index(b64Data, ","); idx != -1 {
		b64Data = b64Data[idx+1:]
	}

	// Decode base64 data
	imageData, err := base64.StdEncoding.DecodeString(b64Data)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64: %w", err)
	}

	// Generate a unique filename
	now := time.Now()
	filename := fmt.Sprintf("gpt_image_%d_%d.png", now.Unix(), rand.Intn(10000))
	filePath := filepath.Join(userDir, filename)

	// Write the file
	err = os.WriteFile(filePath, imageData, 0644)
	if err != nil {
		return "", fmt.Errorf("failed to write file: %w", err)
	}

	// Generate the local URL
	localURL := fmt.Sprintf("%s/fileSystem/gpt-image/%d/%s",
		lo.If(config.GptImageStorageAddress == "", config.ServerAddress).Else(config.GptImageStorageAddress),
		userId, filename)

	return localURL, nil
}

// extractPromptFromRequest extracts the prompt from the chat completion request
func extractPromptFromRequest(request ChatCompletionRequest) string {
	for _, message := range request.Messages {
		if message.Role == "user" {
			switch content := message.Content.(type) {
			case string:
				return content
			case []interface{}:
				for _, item := range content {
					if contentMap, ok := item.(map[string]interface{}); ok {
						if contentMap["type"] == "text" {
							if text, ok := contentMap["text"].(string); ok {
								return text
							}
						}
					}
				}
			}
		}
	}
	return "Generated image"
}

// generateRandomID generates a random ID for chat completion
func generateRandomID() string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, 16)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

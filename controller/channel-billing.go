package controller

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common/client"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/middleware"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/monitor"
	"github.com/songquanpeng/one-api/relay/channeltype"
	"golang.org/x/net/proxy"
)

// https://github.com/songquanpeng/one-api/issues/79

type OpenAISubscriptionResponse struct {
	Object             string      `json:"object"`
	HasPaymentMethod   bool        `json:"has_payment_method"`
	SoftLimitUSD       float64     `json:"soft_limit_usd"`
	HardLimitUSD       float64     `json:"hard_limit_usd"`
	SystemHardLimitUSD float64     `json:"system_hard_limit_usd"`
	AccessUntil        interface{} `json:"access_until"`
}

type PublicOpenAISubscriptionResponse struct {
	Object             string  `json:"object"`
	HasPaymentMethod   bool    `json:"has_payment_method"`
	SoftLimitUSD       float64 `json:"soft_limit_usd"`
	HardLimitUSD       float64 `json:"hard_limit_usd"`
	SystemHardLimitUSD float64 `json:"system_hard_limit_usd"`
	AccessUntil        int64   `json:"access_until"`
	// 令牌真实消耗
	UsedQuota int64 `json:"used_quota"`
	// 令牌真实剩余额度
	RemainQuota int64 `json:"remain_quota"`
	// 令牌调用次数
	UsedCount int `json:"used_count"`
}

type OpenAIUsageDailyCost struct {
	Timestamp float64 `json:"timestamp"`
	LineItems []struct {
		Name string  `json:"name"`
		Cost float64 `json:"cost"`
	}
}

type OpenAICreditGrants struct {
	Object         string  `json:"object"`
	TotalGranted   float64 `json:"total_granted"`
	TotalUsed      float64 `json:"total_used"`
	TotalAvailable float64 `json:"total_available"`
}

type OpenAIUsageResponse struct {
	Object string `json:"object"`
	//DailyCosts []OpenAIUsageDailyCost `json:"daily_costs"`
	TotalUsage float64 `json:"total_usage"` // unit: 0.01 dollar
}

type OpenAISBUsageResponse struct {
	Msg  string `json:"msg"`
	Data *struct {
		Credit string `json:"credit"`
	} `json:"data"`
}

type AIProxyUserOverviewResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	ErrorCode int    `json:"error_code"`
	Data      struct {
		TotalPoints float64 `json:"totalPoints"`
	} `json:"data"`
}

type API2GPTUsageResponse struct {
	Object         string  `json:"object"`
	TotalGranted   float64 `json:"total_granted"`
	TotalUsed      float64 `json:"total_used"`
	TotalRemaining float64 `json:"total_remaining"`
}

type APGC2DGPTUsageResponse struct {
	//Grants         interface{} `json:"grants"`
	Object         string  `json:"object"`
	TotalAvailable float64 `json:"total_available"`
	TotalGranted   float64 `json:"total_granted"`
	TotalUsed      float64 `json:"total_used"`
}

type SiliconFlowUsageResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Status  bool   `json:"status"`
	Data    struct {
		ID            string `json:"id"`
		Name          string `json:"name"`
		Image         string `json:"image"`
		Email         string `json:"email"`
		IsAdmin       bool   `json:"isAdmin"`
		Balance       string `json:"balance"`
		Status        string `json:"status"`
		Introduction  string `json:"introduction"`
		Role          string `json:"role"`
		ChargeBalance string `json:"chargeBalance"`
		TotalBalance  string `json:"totalBalance"`
		Category      string `json:"category"`
	} `json:"data"`
}

type DeepSeekUsageResponse struct {
	IsAvailable  bool `json:"is_available"`
	BalanceInfos []struct {
		Currency        string `json:"currency"`
		TotalBalance    string `json:"total_balance"`
		GrantedBalance  string `json:"granted_balance"`
		ToppedUpBalance string `json:"topped_up_balance"`
	} `json:"balance_infos"`
}

// GetAuthHeader get auth header
func GetAuthHeader(token string) http.Header {
	h := http.Header{}
	h.Add("Authorization", fmt.Sprintf("Bearer %s", token))
	return h
}

// CreateProxyClient 创建一个使用代理的HTTP客户端
func CreateProxyClient(proxyURLStr string, proxyAuth string, timeout time.Duration) (*http.Client, error) {
	// 使用net/url包解析代理URL
	logger.SysLog(fmt.Sprintf("准备设置代理: %s", proxyURLStr))
	proxyURL, err := url.Parse(proxyURLStr)
	if err != nil {
		return nil, fmt.Errorf("解析代理URL失败: %s", err.Error())
	}

	// 创建代理Transport
	transport := &http.Transport{
		// 添加默认的TLS配置
		TLSHandshakeTimeout:   10 * time.Second,
		ResponseHeaderTimeout: 30 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
		IdleConnTimeout:       90 * time.Second,
		MaxIdleConns:          100,
		MaxIdleConnsPerHost:   10,
	}

	// 处理不同类型的代理
	if proxyURL.Scheme == "socks5" || proxyURL.Scheme == "socks5h" {
		logger.SysLog(fmt.Sprintf("使用SOCKS5代理: %s", proxyURL.Host))

		// 解析用户名和密码
		var username, password string
		if proxyAuth != "" {
			parts := strings.SplitN(proxyAuth, ":", 2)
			if len(parts) == 2 {
				username = parts[0]
				password = parts[1]
				logger.SysLog(fmt.Sprintf("SOCKS5代理使用认证: 用户名=%s", username))
			}
		} else if proxyURL.User != nil {
			// 从URL中获取认证信息
			username = proxyURL.User.Username()
			password, _ = proxyURL.User.Password()
			logger.SysLog(fmt.Sprintf("从URL获取SOCKS5代理认证: 用户名=%s", username))
		} else {
			logger.SysLog("SOCKS5代理未使用认证")
		}

		// 创建认证对象
		var auth *proxy.Auth
		if username != "" {
			auth = &proxy.Auth{
				User:     username,
				Password: password,
			}
		} else {
			auth = nil
		}

		// 创建拨号器
		dialer, err := proxy.SOCKS5("tcp", proxyURL.Host, auth, proxy.Direct)
		if err != nil {
			errMsg := fmt.Sprintf("创建SOCKS5拨号器失败: %s", err.Error())
			logger.SysError(errMsg)
			return nil, fmt.Errorf(errMsg)
		}

		// 设置自定义拨号器
		transport.DialContext = func(ctx context.Context, network, addr string) (net.Conn, error) {
			logger.SysLog(fmt.Sprintf("SOCKS5代理尝试连接: %s", addr))

			// 对于socks5h方案，我们只需要使用拨号器，它会处理DNS解析
			conn, err := dialer.Dial(network, addr)

			if err != nil {
				logger.SysError(fmt.Sprintf("SOCKS5代理连接失败: %s, 错误: %s", addr, err.Error()))
			} else {
				logger.SysLog(fmt.Sprintf("SOCKS5代理连接成功: %s", addr))
			}
			return conn, err
		}
	} else {
		// 处理 HTTP/HTTPS 代理
		logger.SysLog(fmt.Sprintf("使用HTTP/HTTPS代理: %s", proxyURL.String()))
		transport.Proxy = http.ProxyURL(proxyURL)

		// 添加代理认证
		if proxyAuth != "" {
			transport.ProxyConnectHeader = http.Header{}
			transport.ProxyConnectHeader.Set("Proxy-Authorization", proxyAuth)
			logger.SysLog("HTTP/HTTPS代理使用认证")
		}
	}

	// 创建客户端
	client := &http.Client{
		Transport: transport,
		Timeout:   timeout,
	}
	logger.SysLog("代理客户端创建成功")
	return client, nil
}

func GetResponseBody(method, url string, channel *model.Channel, headers http.Header) ([]byte, error) {
	req, err := http.NewRequest(method, url, nil)
	if err != nil {
		return nil, err
	}
	for k := range headers {
		req.Header.Add(k, headers.Get(k))
	}

	// 加载渠道配置并检查是否配置了代理
	config, err := channel.LoadConfig()
	if err != nil {
		return nil, err
	}

	// 如果渠道配置了专属代理，则使用专属代理进行请求
	if config.GetProxyEnabled() && config.GetProxyURL() != "" {
		// 记录代理信息到日志
		logger.SysLog(fmt.Sprintf("使用代理 %s 发送请求", config.GetProxyURL()))

		// 创建使用代理的HTTP客户端
		customClient, err := CreateProxyClient(config.GetProxyURL(), config.GetProxyAuth(), client.HTTPClient.Timeout)
		if err != nil {
			logger.SysError(fmt.Sprintf("创建代理客户端失败: %s", err.Error()))
		} else {
			// 使用自定义客户端发送请求
			res, err := customClient.Do(req)
			if err != nil {
				return nil, err
			}
			if res.StatusCode != http.StatusOK {
				return nil, fmt.Errorf("status code: %d", res.StatusCode)
			}
			body, err := io.ReadAll(res.Body)
			if err != nil {
				return nil, err
			}
			err = res.Body.Close()
			if err != nil {
				return nil, err
			}
			return body, nil
		}
	}

	// 使用默认HTTP客户端发送请求
	res, err := client.HTTPClient.Do(req)
	if err != nil {
		return nil, err
	}
	if res.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("status code: %d", res.StatusCode)
	}
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}
	err = res.Body.Close()
	if err != nil {
		return nil, err
	}
	return body, nil
}

func updateChannelCloseAIBalance(channel *model.Channel) (float64, error) {
	url := fmt.Sprintf("%s/dashboard/billing/credit_grants", channel.GetBaseURL())
	body, err := GetResponseBody("GET", url, channel, GetAuthHeader(channel.Key))

	if err != nil {
		return 0, err
	}
	response := OpenAICreditGrants{}
	err = json.Unmarshal(body, &response)
	if err != nil {
		return 0, err
	}
	channel.UpdateBalance(response.TotalAvailable)
	return response.TotalAvailable, nil
}

func updateChannelOpenAISBBalance(channel *model.Channel) (float64, error) {
	url := fmt.Sprintf("https://api.openai-sb.com/sb-api/user/status?api_key=%s", channel.Key)
	body, err := GetResponseBody("GET", url, channel, GetAuthHeader(channel.Key))
	if err != nil {
		return 0, err
	}
	response := OpenAISBUsageResponse{}
	err = json.Unmarshal(body, &response)
	if err != nil {
		return 0, err
	}
	if response.Data == nil {
		return 0, errors.New(response.Msg)
	}
	balance, err := strconv.ParseFloat(response.Data.Credit, 64)
	if err != nil {
		return 0, err
	}
	channel.UpdateBalance(balance)
	return balance, nil
}

func updateChannelAIProxyBalance(channel *model.Channel) (float64, error) {
	url := "https://aiproxy.io/api/report/getUserOverview"
	headers := http.Header{}
	headers.Add("Api-Key", channel.Key)
	body, err := GetResponseBody("GET", url, channel, headers)
	if err != nil {
		return 0, err
	}
	response := AIProxyUserOverviewResponse{}
	err = json.Unmarshal(body, &response)
	if err != nil {
		return 0, err
	}
	if !response.Success {
		return 0, fmt.Errorf("code: %d, message: %s", response.ErrorCode, response.Message)
	}
	channel.UpdateBalance(response.Data.TotalPoints)
	return response.Data.TotalPoints, nil
}

func updateChannelAPI2GPTBalance(channel *model.Channel) (float64, error) {
	url := "https://api.api2gpt.com/dashboard/billing/credit_grants"
	body, err := GetResponseBody("GET", url, channel, GetAuthHeader(channel.Key))

	if err != nil {
		return 0, err
	}
	response := API2GPTUsageResponse{}
	err = json.Unmarshal(body, &response)
	if err != nil {
		return 0, err
	}
	channel.UpdateBalance(response.TotalRemaining)
	return response.TotalRemaining, nil
}

func updateChannelAIGC2DBalance(channel *model.Channel) (float64, error) {
	url := "https://api.aigc2d.com/dashboard/billing/credit_grants"
	body, err := GetResponseBody("GET", url, channel, GetAuthHeader(channel.Key))
	if err != nil {
		return 0, err
	}
	response := APGC2DGPTUsageResponse{}
	err = json.Unmarshal(body, &response)
	if err != nil {
		return 0, err
	}
	channel.UpdateBalance(response.TotalAvailable)
	return response.TotalAvailable, nil
}

func updateChannelSiliconFlowBalance(channel *model.Channel) (float64, error) {
	url := "https://api.siliconflow.cn/v1/user/info"
	body, err := GetResponseBody("GET", url, channel, GetAuthHeader(channel.Key))
	if err != nil {
		return 0, err
	}
	response := SiliconFlowUsageResponse{}
	err = json.Unmarshal(body, &response)
	if err != nil {
		return 0, err
	}
	if response.Code != 20000 {
		return 0, fmt.Errorf("code: %d, message: %s", response.Code, response.Message)
	}
	balance, err := strconv.ParseFloat(response.Data.TotalBalance, 64)
	if err != nil {
		return 0, err
	}
	channel.UpdateBalance(balance)
	return balance, nil
}

func updateChannelDeepSeekBalance(channel *model.Channel) (float64, error) {
	url := "https://api.deepseek.com/user/balance"
	body, err := GetResponseBody("GET", url, channel, GetAuthHeader(channel.Key))
	if err != nil {
		return 0, err
	}
	response := DeepSeekUsageResponse{}
	err = json.Unmarshal(body, &response)
	if err != nil {
		return 0, err
	}
	index := -1
	for i, balanceInfo := range response.BalanceInfos {
		if balanceInfo.Currency == "CNY" {
			index = i
			break
		}
	}
	if index == -1 {
		return 0, errors.New("currency CNY not found")
	}
	balance, err := strconv.ParseFloat(response.BalanceInfos[index].TotalBalance, 64)
	if err != nil {
		return 0, err
	}
	channel.UpdateBalance(balance)
	return balance, nil
}

// 添加新的辅助函数
func cleanMJSuffixFromURL(baseURL string) string {
	mjSuffixes := []string{"/mj-fast", "/mj-relax", "/mj-turbo"}
	for _, suffix := range mjSuffixes {
		if strings.HasSuffix(baseURL, suffix) {
			return strings.TrimSuffix(baseURL, suffix)
		}
	}
	return baseURL
}

func updateChannelBalance(channel *model.Channel) (float64, error) {
	channelExtend, _ := model.CacheGetChannelExByChannelId(channel.Id)

	// 构建请求头
	headers := GetAuthHeader(channel.Key)
	if channelExtend != nil {
		// 如果配置了上游用户ID，添加所有可能的请求头
		if channelExtend.UpstreamUserId != "" {
			for headerKey := range channeltype.GetUpstreamUserHeaders(channel.Type) {
				headers.Add(headerKey, channelExtend.UpstreamUserId)
			}
		}

		// 如果填写了PlatformAccessToken，使用它替代普通token
		if channelExtend.PlatformAccessToken != "" {
			baseURL := cleanMJSuffixFromURL(channel.GetBaseURL())
			url := fmt.Sprintf("%s/api/user/self", baseURL)

			// 创建新的请求头，保留之前设置的上游用户ID相关的头
			platformHeaders := http.Header{}
			// 添加 PlatformAccessToken 的认证头
			platformHeaders.Add("Authorization", fmt.Sprintf("Bearer %s", channelExtend.PlatformAccessToken))
			// 复制之前设置的所有上游用户ID相关的头
			for headerKey := range channeltype.GetUpstreamUserHeaders(channel.Type) {
				if value := headers.Get(headerKey); value != "" {
					platformHeaders.Add(headerKey, value)
				}
			}

			body, err := GetResponseBody("GET", url, channel, platformHeaders)
			if err != nil {
				return 0, err
			}

			var baseResponse struct {
				Success bool   `json:"success"`
				Message string `json:"message"`
			}
			err = json.Unmarshal(body, &baseResponse)
			if err != nil {
				return 0, fmt.Errorf("failed to parse response body: %v", err)
			}

			if !baseResponse.Success {
				return 0, fmt.Errorf("%s", baseResponse.Message)
			}

			var responseData struct {
				Data struct {
					Quota int `json:"quota"`
				} `json:"data"`
			}
			err = json.Unmarshal(body, &responseData)
			if err != nil {
				return 0, fmt.Errorf("failed to parse data from response body: %v", err)
			}

			balance := float64(responseData.Data.Quota) / 500000
			channel.UpdateBalance(balance)
			return balance, nil
		}
	}

	// 如果填写了自定义余额上限，用上限减去当前消费量
	if channel.CustomBalanceLimit > 0 {
		balance := channel.CustomBalanceLimit - float64(channel.UsedQuota)/500000
		channel.UpdateBalance(balance)
		return balance, nil
	}
	baseURL := channeltype.ChannelBaseURLs[channel.Type]
	if channel.GetBaseURL() == "" {
		channel.BaseURL = &baseURL
	}
	switch channel.Type {
	case channeltype.OpenAI, channeltype.NewBingXMY, channeltype.GPTGod, channeltype.ShellAPI:
		if channel.GetBaseURL() != "" {
			baseURL = cleanMJSuffixFromURL(channel.GetBaseURL())
		}
	case channeltype.Azure:
		return 0, errors.New("尚未实现")
	case channeltype.Custom:
		baseURL = channel.GetBaseURL()
	case channeltype.CloseAI:
		return updateChannelCloseAIBalance(channel)
	case channeltype.OpenAISB:
		return updateChannelOpenAISBBalance(channel)
	case channeltype.AIProxy:
		return updateChannelAIProxyBalance(channel)
	case channeltype.API2GPT:
		return updateChannelAPI2GPTBalance(channel)
	case channeltype.AIGC2D:
		return updateChannelAIGC2DBalance(channel)
	case channeltype.SiliconFlow:
		return updateChannelSiliconFlowBalance(channel)
	case channeltype.DeepSeek:
		return updateChannelDeepSeekBalance(channel)
	default:
		return 0, errors.New("尚未实现")
	}

	url := fmt.Sprintf("%s/v1/dashboard/billing/subscription", baseURL)

	body, err := GetResponseBody("GET", url, channel, GetAuthHeader(channel.Key))
	if err != nil {
		return 0, err
	}
	subscription := OpenAISubscriptionResponse{}
	err = json.Unmarshal(body, &subscription)
	if err != nil {
		return 0, err
	}
	now := time.Now()
	startDate := fmt.Sprintf("%s-01", now.Format("2006-01"))
	endDate := now.Format("2006-01-02")
	if !subscription.HasPaymentMethod {
		startDate = now.AddDate(0, 0, -100).Format("2006-01-02")
	}
	url = fmt.Sprintf("%s/v1/dashboard/billing/usage?start_date=%s&end_date=%s", baseURL, startDate, endDate)
	body, err = GetResponseBody("GET", url, channel, GetAuthHeader(channel.Key))
	if err != nil {
		return 0, err
	}
	usage := OpenAIUsageResponse{}
	err = json.Unmarshal(body, &usage)
	if err != nil {
		return 0, err
	}
	balance := subscription.HardLimitUSD - usage.TotalUsage/100
	channel.UpdateBalance(balance)
	return balance, nil
}

func UpdateChannelBalance(c *gin.Context) {
	// 检查渠道编辑权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")
	hasChannelWritePermission := role == 100 || // 超级管理员
		middleware.HasWritePermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelWrite)

	if !hasChannelWritePermission {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权限执行渠道余额更新操作",
		})
		return
	}

	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "渠道 ID 无效",
		})
		return
	}
	channel, err := model.GetChannelById(id, true)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "渠道 ID 不存在",
		})
		return
	}
	balance, err := updateChannelBalance(channel)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"balance": balance,
	})
	return
}

func updateAllChannelsBalance() error {
	channels, err := model.GetAllChannels(0, 0, "", false, 0, "", "", "", "", 0, 0, 0, "", "", "all", "")
	if err != nil {
		return err
	}
	for _, channel := range channels {
		if channel.Status != model.ChannelStatusEnabled {
			continue
		}
		// TODO: support Azure
		if channel.Type != channeltype.OpenAI && channel.Type != channeltype.Custom {
			continue
		}
		balance, err := updateChannelBalance(channel)
		if err != nil {
			continue
		} else {
			// err is nil & balance <= 0 means quota is used up
			if balance <= 0 {
				monitor.DisableChannel(channel.Id, channel.Name, "余额不足")
			}
		}
		time.Sleep(config.RequestInterval)
	}
	return nil
}

func UpdateAllChannelsBalance(c *gin.Context) {
	// 检查渠道编辑权限
	role := c.GetInt("role")
	adminAccessFlags := c.GetInt64("admin_access_flags")
	hasChannelWritePermission := role == 100 || // 超级管理员
		middleware.HasWritePermission(adminAccessFlags, middleware.PermissionChannel, middleware.PermissionChannelWrite)

	if !hasChannelWritePermission {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权限执行批量渠道余额更新操作",
		})
		return
	}

	//err := updateAllChannelsBalance()
	//if err != nil {
	//	c.JSON(http.StatusOK, gin.H{
	//		"success": false,
	//		"message": err.Error(),
	//	})
	//	return
	//}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

func AutomaticallyUpdateChannels(frequency int) {
	for {
		time.Sleep(time.Duration(frequency) * time.Minute)
		logger.SysLog("updating all channels")
		_ = updateAllChannelsBalance()
		logger.SysLog("channels update done")
	}
}

package controller

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/model"
	"net/http"
	"strconv"
	"time"
)

type CreateRedPacketRequest struct {
	Type     int    `json:"type" binding:"required"`  // 红包类型，1-普通红包，2-拼手气红包
	Quota    int64  `json:"quota" binding:"required"` // 红包总金额
	Count    int    `json:"count" binding:"required"` // 红包总个数
	Blessing string `json:"blessing"`                 // 祝福语
}

func CreateRedPacket(c *gin.Context) {
	ctx := c.Request.Context()
	var request CreateRedPacketRequest
	err := c.BindJSON(&request)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"message": "参数错误",
			"success": false,
		})
		return
	}

	// 红包金额、数量校验
	if request.Quota < 1 || request.Count < 1 {
		c.JSON(http.StatusOK, gin.H{
			"message": "红包金额、数量不能小于1",
			"success": false,
		})
		return
	}

	// 扣除创建者额度
	id := c.GetInt("id")
	user, err := model.GetUserById(id, true)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"message": "用户不存在",
			"success": false,
		})
		return
	}

	// 检查是否超级管理员
	if user.Role != 100 {
		c.JSON(http.StatusOK, gin.H{
			"message": "无权限创建红包",
			"success": false,
		})
		return
	}

	// 拼手气红包，额度=红包金额。普通红包，额度=红包金额*红包个数
	quota := request.Quota
	if request.Type == 1 {
		quota = request.Quota * int64(request.Count)
	}
	if user.Quota < quota {
		c.JSON(http.StatusOK, gin.H{
			"message": "用户额度不足",
			"success": false,
		})
		return
	}
	user.Quota -= quota
	err = user.Update(false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"message": "扣除额度失败",
			"success": false,
		})
		return
	}
	//更新日志
	model.RecordLog(ctx, id, model.LogTypeConsume, fmt.Sprintf("创建红包，红包类型：%d，红包金额：%s，红包个数：%d，祝福语：%s", request.Type, common.LogQuota(request.Quota), request.Count, request.Blessing))

	redPacket := model.RedPacket{
		Type:          request.Type,
		Quota:         request.Quota,
		Count:         request.Count,
		RemainCount:   request.Count,
		RemainQuota:   request.Quota,
		CreatedAt:     time.Now().Unix(),
		Status:        1,
		Blessing:      request.Blessing,
		RedPacketUUID: helper.GenerateUUID(),
	}
	err = redPacket.Insert()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"message": "创建红包失败",
			"success": false,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "创建红包成功",
		"success": true,
		"data":    redPacket,
	})
}

// DeleteRedPacket 删除红包，传入红包id
func DeleteRedPacket(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"message": "参数错误",
			"success": false,
		})
		return
	}

	redPacket := model.RedPacket{
		Id: id,
	}
	err = redPacket.Get()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"message": "红包不存在",
			"success": false,
		})
		return
	}

	// 删除红包
	err = redPacket.Delete()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"message": "删除红包失败",
			"success": false,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "删除红包成功",
		"success": true,
	})
}

// GetAllRedPackets 获取所有红包详情
func GetAllRedPackets(c *gin.Context) {
	redPackets, err := model.GetAllRedPackets()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"message": "获取红包失败",
			"success": false,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "获取红包成功",
		"success": true,
		"data":    redPackets,
	})
}

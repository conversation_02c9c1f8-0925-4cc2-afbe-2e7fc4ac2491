package controller

import (
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/model"
	billingratio "github.com/songquanpeng/one-api/relay/billing/ratio"

	"net/http"
	"strconv"
)

// GetAgencyUsers 获取代理商下属用户列表
func GetAgencyUsers(c *gin.Context) {
	userId := c.GetInt("id")
	agency, err := model.GetAgencyByUserId(userId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取代理商信息失败",
		})
		return
	}

	users, err := model.GetUsersByAgencyId(agency.UserId)
	if err != nil {
		c.JSO<PERSON>(http.StatusOK, gin.H{
			"success": false,
			"message": "获取用户列表失败",
		})
		return
	}

	c.<PERSON>SO<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    users,
	})
}

// CountAgencyUsers
func CountAgencyUsers(c *gin.Context) {
	userId := c.GetInt("id")
	agency, err := model.GetAgencyByUserId(userId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取代理商信息失败",
		})
		return
	}

	count, err := model.CountUsersByAgencyId(agency.Id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取用户数量失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"count": count,
		},
	})

}

// GetAgencyUser 获取代理商下属用户信息
func GetAgencyUser(c *gin.Context) {
	userId := c.GetInt("id")
	agency, err := model.GetAgencyByUserId(userId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取代理商信息失败",
		})
		return
	}

	id, _ := strconv.Atoi(c.Param("id"))
	user, err := model.GetUserById(id, false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取用户信息失败",
		})
		return
	}

	if user.AgencyId != agency.UserId {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "用户不属于该代理商",
		})
		return
	}
	// 获取ex
	userEx, _ := model.GetUserExByUserId(user.Id)
	if userEx != nil {
		userEx.ChatRecordJsonData = ""
	}
	rsMap, err := helper.MergeStructsToMap(user, userEx, "id", "user_id")
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    rsMap,
	})

}

// CreateAgencyUser 创建代理商下属用户
func CreateAgencyUser(c *gin.Context) {
	ctx := c.Request.Context()
	agencyUserId := c.GetInt("id")
	_, err := model.GetAgencyByUserId(agencyUserId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取代理商信息失败",
		})
		return
	}

	var user model.User
	err = json.NewDecoder(c.Request.Body).Decode(&user)
	if err != nil || user.Username == "" || user.Password == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}
	if err := common.Validate.Struct(&user); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "输入不合法 " + err.Error(),
		})
		return
	}
	if user.DisplayName == "" {
		user.DisplayName = user.Username
	}
	myRole := c.GetInt("role")
	if user.Role >= myRole {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无法创建权限大于等于自己的用户",
		})
		return
	}
	// Even for admin users, we cannot fully trust them!
	cleanUser := model.User{
		Username:    user.Username,
		Password:    user.Password,
		DisplayName: user.DisplayName,
		AgencyId:    agencyUserId,
	}
	if err := cleanUser.Insert(ctx, 0); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	model.RecordLog(ctx, c.GetInt("id"), model.LogTypeOperation, fmt.Sprintf("代理商创建了新用户 %s", user.Username))
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return

}

// UpdateAgencyUser 更新代理商下属用户信息
func UpdateAgencyUser(c *gin.Context) {
	agencyUserId := c.GetInt("id")
	agency, err := model.GetAgencyByUserId(agencyUserId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取代理商信息失败",
		})
		return
	}

	var updatedUser model.User
	var updatedUserExtend model.UserExtend
	err = common.BindAndDecodeMainAndExtend(c, &updatedUser, &updatedUserExtend)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数: " + err.Error(),
		})
		return
	}

	// 检查用户是否存在
	originUser, err := model.GetUserById(updatedUser.Id, false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "用户不存在",
		})
		return
	}

	// 检查用户是否属于该代理商
	if originUser.AgencyId != agency.UserId {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "用户不属于该代理商",
		})
		return
	}

	// 不允许修改用户分组
	updatedUser.Group = originUser.Group

	// 不允许修改用户角色
	updatedUser.Role = originUser.Role

	// 不允许修改用户余额
	if updatedUser.Quota != 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "不允许修改用户额度",
		})
		return
	}

	// 处理密码更新
	updatePassword := false
	if updatedUser.Password != "" && updatedUser.Password != "$I_LOVE_U" {
		updatePassword = true
	} else {
		updatedUser.Password = "" // 如果没有提供新密码，保持原密码不变
	}

	// 更新用户基本信息
	if err := updatedUser.Update(updatePassword); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "更新用户信息失败: " + err.Error(),
		})
		return
	}

	// 更新用户扩展信息，排除ChatRecordJsonData字段
	updatedUserExtend.UserId = updatedUser.Id
	updatedUserExtend.Id = 0
	originUserEx, err := model.GetUserExByUserId(updatedUser.Id)
	if err == nil {
		updatedUserExtend.Id = originUserEx.Id
	}
	// 检测updatedUserExtend中的所有费率是否都在合理范围内
	if err = CheckUserExtendRate(&updatedUser, &updatedUserExtend); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "更新用户扩展信息失败(费率不合理): " + err.Error(),
		})
		return
	}
	if err := updatedUserExtend.UpdateWithoutChatRecordJsonData(); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "更新用户扩展信息失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "用户信息更新成功",
	})
}

// DeleteAgencyUser 删除代理商下属用户
func DeleteAgencyUser(c *gin.Context) {
	userId := c.GetInt("id")
	agency, err := model.GetAgencyByUserId(userId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取代理商信息失败",
		})
		return
	}

	id, _ := strconv.Atoi(c.Param("id"))
	user, err := model.GetUserById(id, false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "获取用户信息失败",
		})
		return
	}

	if user.AgencyId != agency.UserId {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "用户不属于该代理商",
		})
		return
	}

	err = model.DeleteAgencyUser(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除成功",
	})

}

func CheckUserExtendRate(user *model.User, userExtend *model.UserExtend) error {
	// 解析userExtend中的ModelRatio, ModelFixedPrice, CompletionRatio,先判空,如果是空则不需要解析,也不用对比,如果不为空则解析后对比ratio.ModelRatio的各项
	if userExtend.GetModelRatio() != "" {
		// 解析json
		var modelRatio map[string]float64
		err := json.Unmarshal([]byte(userExtend.GetModelRatio()), &modelRatio)
		if err != nil {
			return fmt.Errorf("model ratio is invalid")
		}
		// 对比ratio.ModelRatio遍历对比
		for modelName, rate := range modelRatio {
			rootRatio := billingratio.GetModelRatio(modelName, 1)
			if rootRatio > rate {
				return fmt.Errorf("model [%s] ratio is too low,can not less than [%v]", modelName, rootRatio)
			}
		}
	}

	if userExtend.GetModelFixedPrice() != "" {
		// 解析json
		var modelFixedPrice map[string]float64
		err := json.Unmarshal([]byte(userExtend.GetModelFixedPrice()), &modelFixedPrice)
		if err != nil {
			return fmt.Errorf("model fixed price is invalid")
		}
		// 对比ratio.ModelFixedPrice遍历对比
		for modelName, price := range modelFixedPrice {
			rootPrice, getModelFixedPriceErr := billingratio.GetModelFixedPrice(modelName)
			if getModelFixedPriceErr != nil {
				return fmt.Errorf("model [%s] fixed price is not set , please contact the admin", modelName)
			}
			if rootPrice > price {
				return fmt.Errorf("model [%s] fixed price is too low,can not less than [%v]", modelName, rootPrice)
			}
		}
	}

	// CompletionRatio
	if userExtend.GetCompletionRatio() != "" {
		// 解析json
		var completionRatio map[string]float64
		err := json.Unmarshal([]byte(userExtend.GetCompletionRatio()), &completionRatio)
		if err != nil {
			return fmt.Errorf("completion ratio is invalid")
		}
		// 对比ratio.CompletionRatio遍历对比
		for modelName, rate := range completionRatio {
			rootRatio := billingratio.GetCompletionRatio(modelName, 1)
			if rootRatio > rate {
				return fmt.Errorf("model [%s] completion ratio is too low,can not less than [%v]", modelName, rootRatio)
			}
		}
	}

	// TopupRatio
	if userExtend.TopupRatio != nil && *userExtend.TopupRatio != 0 {
		rootRatio := billingratio.GetTopupGroupRatio(user.Group)
		if rootRatio > *userExtend.TopupRatio {
			return fmt.Errorf("topup ratio is too low,can not less than [%v]", rootRatio)
		}
	}

	return nil
}

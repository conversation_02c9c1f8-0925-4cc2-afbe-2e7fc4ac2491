package controller

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/model"
	"gorm.io/gorm"
)

// GetAllUserTimeoutConfigs 管理员获取所有用户的超时配置
func GetAllUserTimeoutConfigs(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	size, _ := strconv.Atoi(c.<PERSON>("size", "20"))
	userId := c.Query("user_id")
	modelName := c.Query("model_name")

	configs, total, err := model.GetAllUserTimeoutConfigs(page, size, userId, modelName)
	if err != nil {
		logger.Errorf(c.Request.Context(), "Failed to get timeout configs: %v", err)
		c.<PERSON>(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取超时配置失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    configs,
		"total":   total,
		"page":    page,
		"size":    size,
	})
}

// GetUserTimeoutConfigs 管理员获取指定用户的超时配置
func GetUserTimeoutConfigs(c *gin.Context) {
	userIdStr := c.Param("user_id")
	userId, err := strconv.Atoi(userIdStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的用户ID",
		})
		return
	}

	configs, err := model.GetUserTimeoutConfigs(userId)
	if err != nil {
		logger.Errorf(c.Request.Context(), "Failed to get user timeout configs: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取用户超时配置失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    configs,
	})
}

// GetUserTimeoutConfig 管理员获取用户特定模型的超时配置
func GetUserTimeoutConfig(c *gin.Context) {
	userIdStr := c.Query("user_id")
	if userIdStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "缺少用户ID参数",
		})
		return
	}

	userId, err := strconv.Atoi(userIdStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的用户ID",
		})
		return
	}

	modelName := c.Query("model")
	if modelName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "缺少模型名称参数",
		})
		return
	}

	config, err := model.GetUserTimeoutConfig(userId, modelName)
	if err != nil {
		logger.Errorf(c.Request.Context(), "Failed to get user timeout config: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取超时配置失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    config,
	})
}

// SetUserTimeoutConfig 管理员设置用户超时配置
func SetUserTimeoutConfig(c *gin.Context) {
	var req struct {
		UserId            int     `json:"user_id" binding:"required"`
		ModelName         string  `json:"model_name" binding:"required"`
		FirstByteTimeout  int     `json:"first_byte_timeout" binding:"required,min=1"`
		TotalTimeout      int     `json:"total_timeout" binding:"required,min=1"`
		TpsThreshold      float64 `json:"tps_threshold" binding:"min=0"`
		TimeoutCostBearer string  `json:"timeout_cost_bearer"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 验证首字节超时不能大于总超时
	if req.FirstByteTimeout > req.TotalTimeout {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "首字节超时不能大于总超时",
		})
		return
	}

	// 设置默认费用承担方
	if req.TimeoutCostBearer == "" {
		req.TimeoutCostBearer = "user"
	}

	// 验证费用承担方
	if req.TimeoutCostBearer != "user" && req.TimeoutCostBearer != "admin" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "超时费用承担方必须是 'user' 或 'admin'",
		})
		return
	}

	adminId := c.GetInt(ctxkey.Id)
	logger.Infof(c.Request.Context(), "Admin %d setting timeout config for user %d model %s, cost bearer: %s",
		adminId, req.UserId, req.ModelName, req.TimeoutCostBearer)

	err := model.SetUserTimeoutConfig(req.UserId, req.ModelName, req.FirstByteTimeout, req.TotalTimeout, req.TpsThreshold, req.TimeoutCostBearer)
	if err != nil {
		logger.Errorf(c.Request.Context(), "Failed to set user timeout config: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "设置超时配置失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "超时配置设置成功",
	})
}

// DeleteUserTimeoutConfig 管理员删除用户超时配置
func DeleteUserTimeoutConfig(c *gin.Context) {
	userIdStr := c.Param("user_id")
	userId, err := strconv.Atoi(userIdStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的用户ID",
		})
		return
	}

	modelName := c.Query("model")
	if modelName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "缺少模型名称参数",
		})
		return
	}

	adminId := c.GetInt(ctxkey.Id)
	logger.Infof(c.Request.Context(), "Admin %d deleting timeout config for user %d model %s",
		adminId, userId, modelName)

	err = model.DeleteUserTimeoutConfig(userId, modelName)
	if err != nil {
		logger.Errorf(c.Request.Context(), "Failed to delete user timeout config: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "删除超时配置失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "超时配置删除成功",
	})
}

// UpdateUserTimeoutConfig 管理员更新用户超时配置
func UpdateUserTimeoutConfig(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的配置ID",
		})
		return
	}

	var req struct {
		UserId            int     `json:"user_id" binding:"required"`
		ModelName         string  `json:"model_name" binding:"required"`
		FirstByteTimeout  int     `json:"first_byte_timeout" binding:"required,min=1"`
		TotalTimeout      int     `json:"total_timeout" binding:"required,min=1"`
		TpsThreshold      float64 `json:"tps_threshold" binding:"min=0"`
		TimeoutCostBearer string  `json:"timeout_cost_bearer"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 验证首字节超时不能大于总超时
	if req.FirstByteTimeout > req.TotalTimeout {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "首字节超时不能大于总超时",
		})
		return
	}

	// 设置默认费用承担方
	if req.TimeoutCostBearer == "" {
		req.TimeoutCostBearer = "user"
	}

	// 验证费用承担方
	if req.TimeoutCostBearer != "user" && req.TimeoutCostBearer != "admin" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "超时费用承担方必须是 'user' 或 'admin'",
		})
		return
	}

	adminId := c.GetInt(ctxkey.Id)
	logger.Infof(c.Request.Context(), "Admin %d updating timeout config %d for user %d model %s, cost bearer: %s",
		adminId, id, req.UserId, req.ModelName, req.TimeoutCostBearer)

	// 先获取现有配置
	var config model.UserTimeoutConfig
	err = model.DB.Where("id = ?", id).First(&config).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "超时配置不存在",
			})
		} else {
			logger.Errorf(c.Request.Context(), "Failed to get timeout config: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "获取超时配置失败",
			})
		}
		return
	}

	// 更新配置
	config.UserId = req.UserId
	config.ModelName = req.ModelName
	config.FirstByteTimeout = req.FirstByteTimeout
	config.TotalTimeout = req.TotalTimeout
	config.TpsThreshold = req.TpsThreshold
	config.TimeoutCostBearer = req.TimeoutCostBearer

	err = config.Update()
	if err != nil {
		logger.Errorf(c.Request.Context(), "Failed to update user timeout config: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "更新超时配置失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "超时配置更新成功",
	})
}

// DeleteUserTimeoutConfigByID 管理员按ID删除用户超时配置
func DeleteUserTimeoutConfigByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的配置ID",
		})
		return
	}

	adminId := c.GetInt(ctxkey.Id)
	logger.Infof(c.Request.Context(), "Admin %d deleting timeout config by ID %d", adminId, id)

	// 先获取配置信息，用于清除缓存
	var config model.UserTimeoutConfig
	err = model.DB.Where("id = ?", id).First(&config).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "超时配置不存在",
			})
		} else {
			logger.Errorf(c.Request.Context(), "Failed to get timeout config: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "获取超时配置失败",
			})
		}
		return
	}

	// 删除配置
	err = config.Delete()
	if err != nil {
		logger.Errorf(c.Request.Context(), "Failed to delete user timeout config: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "删除超时配置失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "超时配置删除成功",
	})
}

// LoadAllUserTimeoutConfigs 装载所有用户超时配置到内存缓存
func LoadAllUserTimeoutConfigs(c *gin.Context) {
	// 判断超时功能开关
	if !config.UserTimeoutEnabled {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "用户超时功能已关闭",
		})
		return
	}

	cnt, err := DoLoadAllUserTimeoutConfigs()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    cnt,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    cnt,
	})
}

// DoLoadAllUserTimeoutConfigs 执行加载所有用户超时配置到内存
func DoLoadAllUserTimeoutConfigs() (int, error) {
	// 查询所有启用的用户超时配置
	var timeoutConfigs []model.UserTimeoutConfig
	if err := model.DB.Where("enabled = ?", true).Find(&timeoutConfigs).Error; err != nil {
		return 0, err
	}

	// 清空现有缓存 - sync.Map需要逐个删除
	config.UserTimeoutConfigsMap.Range(func(key, value interface{}) bool {
		config.UserTimeoutConfigsMap.Delete(key)
		return true
	})

	// 加载配置到内存
	for _, timeoutConfig := range timeoutConfigs {
		key := fmt.Sprintf("%d:%s", timeoutConfig.UserId, timeoutConfig.ModelName)
		cacheConfig := &config.UserTimeoutConfig{
			UserId:            timeoutConfig.UserId,
			ModelName:         timeoutConfig.ModelName,
			FirstByteTimeout:  timeoutConfig.FirstByteTimeout,
			TotalTimeout:      timeoutConfig.TotalTimeout,
			TpsThreshold:      timeoutConfig.TpsThreshold,
			TimeoutCostBearer: timeoutConfig.TimeoutCostBearer,
		}
		config.UserTimeoutConfigsMap.Store(key, cacheConfig)
	}

	return len(timeoutConfigs), nil
}

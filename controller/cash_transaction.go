package controller

import (
	"encoding/json"
	"github.com/songquanpeng/one-api/common/config"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/model"
)

func GetAllCashTransactions(c *gin.Context) {
	p, _ := strconv.Atoi(c.Query("p"))
	if p < 0 {
		p = 0
	}
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}
	userId, _ := strconv.Atoi(c.Query("user_id"))
	transactionType := c.Query("type")

	transactions, err := model.GetCashTransactions(p*pageSize, pageSize, userId, transactionType)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    transactions,
	})
}

func CountCashTransactions(c *gin.Context) {
	userId, _ := strconv.Atoi(c.Query("user_id"))
	transactionType := c.Query("type")

	count, err := model.CountCashTransactions(userId, transactionType)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    0,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"count": count,
		},
	})
}

func GetCashTransaction(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	transaction, err := model.GetCashTransactionById(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    transaction,
	})
}

func CreateCashTransaction(c *gin.Context) {
	var transaction model.CashTransaction
	err := json.NewDecoder(c.Request.Body).Decode(&transaction)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}

	err = model.CreateCashTransaction(&transaction)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "交易记录创建成功",
		"data":    transaction,
	})
}

func UpdateCashTransactionStatus(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的ID",
		})
		return
	}

	var req struct {
		Status string `json:"status"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}

	err = model.UpdateCashTransactionStatus(id, req.Status)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "交易状态更新成功",
	})
}

func GetUserBalance(c *gin.Context) {
	userId, err := strconv.Atoi(c.Param("user_id"))
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的用户ID",
		})
		return
	}

	balance, err := model.GetUserBalance(userId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"balance": balance,
		},
	})
}

func GetAgencyCommissions(c *gin.Context) {
	agencyId := c.GetInt("id")
	if agencyId == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的代理商ID",
		})
		return
	}
	p, _ := strconv.Atoi(c.Query("p"))
	if p < 0 {
		p = 0
	}
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}

	commissions, err := model.GetAgencyCommissions(agencyId, p*pageSize, pageSize)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    commissions,
	})
}

func CountAgencyCommissions(c *gin.Context) {
	agencyId, err := strconv.Atoi(c.Param("agency_id"))
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的代理商ID",
		})
		return
	}

	count, err := model.CountAgencyCommissions(agencyId)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    0,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"count": count,
		},
	})
}

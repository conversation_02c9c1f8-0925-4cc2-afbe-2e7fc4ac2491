package controller

import (
	"encoding/json"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
)

var tips = "Redis未开启。请检查配置文件中的SYNC_FREQUENCY是否配置了数字（例如：SYNC_FREQUENCY=60）" +
	"且REDIS_CONN_STRING是否正确配置（例如：REDIS_CONN_STRING=redis://redis）。" +
	"必须正确配置这两项才能开启Redis功能。"

// UAAnalysisResult 用于存储UA分析结果
type UAAnalysisResult struct {
	UserAgent  string `json:"user_agent"`
	Source     string `json:"source"`
	AccessTime string `json:"access_time"`
}

// ClientInfoAnalysisResult 用于存储客户端信息分析结果
type ClientInfoAnalysisResult struct {
	UserAgent      string    `json:"user_agent"`
	IP             string    `json:"ip"`
	RemoteAddr     string    `json:"remote_addr"`
	CFConnectingIP string    `json:"cf_connecting_ip"`
	Method         string    `json:"method"`
	Host           string    `json:"host"`
	Referer        string    `json:"referer"`
	Source         string    `json:"source"`
	AccessTime     time.Time `json:"access_time"`
}

func GetRedisKeys(c *gin.Context) {
	// 首先检查Redis是否启用
	if !common.RedisEnabled {
		c.JSON(200, gin.H{
			"success": false,
			"message": tips,
			"data":    []gin.H{},
		})
		return
	}
	// 获取查询参数
	current, _ := strconv.Atoi(c.DefaultQuery("current", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	keyFilter := c.DefaultQuery("key", "*")
	typeFilter := c.DefaultQuery("type", "")
	sizeMin, _ := strconv.ParseInt(c.DefaultQuery("sizeMin", "0"), 10, 64)
	sizeMax, _ := strconv.ParseInt(c.DefaultQuery("sizeMax", "-1"), 10, 64)
	ttlMin, _ := strconv.ParseFloat(c.DefaultQuery("ttlMin", "-1"), 64)
	ttlMax, _ := strconv.ParseFloat(c.DefaultQuery("ttlMax", "-1"), 64)

	// 如果没有指定过滤器，使用通配符
	if keyFilter == "" {
		keyFilter = "*"
	} else if !strings.Contains(keyFilter, "*") {
		// 如果提供的过滤器不包含通配符，我们在两端添加通配符
		keyFilter = "*" + keyFilter + "*"
	}

	// 获取所有匹配的键
	keys, err := common.RDB.Keys(c, keyFilter).Result()
	if err != nil {
		c.JSON(200, gin.H{
			"success": false,
			"message": "获取 Redis 键列表失败：" + err.Error(),
			"data":    []gin.H{},
			"total":   0,
		})
		return
	}

	// 过滤和构造返回的数据结构
	redisKeys := make([]gin.H, 0)
	for _, key := range keys {
		keyType, err := common.RDB.Type(c, key).Result()
		if err != nil || (typeFilter != "" && keyType != typeFilter) {
			continue
		}

		size, err := common.RDB.MemoryUsage(c, key).Result()
		if err != nil || (sizeMax != -1 && (size < sizeMin || size > sizeMax)) {
			continue
		}

		ttl, err := common.RDB.TTL(c, key).Result()
		if err != nil {
			ttl = -1
		}
		ttlSeconds := ttl.Seconds()
		if (ttlMin != -1 && ttlSeconds < ttlMin) || (ttlMax != -1 && ttlSeconds > ttlMax) {
			continue
		}

		redisKeys = append(redisKeys, gin.H{
			"key":  key,
			"type": keyType,
			"size": size,
			"ttl":  ttlSeconds,
		})
	}

	// 计算总数和分页
	total := len(redisKeys)
	start := (current - 1) * pageSize
	end := start + pageSize
	if start >= total {
		// 如果起始索引超出范围，返回空数组
		c.JSON(200, gin.H{
			"success": true,
			"message": "",
			"data":    []gin.H{},
			"total":   total,
		})
		return
	}
	if end > total {
		end = total
	}
	paginatedKeys := redisKeys[start:end]

	c.JSON(200, gin.H{
		"success": true,
		"message": "",
		"data":    paginatedKeys,
		"total":   total,
	})
}

func SearchRedisKeys(c *gin.Context) {
	// 首先检查Redis是否启用
	if !common.RedisEnabled {
		c.JSON(200, gin.H{
			"success": false,
			"message": tips,
			"data":    []gin.H{},
		})
		return
	}
	prefix := c.Param("prefix")
	current, _ := strconv.Atoi(c.DefaultQuery("current", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))

	keys, err := common.RDB.Keys(c, prefix+"*").Result()
	if err != nil {
		c.JSON(200, gin.H{
			"success": false,
			"message": "搜索键失败：" + err.Error(),
			"data":    []gin.H{},
			"total":   0,
		})
		return
	}

	// 计算总数和分页
	total := len(keys)
	start := (current - 1) * pageSize
	end := start + pageSize
	if start >= total {
		// 如果起始索引超出范围，返回空数组
		c.JSON(200, gin.H{
			"success": true,
			"message": "",
			"data":    []gin.H{},
			"total":   total,
		})
		return
	}
	if end > total {
		end = total
	}
	paginatedKeys := keys[start:end]

	// 构造返回的数据结构
	redisKeys := make([]gin.H, 0, len(paginatedKeys))
	for _, key := range paginatedKeys {
		keyType, err := common.RDB.Type(c, key).Result()
		if err != nil {
			keyType = "unknown"
		}
		size, err := common.RDB.MemoryUsage(c, key).Result()
		if err != nil {
			size = 0
		}
		ttl, err := common.RDB.TTL(c, key).Result()
		if err != nil {
			ttl = -1
		}
		redisKeys = append(redisKeys, gin.H{
			"key":  key,
			"type": keyType,
			"size": size,
			"ttl":  ttl.Seconds(),
		})
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "",
		"data":    redisKeys,
		"total":   total,
	})
}

func GetRedisValue(c *gin.Context) {
	// 首先检查Redis是否启用
	if !common.RedisEnabled {
		c.JSON(200, gin.H{
			"success": false,
			"message": tips,
			"data":    []gin.H{},
		})
		return
	}
	var params struct {
		Key string `json:"key" binding:"required"`
	}

	if err := c.ShouldBindJSON(&params); err != nil {
		c.JSON(200, gin.H{
			"success": false,
			"message": "参数错误：" + err.Error(),
		})
		return
	}

	// 首先获取键的类型
	keyType, err := common.RDB.Type(c, params.Key).Result()
	if err != nil {
		c.JSON(200, gin.H{
			"success": false,
			"message": "获取键类型失败：" + err.Error(),
		})
		return
	}

	var value interface{}

	switch keyType {
	case "string":
		value, err = common.RDB.Get(c, params.Key).Result()
	case "list":
		value, err = common.RDB.LRange(c, params.Key, 0, -1).Result()
	case "set":
		value, err = common.RDB.SMembers(c, params.Key).Result()
	case "zset":
		value, err = common.RDB.ZRangeWithScores(c, params.Key, 0, -1).Result()
	case "hash":
		value, err = common.RDB.HGetAll(c, params.Key).Result()
	default:
		c.JSON(200, gin.H{
			"success": false,
			"message": "不支持的数据类型：" + keyType,
		})
		return
	}

	if err != nil {
		c.JSON(200, gin.H{
			"success": false,
			"message": "获取键值失败：" + err.Error(),
		})
		return
	}

	// 如果是列表类型，解析每个元素中的JSON数据
	if keyType == "list" {
		if listValues, ok := value.([]string); ok {
			var parsedValues []interface{}
			for _, item := range listValues {
				var parsedItem interface{}
				if err := json.Unmarshal([]byte(item), &parsedItem); err == nil {
					parsedValues = append(parsedValues, parsedItem)
				} else {
					// 如果解析失败，使用原始字符串
					parsedValues = append(parsedValues, item)
				}
			}
			value = parsedValues
		}
	}

	c.JSON(200, gin.H{
		"success": true,
		"data": gin.H{
			"type":  keyType,
			"value": value,
		},
	})
}

func DeleteRedisKey(c *gin.Context) {
	// 首先检查Redis是否启用
	if !common.RedisEnabled {
		c.JSON(200, gin.H{
			"success": false,
			"message": tips,
		})
		return
	}
	var params struct {
		Key string `json:"key" binding:"required"`
	}

	if err := c.ShouldBindJSON(&params); err != nil {
		c.JSON(200, gin.H{
			"success": false,
			"message": "参数错误：" + err.Error(),
		})
		return
	}

	_, err := common.RDB.Del(c, params.Key).Result()
	if err != nil {
		c.JSON(200, gin.H{
			"success": false,
			"message": "删除键失败：" + err.Error(),
		})
		return
	}
	c.JSON(200, gin.H{
		"success": true,
		"message": "键删除成功",
	})
}

// GetUserAgentAnalysis 获取并分析特定URL的访问记录
func GetUserAgentAnalysis(c *gin.Context) {
	// 首先检查Redis是否启用
	if !common.RedisEnabled {
		c.JSON(200, gin.H{
			"success": false,
			"message": tips,
			"data":    []ClientInfoAnalysisResult{},
		})
		return
	}

	var params struct {
		Key string `json:"key" binding:"required"`
	}

	if err := c.ShouldBindJSON(&params); err != nil {
		c.JSON(200, gin.H{
			"success": false,
			"message": "参数错误：" + err.Error(),
			"data":    []ClientInfoAnalysisResult{},
		})
		return
	}

	// 从Redis获取所有记录
	values, err := common.RDB.HGetAll(c, params.Key).Result()
	if err != nil {
		c.JSON(200, gin.H{
			"success": false,
			"message": "获取访问记录失败：" + err.Error() +
				"。请确认Redis连接正常，且配置正确。",
			"data": []ClientInfoAnalysisResult{},
		})
		return
	}

	// 如果没有数据
	if len(values) == 0 {
		c.JSON(200, gin.H{
			"success": true,
			"message": "暂无访问记录",
			"data":    []ClientInfoAnalysisResult{},
		})
		return
	}

	// 创建一个包含时间戳的键值对切片
	type timeEntry struct {
		timestamp int64 // 使用纳秒级时间戳
		value     string
	}
	entries := make([]timeEntry, 0, len(values))

	// 解析时间戳（field名）并存储
	for field, value := range values {
		// 由于我们在存储时使用的是 time.Now().UnixNano()
		// 所以这里直接解析为纳秒级时间戳
		timestamp, err := strconv.ParseInt(field, 10, 64)
		if err != nil {
			continue
		}
		entries = append(entries, timeEntry{timestamp: timestamp, value: value})
	}

	// 按纳秒级时间戳排序
	sort.SliceStable(entries, func(i, j int) bool {
		return entries[i].timestamp < entries[j].timestamp
	})

	// 分析结果
	results := make([]ClientInfoAnalysisResult, 0)
	for _, entry := range entries {
		var clientInfo map[string]interface{}
		if err := json.Unmarshal([]byte(entry.value), &clientInfo); err != nil {
			continue
		}

		// 获取时间戳并转换为time.Time
		timestamp, ok := clientInfo["timestamp"].(float64)
		if !ok {
			continue
		}
		accessTime := time.Unix(int64(timestamp), 0)

		// 分析UA来源
		ua := clientInfo["user_agent"].(string)
		source := analyzeUserAgent(ua)

		results = append(results, ClientInfoAnalysisResult{
			UserAgent:      ua,
			IP:             clientInfo["ip"].(string),
			RemoteAddr:     clientInfo["remote_addr"].(string),
			CFConnectingIP: clientInfo["cf_connecting_ip"].(string),
			Method:         clientInfo["method"].(string),
			Host:           clientInfo["host"].(string),
			Referer:        clientInfo["referer"].(string),
			Source:         source,
			AccessTime:     accessTime,
		})
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "",
		"data":    results,
	})
}

// analyzeUserAgent 分析UA字符串并返回来源
func analyzeUserAgent(ua string) string {
	switch {
	case strings.Contains(ua, "Go-http-client"):
		return "基于go语言开发的中转客户端"
	case strings.Contains(ua, "IPS"):
		return "Azure"
	case strings.Contains(ua, "OpenAI"):
		return "OpenAI"
	default:
		return "未知"
	}
}

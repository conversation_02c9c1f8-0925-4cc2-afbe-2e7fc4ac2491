package controller

import (
	"bytes"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/model"
)

func UploadFile(c *gin.Context) {
	// 单文件
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 检查文件大小
	if file.Size > 10<<20 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "单个文件大小不能超过10MB",
		})
		return
	}

	// 如果启用代理模式，则转发到第三方存储服务
	if config.FileSystemProxyModeEnabled && config.FileSystemProxyURL != "" {
		handleProxyUpload(c, file)
		return
	}

	// 计算文件hash
	f, err := file.Open()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "无法读取文件",
		})
		return
	}
	defer f.Close()

	h := sha256.New()
	if _, err := io.Copy(h, f); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "计算文件hash失败",
		})
		return
	}
	fileHash := hex.EncodeToString(h.Sum(nil))

	// 检查文件是否已存在（包括已删除的文件）
	existingFile, err := model.GetFileByHashIncludeDeleted(fileHash)
	if err == nil {
		// 文件已存在
		if existingFile.Status == 0 {
			// 如果文件被标记为删除，则恢复它
			existingFile.Status = 1
			existingFile.UploaderID = c.GetInt64("id")
			existingFile.UploadTime = time.Now().Unix()
			existingFile.OriginalName = file.Filename
			if err := existingFile.Update(); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{
					"success": false,
					"message": "恢复文件记录失败：" + err.Error(),
				})
				return
			}
		}

		// 无论文件是否被删除，都返回现有文件的URL
		pathParts := strings.Split(existingFile.FilePath, "/")
		if len(pathParts) >= 2 {
			datePath := pathParts[len(pathParts)-2]
			fileName := pathParts[len(pathParts)-1]
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": "文件已存在",
				"data":    existingFile,
				"url": fmt.Sprintf("%s/fileSystem/download/%s/%s",
					config.FileSystemServerAddress,
					datePath,
					url.PathEscape(fileName)),
			})
			return
		}
	}

	// 生成文件存储的目录：./uploads/20230405/
	now := time.Now()
	pathPrefix := now.Format("20060102")
	dir := filepath.Join("./uploads", pathPrefix)
	if err := os.MkdirAll(dir, os.ModePerm); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "创建目录失败",
		})
		return
	}

	// 使用hash作为文件名
	fileExtension := filepath.Ext(file.Filename)
	fileName := fileHash + fileExtension
	filePath := filepath.Join(dir, fileName)

	// 重新打开文件用于保存
	f, err = file.Open()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "无法读取文件",
		})
		return
	}
	defer f.Close()

	// 创建目标文件
	dst, err := os.Create(filePath)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "创建文件失败",
		})
		return
	}
	defer dst.Close()

	// 保存文件
	if _, err = io.Copy(dst, f); err != nil {
		os.Remove(filePath) // 清理已创建的文件
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "保存文件失败",
		})
		return
	}

	// 创建文件元数据
	metadata := &model.FileMetadata{
		FileHash:       fileHash,
		OriginalName:   file.Filename,
		FilePath:       filePath,
		FileSize:       file.Size,
		MimeType:       file.Header.Get("Content-Type"),
		UploadTime:     now.Unix(),
		ReferenceCount: 1,
		UploaderID:     c.GetInt64("id"),
		Status:         1,
	}

	if err := metadata.Create(); err != nil {
		os.Remove(filePath) // 清理已保存的文件
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "保存文件元数据失败",
		})
		return
	}

	// 记录日志
	model.RecordLog(c.Request.Context(), c.GetInt("id"), model.LogTypeOperation, "上传文件："+file.Filename)

	// 构建下载URL，使用存储路径
	downloadURL := fmt.Sprintf("%s/fileSystem/download/%s/%s",
		config.FileSystemServerAddress,
		pathPrefix,               // 使用日期作为路径
		url.PathEscape(fileName)) // 使用哈希文件名

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "文件上传成功",
		"data":    metadata,
		"url":     downloadURL,
	})
}

// 处理代理上传模式
func handleProxyUpload(c *gin.Context, file *multipart.FileHeader) {
	userId := c.GetInt64("id")

	// 打开源文件
	f, err := file.Open()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "无法读取文件",
		})
		return
	}
	defer f.Close()

	// 创建请求
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// 创建表单字段，使用配置的字段名
	uploadField := config.FileSystemProxyUploadFieldName
	if uploadField == "" {
		uploadField = "file" // 默认值
	}

	part, err := writer.CreateFormFile(uploadField, file.Filename)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "创建表单失败：" + err.Error(),
		})
		return
	}

	// 复制文件内容
	if _, err = io.Copy(part, f); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "处理文件失败：" + err.Error(),
		})
		return
	}

	// 关闭multipart writer
	writer.Close()

	// 创建请求
	req, err := http.NewRequest(config.FileSystemProxyMethod, config.FileSystemProxyURL, body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "创建代理请求失败：" + err.Error(),
		})
		return
	}

	// 设置Content-Type
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// 添加认证信息
	if config.FileSystemProxyAuthEnabled && config.FileSystemProxyAuthValue != "" {
		switch config.FileSystemProxyAuthType {
		case "bearer":
			req.Header.Set("Authorization", "Bearer "+config.FileSystemProxyAuthValue)
		case "basic":
			req.Header.Set("Authorization", "Basic "+config.FileSystemProxyAuthValue)
		case "custom":
			req.Header.Set("Authorization", config.FileSystemProxyAuthValue)
		}
	}

	// 添加自定义请求头
	if config.FileSystemProxyHeaders != "" {
		var headers map[string]string
		if err := json.Unmarshal([]byte(config.FileSystemProxyHeaders), &headers); err == nil {
			for k, v := range headers {
				req.Header.Set(k, v)
			}
		}
	}

	// 发送请求
	client := &http.Client{
		Timeout: 30 * time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "代理请求失败：" + err.Error(),
		})
		return
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "读取代理响应失败：" + err.Error(),
		})
		return
	}

	// 记录日志
	model.RecordLog(c.Request.Context(), int(userId), model.LogTypeOperation, "通过代理上传文件："+file.Filename)

	// 如果代理请求不成功
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": fmt.Sprintf("代理服务响应错误，状态码：%d", resp.StatusCode),
			"data":    string(respBody),
		})
		return
	}

	// 返回代理的响应（原样返回）
	c.Data(resp.StatusCode, resp.Header.Get("Content-Type"), respBody)
}

func DownloadFile(c *gin.Context) {
	path := c.Param("path")
	fileName := c.Param("fileName")

	// 构建完整的文件路径
	filePath := filepath.Join("uploads", path, fileName)

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "文件不存在",
		})
		return
	}

	// 从数据库获取文件元数据
	metadata, err := model.GetFileByPath(filePath)
	if err == nil {
		// 如果找到元数据，检查文件状态
		if metadata.Status == 0 {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "文件已删除",
			})
			return
		}
		// 使用原始文件名和MIME类型
		c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", url.QueryEscape(metadata.OriginalName)))
		c.Header("Content-Type", metadata.MimeType)
	} else {
		// 如果没有找到元数据，使用URL中的文件名
		c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", url.QueryEscape(fileName)))
	}

	// 发送文件
	c.File(filePath)
}

func DownloadMJImage(c *gin.Context) {
	userId := c.Param("userId")
	filePath := c.Param("filePath")

	// 构建完整的文件路径
	fullPath := filepath.Join("storage/mj", userId, filePath)

	// 检查文件是否存在
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "file not found",
		})
		return
	}

	// 设置响应头
	c.Header("Content-Type", "image/png")
	c.File(fullPath)
}

func DownloadGptImage(c *gin.Context) {
	userId := c.Param("userId")
	filePath := c.Param("filePath")

	// 构建完整的文件路径
	fullPath := filepath.Join("storage/gpt-image", userId, filePath)

	// 检查文件是否存在
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "file not found",
		})
		return
	}

	// 设置响应头
	c.Header("Content-Type", "image/png")
	c.File(fullPath)
}

func DownloadResponseImage(c *gin.Context) {
	userId := c.Param("userId")
	filePath := c.Param("filePath")

	// 构建完整的文件路径
	fullPath := filepath.Join("storage/response_images", userId, filePath)

	// 检查文件是否存在
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "file not found",
		})
		return
	}

	// 根据文件扩展名设置正确的Content-Type
	contentType := getImageContentType(fullPath)
	c.Header("Content-Type", contentType)
	c.File(fullPath)
}

func DownloadVideo(c *gin.Context) {
	filePath := c.Param("filePath")

	// 构建完整的文件路径
	fullPath := filepath.Join("storage/videos", filePath)

	// 检查文件是否存在
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "video file not found",
		})
		return
	}

	// 根据文件扩展名设置正确的Content-Type
	contentType := getVideoContentType(fullPath)
	c.Header("Content-Type", contentType)
	c.File(fullPath)
}

// isImage 检查文件扩展名是否为图片
func isImage(filename string) bool {
	// 这里简单地检查文件扩展名
	switch filepath.Ext(filename) {
	case ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp":
		return true
	default:
		return false
	}
}

// 新增函数：根据文件扩展名返回对应的 Content-Type
func getImageContentType(filename string) string {
	ext := filepath.Ext(filename)
	switch ext {
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".png":
		return "image/png"
	case ".gif":
		return "image/gif"
	case ".bmp":
		return "image/bmp"
	case ".webp":
		return "image/webp"
	default:
		return "application/octet-stream"
	}
}

// getVideoContentType 根据文件扩展名获取视频的Content-Type
func getVideoContentType(filePath string) string {
	ext := strings.ToLower(filepath.Ext(filePath))
	switch ext {
	case ".mp4":
		return "video/mp4"
	case ".webm":
		return "video/webm"
	case ".avi":
		return "video/avi"
	case ".mov":
		return "video/quicktime"
	case ".wmv":
		return "video/x-ms-wmv"
	case ".flv":
		return "video/x-flv"
	case ".mkv":
		return "video/x-matroska"
	default:
		return "video/mp4" // 默认为MP4
	}
}

func getFileHash(filePath string) string {
	// 实现文件哈希计算逻辑
	// 这里可以使用任何哈希算法（如SHA-256）来计算文件的哈希值
	// 这里简单地返回文件路径作为哈希值
	return filePath
}

func GetFileList(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("p", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))
	search := c.Query("search")
	mimeType := c.Query("mimeType")
	sortField := c.Query("sortField")
	sortOrder := c.Query("sortOrder")

	params := model.FileListParams{
		Page:      page,
		PageSize:  pageSize,
		Search:    search,
		MimeType:  mimeType,
		SortField: sortField,
		SortOrder: sortOrder,
	}

	// 获取用户ID和管理员状态
	userId := c.GetInt("id")
	isAdmin := model.IsAdmin(userId)

	// 获取文件列表
	files, total, err := model.GetFileList(params, int64(userId), isAdmin)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取文件列表失败：" + err.Error(),
		})
		return
	}

	// 构建响应数据
	var fileList []gin.H
	for _, file := range files {
		// 从文件路径构建URL
		pathParts := strings.Split(file.FilePath, "/")
		var downloadUrl string
		if len(pathParts) >= 2 {
			datePath := pathParts[len(pathParts)-2]
			fileName := pathParts[len(pathParts)-1]
			downloadUrl = fmt.Sprintf("%s/fileSystem/download/%s/%s",
				config.FileSystemServerAddress,
				datePath,
				url.PathEscape(fileName))
		}

		fileList = append(fileList, gin.H{
			"id":              file.ID,
			"file_hash":       file.FileHash,
			"original_name":   file.OriginalName,
			"file_size":       file.FileSize,
			"mime_type":       file.MimeType,
			"upload_time":     file.UploadTime,
			"reference_count": file.ReferenceCount,
			"uploader_id":     file.UploaderID,
			"url":             downloadUrl,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取文件列表成功",
		"data": gin.H{
			"items": fileList,
			"total": total,
		},
	})
}

func DeleteFile(c *gin.Context) {
	// 获取文件ID
	fileID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的文件ID",
		})
		return
	}

	// 获取用户ID和管理员状态
	userID := c.GetInt64("id")
	isAdmin := model.IsAdmin(int(userID))

	// 获取文件信息
	file, err := model.GetFileByID(fileID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "文件不存在",
		})
		return
	}

	// 检查权限：只有管理员或文件上传者可以删除
	if !isAdmin && file.UploaderID != userID {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "没有权限删除此文件",
		})
		return
	}

	// 软删除文件（更新状态）
	err = model.DeleteFile(fileID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "删除文件失败：" + err.Error(),
		})
		return
	}

	// 记录操作日志
	model.RecordLog(c.Request.Context(), int(userID), model.LogTypeOperation, "删除文件："+file.OriginalName)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "文件删除成功",
	})
}

func PermanentDeleteFile(c *gin.Context) {
	// 获取文件ID
	fileID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的文件ID",
		})
		return
	}

	// 获取用户ID和管理员状态
	userID := c.GetInt64("id")
	isAdmin := model.IsAdmin(int(userID))

	// 获取文件信息
	file, err := model.GetFileByID(fileID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "文件不存在",
		})
		return
	}

	// 检查权限：只有管理员或文件上传者可以删除
	if !isAdmin && file.UploaderID != userID {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "没有权限删除此文件",
		})
		return
	}

	// 删除物理文件
	if err := os.Remove(file.FilePath); err != nil && !os.IsNotExist(err) {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "删除物理文件失败：" + err.Error(),
		})
		return
	}

	// 从数据库中删除记录
	if err := model.PermanentDeleteFile(fileID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "删除数据库记录失败：" + err.Error(),
		})
		return
	}

	// 记录操作日志
	model.RecordLog(c.Request.Context(), int(userID), model.LogTypeOperation, "彻底删除文件："+file.OriginalName)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "文件已彻底删除",
	})
}

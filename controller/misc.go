package controller

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/samber/lo"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/client"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/license"
	billingratio "github.com/songquanpeng/one-api/relay/billing/ratio"

	"regexp"
	"strconv"

	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/i18n"
	"github.com/songquanpeng/one-api/common/message"
	"github.com/songquanpeng/one-api/model"

	"github.com/gin-gonic/gin"
)

//如果修改了此处，请同时修改前端的接口定义（web/${theme}/src/context/Status/reducer.tsx）

func GetStatus(c *gin.Context) {
	// 获取当前域名
	host := c.Request.Host
	// 根据不同代理商的域名，返回不同的首页内容
	agency, err := model.GetAgencyByDomain(host)
	if err == nil && agency != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "",
			"data": gin.H{
				"version":                       common.Version,
				"instanceId":                    config.LicenseInstanceId,
				"start_time":                    common.StartTime,
				"email_verification":            config.EmailVerificationEnabled,
				"github_oauth":                  config.GitHubOAuthEnabled,
				"github_client_id":              config.GitHubClientId,
				"GoogleOAuthEnabled":            config.GoogleOAuthEnabled,
				"GoogleClientId":                config.GoogleClientId,
				"lark_client_id":                config.LarkClientId,
				"system_name":                   agency.Name,
				"logo":                          agency.Logo,
				"footer_html":                   config.Footer,
				"header_script":                 config.HeaderScript,
				"wechat_qrcode":                 config.WeChatAccountQRCodeImageURL,
				"wechat_login":                  config.WeChatAuthEnabled,
				"server_address":                agency.ServerAddress,
				"file_system_server_address":    agency.FileSystemServerAddress,
				"SwitchUIEnabled":               config.SwitchUIEnabled,
				"OnlineTopupEnabled":            config.OnlineTopupEnabled,
				"price":                         config.Price,
				"customUsdtRate":                config.CustomUsdtRate,
				"customPayPalUsdRate":           config.CustomPayPalUsdRate,
				"PayPalMinimumFee":              config.PayPalMinimumFee,
				"CustomEthRate":                 config.CustomEthRate,
				"MaxTopUpLimit":                 config.MaxTopUpLimit,
				"QuotaExpireEnabled":            config.QuotaExpireEnabled,
				"QuotaExpireDays":               config.QuotaExpireDays,
				"turnstile_check":               config.TurnstileCheckEnabled,
				"CaptchaCheckEnabled":           config.CaptchaCheckEnabled,
				"turnstile_site_key":            config.TurnstileSiteKey,
				"top_up_link":                   config.TopUpLink,
				"chat_link":                     config.ChatLink,
				"quota_per_unit":                config.QuotaPerUnit,
				"display_in_currency":           config.DisplayInCurrencyEnabled,
				"oidc":                          config.OidcEnabled,
				"oidc_client_id":                config.OidcClientId,
				"oidc_well_known":               config.OidcWellKnown,
				"oidc_authorization_endpoint":   config.OidcAuthorizationEndpoint,
				"oidc_token_endpoint":           config.OidcTokenEndpoint,
				"oidc_userinfo_endpoint":        config.OidcUserinfoEndpoint,
				"FloatButtonEnabled":            config.FloatButtonEnabled,
				"UnsubscribeEnabled":            config.UnsubscribeEnabled,
				"UserLogViewEnabled":            config.UserLogViewEnabled,
				"GuestQueryEnabled":             config.GuestQueryEnabled,
				"MidjourneyEnabled":             config.MidjourneyEnabled,
				"GuestChatPageEnabled":          config.GuestChatPageEnabled,
				"PptGenPageEnabled":             config.PptGenPageEnabled,
				"AgentMenuEnabled":              false,
				"DocumentInfo":                  config.DocumentInfo,
				"QqInfo":                        config.QqInfo,
				"WechatInfo":                    config.WechatInfo,
				"RegisterInfo":                  config.RegisterInfo,
				"DebugEnabled":                  config.DebugEnabled,
				"eise":                          config.EncryptImageServerEnabled,
				"CustomAppList":                 config.CustomAppList,
				"CustomThemeConfig":             config.CustomThemeConfig,
				"CustomDarkThemeConfig":         config.CustomDarkThemeConfig,
				"Theme":                         config.Theme,
				"LogDetailConsumeEnabled":       config.LogDetailConsumeEnabled,
				"ShellApiLogOptimizerEnabled":   common.ShellApiLogOptimizerEnabled,
				"CheckinEnabled":                config.CheckinEnabled,
				"CheckinQuota":                  config.CheckinQuota,
				"RegisterEnabled":               config.RegisterEnabled,
				"PasswordRegisterEnabled":       config.PasswordRegisterEnabled,
				"CustomVerificationTypesConfig": common.CustomVerificationTypesConfig,
				"SiteDescription":               config.SiteDescription,
				"SMSVerificationEnabled":        config.SMSVerificationEnabled,
				"SMSRegisterEnabled":            config.SMSRegisterEnabled,
				"SMSLoginEnabled":               config.SMSLoginEnabled,
				"TransferEnabled":               config.TransferEnabled,
				"LimitedAccessURL":              config.LimitedAccessURL,
				"LimitedAccessConfigs":          config.LimitedAccessConfigs,
				"LimitedAccessType":             config.LimitedAccessType,
				"TelegramOAuthEnabled":          config.TelegramOAuthEnabled,
				"TelegramBotName":               config.TelegramBotName,
				"CustomAvailablePayMethods":     config.CustomAvailablePayMethods,
				"CustomAvailablePayMethods2":    config.CustomAvailablePayMethods2,
				"NoticeVersion":                 config.NoticeVersion,
				"StatusPageUrl":                 config.StatusPageUrl,
				//"NavExtMenus":                   config.NavExtMenus,
				"NewHomeConf":                       agency.NewHomeConf,
				"PureHomePageEnabled":               config.PureHomePageEnabled,
				"TokenGroupChangeEnabled":           config.TokenGroupChangeEnabled,
				"XYHelperDBEnabled":                 config.XYHelperDBEnabled,
				"DataExportEnabled":                 config.DataExportEnabled,
				"DataExportInterval":                config.DataExportInterval,
				"DataExportDefaultTime":             config.DataExportDefaultTime,
				"DataExportDisplayEnabled":          config.DataExportDisplayEnabled,
				"AlipayFaceToFaceEnabled":           config.AlipayFaceToFaceEnabled,
				"SuixingpayEnabled":                 config.SuixingpayEnabled,
				"MidjourneyShowDerivedRatesEnabled": config.MidjourneyShowDerivedRatesEnabled,
			},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"version":                           common.Version,
			"instanceId":                        config.LicenseInstanceId,
			"start_time":                        common.StartTime,
			"email_verification":                config.EmailVerificationEnabled,
			"github_oauth":                      config.GitHubOAuthEnabled,
			"github_client_id":                  config.GitHubClientId,
			"GoogleOAuthEnabled":                config.GoogleOAuthEnabled,
			"GoogleClientId":                    config.GoogleClientId,
			"lark_client_id":                    config.LarkClientId,
			"system_name":                       config.SystemName,
			"logo":                              config.Logo,
			"footer_html":                       config.Footer,
			"header_script":                     config.HeaderScript,
			"wechat_qrcode":                     config.WeChatAccountQRCodeImageURL,
			"wechat_login":                      config.WeChatAuthEnabled,
			"server_address":                    config.ServerAddress,
			"file_system_server_address":        config.FileSystemServerAddress,
			"SwitchUIEnabled":                   config.SwitchUIEnabled,
			"OnlineTopupEnabled":                config.OnlineTopupEnabled,
			"price":                             config.Price,
			"customUsdtRate":                    config.CustomUsdtRate,
			"customPayPalUsdRate":               config.CustomPayPalUsdRate,
			"PayPalMinimumFee":                  config.PayPalMinimumFee,
			"CustomEthRate":                     config.CustomEthRate,
			"MaxTopUpLimit":                     config.MaxTopUpLimit,
			"QuotaExpireEnabled":                config.QuotaExpireEnabled,
			"QuotaExpireDays":                   config.QuotaExpireDays,
			"turnstile_check":                   config.TurnstileCheckEnabled,
			"CaptchaCheckEnabled":               config.CaptchaCheckEnabled,
			"turnstile_site_key":                config.TurnstileSiteKey,
			"top_up_link":                       config.TopUpLink,
			"chat_link":                         config.ChatLink,
			"quota_per_unit":                    config.QuotaPerUnit,
			"display_in_currency":               config.DisplayInCurrencyEnabled,
			"oidc":                              config.OidcEnabled,
			"oidc_client_id":                    config.OidcClientId,
			"oidc_well_known":                   config.OidcWellKnown,
			"oidc_authorization_endpoint":       config.OidcAuthorizationEndpoint,
			"oidc_token_endpoint":               config.OidcTokenEndpoint,
			"oidc_userinfo_endpoint":            config.OidcUserinfoEndpoint,
			"FloatButtonEnabled":                config.FloatButtonEnabled,
			"UnsubscribeEnabled":                config.UnsubscribeEnabled,
			"UserLogViewEnabled":                config.UserLogViewEnabled,
			"GuestQueryEnabled":                 config.GuestQueryEnabled,
			"MidjourneyEnabled":                 config.MidjourneyEnabled,
			"GuestChatPageEnabled":              config.GuestChatPageEnabled,
			"PptGenPageEnabled":                 config.PptGenPageEnabled,
			"AgentMenuEnabled":                  config.AgentMenuEnabled,
			"DocumentInfo":                      config.DocumentInfo,
			"QqInfo":                            config.QqInfo,
			"WechatInfo":                        config.WechatInfo,
			"RegisterInfo":                      config.RegisterInfo,
			"DebugEnabled":                      config.DebugEnabled,
			"eise":                              config.EncryptImageServerEnabled,
			"CustomAppList":                     config.CustomAppList,
			"CustomThemeConfig":                 config.CustomThemeConfig,
			"CustomDarkThemeConfig":             config.CustomDarkThemeConfig,
			"Theme":                             config.Theme,
			"LogDetailConsumeEnabled":           config.LogDetailConsumeEnabled,
			"ShellApiLogOptimizerEnabled":       common.ShellApiLogOptimizerEnabled,
			"CheckinEnabled":                    config.CheckinEnabled,
			"CheckinQuota":                      config.CheckinQuota,
			"RegisterEnabled":                   config.RegisterEnabled,
			"PasswordRegisterEnabled":           config.PasswordRegisterEnabled,
			"CustomVerificationTypesConfig":     common.CustomVerificationTypesConfig,
			"SiteDescription":                   config.SiteDescription,
			"SMSVerificationEnabled":            config.SMSVerificationEnabled,
			"SMSRegisterEnabled":                config.SMSRegisterEnabled,
			"SMSLoginEnabled":                   config.SMSLoginEnabled,
			"TransferEnabled":                   config.TransferEnabled,
			"LimitedAccessURL":                  config.LimitedAccessURL,
			"LimitedAccessConfigs":              config.LimitedAccessConfigs,
			"LimitedAccessType":                 config.LimitedAccessType,
			"TelegramOAuthEnabled":              config.TelegramOAuthEnabled,
			"TelegramBotName":                   config.TelegramBotName,
			"CustomAvailablePayMethods":         config.CustomAvailablePayMethods,
			"CustomAvailablePayMethods2":        config.CustomAvailablePayMethods2,
			"NoticeVersion":                     config.NoticeVersion,
			"StatusPageUrl":                     config.StatusPageUrl,
			"NavExtMenus":                       config.NavExtMenus,
			"NewHomeConf":                       config.NewHomeConf,
			"PureHomePageEnabled":               config.PureHomePageEnabled,
			"TokenGroupChangeEnabled":           config.TokenGroupChangeEnabled,
			"XYHelperDBEnabled":                 config.XYHelperDBEnabled,
			"DataExportEnabled":                 config.DataExportEnabled,
			"DataExportInterval":                config.DataExportInterval,
			"DataExportDefaultTime":             config.DataExportDefaultTime,
			"DataExportDisplayEnabled":          config.DataExportDisplayEnabled,
			"AlipayFaceToFaceEnabled":           config.AlipayFaceToFaceEnabled,
			"SuixingpayEnabled":                 config.SuixingpayEnabled,
			"MidjourneyShowDerivedRatesEnabled": config.MidjourneyShowDerivedRatesEnabled,
		},
	})
	return
}

func GetAdminStatus(c *gin.Context) {
	// 获取所有模型名称
	allModels, err := model.GetAllDistinctModels()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 获取所有已启用的模型名称
	enabledModels, err := model.GetAllEnabledDistinctModels()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"GroupColorMapping":      billingratio.GetGroupColorMappingMap(),
			"GroupRatio":             billingratio.GetGroupRatioMap(),
			"LastLicenseCheckedTime": license.LastLicenseCheckedTime,
			"ServerTime":             helper.GetTimestamp(),
			"OriginModelRatio":       billingratio.GetModelRatioMap(),
			"OriginCompletionRatio":  billingratio.GetCompletionRatioMap(),
			"OriginModelFixedPrice":  billingratio.GetModelFixedPriceMap(),
			"AllModels":              allModels,     // 所有模型
			"EnabledModels":          enabledModels, // 已启用的模型
		},
	})
	return
}

func GetNotice(c *gin.Context) {
	// 获取当前域名
	host := c.Request.Host
	// 根据不同代理商的域名，返回不同的首页内容
	agency, err := model.GetAgencyByDomain(host)
	if err == nil && agency != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "",
			"data":    agency.Notice,
		})
		return
	}
	config.OptionMapRWMutex.RLock()
	defer config.OptionMapRWMutex.RUnlock()
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    config.OptionMap["Notice"],
	})
	return
}

func GetAbout(c *gin.Context) {
	config.OptionMapRWMutex.RLock()
	defer config.OptionMapRWMutex.RUnlock()
	host := c.Request.Host
	// 根据不同代理商的域名，返回不同的首页内容
	agency, err := model.GetAgencyByDomain(host)
	if err == nil && agency != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "",
			"data":    agency.AboutContent,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    config.OptionMap["About"],
	})
	return
}

func GetMidjourney(c *gin.Context) {
	config.OptionMapRWMutex.RLock()
	defer config.OptionMapRWMutex.RUnlock()
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    config.OptionMap["Midjourney"],
	})
	return
}

func GetHomePageContent(c *gin.Context) {
	config.OptionMapRWMutex.RLock()
	defer config.OptionMapRWMutex.RUnlock()
	host := c.Request.Host
	// 根据不同代理商的域名，返回不同的首页内容
	agency, err := model.GetAgencyByDomain(host)
	if err == nil && agency != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "",
			"data":    agency.HomepageContent,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    config.OptionMap["HomePageContent"],
	})
	return
}

func normalizeEmail(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return email
	}

	localPart := parts[0]
	domain := parts[1]

	// 移除 '+' 及其后的内容
	if idx := strings.Index(localPart, "+"); idx != -1 {
		localPart = localPart[:idx]
	}

	// 移除所有 '.'
	localPart = strings.ReplaceAll(localPart, ".", "")

	return localPart + "@" + domain
}

func checkEmailForRegistration(c *gin.Context, email string) bool {
	if err := common.Validate.Var(email, "required,email"); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": i18n.Translate(c, "invalid_parameter"),
		})
		return true
	}

	if config.EmailDomainRestrictionEnabled {
		allowed := false
		for _, domain := range config.EmailDomainWhitelist {
			// 检查域名是否包含正则表达式特殊字符
			if strings.ContainsAny(domain, "*+?[]{}()\\") {
				// 转换通配符格式到正则表达式
				regexPattern := strings.ReplaceAll(domain, "*", ".*")
				regexPattern = strings.ReplaceAll(regexPattern, ".", "\\.")
				// 编译正则表达式
				r, err := regexp.Compile(regexPattern + "$")
				if err != nil {
					// 处理正则表达式编译错误
					c.JSON(http.StatusOK, gin.H{
						"success": false,
						"message": "处理正则表达式编译错误，请管理员检查邮箱白名单配置",
					})
					return true
				}
				if r.MatchString(email) {
					allowed = true
					break
				}
			} else {
				// 普通字符串匹配
				if strings.HasSuffix(email, "@"+domain) {
					allowed = true
					break
				}
			}
		}
		// 此正则表达式检查 '@' 之前是否含有 '+' 或 '.'
		pattern := `^[a-zA-Z0-9._%+-]+[+.][a-zA-Z0-9._%+-]*@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$`
		matched, _ := regexp.MatchString(pattern, email)
		if matched {
			fmt.Println("电子邮件地址中包含特殊字符 '+' 或 '.'")
			allowed = false
			if !allowed {
				c.JSON(http.StatusOK, gin.H{
					"success": false,
					"message": "您的邮箱地址被系统判定为临时邮箱，请使用其他邮箱或联系管理员注册",
				})
				return true
			}
		}

		// 这里逻辑有问题,应该是@qq.com结尾的邮箱才走这个逻辑,要不然其他白名单中的邮箱都会被拦截
		if config.EmailDomainQQNumberOnlyEnabled && strings.HasSuffix(email, "@qq.com") {
			//此正则表达式检查 '@qq.com' 之前是否为纯数字
			pattern2 := `^[0-9]+@qq.com$`
			matched2, _ := regexp.MatchString(pattern2, email)
			if !matched2 {
				fmt.Println("邮箱地址不为纯数字QQ邮箱", email)
				allowed = false
				if !allowed {
					c.JSON(http.StatusOK, gin.H{
						"success": false,
						"message": "您的邮箱地址被系统判定为临时邮箱，请使用纯数字QQ邮箱",
					})
					return true
				}
			}
		}

		if !allowed {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "您的邮箱域名不在白名单中，请使用其他邮箱",
			})
			return true
		}
	}

	// 替换原有的特殊字符检测逻辑
	normalizedNewEmail := normalizeEmail(email)

	// 获取所有已存在的邮箱并进行规范化比对
	existingEmails := model.GetAllEmails()
	for _, existingEmail := range existingEmails {
		if normalizeEmail(existingEmail) == normalizedNewEmail {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "邮箱地址已存在或您的邮箱地址被系统判定为临时邮箱，请使用其他邮箱或联系管理员注册",
			})
			return true
		}
	}

	return false
}

func SendEmailVerification(c *gin.Context) {
	sendMailSystemName := config.SystemName
	agency, err := model.GetAgencyByDomain(c.Request.Host)
	if err == nil && agency != nil {
		sendMailSystemName = agency.Name
	}

	email := c.Query("email")
	if checkEmailForRegistration(c, email) {
		return
	}
	code := common.GenerateVerificationCodeWithNumber(6) //这里改成纯数字验证码了
	common.RegisterVerificationCodeWithKey(email, code, common.EmailVerificationPurpose)
	subject := fmt.Sprintf("%s 邮箱验证邮件", config.SystemName)
	content := message.EmailTemplate(
		subject,
		fmt.Sprintf(`
			<p>您好！</p>
			<p>您正在进行 %s 邮箱验证。</p>
			<p>您的验证码为：</p>
			<p style="font-size: 24px; font-weight: bold; color: #333; background-color: #f8f8f8; padding: 10px; text-align: center; border-radius: 4px;">%s</p>
			<p style="color: #666;">验证码 %d 分钟内有效，如果不是本人操作，请忽略。</p>
			<p>%s 团队</p>
		`, sendMailSystemName, code, common.VerificationValidMinutes, sendMailSystemName),
	)
	err = message.SendEmail(subject, email, content)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

func SendEmailWithRegistrations(c *gin.Context) {
	isAgency := false
	systemNameForSendMail := config.SystemName
	agency, err := model.GetAgencyByDomain(c.Request.Host)
	if err == nil && agency != nil {
		isAgency = true
		systemNameForSendMail = agency.Name
	}

	email := c.Query("email")
	// 邀请码
	affCode := c.Query("affCode")
	if checkEmailForRegistration(c, email) {
		return
	}

	code := common.GenerateVerificationCodeWithNumber(6) //这里改成纯数字验证码了
	common.RegisterVerificationCodeWithKey(email, code, common.EmailVerificationPurpose)
	// 拼接注册链接地址
	link := fmt.Sprintf("%s/api/user/register?email=%s&code=%s&aff_code=%s", lo.If(isAgency, fmt.Sprintf("https://%s", agency.Domain)).Else(config.ServerAddress), email, code, affCode)
	subject := fmt.Sprintf("%s 邮箱验证邮件", config.SystemName)
	content := message.EmailTemplate(
		subject,
		fmt.Sprintf(`
			<p>您好！</p>
			<p>您正在进行 %s 邮箱验证。</p>
			<p>您的验证码为：</p>
			<p style="font-size: 24px; font-weight: bold; color: #333; background-color: #f8f8f8; padding: 10px; text-align: center; border-radius: 4px;">%s</p>
			<p style="color: #666;">验证码 %d 分钟内有效，如果不是本人操作，请忽略。</p>
			<p>点击下面的链接进行注册并且激活账户</p>
			<a href="%s">%s</a>
			<p>如果这不是您本人的操作，请忽略此邮件</p><p>%s 团队</p>
		`, systemNameForSendMail, code, common.VerificationValidMinutes, link, link, systemNameForSendMail),
	)
	err = message.SendEmail(subject, email, content)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

func SendPasswordResetEmail(c *gin.Context) {
	isAgency := false
	sendMailSystemName := config.SystemName
	var agencyDomain string
	agency, err := model.GetAgencyByDomain(c.Request.Host)
	if err == nil && agency != nil {
		isAgency = true
		sendMailSystemName = agency.Name
		agencyDomain = agency.Domain
	}
	email := c.Query("email")
	if err := common.Validate.Var(email, "required,email"); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": i18n.Translate(c, "invalid_parameter"),
		})
		return
	}
	if !model.IsEmailAlreadyTaken(email) {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "该邮箱地址未注册",
		})
		return
	}
	code := common.GenerateVerificationCode(0)
	common.RegisterVerificationCodeWithKey(email, code, common.PasswordResetPurpose)
	link := fmt.Sprintf("%s/user/reset?email=%s&token=%s",
		lo.If(isAgency, fmt.Sprintf("https://%s", agencyDomain)).Else(config.ServerAddress),
		email, code)
	subject := fmt.Sprintf("%s 密码重置", sendMailSystemName)
	content := message.EmailTemplate(
		subject,
		fmt.Sprintf(`
			<p>您好！</p>
			<p>您正在进行 %s 密码重置。</p>
			<p>请点击下面的按钮进行密码重置：</p>
			<p style="text-align: center; margin: 30px 0;">
				<a href="%s" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">重置密码</a>
			</p>
			<p style="color: #666;">如果按钮无法点击，请复制以下链接到浏览器中打开：</p>
			<p style="background-color: #f8f8f8; padding: 10px; border-radius: 4px; word-break: break-all;">%s</p>
			<p style="color: #666;">重置链接 %d 分钟内有效，如果不是本人操作，请忽略。</p>
		`, sendMailSystemName, link, link, common.VerificationValidMinutes),
	)
	err = message.SendEmail(subject, email, content)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

func SendTestEmail(c *gin.Context) {
	email := c.Query("email")
	if err := common.Validate.Var(email, "required,email"); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}
	code := common.GenerateVerificationCode(0)
	common.RegisterVerificationCodeWithKey(email, code, common.PasswordResetPurpose)
	subject := fmt.Sprintf("%s系统邮件", config.SystemName)
	content := fmt.Sprintf("<p>这是一封系统测试邮件，收到此邮件即说明您在 %s 项目中配置的 SMTP 服务可正常使用</p>", config.SystemName)
	err := message.SendEmail(subject, email, content)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": fmt.Sprintf("%s%s", i18n.Translate(c, "send_email_failed"), err.Error()),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

func ProxyEmail(c *gin.Context) {
	var req common.ProxyEmailRequest
	err := json.NewDecoder(c.Request.Body).Decode(&req)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}
	err = message.SendProxyEmail(req.SystemName, req.Email, req.Subject, req.Content, req.SMTPServer, req.SMTPPort, req.SMTPAccount, req.SMTPFrom, req.SMTPToken)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "代理邮件发送成功",
	})
	return
}

func SendTestWxPusher(c *gin.Context) {
	rootUserWxPusherUid := c.Query("RootUserWxPusherUid")
	subject := fmt.Sprintf("%s 系统消息", config.SystemName)
	content := fmt.Sprintf("收到此测试消息说明您在 %s 项目配置的WxPusher服务可用", config.SystemName)
	err := common.SendWxPusherMessage(subject, rootUserWxPusherUid, content)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

func SendTestQyWxBotPush(c *gin.Context) {
	subject := fmt.Sprintf("%s系统消息", config.SystemName)
	content := fmt.Sprintf("收到此测试消息说明您在 %s 项目配置的企业微信机器人推送可用", config.SystemName)
	err := common.SendQyWxBotMessage(subject, content)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
	return
}

type PasswordResetRequest struct {
	Email string `json:"email"`
	Token string `json:"token"`
}

func ResetPassword(c *gin.Context) {
	var req PasswordResetRequest
	err := json.NewDecoder(c.Request.Body).Decode(&req)
	if req.Email == "" || req.Token == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": i18n.Translate(c, "invalid_parameter"),
		})
		return
	}
	if !common.VerifyCodeWithKey(req.Email, req.Token, common.PasswordResetPurpose) {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "重置链接无效或已过期",
		})
		return
	}
	password := common.GenerateVerificationCode(12)
	err = model.ResetUserPasswordByEmail(req.Email, password)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	common.DeleteKey(req.Email, common.PasswordResetPurpose)
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    password,
	})
	return
}

func GetVersion(c *gin.Context) {
	// 保持原有参数命名 p 和 pageSize
	p, _ := strconv.Atoi(c.Query("p"))
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))

	// 参数校验和默认值设置
	if p < 0 {
		p = 0 // 保持从0开始
	}
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}

	// 请求第三方接口
	req, err := http.NewRequest(c.Request.Method,
		fmt.Sprintf("https://license.shellapi.vip/public/version/?p=%d&pageSize=%d&product_type=%s",
			p,
			pageSize,
			license.ProductType,
		),
		nil,
	)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	req.Header.Set("Content-Type", c.Request.Header.Get("Content-Type"))
	req.Header.Set("Accept", c.Request.Header.Get("Accept"))

	resp, err := client.HTTPClient.Do(req)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	defer resp.Body.Close()

	var versionResponse struct {
		Success bool        `json:"success"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
		Total   int         `json:"total"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&versionResponse); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    versionResponse.Data,
		"total":   versionResponse.Total,
	})
}

func FixDatabase(c *gin.Context) {
	var totalRows int64

	// 清理孤立的 abilities 记录
	rows, err := model.DeleteAbilitiesNotExistsInChannel()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    rows,
		})
		return
	}
	totalRows += rows

	// 清理孤立的 channel_extends 记录
	rows, err = model.DeleteChannelExtendsNotExistsInChannel()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    totalRows,
		})
		return
	}
	totalRows += rows

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    totalRows,
	})
	return
}

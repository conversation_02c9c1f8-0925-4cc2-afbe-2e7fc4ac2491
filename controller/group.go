package controller

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/model"
	billingratio "github.com/songquanpeng/one-api/relay/billing/ratio"
)

func GetGroups(c *gin.Context) {
	groupNames := make([]string, 0)
	for groupName := range billingratio.GetGroupRatioMap() {
		groupNames = append(groupNames, groupName)
	}

	// 添加用户额外可见分组
	userId := c.GetInt("id")
	groupNames = model.AddUserExtraVisibleGroupsToGroupList(userId, groupNames)

	c.J<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    groupNames,
	})
}

// GetSelectableGroups 获取可选的用户组（普通用户可访问）
func GetSelectableGroups(c *gin.Context) {
	p, _ := strconv.Atoi(c.Query("p"))
	if p < 0 {
		p = 0
	}
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}
	name := c.Query("name")
	displayName := c.Query("display_name")
	id := c.GetInt("id")
	user, _ := model.CacheGetUserById(id, false)
	if user == nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "用户不存在",
		})
		return
	}

	// 判断是否为管理员
	isAdmin := model.IsAdmin(id)

	groups, err := model.GetSelectableGroupsWithRatio(p*pageSize, pageSize, name, displayName, user.Group, isAdmin)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// 添加用户额外可见分组
	groups, err = model.AddUserExtraVisibleGroupsToGroupWithRatioList(id, user.Group, groups)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "添加额外可见分组失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    groups,
	})
}

// GetAllGroups 获取所有用户组
func GetAllGroups(c *gin.Context) {
	p, _ := strconv.Atoi(c.Query("p"))
	if p < 0 {
		p = 0
	}
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}
	name := c.Query("name")
	displayName := c.Query("display_name")
	groups, err := model.GetAllGroups(p*pageSize, pageSize, name, displayName)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    groups,
	})
}

// CountAllGroups 计算用户组总数
func CountAllGroups(c *gin.Context) {
	name := c.Query("name")
	displayName := c.Query("display_name")
	count, err := model.CountAllGroups(name, displayName)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    0,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"count": count,
		},
	})
}

// GetGroup 获取特定用户组
func GetGroup(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	group, err := model.GetGroupById(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    group,
	})
}

// AddGroup 添加新用户组
func AddGroup(c *gin.Context) {
	group := model.Group{}
	err := c.ShouldBindJSON(&group)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	if len(group.Name) == 0 || len(group.Name) > 32 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "用户组名称长度必须在1-32之间",
		})
		return
	}
	err = group.Insert()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    group,
	})
}

// DeleteGroup 删除用户组
func DeleteGroup(c *gin.Context) {
	id, _ := strconv.Atoi(c.Param("id"))
	err := model.DeleteGroupById(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
}

// UpdateGroup 更新用户组
func UpdateGroup(c *gin.Context) {
	sortOrderOnly := c.Query("sort_order_only")
	isSelectableOnly := c.Query("is_selectable_only")
	isVisibleOnly := c.Query("is_visible_only")
	group := model.Group{}
	err := c.ShouldBindJSON(&group)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	if isSelectableOnly == "" && isVisibleOnly == "" && sortOrderOnly == "" {
		// 如果不是只更新 is_selectable 或 is_visible，则进行全字段更新
		if len(group.Name) == 0 || len(group.Name) > 32 {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "用户组名称长度必须在1-32之间",
			})
			return
		}
		err = group.Update()
	} else {
		// 获取现有分组信息
		existingGroup, err := model.GetGroupById(group.Id)
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "获取用户组失败：" + err.Error(),
			})
			return
		}
		if sortOrderOnly != "" {
			// 获取现有分组信息
			// 只更新 sort_order
			existingGroup.SortOrder = group.SortOrder
			err = existingGroup.UpdateSortOrder()
		} else if isSelectableOnly != "" {
			// 如果只更新 is_selectable
			existingGroup.IsSelectable = group.IsSelectable
			err = existingGroup.UpdateIsSelectable()
		} else {
			// 如果只更新 is_visible
			existingGroup.IsVisible = group.IsVisible
			err = existingGroup.UpdateIsVisible()
		}
	}

	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    group,
	})
}

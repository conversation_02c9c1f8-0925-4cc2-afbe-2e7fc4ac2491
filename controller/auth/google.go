package auth

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"

	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/controller"
	"github.com/songquanpeng/one-api/model"
)

type GoogleOAuthResponse struct {
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
	ExpiresIn   int    `json:"expires_in"`
	Scope       string `json:"scope"`
}

type GoogleUser struct {
	ID            string `json:"id"`
	Email         string `json:"email"`
	VerifiedEmail bool   `json:"verified_email"`
	Name          string `json:"name"`
	GivenName     string `json:"given_name"`
	FamilyName    string `json:"family_name"`
	Picture       string `json:"picture"`
	Locale        string `json:"locale"`
}

func getGoogleUserInfoByCode(code string) (*GoogleUser, error) {
	// 注意：这个函数已废弃，应该使用 getGoogleUserInfoByCodeWithRedirectUri 并传入正确的 redirect_uri
	return getGoogleUserInfoByCodeWithRedirectUri(code, fmt.Sprintf("%s/oauth/google", config.ServerAddress))
}

func getGoogleUserInfoByCodeWithRedirectUri(code string, redirectUri string) (*GoogleUser, error) {
	if code == "" {
		return nil, errors.New("无效的参数")
	}

	// 记录请求参数用于调试
	logger.SysLog(fmt.Sprintf("Google OAuth Token 请求参数 - client_id: %s, redirect_uri: %s, code: %s",
		config.GoogleClientId, redirectUri, code))

	// 构建获取access token的请求
	values := map[string]string{
		"client_id":     config.GoogleClientId,
		"client_secret": config.GoogleClientSecret,
		"code":          code,
		"grant_type":    "authorization_code",
		"redirect_uri":  redirectUri,
	}

	jsonData, err := json.Marshal(values)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", "https://oauth2.googleapis.com/token", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	client := http.Client{
		Timeout: 10 * time.Second,
	}

	res, err := client.Do(req)
	if err != nil {
		logger.SysLog(err.Error())
		return nil, errors.New("无法连接至 Google 服务器，请稍后重试！")
	}
	defer res.Body.Close()

	// 读取响应体用于调试
	bodyBytes, err := io.ReadAll(res.Body)
	if err != nil {
		logger.SysLog("读取 Google OAuth 响应失败: " + err.Error())
		return nil, err
	}

	// 记录详细的请求和响应信息用于调试
	logger.SysLog(fmt.Sprintf("Google OAuth 请求 - redirect_uri: %s, client_id: %s", redirectUri, config.GoogleClientId))
	logger.SysLog(fmt.Sprintf("Google OAuth 客户端密钥长度: %d", len(config.GoogleClientSecret)))
	logger.SysLog(fmt.Sprintf("Google OAuth 响应状态: %d", res.StatusCode))
	logger.SysLog(fmt.Sprintf("Google OAuth 响应内容: %s", string(bodyBytes)))

	var oAuthResponse GoogleOAuthResponse
	err = json.Unmarshal(bodyBytes, &oAuthResponse)
	if err != nil {
		logger.SysLog("解析 Google OAuth 响应失败: " + err.Error())
		return nil, err
	}

	if oAuthResponse.AccessToken == "" {
		// 如果没有 access_token，可能是错误响应，尝试解析错误信息
		var errorResponse map[string]interface{}
		if json.Unmarshal(bodyBytes, &errorResponse) == nil {
			if errorMsg, exists := errorResponse["error"]; exists {
				logger.SysLog(fmt.Sprintf("Google OAuth 错误: %v", errorMsg))
				if errorDesc, exists := errorResponse["error_description"]; exists {
					logger.SysLog(fmt.Sprintf("Google OAuth 错误描述: %v", errorDesc))
				}
			}
		}
		return nil, errors.New("获取 Google access token 失败")
	}

	// 使用access token获取用户信息
	req, err = http.NewRequest("GET", "https://www.googleapis.com/oauth2/v2/userinfo", nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", oAuthResponse.AccessToken))

	res2, err := client.Do(req)
	if err != nil {
		logger.SysLog(err.Error())
		return nil, errors.New("无法连接至 Google 服务器，请稍后重试！")
	}
	defer res2.Body.Close()

	var googleUser GoogleUser
	err = json.NewDecoder(res2.Body).Decode(&googleUser)
	if err != nil {
		return nil, err
	}

	if googleUser.ID == "" {
		return nil, errors.New("返回值非法，用户字段为空，请稍后重试！")
	}

	return &googleUser, nil
}

func GoogleOAuth(c *gin.Context) {
	ctx := c.Request.Context()
	session := sessions.Default(c)
	state := c.Query("state")
	code := c.Query("code")

	// 如果没有code和state，可能是配置检查请求
	if code == "" && state == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "缺少必要的OAuth参数",
			"debug": map[string]interface{}{
				"enabled":              config.GoogleOAuthEnabled,
				"client_id":            config.GoogleClientId,
				"client_secret_length": len(config.GoogleClientSecret),
			},
		})
		return
	}

	if state == "" || session.Get("oauth_state") == nil || state != session.Get("oauth_state").(string) {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "state is empty or not same",
		})
		return
	}
	username := session.Get("username")
	if username != nil {
		GoogleBind(c)
		return
	}

	if !config.GoogleOAuthEnabled {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "管理员未开启 Google 验证",
		})
		return
	}
	// 构建重定向 URI，使用请求的 Host 和协议
	scheme := helper.GetRequestScheme(c)
	redirectUri := fmt.Sprintf("%s://%s/oauth/google", scheme, c.Request.Host)

	googleUser, err := getGoogleUserInfoByCodeWithRedirectUri(code, redirectUri)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	user := model.User{
		GoogleId: googleUser.ID,
	}
	if model.IsGoogleIdAlreadyTaken(user.GoogleId) {
		err := user.FillUserByGoogleId()
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}
	} else {
		if config.RegisterEnabled {
			// 使用邮箱前缀生成用户名
			user.Username = model.GenerateUsernameFromEmail(googleUser.Email)
			if user.Username == "" {
				user.Username = "google_" + strconv.Itoa(model.GetMaxUserId()+1)
			}

			if googleUser.Name != "" {
				user.DisplayName = googleUser.Name
			} else {
				user.DisplayName = "Google User"
			}

			//避免出现相同邮箱地址的两个不同账户
			if !model.IsEmailAlreadyTaken(googleUser.Email) {
				user.Email = googleUser.Email
			}

			user.Role = model.RoleCommonUser
			user.Status = model.UserStatusEnabled
			user.UserType = model.UserTypeGoogle // 设置用户类型为Google登录

			if err := user.Insert(ctx, 0); err != nil {
				c.JSON(http.StatusOK, gin.H{
					"success": false,
					"message": err.Error(),
				})
				return
			}
		} else {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "管理员关闭了新用户注册",
			})
			return
		}
	}

	if user.Status != model.UserStatusEnabled {
		c.JSON(http.StatusOK, gin.H{
			"message": "用户已被封禁",
			"success": false,
		})
		return
	}
	controller.SetupLogin(&user, c)
}

func GoogleBind(c *gin.Context) {
	if !config.GoogleOAuthEnabled {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "管理员未开启 Google 验证",
		})
		return
	}

	// 记录Google OAuth配置信息
	logger.SysLog(fmt.Sprintf("Google OAuth配置 - Enabled: %v, ClientId: %s, ClientSecret长度: %d",
		config.GoogleOAuthEnabled,
		config.GoogleClientId,
		len(config.GoogleClientSecret)))
	code := c.Query("code")
	// 构建重定向 URI，使用请求的 Host 和协议
	scheme := helper.GetRequestScheme(c)
	redirectUri := fmt.Sprintf("%s://%s/oauth/google", scheme, c.Request.Host)

	googleUser, err := getGoogleUserInfoByCodeWithRedirectUri(code, redirectUri)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	user := model.User{
		GoogleId: googleUser.ID,
	}
	if model.IsGoogleIdAlreadyTaken(user.GoogleId) {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "该 Google 账户已绑定",
		})
		return
	}
	session := sessions.Default(c)
	id := session.Get("id")
	// id := c.GetInt("id")  // critical bug!
	user.Id = id.(int)
	err = user.FillUserById()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	user.GoogleId = googleUser.ID
	err = user.Update(false)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "bind",
	})
	return
}

package controller

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/xyhelper"
)

// GetAllPackagePlanInstances 获取所有套餐实例
func GetAllPackagePlanInstances(c *gin.Context) {
	// 获取用户id
	userId := c.GetInt("id")
	// 用户id校验
	if userId == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的用户id",
		})
		return
	}
	p, _ := strconv.Atoi(c.Query("p"))
	if p < 0 {
		p = 0
	}
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}
	id, _ := strconv.Atoi(c.Query("id"))
	name := c.Query("name")
	status, _ := strconv.Atoi(c.Query("status"))
	packagePlanInstances, err := model.GetAllPackagePlanInstances(userId, p*pageSize, pageSize, id, name, status)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    packagePlanInstances,
	})
}

// CountAllPackagePlanInstances 获取所有套餐实例数量
func CountAllPackagePlanInstances(c *gin.Context) {
	// 获取用户id
	userId := c.GetInt("id")
	// 用户id校验
	if userId == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的用户id",
		})
		return
	}
	id, _ := strconv.Atoi(c.Query("id"))
	name := c.Query("name")
	status, _ := strconv.Atoi(c.Query("status"))
	count, err := model.CountAllPackagePlanInstances(userId, id, name, status)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"count": count,
		},
	})
}

// GetPackagePlanInstance 获取套餐实例
func GetPackagePlanInstance(c *gin.Context) {
	id, _ := strconv.Atoi(c.Param("id"))
	packagePlanInstance, err := model.GetPackagePlanInstanceById(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    packagePlanInstance,
	})
}

type PackagePlanInstanceRequest struct {
	PackagePlanId int    `json:"package_plan_id" binding:"required"`
	Password      string `json:"password" binding:"required"`
}

// AddPackagePlanInstance 添加套餐实例
func AddPackagePlanInstance(c *gin.Context) {
	ctx := c.Request.Context()
	var req PackagePlanInstanceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "参数错误",
		})
		return
	}
	// 获取用户id
	userId := c.GetInt("id")
	// 校验密码，如果密码错误则返回错误，如果原密码为空不会返回错误user.ValidatePassword(req.Password)
	isValid := ValidatePasswordByUserId(userId, req.Password)
	if !isValid {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "密码错误",
		})
		return
	}

	// 校验套餐id
	packagePlan, err := model.GetPackagePlanById(int64(req.PackagePlanId))
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的套餐",
		})
		return
	}

	// 校验套餐是否对当前购买的用户分组可见

	if packagePlan.VisibleGroups != "" {
		user, err := model.GetUserById(userId, false)
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "用户不存在",
			})
			return
		}
		if user == nil {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "用户不存在",
			})
			return
		}
		// 按照逗号拆分
		split := strings.Split(packagePlan.VisibleGroups, ",")
		// FIND_IN_SET
		isVisible := false
		for _, group := range split {
			if group == user.Group {
				isVisible = true
				break
			}
		}
		if !isVisible {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "无效的套餐",
			})
			return
		}
	}

	// 将套餐属性赋值给套餐实例
	var packagePlanInstance model.PackagePlanInstance
	err = copier.Copy(&packagePlanInstance, &packagePlan)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "套餐查询失败",
		})
		return
	}
	// 开启事务
	tx := model.DB.Begin()
	err = model.DecreasePackagePlanInventoryByTx(tx, req.PackagePlanId, 1)
	if err != nil {
		tx.Rollback()
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	// 校验用户余额是否足够
	userQuota, err := model.CacheGetUserQuota(ctx, userId)
	if err != nil {
		tx.Rollback()
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	if packagePlan.Price*500000 > float64(userQuota) {
		tx.Rollback()
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "余额不足",
		})
		return
	}
	err = model.DecreaseUserQuotaAndRedis(userId, (int64)(packagePlanInstance.Price*500000))
	if err != nil {
		tx.Rollback()
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	packagePlanInstance.PackagePlanId = req.PackagePlanId
	packagePlanInstance.UserId = userId
	packagePlanInstance.Id = 0
	packagePlanInstance.CreatedAt = helper.GetTimestamp()
	if packagePlan.ValidPeriod > 0 {
		packagePlanInstance.ExpiredTime = helper.GetTimestamp() + (int64)(packagePlanInstance.ValidPeriod*86400)
	}
	err = model.CacheCreatePackagePlanInstanceAndSyncToDbByTx(tx, &packagePlanInstance)
	if err != nil {
		tx.Rollback()
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	// 记录套餐购买日志
	model.RecordLog(ctx, userId, model.LogTypeConsume, fmt.Sprintf("购买套餐「%s」 ，消费 $%f", packagePlan.Name, packagePlan.Price))
	tx.Commit()

	// 添加 XYHelper 相关逻辑
	if packagePlan.IsXYHelper {
		user, err := model.GetUserById(userId, false)
		if err != nil {
			logger.SysError("获取用户信息失败: " + err.Error())
		} else {
			initialToken, err := model.GetUserInitialToken(user.Id)
			if err != nil {
				logger.SysError("获取用户初始token失败: " + err.Error())
			} else {
				err = xyhelper.XYHelperInsertChatGPTUser(config.XYHelperDBDSN, fmt.Sprintf("sk-%s", initialToken.Key), user.Username, packagePlan.ValidPeriod)
				if err != nil {
					logger.SysError("XYHelper插入外部数据库失败: " + err.Error())
				}
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    packagePlanInstance,
	})
}

// UpdatePackagePlanInstance 更新套餐实例
func UpdatePackagePlanInstance(c *gin.Context) {
	// 尚未实现
}

// DisablePackagePlanInstance
func DisablePackagePlanInstance(c *gin.Context) {
	// 获取用户id
	userId := c.GetInt("id")
	id, _ := strconv.Atoi(c.Param("id"))
	// 校验该用户名下是否存在该实例
	packagePlanInst, err := model.CacheGetPackagePlanInstanceById(userId, id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "套餐实例不存在",
		})
		return
	}
	if packagePlanInst.UserId != userId {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的套餐实例",
		})
		return
	}

	// 判断套餐是否为正常状态
	if packagePlanInst.Status != 1 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "套餐实例状态不正常",
		})
		return

	}
	packagePlanInst.Status = 2
	err = model.CacheUpdatePackagePlanInstanceAndSyncToDb(packagePlanInst)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
}

// EnablePackagePlanInstance
func EnablePackagePlanInstance(c *gin.Context) {
	// 获取用户id
	userId := c.GetInt("id")
	id, _ := strconv.Atoi(c.Param("id"))
	// 校验该用户名下是否存在该实例
	packagePlanInst, err := model.CacheGetPackagePlanInstanceById(userId, id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "套餐实例不存在",
		})
		return
	}
	if packagePlanInst.UserId != userId {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的套餐实例",
		})
		return
	}
	// 判断套餐是否为禁用状态
	if packagePlanInst.Status != 2 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "套餐实例状态不正常",
		})
		return
	}
	packagePlanInst.Status = 1
	err = model.CacheUpdatePackagePlanInstanceAndSyncToDb(packagePlanInst)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
}

// DeletePackagePlanInstance 删除套餐实例
func DeletePackagePlanInstance(c *gin.Context) {
	// 获取用户id
	userId := c.GetInt("id")
	id, _ := strconv.Atoi(c.Param("id"))
	// 校验该用户名下是否存在该实例
	packagePlanInst, err := model.GetPackagePlanInstanceById(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "套餐实例不存在",
		})
		return
	}
	if packagePlanInst.UserId != userId {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的套餐实例",
		})
		return
	}
	err = model.DeletePackagePlanInstance(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
}

// DeletePackagePlanInstanceByIds 批量删除套餐实例
func DeletePackagePlanInstanceByIds(c *gin.Context) {
	// 尚未实现
}

package controller

import (
	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/model"
	"net/http"
	"strconv"
)

func GetAllJobs(c *gin.Context) {
	p, _ := strconv.Atoi(c.Query("p"))
	if p < 0 {
		p = 0
	}
	pageSize, _ := strconv.Atoi(c.Query("pageSize"))
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}
	name := c.<PERSON>ry("name")
	cron := c.Query("corn")
	jobType, _ := strconv.Atoi(c.Query("job_type"))
	sensitiveWords, err := model.GetAllJobs(p, pageSize, jobType, name, cron)
	if err != nil {
		c.JSO<PERSON>(http.StatusOK, gin.H{
			"success": false,
			"message": err.<PERSON>rror(),
		})
		return
	}
	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    sensitiveWords,
	})
	return
}

func CountJobs(c *gin.Context) {
	name := c.<PERSON><PERSON>("name")
	cron := c.Query("corn")
	jobType, _ := strconv.Atoi(c.Query("job_type"))
	count, err := model.CountJobs(jobType, name, cron)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    0,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"count": count,
		},
	})
	return
}

func AddJob(c *gin.Context) {
	var job model.Job
	err := c.ShouldBindJSON(&job)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    10,
		})
		return
	}
	err = job.Insert()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    10,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    job.Id,
	})
	return
}

func UpdateJob(c *gin.Context) {
	var job model.Job
	err := c.ShouldBindJSON(&job)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    0,
		})
		return
	}
	rows, err := job.Update()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    rows,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    rows,
	})
	return
}

func DeleteJob(c *gin.Context) {
	id, _ := strconv.Atoi(c.Param("id"))
	rows, err := model.DeleteJobById(id)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    rows,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    rows,
		"message": "",
	})
}

func DeleteJobByIds(c *gin.Context) {
	// 解析数组
	var ids []int
	err := c.ShouldBindJSON(&ids)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "参数错误",
		})
		return
	}
	rows, err := model.DeleteJobByIds(ids)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
			"data":    rows,
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    rows,
		"message": "",
	})
	return
}

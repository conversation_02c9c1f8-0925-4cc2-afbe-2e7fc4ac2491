package controller

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"github.com/samber/lo"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/client"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/controller/optimizer"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/relay/adaptor/openai"
	"github.com/songquanpeng/one-api/relay/billing"
	billingratio "github.com/songquanpeng/one-api/relay/billing/ratio"
	"github.com/songquanpeng/one-api/relay/channeltype"
	"github.com/songquanpeng/one-api/relay/meta"
	relaymodel "github.com/songquanpeng/one-api/relay/model"
	"github.com/songquanpeng/one-api/relay/util"

	"github.com/gin-gonic/gin"
)

// 结构体定义，用于解析请求和构造Midjourney Proxy的请求
type ImagineDTO struct {
	Prompt     string `json:"prompt"`
	NotifyHook string `json:"notifyHook"`
	State      string `json:"state"`
	Base64     string `json:"base64"`
}

type TaskStatusResponse struct {
	ID          string `json:"id"`
	ImageUrl    string `json:"imageUrl"`
	Progress    string `json:"progress"`
	Status      string `json:"status"`
	SubmitTime  int64  `json:"submitTime"`
	StartTime   int64  `json:"startTime"`
	FinishTime  int64  `json:"finishTime"`
	FailReason  string `json:"failReason"`
	Description string `json:"description"`
	Action      string `json:"action"`
	PromptEn    string `json:"promptEn"`
	Prompt      string `json:"prompt"`
}

// 提交任务到Midjourney Proxy
func submitTaskToMidjourney(c *gin.Context, resp *http.Response, dto ImagineDTO, baseUrl string, mode string) (string, bool, error) {
	userId := c.GetInt("id")
	ctx := c.Request.Context()
	requestId, ok := ctx.Value(helper.RequestIdKey).(string)
	if !ok {
		requestId = ""
	}
	requestBody, err := json.Marshal(dto)
	if err != nil {
		return "", false, err
	}

	// add Authorization
	// 替换为Midjourney Proxy的实际地址和端点
	//resp, err := http.Post(baseUrl+"/mj/submit/imagine", "application/json", bytes.NewBuffer(requestBody))
	modeUrl := ""
	switch mode {
	case "fast":
		modeUrl = ""
	case "relax":
		modeUrl = "/mj-relax"
	case "turbo":
		modeUrl = "/mj-turbo"
	}
	req, err := http.NewRequest("POST", fmt.Sprintf("%s%s/mj/submit/imagine", baseUrl, modeUrl), bytes.NewBuffer(requestBody))
	apiKey := c.Request.Header.Get("Authorization")
	apiKey = strings.TrimPrefix(apiKey, "Bearer ")
	req.Header.Set("Authorization", apiKey)
	req.Header.Set("mj-api-secret", apiKey)
	req.Header.Set("Content-Type", c.Request.Header.Get("Content-Type"))
	resp, err = client.HTTPClient.Do(req)
	if err != nil {
		return "", false, err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", strings.HasPrefix(resp.Header.Get("Content-Type"), "text/event-stream"), err
	}

	var result map[string]interface{}
	json.Unmarshal(body, &result)
	taskID, ok := result["result"].(string)
	if !ok {
		return "", strings.HasPrefix(resp.Header.Get("Content-Type"), "text/event-stream"), fmt.Errorf("failed to get task ID")
	}
	midjourneyTask := &model.Midjourney{
		RequestId: requestId,
		UserId:    userId,
		//Code:        midjResponse.Code,
		Action:   "IMAGINE",
		MjId:     taskID,
		Prompt:   dto.Prompt,
		PromptEn: "",
		//Description: midjResponse.Description,
		State:      "",
		SubmitTime: 0,
		StartTime:  0,
		FinishTime: 0,
		ImageUrl:   "",
		Status:     "",
		Progress:   "0%",
		FailReason: "",
		Mode:       mode,
		ChannelId:  c.GetInt("channel_id"),
	}
	err = midjourneyTask.Insert()
	if err != nil {
		return "", strings.HasPrefix(resp.Header.Get("Content-Type"), "text/event-stream"), err
	}
	return taskID, strings.HasPrefix(resp.Header.Get("Content-Type"), "text/event-stream"), nil
}

// 轮询任务状态
func pollTaskStatusStream(c *gin.Context, taskID string, baseUrl string, modelName string) (*TaskStatusResponse, error) {
	userId := c.GetInt("id")
	mjDiscordProxyUrl := c.GetString("mj_discord_proxy_url")
	imageInMarkdown := c.GetBool("image_in_markdown")
	var taskStatus TaskStatusResponse
	common.SetEventStreamHeaders(c)
	c.Writer.Header().Set("Mj-Mode", "1")
	c.Stream(func(w io.Writer) bool {
		for {
			req, err := http.NewRequest("GET", fmt.Sprintf(baseUrl+"/mj/task/%s/fetch", taskID), nil)
			apiKey := c.Request.Header.Get("Authorization")
			apiKey = strings.TrimPrefix(apiKey, "Bearer ")
			req.Header.Set("Authorization", apiKey)
			req.Header.Set("Content-Type", c.Request.Header.Get("Content-Type"))
			resp, err := client.HTTPClient.Do(req)
			if err != nil {
				return false
			}
			defer resp.Body.Close()

			body, err := ioutil.ReadAll(resp.Body)
			if err != nil {
				return false
			}

			json.Unmarshal(body, &taskStatus)
			privateImageUrl := taskStatus.ImageUrl
			midjourneyTask := model.GetByMJId(userId, taskID)
			if midjourneyTask != nil {
				midjourneyTask.Progress = taskStatus.Progress
				midjourneyTask.PromptEn = taskStatus.PromptEn
				midjourneyTask.SubmitTime = taskStatus.SubmitTime
				midjourneyTask.StartTime = taskStatus.StartTime
				midjourneyTask.FinishTime = taskStatus.FinishTime
				midjourneyTask.ImageUrl = taskStatus.ImageUrl
				midjourneyTask.Status = taskStatus.Status
				midjourneyTask.FailReason = taskStatus.FailReason
				err = midjourneyTask.Update()
				if config.MidjourneyCustomImageUrlEnabled {
					if strings.HasPrefix(taskStatus.ImageUrl, "https://cdn.discordapp.com") && config.MjDiscordCdnProxy != "" {
						privateImageUrl = strings.Replace(taskStatus.ImageUrl, "https://cdn.discordapp.com", config.MjDiscordCdnProxy, 1)
					} else {
						privateImageUrl = config.MidjourneyCustomImageUrl + "/mj/image/" + taskID
					}
				} else {
					privateImageUrl = taskStatus.ImageUrl
				}
				if mjDiscordProxyUrl != "" {
					privateImageUrl = strings.Replace(taskStatus.ImageUrl, "https://cdn.discordapp.com", mjDiscordProxyUrl, 1)
				}
			}

			rsContent := ""
			if imageInMarkdown {
				// 如果进度不是100%则不显示图片
				if taskStatus.Status == "SUCCESS" {
					// [![图片描述](https://openai.qiheweb.com/mj/image/1701364885406627?_=1701364887)](https://openai.qiheweb.com/mj/image/1701364885406627?_=1701364887)[点击下载](https://openai.qiheweb.com/mj/image/1701364885406627?_=1701364887)
					rsContent = fmt.Sprintf("[![图片描述](%s?_=%d)](%s?_=%d)[点击下载](%s?_=%d)", privateImageUrl, helper.GetTimestamp(), privateImageUrl, helper.GetTimestamp(), privateImageUrl, helper.GetTimestamp())
				} else {
					// 只显示当前进度
					rsContent = fmt.Sprintf("当前状态[%s],当前进度%s\n", taskStatus.Status, taskStatus.Progress)
				}
			} else {
				rsContent = privateImageUrl
			}

			// 创建并填充数据
			streamResponses := openai.MJChatCompletionsStreamResponse{
				Id:      taskStatus.ID,
				Object:  "chat.completion.chunk",
				Created: 1234567890,
				Model:   modelName,
				Choices: []openai.MJChatCompletionsStreamResponseChoice{
					{
						Delta: struct {
							Content    string `json:"content"`
							Progress   string `json:"progress"`
							Prompt     string `json:"prompt"`
							PromptEn   string `json:"promptEn"`
							SubmitTime int64  `json:"submitTime"`
							StartTime  int64  `json:"startTime"`
							FinishTime int64  `json:"finishTime"`
							ImageUrl   string `json:"imageUrl"`
							Status     string `json:"status"`
						}{
							Content:    rsContent,
							Progress:   taskStatus.Progress,
							Prompt:     taskStatus.Prompt,
							PromptEn:   taskStatus.PromptEn,
							SubmitTime: taskStatus.SubmitTime,
							StartTime:  taskStatus.StartTime,
							FinishTime: taskStatus.FinishTime,
							ImageUrl:   privateImageUrl,
							Status:     taskStatus.Status,
						},
						FinishReason: nil,
					},
				},
			}
			// 转换成JSON格式
			// 转换为JSON
			jsonData, err := json.Marshal(streamResponses)
			c.Render(-1, common.CustomEvent{Data: "data: " + fmt.Sprintf("%s", string(jsonData))})
			c.Writer.Flush()
			if taskStatus.Status == "SUCCESS" || taskStatus.Status == "FAILURE" {
				break
			}
			// 等待一段时间后再次检查任务状态
			time.Sleep(5 * time.Second)
		}
		c.Render(-1, common.CustomEvent{Data: "data: [DONE]"})
		return false
	})

	return &taskStatus, nil
}

func relayOpenaiMJTextHelper(c *gin.Context, relayMode int) *relaymodel.ErrorWithStatusCode {
	ctx := c.Request.Context()
	meta := meta.GetByContext(c)
	billingType := c.GetInt(ctxkey.BillingType)
	channelType := c.GetInt("channel")
	userId := c.GetInt("id")
	group := c.GetString("group")
	_startTime := helper.GetTimestamp()

	var textRequest relaymodel.GeneralOpenAIRequest

	err := common.UnmarshalBodyReusable(c, &textRequest)
	if err != nil {
		return openai.ErrorWrapper(err, "bind_request_body_failed", http.StatusBadRequest)
	}

	// 只取textRequest.Messages的最后一条
	if len(textRequest.Messages) > 0 {
		textRequest.Messages = textRequest.Messages[len(textRequest.Messages)-1:]
	}
	realTextRequest := relaymodel.GeneralOpenAIRequest{
		Model:    textRequest.Model,
		Messages: textRequest.Messages,
		Stream:   textRequest.Stream,
	}
	// request validation
	if textRequest.Model == "" {
		return openai.ErrorWrapper(errors.New("model is required"), "required_field_missing", http.StatusBadRequest)
	}
	if textRequest.Messages == nil || len(textRequest.Messages) == 0 {
		return openai.ErrorWrapper(errors.New("field messages is required"), "required_field_missing", http.StatusBadRequest)
	}
	mode := "fast"
	if textRequest.Model == "mj-relax" || textRequest.Model == "midjourney-relax" {
		mode = "relax"
	} else if textRequest.Model == "mj-turbo" || textRequest.Model == "midjourney-turbo" {
		mode = "turbo"
	}
	// map model name
	isModelMapped := false
	textRequest.Model, isModelMapped = util.GetMappedModelName(textRequest.Model, meta.ModelMapping, meta.ModelMappingArr)
	meta.ActualModelName = textRequest.Model
	if isModelMapped {
		realTextRequest.Model = textRequest.Model
	}
	baseURL := channeltype.ChannelBaseURLs[channelType]
	requestURL := c.Request.URL.String()
	// 用于保存详细聊天记录日志
	var detailPrompt = ""
	var detailCompletion = ""
	if c.GetString("base_url") != "" {
		baseURL = c.GetString("base_url")
	}
	fullRequestURL := util.GetFullRequestURL(baseURL, requestURL, channelType)
	var promptTokens int
	var completionTokens int
	promptTokens, _, _, _ = openai.CountTokenMessages(textRequest.Messages, textRequest.Model, meta)
	modelRatio := billingratio.GetModelRatio(textRequest.Model, meta.ChannelType)
	groupRatio := billingratio.GetGroupRatio(group)
	ratio := modelRatio * groupRatio
	userQuota, quotaExpireTime, err := model.CacheGetUserQuotaAndExpireTime(ctx, userId)
	if err != nil {
		return openai.ErrorWrapper(err, "get_user_quota_failed", http.StatusInternalServerError)
	}
	if config.QuotaExpireEnabled && quotaExpireTime > 0 && quotaExpireTime < helper.GetTimestamp() {
		model.RecordSysLogToDBAndFile(c.Request.Context(), c.GetString(helper.RequestIdKey), model.LogTypeSystemErr, userId, 0, textRequest.Model, "", "", "user quota expired", "尚未解析")
		return openai.ErrorWrapper(errors.New("user quota expired"), "user_quota_expired", http.StatusForbidden)
	}
	sizeRatio := 1.0
	var v7FastConvertToTurbo = false
	if config.MidjourneyV7TurboEnabled {
		if mode == "fast" && strings.Contains(textRequest.Messages[len(textRequest.Messages)-1].StringContent(), "--v 7") {
			v7FastConvertToTurbo = true
		}
	}

	// For MJ image generation, the action is always "IMAGINE"
	const action = "IMAGINE"

	// Check if the prompt contains --draft parameter for half-price calculation
	var isDraftMode = false
	if config.MidjourneyDraftHalfPriceEnabled {
		if strings.Contains(textRequest.Messages[len(textRequest.Messages)-1].StringContent(), "--draft") &&
			(mode == "fast" || mode == "turbo") {
			isDraftMode = true
		}
	}

	topupConvertRatio, userDiscount := billing.GetTopupConvertRatioByMeta(meta)

	// Apply half-price discount for draft mode
	var draftDiscount = 1.0
	if isDraftMode {
		draftDiscount = 0.5 // 50% discount for draft mode
	}

	quota := int64(ratio * sizeRatio * 1000 * topupConvertRatio * userDiscount * draftDiscount)
	var modelFixedPrice float64
	var modelFixedPriceErr error
	if billingType == common.BillingTypeByCount {
		if v7FastConvertToTurbo {
			// 如果是v7 fast模式,则转换为turbo模式
			modelFixedPrice, modelFixedPriceErr = billingratio.GetModelFixedPrice("mj_turbo_imagine")
			if modelFixedPriceErr != nil {
				modelFixedPrice, modelFixedPriceErr = billingratio.GetModelFixedPrice("midjourney-turbo")
				if modelFixedPriceErr != nil {
					modelFixedPrice, modelFixedPriceErr = billingratio.GetModelFixedPrice("mj-turbo")
					if modelFixedPriceErr != nil {
						// 抛出
						return openai.ErrorWrapper(modelFixedPriceErr, "model_fixed_price_not_config", http.StatusForbidden)
					}
				}
			}
		} else {
			modelFixedPrice, modelFixedPriceErr = billingratio.GetModelFixedPrice(textRequest.Model)
		}
		if modelFixedPriceErr != nil {
			return openai.ErrorWrapper(modelFixedPriceErr, "model_fixed_price_not_config", http.StatusForbidden)
		}
		// 替换用户个性费率
		if v7FastConvertToTurbo {
			userModelFixedPrice, ok, _, _ := model.CacheGetUserModelFixedPrice(meta.UserId, "mj_turbo_imagine")
			if ok {
				modelFixedPrice = userModelFixedPrice
			} else {
				userModelFixedPrice, ok, _, _ := model.CacheGetUserModelFixedPrice(meta.UserId, "midjourney-turbo")
				if ok {
					modelFixedPrice = userModelFixedPrice
				} else {
					userModelFixedPrice, ok, _, _ := model.CacheGetUserModelFixedPrice(meta.UserId, "mj-turbo")
					if ok {
						modelFixedPrice = userModelFixedPrice
					}
				}
			}
		} else {
			userModelFixedPrice, ok, _, _ := model.CacheGetUserModelFixedPrice(meta.UserId, lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(textRequest.Model))
			if ok {
				modelFixedPrice = userModelFixedPrice
			}
		}
		meta.ModelFixedPrice = modelFixedPrice
		quota = int64(modelFixedPrice * groupRatio * 500000 * topupConvertRatio * userDiscount)
	}
	if userQuota <= 0 || userQuota-quota < 0 {
		return openai.ErrorWrapper(errors.New(fmt.Sprintf("user [%d] quota [%d] quota [%d] is not enough", meta.UserId, userQuota, quota)), "insufficient_user_quota", http.StatusForbidden)
	}
	var requestBody io.Reader
	if isModelMapped {
		jsonStr, err := json.Marshal(realTextRequest)
		if err != nil {
			return openai.ErrorWrapper(err, "marshal_text_request_failed", http.StatusInternalServerError)
		}
		requestBody = bytes.NewBuffer(jsonStr)
	} else {
		jsonStr, err := json.Marshal(realTextRequest)
		if err != nil {
			return openai.ErrorWrapper(err, "marshal_text_request_failed", http.StatusInternalServerError)
		}
		requestBody = bytes.NewBuffer(jsonStr)
	}
	// 读取 Body 内容
	bodyBytes, _ := io.ReadAll(requestBody)
	detailPrompt = string(bodyBytes)
	// 根据配置排除请求字段
	detailPrompt, bodyBytes, err = util.ExcludeFields(c, detailPrompt, bodyBytes)
	detailPrompt, bodyBytes, err = util.AddExtraFields(c, detailPrompt, bodyBytes)
	if err != nil {
		return openai.ErrorWrapper(err, "exclude_fields_failed", http.StatusInternalServerError)
	}
	// 恢复原始的 Body
	requestBody = io.NopCloser(bytes.NewBuffer(bodyBytes))

	var req *http.Request
	var resp *http.Response
	isStream := textRequest.Stream

	req, err = http.NewRequest(c.Request.Method, fullRequestURL, requestBody)
	if err != nil {
		return openai.ErrorWrapper(err, "new_request_failed", http.StatusInternalServerError)
	}
	apiKey := c.Request.Header.Get("Authorization")
	apiKey = strings.TrimPrefix(apiKey, "Bearer ")
	req.Header.Set("Authorization", apiKey)
	req.Header.Set("Content-Type", c.Request.Header.Get("Content-Type"))
	req.Header.Set("Accept", c.Request.Header.Get("Accept"))
	if isStream && c.Request.Header.Get("Accept") == "" {
		req.Header.Set("Accept", "text/event-stream")
	}
	dto := ImagineDTO{
		Prompt:     textRequest.Messages[len(textRequest.Messages)-1].StringContent(),
		NotifyHook: "",
		State:      "",
		Base64:     "",
	}
	// 校验入参是否包含"直接返回"
	if strings.Contains(dto.Prompt, "直接返回") {
		return openai.ErrorWrapper(errors.New("prompt contains invalid content"), "invalid_midjourney_prompt", http.StatusBadRequest)
	}
	midjourneyTaskId, respIsStream, err := submitTaskToMidjourney(c, resp, dto, baseURL, mode)
	if err != nil {
		return openai.ErrorWrapper(err, "submit_task_to_midjourney_failed", http.StatusInternalServerError)
	}
	isStream = isStream || respIsStream

	tokenName := c.GetString("token_name")
	tokenKey := c.GetString("token_key")
	tokenId := c.GetInt("token_id")
	channelId := c.GetInt("channel_id")
	channelName := c.GetString("channel_name")

	defer func(ctx context.Context) {
		go func() {
			// 即使是请求错误导致的消费为0也记录日志,并且记录耗时
			requestDuration := helper.GetTimestamp() - _startTime
			logContent := fmt.Sprintf("模型倍率 %.2f，分组倍率 %.2f，充值转换率 %.2f，用户折扣率 %.2f，用时 %d秒", modelRatio, groupRatio, topupConvertRatio, userDiscount, requestDuration)
			if billingType == common.BillingTypeByCount {
				logContent = fmt.Sprintf("模型按次使用固定价格 %.6f，分组倍率 %.2f，充值转换率 %.2f，用户折扣率 %.2f，用时 %d秒", modelFixedPrice, groupRatio, topupConvertRatio, userDiscount, requestDuration)
			}
			err := model.PostConsumeTokenQuota(tokenId, quota)
			if err != nil {
				logger.Error(ctx, "error consuming token remain quota: "+err.Error())
			}
			err = model.CacheUpdateUserQuota(ctx, userId)
			if err != nil {
				logger.Error(ctx, "error update user quota cache: "+err.Error())
			}
			model.UpdateUserUsedQuotaAndRequestCount(userId, quota)
			model.UpdateChannelUsedQuota(channelId, quota)
			// 即使是请求错误导致的消费为0也记录日志,并且记录耗时
			if v7FastConvertToTurbo {
				mode = mode + "-v7"
			}
			if isDraftMode {
				mode = mode + "-draft"
			}
			createdLog := model.RecordConsumeLog(ctx, userId, channelId, promptTokens, completionTokens, fmt.Sprintf("mj-chat(%s)", mode), tokenName, tokenKey, channelName, int(quota), requestDuration, false, logContent)
			helper.SafeGoroutine(func() {
				// 记录详细聊天记录
				model.RecordLogExtend(ctx, createdLog, detailPrompt, detailCompletion, "", meta.UpstreamResponse, meta.FullResponse, meta.RequestURLPath)
				// 推送优化器
				optimizer.RecordConsumeLog(createdLog)
			})

		}()
	}(c.Request.Context())
	_, err = pollTaskStatusStream(c, midjourneyTaskId, baseURL, textRequest.Model)
	if err != nil {
		return openai.ErrorWrapper(err, "poll_task_status_stream_failed", http.StatusInternalServerError)
	}
	return nil
}

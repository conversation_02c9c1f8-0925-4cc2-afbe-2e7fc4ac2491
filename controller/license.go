package controller

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/license"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/model"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"
)

var HTTPClient *http.Client

func init() {
	HTTPClient = &http.Client{}
}

func AutomaticallyHeartbeat(frequency int) {
	for {
		time.Sleep(time.Duration(frequency) * time.Minute)
		_ = doHeartbeat()
	}
}

func LicenseReLogin() {
	// 如果没有填写key就没必要重试了
	if config.LicenseKey == "" || config.LicenseKey == "COMMON_KEY" {
		return
	}
	// 每个小时重试登录1次,一共重试10次
	for i := 0; i < 10; i++ {
		err := LicenseLogin()
		if err != nil {
			logger.SysError(fmt.Sprintf("license relogin failed : %s", err.Error()))
			time.Sleep(time.Duration(1) * time.Hour)
		} else {
			logger.SysLog(fmt.Sprintf("license relogin success"))
			break
		}
	}
}

func LicenseLogin() error {
	// 如果当前实例id不存在就新建并且存表
	if config.LicenseInstanceId == "" {
		config.LicenseInstanceId = helper.GenerateKey()
		err := model.UpdateOption("LicenseInstanceId", config.LicenseInstanceId)
		if err != nil {
			return err
		}
	}
	if config.LicenseKey == "" || config.LicenseKey == "COMMON_KEY" {
		config.LicenseEnabled = false
		logger.SysError("未提供授权码，中继功能不可用. Relay function is not available without a LICENSE KEY.")
	}
	key := config.LicenseKey
	id := config.LicenseInstanceId
	jsonData, ak, ak2, ak3, akss, akss9, err := PrepareEncryptJsonData()
	req, err := http.NewRequest(http.MethodPost, fmt.Sprintf("%s/public/license/startup/", config.LicenseGateway), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "*/*")
	resp, err := HTTPClient.Do(req)
	if err != nil {
		// 失败换备用地址重试
		req, err = http.NewRequest(http.MethodPost, fmt.Sprintf("%s/public/license/startup/", config.LicenseGatewayBack1), bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Accept", "*/*")
		resp, err = HTTPClient.Do(req)
		if err != nil {
			logger.SysError(err.Error())
			return err
		}
	}
	if resp != nil {
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				logger.SysError(err.Error())
			}
		}(resp.Body)
	}
	// 解析resp.Body为JSON,不用结构体接受
	type LoginResponse struct {
		Success   bool   `json:"success"`
		FatalCode string `json:"fatal_code"`
		Message   string `json:"message"`
		Sign      string `json:"sign"`
		Sign7     string `json:"sign7"`
		Sign9     string `json:"sign9"`
		Sess      string `json:"sess"`
	}
	var loginResponse LoginResponse
	err = json.NewDecoder(resp.Body).Decode(&loginResponse)
	if err != nil {
		return err
	}
	if !loginResponse.Success {
		logger.SysError(fmt.Sprintf("license login failed : %s", loginResponse.Message))
		return fmt.Errorf("license login failed : %s", loginResponse.Message)
	}
	// 解析密文和当前机器Id对比
	decryptString1, _ := helper.DecryptString(ak, loginResponse.Sign)
	decryptString2, _ := helper.DecryptString(ak2, loginResponse.Sign)
	decryptString3, _ := helper.DecryptString(ak3, loginResponse.Sign)

	// 拆解三个字符串中的|,取出第0个索引对比key是否相等,对比最后一位id是否相等,并且在此之前,确保长度够
	split1 := strings.Split(decryptString1, "|")
	split2 := strings.Split(decryptString2, "|")
	split3 := strings.Split(decryptString3, "|")
	// 检查是否至少有一个拆分数组符合条件
	valid := false
	invalidImmediate := false
	if len(split1) >= 4 && split1[0] == key && split1[3] == id {
		valid = true
	}

	if len(split2) >= 4 && split2[0] == key && split2[3] == id {
		valid = true
	}

	if len(split3) >= 4 && split3[0] == key && split3[3] == id {
		valid = true
	}

	// 解析sign7
	decryptString7, _ := helper.DecryptString(akss, loginResponse.Sign7)
	split7 := strings.Split(decryptString7, "|")
	// 判断长度是否大于等于5,并且第2个索引位置为1
	if len(split7) >= 5 {
		// 判断有效性
		if split7[2] == "1" {
			// 如果是1,则说明有效,并且是活跃状态
			valid = true
		} else if split7[2] == "0" {
			// 如果是0,则说明有效,但是无权限
			valid = false
			config.LicenseEnabled = false
		} else if split7[2] == "-3" {
			invalidImmediate = true
			config.LicenseEnabled = false
		}
		// 判断sk是否篡改
		if split7[0] != key {
			// 如果sk不相等,则说明被篡改,暂时不强行停止,只标记
			valid = false
		}
		// 判断时间戳是否篡改,相差一天以内都可以 split7[1] 转数字
		decryptedTime, _ := strconv.ParseInt(split7[1], 10, 64)
		if helper.GetTimestamp()-decryptedTime > 86400 || decryptedTime-helper.GetTimestamp() > 86400 {
			// 如果时间戳相差超过一天,则说明被篡改,暂时不强行停止,只标记
			valid = false
		}
		// 判断ip篡改 , 这个可能会拿到内网ip 不一定准确,先不处理
		//if split7[3] != helper.GetIp() {
		// 如果ip不相等,则说明被篡改,暂时不强行停止,只标记
		//valid = false
		//}
		// 最后判断id是否篡改
		if split7[4] != id {
			valid = false
		}
	} else {
		valid = false
		config.LicenseEnabled = false
		invalidImmediate = true
	}

	// 解析sign9
	decryptString9, _ := helper.DecryptString(akss9, loginResponse.Sign9)
	split9 := strings.Split(decryptString9, "|")
	if len(split9) >= 8 {
		// 加密9需要 IP,产品类型,版本号,id,时间戳,session,是否有效,sk
		// 判断索引1产品类型是不是7
		if split9[1] != license.ProductType {
			valid = false
		}
		if split9[2] != common.Version {
			valid = false
		}
		if split9[3] != id {
			valid = false
		}
		// 判断时间戳是否篡改,相差一天以内都可以 split9[4] 转数字
		decryptedTime, _ := strconv.ParseInt(split9[4], 10, 64)
		if helper.GetTimestamp()-decryptedTime > 86400 || decryptedTime-helper.GetTimestamp() > 86400 {
			// 如果时间戳相差超过一天,则说明被篡改,暂时不强行停止,只标记
			valid = false
		}
		if split9[5] != license.LicenseSess {
			valid = false
		}
		if split9[6] != "1" {
			valid = false
		}
		if split9[6] == "-3" {
			valid = false
			config.LicenseEnabled = false
			invalidImmediate = true
		}
		if split9[7] != key {
			valid = false
		}
	} else {
		valid = false
		invalidImmediate = true
		config.LicenseEnabled = false
	}
	if invalidImmediate {
		valid = false
		config.LicenseEnabled = false
	}
	if !valid {
		config.LicenseEnabled = false
		logger.SysError(fmt.Sprintf("license login failed : not valid"))
		return fmt.Errorf("license login failed : not valid")
	} else {
		config.LicenseEnabled = true
		logger.SysLog(fmt.Sprintf("license login success: sign is [%s] , id is [%s]\n", loginResponse.Sign, id))
		license.LastLicenseCheckedTime = helper.GetTimestamp()
	}
	return nil
}

func doHeartbeat() error {
	// 如果当前时间距离上次成功的时间大于24小时,并且当前日期大于等于2024年1月1日,则认为授权失败
	if helper.GetTimestamp()-license.LastLicenseCheckedTime > 3600*12 {
		// 记录日志
		logger.SysError(fmt.Sprintf("Fatal problem : license heartbeat failed for more than 12 hour and license disabled\n"))
		config.LicenseEnabled = false
	}
	key := config.LicenseKey
	id := config.LicenseInstanceId
	jsonData, ak, ak2, ak3, akss, akss9, err := PrepareEncryptJsonData()
	req, err := http.NewRequest(http.MethodPost, fmt.Sprintf("%s/public/license/heartbeat/", config.LicenseGateway), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	resp, err := HTTPClient.Do(req)
	if err != nil {
		// 错误则请求备用地址
		req, err = http.NewRequest(http.MethodPost, fmt.Sprintf("%s/public/license/heartbeat/", config.LicenseGatewayBack1), bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Accept", "application/json")
		resp, err = HTTPClient.Do(req)
		if err != nil {
			logger.SysError(err.Error())
			return err
		}
	}
	if resp != nil {
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				logger.SysError(err.Error())
			}
		}(resp.Body)
	}
	// 解析resp.Body为JSON,不用结构体接受
	type HeartbeatResponse struct {
		Success   bool   `json:"success"`
		FatalCode string `json:"fatal_code"`
		Message   string `json:"message"`
		Sign      string `json:"sign"`
		Sign7     string `json:"sign7"`
		Sign9     string `json:"sign9"`
		Sess      string `json:"sess"`
	}
	var heartbeatResponse HeartbeatResponse
	err = json.NewDecoder(resp.Body).Decode(&heartbeatResponse)
	if err != nil {
		return err
	}
	// 校验响应中是否有特殊情况
	if heartbeatResponse.FatalCode != "" {
		// 未授权,强制停止
		if heartbeatResponse.FatalCode == "UNAUTHORIZED" {
			config.LicenseEnabled = false
		}
	}

	if heartbeatResponse.Sess != "" && heartbeatResponse.Sess != license.LicenseSess {
		// 会话不一致,强制停止
		config.LicenseEnabled = false
	}

	// 解析密文和当前机器Id对比
	decryptString1, _ := helper.DecryptString(ak, heartbeatResponse.Sign)
	decryptString2, _ := helper.DecryptString(ak2, heartbeatResponse.Sign)
	decryptString3, _ := helper.DecryptString(ak3, heartbeatResponse.Sign)

	// 拆解三个字符串中的|,取出第0个索引对比key是否相等,对比最后一位id是否相等,并且在此之前,确保长度够
	split1 := strings.Split(decryptString1, "|")
	split2 := strings.Split(decryptString2, "|")
	split3 := strings.Split(decryptString3, "|")
	// 检查是否至少有一个拆分数组符合条件
	valid := false
	// 马上失效标识
	invalidImmediate := false
	if len(split1) >= 4 && split1[0] == key && split1[3] == id {
		valid = true
	}

	if len(split2) >= 4 && split2[0] == key && split2[3] == id {
		valid = true
	}

	if len(split3) >= 4 && split3[0] == key && split3[3] == id {
		valid = true
	}

	// 解析sign7
	decryptString7, _ := helper.DecryptString(akss, heartbeatResponse.Sign7)
	split7 := strings.Split(decryptString7, "|")
	// 判断长度是否大于等于5,并且第2个索引位置为1
	if len(split7) >= 5 {
		// 判断有效性
		if split7[2] == "1" {
			// 如果是1,则说明有效,并且是活跃状态
			valid = true
		} else if split7[2] == "0" {
			// 如果是0,则说明有效,但是无权限
			valid = false
			config.LicenseEnabled = false
		} else if split7[2] == "-3" {
			invalidImmediate = true
			config.LicenseEnabled = false
		}
		// 判断sk是否篡改
		if split7[0] != key {
			// 如果sk不相等,则说明被篡改,暂时不强行停止,只标记
			valid = false
		}
		// 判断时间戳是否篡改,相差一天以内都可以 split7[1] 转数字
		decryptedTime, _ := strconv.ParseInt(split7[1], 10, 64)
		if helper.GetTimestamp()-decryptedTime > 86400 || decryptedTime-helper.GetTimestamp() > 86400 {
			// 如果时间戳相差超过一天,则说明被篡改,暂时不强行停止,只标记
			valid = false
		}
		// 判断ip篡改 , 这个可能会拿到内网ip 不一定准确,先不处理
		//if split7[3] != helper.GetIp() {
		// 如果ip不相等,则说明被篡改,暂时不强行停止,只标记
		//valid = false
		//}
		// 最后判断id是否篡改
		if split7[4] != id {
			valid = false
		}
	} else {
		valid = false
		config.LicenseEnabled = false
		invalidImmediate = true
	}

	// 解析sign9
	decryptString9, _ := helper.DecryptString(akss9, heartbeatResponse.Sign9)
	split9 := strings.Split(decryptString9, "|")
	if len(split9) >= 8 {
		// 加密9需要 IP,产品类型,版本号,id,时间戳,session,是否有效,sk
		// 判断索引1产品类型是不是7
		if split9[1] != license.ProductType {
			valid = false
		}
		if split9[2] != common.Version {
			valid = false
		}
		if split9[3] != id {
			valid = false
		}
		// 判断时间戳是否篡改,相差一天以内都可以 split9[4] 转数字
		decryptedTime, _ := strconv.ParseInt(split9[4], 10, 64)
		if helper.GetTimestamp()-decryptedTime > 86400 || decryptedTime-helper.GetTimestamp() > 86400 {
			// 如果时间戳相差超过一天,则说明被篡改,暂时不强行停止,只标记
			valid = false
		}
		if split9[5] != license.LicenseSess {
			valid = false
		}
		if split9[6] != "1" {
			valid = false
		}
		if split9[6] == "-3" {
			valid = false
			config.LicenseEnabled = false
			invalidImmediate = true
		}
		if split9[7] != key {
			valid = false
		}
	} else {
		valid = false
		invalidImmediate = true
		config.LicenseEnabled = false
	}
	if !valid {
		if invalidImmediate {
			config.LicenseEnabled = false
		}
		return nil
	} else {
		license.LastLicenseCheckedTime = helper.GetTimestamp()
		config.LicenseEnabled = true
		return nil
	}
}

// 准备加密json数据
func PrepareEncryptJsonData() ([]byte, []byte, []byte, []byte, []byte, []byte, error) {
	key := config.LicenseKey
	id := config.LicenseInstanceId

	sa1, sa2, sa3 := helper.GenerateSalts(id, key, helper.GetTimestamp())
	ak, err := helper.GenerateAesKeyBySalt(common.DA, 32, []byte(sa1))
	ak2, _ := helper.GenerateAesKeyBySalt(common.DA, 32, []byte(sa2))
	ak3, _ := helper.GenerateAesKeyBySalt(common.DA, 32, []byte(sa3))
	akss, _ := helper.GenerateAesKeyBySalt(common.DA, 32, []byte("akl7777777-sess"))
	akss8, _ := helper.GenerateAesKeyBySalt(common.DA, 32, []byte("akl7777778-sess"))
	// 新版鉴权 随机生成一个key
	akss9, _ := helper.GenerateAesKeyBySalt(common.DA, 32, []byte("sk-m0otFGzedBQpORgC6981F21c5e7748F0Bf3a7bA5D865F9F5"))
	sign, err := helper.EncryptString(ak, fmt.Sprintf("%s|%d|%s|%s", key, helper.GetTimestamp(), helper.GetIp(), id))
	isMasterNodeInt := 0
	if config.IsMasterNode {
		isMasterNodeInt = 1
	}
	str := fmt.Sprintf("%s|%d|%d", license.LicenseSess, isMasterNodeInt, helper.GetTimestamp())
	// 本产品固定7
	str8 := fmt.Sprintf("%s|%s|%d", license.LicenseSess, license.ProductType, helper.GetTimestamp())
	// 加密9需要 IP,产品类型,版本号,id,时间戳,session,是否有效,sk
	str9 := fmt.Sprintf("%s|%s|%s|%s|%d|%s|%d|%s", helper.GetIp(), license.ProductType, common.Version, config.LicenseInstanceId, helper.GetTimestamp(), license.LicenseSess, 1, key)
	sign2, err := helper.EncryptString(akss, str)
	sign3, err := helper.EncryptString(akss8, str8)
	sign9, err := helper.EncryptString(akss9, str9)
	var jsonData []byte
	jsonData, err = json.Marshal(map[string]string{
		"key":     key,
		"sess":    license.LicenseSess,
		"sign":    sign,
		"sign2":   sign2,
		"sign3":   sign3,
		"sign7":   sign3,
		"sign9":   sign9,
		"id":      id,
		"version": common.Version,
	})
	return jsonData, ak, ak2, ak3, akss, akss9, err
}

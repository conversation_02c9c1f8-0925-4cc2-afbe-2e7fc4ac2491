package controller

import (
	"fmt"
	"net/http"
	"strconv"
	"sync"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/message"
	"github.com/songquanpeng/one-api/model"
)

func GetLogDetailsStat(c *gin.Context) {
	logType, _ := strconv.Atoi(c.Query("type"))
	startTimestamp, _ := strconv.ParseInt(c.Query("start_timestamp"), 10, 64)
	endTimestamp, _ := strconv.ParseInt(c.Query("end_timestamp"), 10, 64)
	tokenName := c.Query("token_name")
	tokenKey := c.Query("token_key")
	tokenGroup := c.Query("token_group")
	username := c.Query("username")
	modelName := c.Query("model_name")
	channel, _ := strconv.Atoi(c.Query("channel"))
	userRedis := true
	userRedisStr := c.Query("model_name")
	if userRedisStr == "false" {
		userRedis = false
	}
	quotaNum := model.SumUsedQuota(logType, startTimestamp, endTimestamp, modelName, username, tokenName, tokenKey, tokenGroup, channel, userRedis)
	c.JSON(200, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"quota": quotaNum,
			//"token": tokenNum,
		},
	})
}

func GetLogDetailsSelfStat(c *gin.Context) {
	username := c.GetString("username")
	logType, _ := strconv.Atoi(c.Query("type"))
	startTimestamp, _ := strconv.ParseInt(c.Query("start_timestamp"), 10, 64)
	endTimestamp, _ := strconv.ParseInt(c.Query("end_timestamp"), 10, 64)
	tokenName := c.Query("token_name")
	tokenKey := c.Query("token_key")
	tokenGroup := c.Query("token_group")
	modelName := c.Query("model_name")
	channel, _ := strconv.Atoi(c.Query("channel"))
	userRedis := true
	userRedisStr := c.Query("model_name")
	if userRedisStr == "false" {
		userRedis = false
	}
	quotaNum := model.SumUsedQuota(logType, startTimestamp, endTimestamp, modelName, username, tokenName, tokenKey, tokenGroup, channel, userRedis)
	c.JSON(200, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"quota": quotaNum,
			//"token": tokenNum,
		},
	})
}

var deleteHistoryLogDetailsLock sync.Mutex
var deleteHistoryLogDetailsRunning = false

func DeleteHistoryLogDetails(c *gin.Context) {
	deleteHistoryLogDetailsLock.Lock()
	if deleteHistoryLogDetailsRunning {
		deleteHistoryLogDetailsLock.Unlock()
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": fmt.Sprintf("已有一个删除任务在运行中，请稍后再试... 当前任务执行进度(删除详细日志):已删除[%d]行,共需删除[%d]行,完成度[%d%%]",
				model.TotalAffectedHistoryLogDetailsCount,
				model.TotalShouldDeleteHistoryLogDetailsCount,
				lo.If(model.TotalShouldDeleteHistoryLogDetailsCount == 0, 0).Else(int(float64(model.TotalAffectedHistoryLogDetailsCount)/float64(model.TotalShouldDeleteHistoryLogDetailsCount)*100)),
			),
		})
		return
	}
	targetTimestamp, _ := strconv.ParseInt(c.Query("target_timestamp"), 10, 64)
	beforeNowTimestamp, _ := strconv.ParseInt(c.Query("before_now_timestamp"), 10, 64)
	if targetTimestamp == 0 && beforeNowTimestamp == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "target timestamp or beforeNowTimestamp is required at least one",
		})
		return
	}
	if beforeNowTimestamp != 0 {
		targetTimestamp = helper.GetTimestamp() - beforeNowTimestamp
	}
	deleteHistoryLogDetailsRunning = true
	model.TotalShouldDeleteHistoryLogDetailsCount = 0
	model.TotalAffectedHistoryLogDetailsCount = 0
	deleteHistoryLogDetailsLock.Unlock()
	helper.SafeGoroutine(func() {
		defer func() {
			deleteHistoryLogDetailsLock.Lock()
			deleteHistoryLogDetailsRunning = false
			deleteHistoryLogDetailsLock.Unlock()
		}()
		count, err := model.DeleteLogExByTimestamp(targetTimestamp)
		if err != nil {
			message.NotifyRootUser("删除详细日志任务失败", err.Error())
			return
		}
		message.NotifyRootUser("删除详细日志任务完成", "删除了"+strconv.Itoa(int(count))+"条详细日志")
		return
	})
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除详细日志任务正在后台运行,删除日志的结果稍后会通知管理员",
	})
	return
}

// TruncateLogExtendsTable 清空日志扩展表
func TruncateLogExtendsTable(c *gin.Context) {
	// 使用当前上下文调用模型中的TruncateLogExtendTable函数
	err := model.TruncateLogExtendTable(c)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "清空日志扩展表失败: " + err.Error(),
		})
		return
	}

	// 发送通知给Root用户
	message.NotifyRootUser("清空日志扩展表完成", "已成功清空所有日志扩展表记录")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "已成功清空所有日志扩展表记录",
	})
	return
}

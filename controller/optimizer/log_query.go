package optimizer

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/client"
	"github.com/songquanpeng/one-api/common/constants"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/model"
)

func GetLogsFromLogOptimizer(isAdminSearch bool, userId int, timezone string, logType []int, startTimestamp int64, endTimestamp int64,
	modelName string, username string, tokenName string, tokenKey string, channelName string, channel int,
	page int, size int, promptTokensMin *int, promptTokensMax *int, completionTokensMin *int,
	completionTokensMax *int, totalDurationMin *float64, totalDurationMax *float64, requestDurationMin *float64,
	requestDurationMax *float64, responseFirstByteDurationMin *float64, responseFirstByteDurationMax *float64,
	excludeModels []string, errorCode string, excludeErrorCodes []string) ([]*model.Log, error) {

	// 使用ParamsBuilder构建参数
	paramsBuilder := helper.NewParamsBuilder()

	// 添加基础参数
	paramsBuilder.
		Add("dynamicIndex", common.ShellApiLogOptimizerDynamicIndex)

	// 添加非空的基础参数
	paramsBuilder.
		AddIfNotEmpty("userId", userId).
		AddIfNotEmpty("type", logType).
		AddIfNotEmpty("startTimestamp", startTimestamp).
		AddIfNotEmpty("endTimestamp", endTimestamp).
		AddIfNotEmpty("modelName", modelName).
		AddIfNotEmpty("username", username).
		AddIfNotEmpty("tokenName", tokenName).
		AddIfNotEmpty("tokenKey", tokenKey).
		AddIfNotEmpty("channelName", channelName).
		AddIfNotEmpty("channel", channel).
		AddIfNotEmpty("page", page).
		AddIfNotEmpty("size", size).
		AddIfNotEmpty("errorCode", errorCode)

	// 添加数组参数
	paramsBuilder.
		AddIfNotEmptySlice("excludeModels", excludeModels).
		AddIfNotEmptySlice("excludeErrorCodes", excludeErrorCodes)

	// 添加范围参数
	paramsBuilder.
		AddIfNotNil("promptTokensMin", promptTokensMin).
		AddIfNotNil("promptTokensMax", promptTokensMax).
		AddIfNotNil("completionTokensMin", completionTokensMin).
		AddIfNotNil("completionTokensMax", completionTokensMax).
		AddIfNotNil("totalDurationMin", totalDurationMin).
		AddIfNotNil("totalDurationMax", totalDurationMax).
		AddIfNotNil("requestDurationMin", requestDurationMin).
		AddIfNotNil("requestDurationMax", requestDurationMax).
		AddIfNotNil("responseFirstByteDurationMin", responseFirstByteDurationMin).
		AddIfNotNil("responseFirstByteDurationMax", responseFirstByteDurationMax)

	// 添加非管理员用户的过滤逻辑
	if !isAdminSearch && !model.IsAdmin(userId) {
		paramsBuilder.
			Add("excludeTypes", constants.LogTypeExclude).
			Add("excludeFields", constants.LogFieldsExclude)
	}

	paramsBytes, err := json.Marshal(paramsBuilder.GetParams())
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest(http.MethodPost,
		fmt.Sprintf("%s/shellApiLogOptimizer/service/log/pageQuery?dynamicIndex=%s",
			common.ShellApiLogOptimizerGateWay,
			common.ShellApiLogOptimizerDynamicIndex),
		bytes.NewReader(paramsBytes))
	if err != nil {
		return nil, err
	}
	setAuthHeader(req)
	req.Header.Set("Content-Type", "application/json")

	resp, err := client.HTTPClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("error response status code: %d, body: %s", resp.StatusCode, body)
	}

	var logPageResponse LogPageResponse
	err = json.NewDecoder(resp.Body).Decode(&logPageResponse)
	if err != nil {
		return nil, fmt.Errorf("JSON unmarshaling failed: %s", err)
	}

	var logs []*model.Log
	for _, logHttpEntity := range logPageResponse.Data.List {
		var logItem model.Log
		logItem.Id, _ = strconv.Atoi(logHttpEntity.ID)
		logItem.RequestId = logHttpEntity.RequestId
		logItem.UserId = logHttpEntity.UserID
		logItem.CreatedAt = logHttpEntity.CreatedAt.Unix()
		logItem.Type = logHttpEntity.Type
		logItem.Content = logHttpEntity.Content
		logItem.Username = logHttpEntity.Username
		logItem.TokenName = logHttpEntity.TokenName
		logItem.TokenGroup = logHttpEntity.TokenGroup
		logItem.ModelName = logHttpEntity.ModelName
		logItem.ChannelName = logHttpEntity.ChannelName
		logItem.Quota = logHttpEntity.Quota
		logItem.CostQuota = logHttpEntity.CostQuota
		logItem.PromptTokens = logHttpEntity.PromptTokens
		logItem.CompletionTokens = logHttpEntity.CompletionTokens
		logItem.ChannelId = logHttpEntity.ChannelID
		logItem.TokenKey = logHttpEntity.TokenKey
		logItem.RequestDuration = logHttpEntity.RequestDuration
		logItem.ResponseFirstByteDuration = logHttpEntity.ResponseFirstByteDuration
		logItem.TotalDuration = logHttpEntity.TotalDuration
		logItem.DurationForView = logHttpEntity.DurationForView
		logItem.IsStream = logHttpEntity.IsStream
		logItem.Ip = logHttpEntity.Ip
		logItem.RemoteIp = logHttpEntity.RemoteIp
		logItem.Other = logHttpEntity.Other
		logItem.ErrorCode = logHttpEntity.ErrorCode
		logs = append(logs, &logItem)
	}
	return logs, nil
}

func GetDailyUsageStatsFromLogOptimizer(userId int, timezone string, tokenName, username string, channel int, channelName, modelName string, startTimestamp, endTimestamp int64, dimension string, granularity string) ([]*DailyModelUsageStats, error) {
	// 构建 SQL 查询
	sql := buildDailyUsageStatsSqlQuery(userId, tokenName, username, channel, channelName, modelName, startTimestamp, endTimestamp, dimension, timezone)

	// 执行 SQL 查询
	result, err := ExecuteSql(sql)
	if err != nil {
		return nil, fmt.Errorf("failed to execute SQL query: %v", err)
	}

	// 将 result 转换为 map[string]interface{}
	resultMap, ok := result.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("unexpected result type: %T", result)
	}

	// 检查状态和消息
	status, ok := resultMap["status"].(float64)
	if !ok {
		return nil, fmt.Errorf("status field not found or not a number")
	}
	if int(status) != 200 {
		message, _ := resultMap["message"].(string)
		return nil, fmt.Errorf("API returned non-200 status: %d, message: %s", int(status), message)
	}

	// 获取并解析 data 字段
	data, ok := resultMap["data"].(string)
	if !ok {
		return nil, fmt.Errorf("data field not found or not a string")
	}

	var sqlResult SQLQueryResult
	err = json.Unmarshal([]byte(data), &sqlResult)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal SQL result: %v", err)
	}

	// 解析查询结果
	return sqlResult.ParseRows()
}

func buildDailyUsageStatsSqlQuery(userId int, tokenName, username string, channel int, channelName, modelName string, startTimestamp, endTimestamp int64, dimension, timezone string) string {
	tableName := getTableName()

	// 根据维度设置分组
	var groupDimension string
	switch dimension {
	case "model":
		groupDimension = "modelName"
	case "user":
		groupDimension = "userId, username"
	default:
		groupDimension = "modelName"
	}

	// 使用 DATE_TRUNC 和 DATEADD 函数处理日期
	dateFormat := "DATE_TRUNC('day', createdAt)"
	whereClause := "createdAt >= DATEADD('day', -14, DATE_TRUNC('day', CURRENT_TIMESTAMP))"

	sql := fmt.Sprintf(`
    SELECT 
        %s,
        %s as date, 
        SUM(quota) as sumQuota,
        SUM(quota)/500000.0 as sumUsd 
    FROM 
        %s 
    WHERE 
        modelName != '' 
        AND modelName IS NOT NULL
        AND %s
    `, groupDimension, dateFormat, tableName, whereClause)

	// 添加用户 ID 条件
	if userId != 0 {
		sql += fmt.Sprintf(" AND userId = %d", userId)
	}
	// 添加时间范围条件
	if startTimestamp != 0 {
		startDate := time.Unix(startTimestamp, 0).Format("2006-01-02")
		sql += fmt.Sprintf(" AND createdAt >= CAST('%s' AS TIMESTAMP)", startDate)
	} else {
		sql += " AND createdAt >= DATEADD('day', -14, CURRENT_DATE)"
	}
	if endTimestamp != 0 {
		endDate := time.Unix(endTimestamp, 0).Format("2006-01-02")
		sql += fmt.Sprintf(" AND createdAt < DATEADD('day', 1, CAST('%s' AS TIMESTAMP))", endDate)
	}

	// 添加其他条件
	if tokenName != "" {
		sql += fmt.Sprintf(" AND tokenName = '%s'", tokenName)
	}
	if username != "" {
		sql += fmt.Sprintf(" AND username = '%s'", username)
	}
	if channel != 0 {
		sql += fmt.Sprintf(" AND channelId = %d", channel)
	}
	if channelName != "" {
		sql += fmt.Sprintf(" AND channelName = '%s'", channelName)
	}
	if modelName != "" {
		sql += fmt.Sprintf(" AND modelName = '%s'", modelName)
	}

	sql += fmt.Sprintf(`
    GROUP BY 
        %s,
        %s
    HAVING
        SUM(quota) > 0
    ORDER BY 
        %s ASC,
        SUM(quota) DESC
    `, groupDimension, dateFormat, dateFormat)

	return sql
}

func parseDailyUsageStatsSqlResult(result interface{}, dimension string) ([]*model.DailyModelUsageStats, error) {
	rows, ok := result.([]interface{})
	if !ok {
		return nil, fmt.Errorf("unexpected result type")
	}

	var stats []*model.DailyModelUsageStats

	for _, row := range rows {
		rowMap, ok := row.(map[string]interface{})
		if !ok {
			return nil, fmt.Errorf("unexpected row type")
		}

		stat := &model.DailyModelUsageStats{
			ModelName: rowMap["modelName"].(string),
			UserId:    rowMap["userId"].(string),
			Username:  rowMap["username"].(string),
			SumQuota:  int(rowMap["sumQuota"].(float64)),
			SumUsd:    rowMap["sumUsd"].(float64),
			Date:      rowMap["date"].(string),
		}

		stats = append(stats, stat)
	}

	return stats, nil
}

func CountLogsFromLogOptimizer(isAdminSearch bool, userId int, logType []int, startTimestamp int64, endTimestamp int64, modelName string,
	username string, tokenName string, tokenKey string, channelName string, channel int, isStream string,
	requestId string, ip string, promptTokensMin *int, promptTokensMax *int, completionTokensMin *int,
	completionTokensMax *int, totalDurationMin *float64, totalDurationMax *float64, requestDurationMin *float64,
	requestDurationMax *float64, responseFirstByteDurationMin *float64, responseFirstByteDurationMax *float64,
	excludeModels []string, errorCode string, excludeErrorCodes []string) (int64, error) {

	var req *http.Request
	var resp *http.Response

	// 使用ParamsBuilder构建参数
	paramsBuilder := helper.NewParamsBuilder()

	// 添加基础参数
	paramsBuilder.
		Add("dynamicIndex", common.ShellApiLogOptimizerDynamicIndex)

	// 添加非空的基础参数
	paramsBuilder.
		AddIfNotEmpty("userId", userId).
		AddIfNotEmpty("type", logType).
		AddIfNotEmpty("startTimestamp", startTimestamp).
		AddIfNotEmpty("endTimestamp", endTimestamp).
		AddIfNotEmpty("modelName", modelName).
		AddIfNotEmpty("username", username).
		AddIfNotEmpty("tokenName", tokenName).
		AddIfNotEmpty("tokenKey", tokenKey).
		AddIfNotEmpty("channelName", channelName).
		AddIfNotEmpty("channel", channel).
		AddIfNotEmpty("isStream", isStream).
		AddIfNotEmpty("requestId", requestId).
		AddIfNotEmpty("ip", ip).
		AddIfNotEmpty("errorCode", errorCode)

	// 添加数组参数
	paramsBuilder.
		AddIfNotEmptySlice("excludeModels", excludeModels).
		AddIfNotEmptySlice("excludeErrorCodes", excludeErrorCodes)

	// 添加范围参数
	paramsBuilder.
		AddIfNotNil("promptTokensMin", promptTokensMin).
		AddIfNotNil("promptTokensMax", promptTokensMax).
		AddIfNotNil("completionTokensMin", completionTokensMin).
		AddIfNotNil("completionTokensMax", completionTokensMax).
		AddIfNotNil("totalDurationMin", totalDurationMin).
		AddIfNotNil("totalDurationMax", totalDurationMax).
		AddIfNotNil("requestDurationMin", requestDurationMin).
		AddIfNotNil("requestDurationMax", requestDurationMax).
		AddIfNotNil("responseFirstByteDurationMin", responseFirstByteDurationMin).
		AddIfNotNil("responseFirstByteDurationMax", responseFirstByteDurationMax)

	// 添加非管理员用户的过滤逻辑
	if !isAdminSearch && !model.IsAdmin(userId) {
		paramsBuilder.
			Add("excludeTypes", constants.LogTypeExclude).
			Add("excludeFields", constants.LogFieldsExclude)
	}

	paramsBytes, err := json.Marshal(paramsBuilder.GetParams())
	if err != nil {
		return 0, err
	}

	req, err = http.NewRequest(http.MethodPost,
		fmt.Sprintf("%s/shellApiLogOptimizer/service/log/count?dynamicIndex=%s",
			common.ShellApiLogOptimizerGateWay,
			common.ShellApiLogOptimizerDynamicIndex),
		bytes.NewReader(paramsBytes))
	if err != nil {
		return 0, err
	}

	setAuthHeader(req)
	req.Header.Set("Content-Type", "application/json")

	resp, err = client.HTTPClient.Do(req)
	if err != nil {
		return 0, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return 0, fmt.Errorf("error response status code: %d, body: %s", resp.StatusCode, body)
	}

	var result struct {
		Status  int    `json:"status"`
		Message string `json:"message"`
		Data    int64  `json:"data"`
	}

	if err = json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return 0, err
	}

	if result.Status != 200 {
		return 0, fmt.Errorf("API returned error: %s", result.Message)
	}

	return result.Data, nil
}

func buildCountSqlQuery(userId int, logType []int, startTimestamp int64, endTimestamp int64, modelName string, username string, tokenName string, tokenKey string, channel int) string {
	tableName := getTableName()
	sql := fmt.Sprintf(`
    SELECT 
        COUNT(*) as count
    FROM 
        %s 
    WHERE 1=1
    `, tableName)

	// 添加条件
	if userId != 0 {
		sql += fmt.Sprintf(" AND userId = %d", userId)
	}
	if len(logType) > 0 {
		sql += fmt.Sprintf(" AND type IN (%s)", strings.Trim(strings.Join(strings.Fields(fmt.Sprint(logType)), ","), "[]"))
	}
	if startTimestamp != 0 {
		sql += fmt.Sprintf(" AND createdAt >= %d", startTimestamp)
	}
	if endTimestamp != 0 {
		sql += fmt.Sprintf(" AND createdAt <= %d", endTimestamp)
	}
	if modelName != "" {
		sql += fmt.Sprintf(" AND modelName = '%s'", modelName)
	}
	if username != "" {
		sql += fmt.Sprintf(" AND username = '%s'", username)
	}
	if tokenName != "" {
		sql += fmt.Sprintf(" AND tokenName = '%s'", tokenName)
	}
	if tokenKey != "" {
		sql += fmt.Sprintf(" AND tokenKey = '%s'", tokenKey)
	}
	if channel != 0 {
		sql += fmt.Sprintf(" AND channelId = %d", channel)
	}

	return sql
}

func parseCountResult(result interface{}) (int64, error) {
	resultMap, ok := result.(map[string]interface{})
	if !ok {
		return 0, fmt.Errorf("unexpected result type: %T", result)
	}

	// 检查状态和消息
	status, ok := resultMap["status"].(float64)
	if !ok {
		return 0, fmt.Errorf("status field not found or not a number")
	}
	if int(status) != 200 {
		message, _ := resultMap["message"].(string)
		return 0, fmt.Errorf("API returned non-200 status: %d, message: %s", int(status), message)
	}

	// 获取并解析 data 字段
	data, ok := resultMap["data"].(string)
	if !ok {
		return 0, fmt.Errorf("data field not found or not a string")
	}

	var sqlResult struct {
		Columns []struct {
			Name string `json:"name"`
			Type string `json:"type"`
		} `json:"columns"`
		Rows [][]interface{} `json:"rows"`
	}

	err := json.Unmarshal([]byte(data), &sqlResult)
	if err != nil {
		return 0, fmt.Errorf("failed to unmarshal SQL result: %v", err)
	}

	if len(sqlResult.Rows) == 0 || len(sqlResult.Rows[0]) == 0 {
		return 0, fmt.Errorf("no count result found")
	}

	count, ok := sqlResult.Rows[0][0].(float64)
	if !ok {
		return 0, fmt.Errorf("count is not a number")
	}

	return int64(count), nil
}

func GetAllLogsModelUsageFromLogOptimizer(logType int, timezone string, startTimestamp, endTimestamp int64, modelName, username, tokenName string, channel int, channelName string) ([]*model.ModelUsage, error) {
	// 构建 SQL 查询
	sql := buildModelUsageSqlQuery(logType, startTimestamp, endTimestamp, modelName, username, tokenName, channel, channelName, timezone)

	// 执行 SQL 查询
	result, err := ExecuteSql(sql)
	if err != nil {
		return nil, fmt.Errorf("failed to execute SQL query: %v", err)
	}

	// 解析查询结果
	return parseModelUsageResult(result)
}

func buildModelUsageSqlQuery(logType int, startTimestamp, endTimestamp int64, modelName, username, tokenName string, channel int, channelName, timezone string) string {
	tableName := getTableName()

	// 如果没有提供时区，默认使用 UTC
	if timezone == "" {
		timezone = "UTC"
	}

	var sb strings.Builder

	// 基本查询
	sb.WriteString(fmt.Sprintf(`
    SELECT 
        modelName,
        COUNT(1) as cnt
    FROM 
        %s 
    WHERE 
        type = 2
    `, tableName))

	// 构建 WHERE 子句
	conditions := []string{}

	// 处理时间范围，考虑时区
	if startTimestamp != 0 {
		startDate := time.Unix(startTimestamp, 0).In(mustLoadLocation(timezone)).Format("2006-01-02T15:04:05Z")
		conditions = append(conditions, fmt.Sprintf("createdAt >= CAST('%s' AS TIMESTAMP)", startDate))
	}
	if endTimestamp != 0 {
		endDate := time.Unix(endTimestamp, 0).In(mustLoadLocation(timezone)).Format("2006-01-02T15:04:05Z")
		conditions = append(conditions, fmt.Sprintf("createdAt < CAST('%s' AS TIMESTAMP) + INTERVAL 1 DAY", endDate))
	}
	if startTimestamp == 0 && endTimestamp == 0 {
		oneMonthAgo := time.Now().In(mustLoadLocation(timezone)).AddDate(0, -1, 0).Format("2006-01-02T15:04:05Z")
		conditions = append(conditions, fmt.Sprintf("createdAt >= CAST('%s' AS TIMESTAMP)", oneMonthAgo))
	}

	// 其他条件
	if modelName != "" {
		conditions = append(conditions, fmt.Sprintf("modelName = '%s'", modelName))
	}
	if username != "" {
		conditions = append(conditions, fmt.Sprintf("username = '%s'", username))
	}
	if tokenName != "" {
		conditions = append(conditions, fmt.Sprintf("tokenName = '%s'", tokenName))
	}
	if channel != 0 {
		conditions = append(conditions, fmt.Sprintf("channelId = %d", channel))
	}
	if channelName != "" {
		conditions = append(conditions, fmt.Sprintf("channelName = '%s'", channelName))
	}

	// 添加所有条件到 WHERE 子句
	if len(conditions) > 0 {
		sb.WriteString(" AND ")
		sb.WriteString(strings.Join(conditions, " AND "))
	}

	// GROUP BY 和 ORDER BY 子句
	sb.WriteString(`
    GROUP BY 
        modelName
    ORDER BY 
        cnt DESC
    `)

	return sb.String()
}

// mustLoadLocation 加载时区，如果出错则 panic
func mustLoadLocation(timezone string) *time.Location {
	loc, err := time.LoadLocation(timezone)
	if err != nil {
		panic(fmt.Sprintf("Invalid timezone: %s", timezone))
	}
	return loc
}

func parseModelUsageResult(result interface{}) ([]*model.ModelUsage, error) {
	resultMap, ok := result.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("unexpected result type: %T", result)
	}

	data, ok := resultMap["data"].(string)
	if !ok {
		return nil, fmt.Errorf("data field not found or not a string")
	}

	var sqlResult struct {
		Columns []struct {
			Name string `json:"name"`
			Type string `json:"type"`
		} `json:"columns"`
		Rows [][]interface{} `json:"rows"`
	}

	err := json.Unmarshal([]byte(data), &sqlResult)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal SQL result: %v", err)
	}

	var modelUsages []*model.ModelUsage
	for _, row := range sqlResult.Rows {
		if len(row) != 2 {
			return nil, fmt.Errorf("unexpected number of columns in row")
		}
		modelUsage := &model.ModelUsage{
			ModelName: row[0].(string),
			Cnt:       int(row[1].(float64)),
		}
		modelUsages = append(modelUsages, modelUsage)
	}

	return modelUsages, nil
}

func GetLogsStatFromLogOptimizer(logType int, startTimestamp, endTimestamp int64, modelName, username, tokenName, tokenKey string, channel int) (model.Stat, error) {
	// 构建 SQL 查询
	quotaSQL := buildQuotaSQL(logType, startTimestamp, endTimestamp, modelName, username, tokenName, tokenKey, channel)
	rpmTpmSQL := buildRpmTpmSQL(logType, modelName, username, tokenName, tokenKey, channel, "")

	// 执行 SQL 查询
	quotaResult, err := ExecuteSql(quotaSQL)
	if err != nil {
		return model.Stat{}, fmt.Errorf("failed to execute quota SQL query: %v", err)
	}

	rpmTpmResult, err := ExecuteSql(rpmTpmSQL)
	if err != nil {
		return model.Stat{}, fmt.Errorf("failed to execute rpm/tpm SQL query: %v", err)
	}

	// 解析查询结果
	quota, err := parseQuotaResult(quotaResult)
	if err != nil {
		return model.Stat{}, err
	}

	rpmTpm, err := parseRpmTpmResult(rpmTpmResult)
	if err != nil {
		return model.Stat{}, err
	}

	return model.Stat{
		Quota: quota,
		Rpm:   rpmTpm.Rpm,
		Tpm:   rpmTpm.Tpm,
		Mpm:   rpmTpm.Mpm,
	}, nil
}

func buildCommonConditions(startTimestamp, endTimestamp int64, modelName, username, tokenName, tokenKey string, channel int, channelName string) string {
	var conditions []string

	// 处理时间范围
	if startTimestamp != 0 {
		startDate := time.Unix(startTimestamp, 0).Format("2006-01-02T15:04:05Z")
		conditions = append(conditions, fmt.Sprintf("createdAt >= CAST('%s' AS TIMESTAMP)", startDate))
	}
	if endTimestamp != 0 {
		endDate := time.Unix(endTimestamp, 0).Format("2006-01-02T15:04:05Z")
		conditions = append(conditions, fmt.Sprintf("createdAt < CAST('%s' AS TIMESTAMP) + INTERVAL 1 DAY", endDate))
	}

	// 其他条件
	if modelName != "" {
		conditions = append(conditions, fmt.Sprintf("modelName = '%s'", modelName))
	}
	if username != "" {
		conditions = append(conditions, fmt.Sprintf("username = '%s'", username))
	}
	if tokenName != "" {
		conditions = append(conditions, fmt.Sprintf("tokenName = '%s'", tokenName))
	}
	if tokenKey != "" {
		if strings.HasPrefix(tokenKey, "sk-") && len(strings.Split(tokenKey, "-")) > 1 {
			tokenKey = strings.Split(tokenKey, "-")[1]
		}
		conditions = append(conditions, fmt.Sprintf("tokenKey = '%s'", tokenKey))
	}
	if channel != 0 {
		conditions = append(conditions, fmt.Sprintf("channelId = %d", channel))
	}
	if channelName != "" {
		conditions = append(conditions, fmt.Sprintf("channelName = '%s'", channelName))
	}

	if len(conditions) > 0 {
		return " AND " + strings.Join(conditions, " AND ")
	}
	return ""
}

func buildQuotaSQL(logType int, startTimestamp, endTimestamp int64, modelName, username, tokenName, tokenKey string, channel int) string {
	tableName := getTableName()
	sql := fmt.Sprintf(`
    SELECT 
        SUM(quota) as quota
    FROM 
        %s 
    WHERE 
        type IN (2, 12)
    `, tableName)

	sql += buildCommonConditions(startTimestamp, endTimestamp, modelName, username, tokenName, tokenKey, channel, "")

	return sql
}

func buildRpmTpmSQL(logType int, modelName, username, tokenName, tokenKey string, channel int, channelName string) string {
	tableName := getTableName()

	// 获取当前时间和一分钟前的时间
	now := time.Now()
	oneMinuteAgo := now.Add(-1 * time.Minute)

	sql := fmt.Sprintf(`
    SELECT 
        COUNT(*) as rpm, 
        SUM(promptTokens) + SUM(completionTokens) as tpm,
        SUM(quota) / 500000 as mpm
    FROM 
        %s 
    WHERE 
        type IN (2, 12)
    `, tableName)

	// 使用 Unix 时间戳
	sql += buildCommonConditions(
		oneMinuteAgo.Unix(),
		now.Unix(),
		modelName,
		username,
		tokenName,
		tokenKey,
		channel,
		channelName,
	)

	return sql
}

func parseQuotaResult(result interface{}) (int, error) {
	resultMap, ok := result.(map[string]interface{})
	if !ok {
		return 0, fmt.Errorf("unexpected result type: %T", result)
	}

	status, ok := resultMap["status"].(float64)
	if !ok || int(status) != 200 {
		return 0, fmt.Errorf("unexpected status: %v", resultMap["status"])
	}

	dataStr, ok := resultMap["data"].(string)
	if !ok {
		return 0, fmt.Errorf("data field not found or not a string")
	}

	var sqlResult struct {
		Columns []struct {
			Name string `json:"name"`
			Type string `json:"type"`
		} `json:"columns"`
		Rows [][]interface{} `json:"rows"`
	}

	err := json.Unmarshal([]byte(dataStr), &sqlResult)
	if err != nil {
		return 0, fmt.Errorf("failed to unmarshal SQL result: %v", err)
	}

	if len(sqlResult.Rows) == 0 || len(sqlResult.Rows[0]) == 0 {
		return 0, nil // 没有数据，返回 0
	}

	quota, ok := sqlResult.Rows[0][0].(float64)
	if !ok {
		return 0, fmt.Errorf("quota is not a number")
	}

	return int(quota), nil
}

func parseRpmTpmResult(result interface{}) (model.Stat, error) {
	resultMap, ok := result.(map[string]interface{})
	if !ok {
		return model.Stat{}, fmt.Errorf("unexpected result type: %T", result)
	}

	status, ok := resultMap["status"].(float64)
	if !ok || int(status) != 200 {
		return model.Stat{}, fmt.Errorf("unexpected status: %v", resultMap["status"])
	}

	dataStr, ok := resultMap["data"].(string)
	if !ok {
		return model.Stat{}, fmt.Errorf("data field not found or not a string")
	}

	var sqlResult struct {
		Columns []struct {
			Name string `json:"name"`
			Type string `json:"type"`
		} `json:"columns"`
		Rows [][]interface{} `json:"rows"`
	}

	err := json.Unmarshal([]byte(dataStr), &sqlResult)
	if err != nil {
		return model.Stat{}, fmt.Errorf("failed to unmarshal SQL result: %v", err)
	}

	if len(sqlResult.Rows) == 0 || len(sqlResult.Rows[0]) != 3 {
		return model.Stat{}, fmt.Errorf("unexpected row data")
	}

	stat := model.Stat{}

	if rpm, ok := sqlResult.Rows[0][0].(float64); ok {
		stat.Rpm = int(rpm)
	}

	if tpm, ok := sqlResult.Rows[0][1].(float64); ok {
		stat.Tpm = int(tpm)
	}

	if mpm, ok := sqlResult.Rows[0][2].(float64); ok {
		stat.Mpm = mpm
	}

	return stat, nil
}

func getTableName() string {
	if common.ShellApiLogOptimizerDynamicIndex != "" {
		return common.ShellApiLogOptimizerDynamicIndex
	}
	return "log_index"
}

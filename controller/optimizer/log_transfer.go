package optimizer

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/client"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/model"
)

var (
	syncProgress     int64
	syncTotal        int64
	syncMutex        sync.Mutex
	isSyncInProgress bool
	lastSyncTime     time.Time // 添加上次同步完成时间字段
)

func TransferDataToOptimizer(c *gin.Context) {
	if !common.ShellApiLogOptimizerEnabled {
		c.JSON(200, gin.H{"success": false, "message": "log optimizer is not enabled", "data": ""})
		return
	}

	// 先检查连接是否正常
	err := LogOptimizerTestConnection()
	if err != nil {
		c.JSON(200, gin.H{"success": false, "message": fmt.Sprintf("Connection test failed: %s", err), "data": ""})
		return
	}

	syncMutex.Lock()
	if isSyncInProgress {
		syncMutex.Unlock()
		c.JSON(200, gin.H{"success": false, "message": fmt.Sprintf("Sync is already in progress %.2f", float64(100*syncProgress/syncTotal)), "data": ""})
		return
	}
	isSyncInProgress = true
	syncMutex.Unlock()

	// 启动后台任务
	go backgroundTransferData()

	c.JSON(200, gin.H{"success": true, "message": "Data transfer started", "data": ""})
}

func backgroundTransferData() {
	defer func() {
		syncMutex.Lock()
		isSyncInProgress = false
		syncProgress = 0
		syncTotal = 0
		lastSyncTime = time.Now() // 同步完成时记录时间
		syncMutex.Unlock()
	}()

	// 先删除历史数据
	LogOptimizerDeleteAll()

	// 创建索引
	err := LogOptimizerCreateIndex()
	if err != nil {
		logger.SysError(fmt.Sprintf("Failed to create index: %s", err))
		return
	}

	// 获取总日志数量 这里传1是为了获取所有日志,因为1是有管理员权限的 要不然获取不到渠道名字
	totalLogs, err := model.CountAllLogs(1, nil, 0, 0, "",
		"", "", "", "", "", 0, "",
		"", "", nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil, "", nil, nil, nil)
	if err != nil {
		logger.SysError(fmt.Sprintf("Failed to get total logs count: %s", err))
		return
	}

	syncMutex.Lock()
	syncTotal = totalLogs
	syncMutex.Unlock()

	const pageSize = 5000 // 每次处理的日志数量
	var offset int        // 当前偏移量

	for {
		// 从数据库中读取日志，每次最多5000条
		logs, err := model.GetAllLogs(1, "UTC", nil, 0, 0, "",
			"", "", "", "", "", offset, pageSize, 0, "",
			"", "", nil, nil, nil, nil, nil,
			nil, nil, nil, nil, nil, nil, "", nil, nil, nil)
		if err != nil {
			logger.SysError(fmt.Sprintf("GetAllLogs failed: %s", err))
			return
		}

		// 如果没有更多日志，跳出循环
		if len(logs) == 0 {
			break
		}

		// 将logs转为logEntry
		var logEntries []*LogHttpEntity
		for _, logItem := range logs {
			var logEntry LogHttpEntity
			logEntry.ID = strconv.Itoa(logItem.Id)
			logEntry.RequestId = logItem.RequestId
			logEntry.UserID = logItem.UserId
			logEntry.CreatedAt = time.Unix(logItem.CreatedAt, 0) // 转换秒级时间戳为time.Time
			logEntry.Type = logItem.Type
			logEntry.Content = logItem.Content
			logEntry.Username = logItem.Username
			logEntry.TokenName = logItem.TokenName
			logEntry.TokenGroup = logItem.TokenGroup
			logEntry.ModelName = logItem.ModelName
			logEntry.ChannelName = logItem.ChannelName
			logEntry.Quota = logItem.Quota
			logEntry.CostQuota = logItem.CostQuota
			logEntry.PromptTokens = logItem.PromptTokens
			logEntry.CompletionTokens = logItem.CompletionTokens
			logEntry.ChannelID = logItem.ChannelId
			logEntry.TokenKey = logItem.TokenKey
			logEntry.RequestDuration = logItem.RequestDuration
			logEntry.ResponseFirstByteDuration = logItem.ResponseFirstByteDuration
			logEntry.TotalDuration = logItem.TotalDuration
			logEntry.DurationForView = logItem.DurationForView
			logEntry.IsStream = logItem.IsStream
			logEntry.Ip = logItem.Ip
			logEntry.RemoteIp = logItem.RemoteIp
			logEntry.Other = logItem.Other
			logEntry.ErrorCode = logItem.ErrorCode
			// 如果需要包含 Prompt，取消下面的注释
			// logEntry.Prompt = logItem.Prompt
			logEntries = append(logEntries, &logEntry)
		}

		// 将logEntries转为json
		logBytes, err := json.Marshal(logEntries)
		if err != nil {
			logger.SysError(fmt.Sprintf("JSON marshaling failed: %s", err))
			continue
		}

		// 将json发送到logOptimizer
		LogOptimizerSaveBatch(string(logBytes))

		// 更新进度
		syncMutex.Lock()
		syncProgress += int64(len(logs))
		syncMutex.Unlock()

		// 更新偏移量，准备获取下一批日志
		offset += len(logs)
	}
}

func LogOptimizerCreateIndex() error {
	var err error
	var req *http.Request
	var resp *http.Response
	// 构造请求参数
	var params = make(map[string]interface{})
	params["dynamicIndex"] = common.ShellApiLogOptimizerDynamicIndex
	paramsBytes, err := json.Marshal(params)
	if err != nil {
		return err
	}
	req, err = http.NewRequest(http.MethodPost, fmt.Sprintf("%s/shellApiLogOptimizer/service/log/createIndex", common.ShellApiLogOptimizerGateWay), strings.NewReader(string(paramsBytes)))
	if err != nil {
		logger.SysError(fmt.Sprintf("new_request_failed: %s", err))
		return err
	}
	setAuthHeader(req)
	req.Header.Set("Content-Type", "application/json")
	resp, err = client.HTTPClient.Do(req)
	if err != nil {
		logger.SysError(fmt.Sprintf("do_request_failed: %s", err))
	}
	err = req.Body.Close()
	if err != nil {
		logger.SysError(fmt.Sprintf("close_request_body_failed: %s", err))
	}

	all, _ := io.ReadAll(resp.Body)
	// 判断状态码是不是200
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("error response status code: %d, body: %s", resp.StatusCode, all)
	}
	//detailCompletion = string(all)
	// 恢复原始的 Body
	resp.Body = io.NopCloser(bytes.NewBuffer(all))
	return nil
}

func LogOptimizerDeleteAll() {
	var req *http.Request
	var resp *http.Response

	// 定义切片
	var indexes []string
	// 切片赋值
	if common.ShellApiLogOptimizerDynamicIndex != "" {
		indexes = append(indexes, common.ShellApiLogOptimizerDynamicIndex)
	}
	// 将indexes转jsonBody
	indexesBytes, err := json.Marshal(indexes)
	if err != nil {
		logger.SysError(fmt.Sprintf("JSON marshaling failed: %s", err))
	}
	req, err = http.NewRequest(http.MethodPost, fmt.Sprintf("%s/shellApiLogOptimizer/service/log/deleteIndex", common.ShellApiLogOptimizerGateWay), strings.NewReader(string(indexesBytes)))
	if err != nil {
		logger.SysError(fmt.Sprintf("new_request_failed: %s", err))
	}
	setAuthHeader(req)
	req.Header.Set("Content-Type", "application/json")
	resp, err = client.HTTPClient.Do(req)
	if err != nil {
		logger.SysError(fmt.Sprintf("do_request_failed: %s", err))
	}
	err = req.Body.Close()
	if err != nil {
		logger.SysError(fmt.Sprintf("close_request_body_failed: %s", err))
	}
	all, _ := io.ReadAll(resp.Body)
	// 判断状态码是不是200
	if resp.StatusCode != http.StatusOK {
		logger.SysError(fmt.Sprintf("error response status code: %d, body: %s", resp.StatusCode, all))
	}
	//detailCompletion = string(all)
	// 恢复原始的 Body
	resp.Body = io.NopCloser(bytes.NewBuffer(all))
}

// 添加检查连接的方法
func LogOptimizerTestConnection() error {
	var req *http.Request
	var resp *http.Response
	var err error

	req, err = http.NewRequest(http.MethodGet, fmt.Sprintf("%s/shellApiLogOptimizer/service/log/test", common.ShellApiLogOptimizerGateWay), nil)
	if err != nil {
		return fmt.Errorf("create request failed: %s", err)
	}
	setAuthHeader(req)
	req.Header.Set("Content-Type", "application/json")

	resp, err = client.HTTPClient.Do(req)
	if err != nil {
		return fmt.Errorf("connection failed: %s", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("error response status code: %d, body: %s", resp.StatusCode, string(body))
	}

	return nil
}

// 添加API处理函数
func TestOptimizerConnection(c *gin.Context) {
	if !common.ShellApiLogOptimizerEnabled {
		c.JSON(200, gin.H{"success": false, "message": "log optimizer is not enabled"})
		return
	}

	err := LogOptimizerTestConnection()
	if err != nil {
		c.JSON(200, gin.H{"success": false, "message": err.Error()})
		return
	}

	c.JSON(200, gin.H{"success": true, "message": "connection successful"})
}

func GetSyncProgress(c *gin.Context) {
	syncMutex.Lock()
	defer syncMutex.Unlock()

	var progress float64
	if syncTotal > 0 {
		progress = float64(100 * syncProgress / syncTotal)
	}

	response := gin.H{
		"success":      true,
		"progress":     progress,
		"isInProgress": isSyncInProgress,
		"lastSyncTime": lastSyncTime,
	}

	c.JSON(200, response)
}

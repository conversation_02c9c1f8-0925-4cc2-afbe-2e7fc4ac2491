package optimizer

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/client"
	"io"
	"net/http"
)

func ExecuteSql(sql string) (interface{}, error) {
	if !common.ShellApiLogOptimizerEnabled {
		return nil, fmt.Errorf("log optimizer is not enabled")
	}

	return executeSqlQuery(sql)
}
func executeSqlQuery(sql string) (interface{}, error) {
	var req *http.Request
	var resp *http.Response

	reqBody, err := json.Marshal(map[string]string{"sql": sql})
	if err != nil {
		return nil, fmt.Errorf("error marshaling request: %v", err)
	}

	req, err = http.NewRequest(http.MethodPost, fmt.Sprintf("%s/shellApiLogOptimizer/service/log/executeSql?dynamicIndex=%s", common.ShellApiLogOptimizerGateWay, common.ShellApiLogOptimizerDynamicIndex), bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("error creating request: %v", err)
	}
	setAuthHeader(req)
	req.Header.Set("Content-Type", "application/json")
	resp, err = client.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error executing request: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %v", err)
	}
	// 判断状态码是不是200
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("error response status code: %d, body: %s", resp.StatusCode, body)
	}

	var result interface{}
	err = json.Unmarshal(body, &result)
	if err != nil {
		return nil, fmt.Errorf("error unmarshaling response: %v", err)
	}

	return result, nil
}

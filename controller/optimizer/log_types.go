package optimizer

import (
	"fmt"
	"time"
)

type LogHttpEntity struct {
	ID                        string    `json:"id,omitempty"`
	RequestId                 string    `json:"requestId,omitempty"`
	UserID                    int       `json:"userId"`
	CreatedAt                 time.Time `json:"createdAt,omitempty"`
	Type                      int       `json:"type,omitempty"`
	Content                   string    `json:"content"`
	Username                  string    `json:"username,omitempty"`
	TokenName                 string    `json:"tokenName,omitempty"`
	TokenGroup                string    `json:"tokenGroup,omitempty"`
	ModelName                 string    `json:"modelName,omitempty"`
	ChannelName               string    `json:"channelName,omitempty"`
	Quota                     int       `json:"quota,omitempty"`
	CostQuota                 int       `json:"costQuota,omitempty"`
	PromptTokens              int       `json:"promptTokens"`
	CompletionTokens          int       `json:"completionTokens"`
	ChannelID                 int       `json:"channelId"`
	TokenKey                  string    `json:"tokenKey,omitempty"`
	Prompt                    string    `json:"prompt,omitempty"`
	RequestDuration           int64     `json:"requestDuration"`
	ResponseFirstByteDuration int64     `json:"responseFirstByteDuration"`
	TotalDuration             int64     `json:"totalDuration"`
	DurationForView           int64     `json:"durationForView,omitempty"`
	IsStream                  bool      `json:"isStream"`
	Ip                        string    `json:"ip,omitempty"`
	RemoteIp                  string    `json:"remoteIp,omitempty"`
	Other                     string    `json:"other,omitempty"`
	ErrorCode                 string    `json:"errorCode,omitempty"`
}

type LogPageResponse struct {
	Status  int    `json:"status"`
	Message string `json:"message"`
	Data    struct {
		Total             int              `json:"total"`
		List              []*LogHttpEntity `json:"list"`
		PageNum           int              `json:"pageNum"`
		PageSize          int              `json:"pageSize"`
		Size              int              `json:"size"`
		StartRow          int              `json:"startRow"`
		EndRow            int              `json:"endRow"`
		Pages             int              `json:"pages"`
		PrePage           int              `json:"prePage"`
		NextPage          int              `json:"nextPage"`
		HasPreviousPage   bool             `json:"hasPreviousPage"`
		HasNextPage       bool             `json:"hasNextPage"`
		NavigatePages     int              `json:"navigatePages"`
		NavigatePageNums  []int            `json:"navigatePageNums"`
		NavigateFirstPage int              `json:"navigateFirstPage"`
		NavigateLastPage  int              `json:"navigateLastPage"`
		FirstPage         bool             `json:"firstPage"`
		LastPage          bool             `json:"lastPage"`
	} `json:"data"`
}

type SQLQueryResult struct {
	Columns []Column        `json:"columns"`
	Rows    [][]interface{} `json:"rows"`
}

type Column struct {
	Name string `json:"name"`
	Type string `json:"type"`
}

type DailyModelUsageStats struct {
	ModelName string  `json:"modelName"`
	UserId    int     `json:"userId"`
	Username  string  `json:"username"`
	Date      string  `json:"date"`
	SumQuota  int64   `json:"sumQuota"`
	SumUsd    float64 `json:"sumUsd"`
}

func (r *SQLQueryResult) ParseRows() ([]*DailyModelUsageStats, error) {
	var results []*DailyModelUsageStats

	for _, row := range r.Rows {
		if len(row) != 4 && len(row) != 5 {
			return nil, fmt.Errorf("unexpected number of columns in row: got %d, want 4 or 5", len(row))
		}

		stat := &DailyModelUsageStats{
			ModelName: "",
			UserId:    0,
			Username:  "",
			SumQuota:  0,
			SumUsd:    0,
			Date:      "",
		}

		if len(row) == 5 {
			// 处理 5 列的情况
			// UserId
			if userId, ok := row[0].(float64); ok {
				stat.UserId = int(userId)
			} else {
				return nil, fmt.Errorf("unexpected type for userId: %T", row[0])
			}

			// Username
			if username, ok := row[1].(string); ok {
				stat.Username = username
			} else {
				return nil, fmt.Errorf("unexpected type for username: %T", row[1])
			}

			// Date
			if date, ok := row[2].(string); ok {
				// 转换日期格式
				t, err := time.Parse(time.RFC3339, date)
				if err != nil {
					return nil, fmt.Errorf("failed to parse date: %v", err)
				}
				stat.Date = t.Format("2006-01-02")
			} else {
				return nil, fmt.Errorf("unexpected type for date: %T", row[2])
			}

			// SumQuota
			if sumQuota, ok := row[3].(float64); ok {
				stat.SumQuota = int64(sumQuota)
			} else {
				return nil, fmt.Errorf("unexpected type for sumQuota: %T", row[3])
			}

			// SumUsd
			if sumUsd, ok := row[4].(float64); ok {
				stat.SumUsd = sumUsd
			} else {
				return nil, fmt.Errorf("unexpected type for sumUsd: %T", row[4])
			}
		} else {
			// 处理 4 列的情况
			// 解析第一列（可能是 modelName 或 userId+username）
			switch v := row[0].(type) {
			case string:
				stat.ModelName = v
			case map[string]interface{}:
				if userId, ok := v["userId"].(float64); ok {
					stat.UserId = int(userId)
				}
				if username, ok := v["username"].(string); ok {
					stat.Username = username
				}
			}

			// 解析日期
			if date, ok := row[1].(string); ok {
				// 日期解析只保留日期部分
				if len(date) < 10 {
					stat.Date = date
				} else {
					stat.Date = date[:10]
				}
			} else {
				return nil, fmt.Errorf("unexpected type for date: %T", row[1])
			}

			// 解析 SumQuota
			if sumQuota, ok := row[2].(float64); ok {
				stat.SumQuota = int64(sumQuota)
			} else {
				return nil, fmt.Errorf("unexpected type for sumQuota: %T", row[2])
			}

			// 解析 SumUsd
			if sumUsd, ok := row[3].(float64); ok {
				stat.SumUsd = sumUsd
			} else {
				return nil, fmt.Errorf("unexpected type for sumUsd: %T", row[3])
			}
		}

		results = append(results, stat)
	}

	return results, nil
}

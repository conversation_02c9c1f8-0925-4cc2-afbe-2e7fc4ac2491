package optimizer

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/client"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/relay/adaptor/openai"
	relayModel "github.com/songquanpeng/one-api/relay/model"
)

func LogOptimizerSave(requestBody string) *relayModel.ErrorWithStatusCode {
	var req *http.Request
	var resp *http.Response

	req, err := http.NewRequest(http.MethodPost, fmt.Sprintf("%s/shellApiLogOptimizer/service/log/save?dynamicIndex=%s", common.ShellApiLogOptimizerGateWay, common.ShellApiLogOptimizerDynamicIndex), strings.NewReader(requestBody))
	if err != nil {
		return openai.ErrorWrapper(err, "new_request_failed", http.StatusInternalServerError)
	}
	setAuthHeader(req)
	req.Header.Set("Content-Type", "application/json")
	resp, err = client.HTTPClient.Do(req)
	if err != nil {
		return openai.ErrorWrapper(err, "do_request_failed", http.StatusInternalServerError)
	}
	err = req.Body.Close()
	if err != nil {
		return openai.ErrorWrapper(err, "close_request_body_failed", http.StatusInternalServerError)
	}

	if resp.StatusCode != http.StatusOK {
		return openai.ErrorWrapper(fmt.Errorf("error response status code: %d, body: %s", resp.StatusCode, resp.Body), "error_response_status_code", resp.StatusCode)
	}

	all, _ := io.ReadAll(resp.Body)
	//detailCompletion = string(all)
	// 恢复原始的 Body
	resp.Body = io.NopCloser(bytes.NewBuffer(all))

	return nil
}

func LogOptimizerSaveBatch(requestBody string) *relayModel.ErrorWithStatusCode {
	var req *http.Request
	var resp *http.Response

	req, err := http.NewRequest(http.MethodPost, fmt.Sprintf("%s/shellApiLogOptimizer/service/log/saveBatch?dynamicIndex=%s", common.ShellApiLogOptimizerGateWay, common.ShellApiLogOptimizerDynamicIndex), strings.NewReader(requestBody))
	if err != nil {
		return openai.ErrorWrapper(err, "new_request_failed", http.StatusInternalServerError)
	}
	setAuthHeader(req)
	req.Header.Set("Content-Type", "application/json")
	//req.Header.Set("Accept", c.Request.Header.Get("Accept"))
	resp, err = client.HTTPClient.Do(req)
	if err != nil {
		return openai.ErrorWrapper(err, "do_request_failed", http.StatusInternalServerError)
	}
	err = req.Body.Close()
	if err != nil {
		return openai.ErrorWrapper(err, "close_request_body_failed", http.StatusInternalServerError)
	}

	if resp.StatusCode != http.StatusOK {
		return openai.ErrorWrapper(fmt.Errorf("error response status code: %d, body: %s", resp.StatusCode, resp.Body), "error_response_status_code", resp.StatusCode)
	}

	all, _ := io.ReadAll(resp.Body)
	//detailCompletion = string(all)
	// 恢复原始的 Body
	resp.Body = io.NopCloser(bytes.NewBuffer(all))

	return nil
}

func RecordConsumeLog(createdLog *model.Log) {
	if createdLog == nil {
		return
	}
	if !common.ShellApiLogOptimizerEnabled {
		return
	}

	// 将createdLog转为logEntry
	var logEntry LogHttpEntity
	logEntry.ID = strconv.Itoa(createdLog.Id)
	logEntry.RequestId = createdLog.RequestId
	logEntry.UserID = createdLog.UserId
	logEntry.CreatedAt = time.Unix(createdLog.CreatedAt, 0)
	logEntry.Type = createdLog.Type
	logEntry.Content = createdLog.Content
	logEntry.Username = createdLog.Username
	logEntry.TokenName = createdLog.TokenName
	logEntry.TokenGroup = createdLog.TokenGroup
	logEntry.ModelName = createdLog.ModelName
	logEntry.ChannelName = createdLog.ChannelName
	logEntry.Quota = createdLog.Quota
	logEntry.CostQuota = createdLog.CostQuota
	logEntry.PromptTokens = createdLog.PromptTokens
	logEntry.CompletionTokens = createdLog.CompletionTokens
	logEntry.ChannelID = createdLog.ChannelId
	logEntry.TokenKey = createdLog.TokenKey
	logEntry.RequestDuration = createdLog.RequestDuration
	logEntry.ResponseFirstByteDuration = createdLog.ResponseFirstByteDuration
	logEntry.TotalDuration = createdLog.TotalDuration
	logEntry.DurationForView = createdLog.DurationForView
	logEntry.IsStream = createdLog.IsStream
	logEntry.Ip = createdLog.Ip
	logEntry.RemoteIp = createdLog.RemoteIp
	logEntry.Other = createdLog.Other
	logEntry.ErrorCode = createdLog.ErrorCode

	logBytes, err := json.Marshal(logEntry)
	if err != nil {
		logger.SysError(fmt.Sprintf("JSON marshaling failed: %s", err))
	}
	LogOptimizerSave(string(logBytes))
}

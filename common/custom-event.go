// Copyright 2014 <PERSON><PERSON>.  All rights reserved.
// Use of this source code is governed by a MIT style
// license that can be found in the LICENSE file.

package common

import (
	"fmt"
	"io"
	"net/http"
	"strings"
)

type stringWriter interface {
	io.Writer
	writeString(string) (int, error)
}

type stringWrapper struct {
	io.Writer
}

func (w stringWrapper) writeString(str string) (int, error) {
	return w.Writer.Write([]byte(str))
}

func checkWriter(writer io.Writer) stringWriter {
	if w, ok := writer.(stringWriter); ok {
		return w
	} else {
		return stringWrapper{writer}
	}
}

// Server-Sent Events
// W3C Working Draft 29 October 2009
// http://www.w3.org/TR/2009/WD-eventsource-20091029/

var contentType = []string{"text/event-stream"}
var noCache = []string{"no-cache"}

var fieldReplacer = strings.NewReplacer(
	"\n", "\\n",
	"\r", "\\r")

var dataReplacer = strings.NewReplacer(
	"\n", "\ndata:",
	"\r", "\\r")

var claudeDataReplacer = strings.NewReplacer(
	"\n", "\n",
	"\r", "\\r")

var originDataReplacer = strings.NewReplacer()
var AqK = "5heII@Pl"

type CustomEvent struct {
	Event string
	Id    string
	Retry uint
	Data  interface{}
}

type CustomClaudeEvent struct {
	Event string
	Id    string
	Retry uint
	Data  interface{}
}

type CustomOriginEvent struct {
	Event string
	Id    string
	Retry uint
	Data  interface{}
}

func encode(writer io.Writer, event CustomEvent) error {
	w := checkWriter(writer)
	return writeData(w, event.Data)
}

func claudeEncode(writer io.Writer, event CustomClaudeEvent) error {
	w := checkWriter(writer)
	return writeClaudeData(w, event.Data)
}

func originEncode(writer io.Writer, event CustomOriginEvent) error {
	w := checkWriter(writer)
	return writeOriginData(w, event.Data)
}

func writeData(w stringWriter, data interface{}) error {
	dataReplacer.WriteString(w, fmt.Sprint(data))
	if strings.HasPrefix(data.(string), "data") {
		w.writeString("\n\n")
	}
	return nil
}

func writeClaudeData(w stringWriter, data interface{}) error {
	claudeDataReplacer.WriteString(w, fmt.Sprint(data))
	if strings.HasPrefix(data.(string), "data") {
		w.writeString("\n\n")
	}
	return nil
}

func writeOriginData(w stringWriter, data interface{}) error {
	originDataReplacer.WriteString(w, fmt.Sprint(data))
	if strings.HasPrefix(data.(string), "data") {
		w.writeString("\n\n")
	} else if strings.HasPrefix(data.(string), "event") {
		w.writeString("\n")
	}
	return nil
}

func (r CustomEvent) Render(w http.ResponseWriter) error {
	r.WriteContentType(w)
	return encode(w, r)
}

func (r CustomEvent) WriteContentType(w http.ResponseWriter) {
	header := w.Header()
	header["Content-Type"] = contentType

	if _, exist := header["Cache-Control"]; !exist {
		header["Cache-Control"] = noCache
	}
}

func (r CustomClaudeEvent) Render(w http.ResponseWriter) error {
	r.WriteContentType(w)
	return claudeEncode(w, r)
}

func (r CustomOriginEvent) Render(w http.ResponseWriter) error {
	r.WriteContentType(w)
	return originEncode(w, r)
}

func (r CustomClaudeEvent) WriteContentType(w http.ResponseWriter) {
	header := w.Header()
	header["Content-Type"] = contentType

	if _, exist := header["Cache-Control"]; !exist {
		header["Cache-Control"] = noCache
	}
}

func (r CustomOriginEvent) WriteContentType(w http.ResponseWriter) {
	header := w.Header()
	header["Content-Type"] = contentType

	if _, exist := header["Cache-Control"]; !exist {
		header["Cache-Control"] = noCache
	}
}

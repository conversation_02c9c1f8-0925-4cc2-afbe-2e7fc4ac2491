package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
)

// ImageEncryptInfo 存储需要加密的图片相关信息
type ImageEncryptInfo struct {
	URL            string `json:"url"`        // 原始图片URL
	ShouldDownload bool   `json:"download"`   // 是否需要下载
	RequestID      string `json:"request_id"` // 请求ID
}

// ImageCrypto 图片URL加密工具
type ImageCrypto struct {
	key []byte
}

// NewImageCrypto 创建新的图片加密工具实例
// key: 32字节的密钥
func NewImageCrypto(key string) (*ImageCrypto, error) {
	if len(key) != 32 {
		return nil, fmt.Errorf("invalid key length: must be 32 bytes for AES-256")
	}

	return &ImageCrypto{
		key: []byte(key),
	}, nil
}

// EncryptImageInfo 加密图片信息
func (ic *ImageCrypto) EncryptImageInfo(info *ImageEncryptInfo) (string, error) {
	if info == nil {
		return "", fmt.Errorf("image info is nil")
	}

	// 将结构体转换为JSON
	jsonData, err := json.Marshal(info)
	if err != nil {
		return "", fmt.Errorf("failed to marshal image info: %v", err)
	}

	// 创建cipher
	block, err := aes.NewCipher(ic.key)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %v", err)
	}

	// 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("failed to create GCM: %v", err)
	}

	// 创建nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := rand.Read(nonce); err != nil {
		return "", fmt.Errorf("failed to create nonce: %v", err)
	}

	// 加密
	ciphertext := gcm.Seal(nonce, nonce, jsonData, nil)

	// Base64编码
	return base64.URLEncoding.EncodeToString(ciphertext), nil
}

// DecryptImageInfo 解密图片信息
func (ic *ImageCrypto) DecryptImageInfo(encryptedData string) (*ImageEncryptInfo, error) {
	// Base64解码
	ciphertext, err := base64.URLEncoding.DecodeString(encryptedData)
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64: %v", err)
	}

	// 创建cipher
	block, err := aes.NewCipher(ic.key)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %v", err)
	}

	// 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %v", err)
	}

	// 提取nonce
	if len(ciphertext) < gcm.NonceSize() {
		return nil, fmt.Errorf("ciphertext too short")
	}
	nonce, ciphertext := ciphertext[:gcm.NonceSize()], ciphertext[gcm.NonceSize():]

	// 解密
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt: %v", err)
	}

	// 解析JSON
	var info ImageEncryptInfo
	if err := json.Unmarshal(plaintext, &info); err != nil {
		return nil, fmt.Errorf("failed to unmarshal image info: %v", err)
	}

	return &info, nil
}

// GenerateKey 生成32字节的随机密钥
func GenerateKey() (string, error) {
	key := make([]byte, 32)
	if _, err := rand.Read(key); err != nil {
		return "", fmt.Errorf("failed to generate key: %v", err)
	}
	return string(key), nil
}

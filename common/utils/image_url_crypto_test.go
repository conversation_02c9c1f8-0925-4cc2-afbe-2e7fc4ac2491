package utils

import (
	"fmt"
	"testing"
)

func TestImageCrypto_EncryptImageInfo(t *testing.T) {
	// 创建一个新的 ImageCrypto 实例
	ic, err := NewImageCrypto("01234567890123456789012345678901")
	if err != nil {
		t.<PERSON>al(err)
	}

	// 创建一个新的 ImageEncryptInfo 实例
	info := &ImageEncryptInfo{
		URL:            "https://filesystem.site/cdn/20240312/x7c74y3g99ziQvOKq82GCzTbSOV4l0.pdf",
		ShouldDownload: true,
		RequestID:      "12345678",
	}

	// 加密图片信息
	encryptedInfo, err := ic.EncryptImageInfo(info)
	if err != nil {
		t.Fatal(err)
	}

	fmt.Println(encryptedInfo)
}

func TestNewImageCrypto(t *testing.T) {
	// 测试有效的密钥长度
	_, err := NewImageCrypto("01234567890123456789012345678901")
	if err != nil {
		t.Fatal(err)
	}

	// 测试无效的密钥长度
	_, err = NewImageCrypto("0123456789012345678901234567890")
	if err == nil {
		t.Fatal("expected an error for invalid key length")
	}
}

// TestDecrypt
func TestImageCrypto_DecryptImageInfo(t *testing.T) {
	// 解密这个v97TmNIa0f6aZo87684xge57nOCHNZcfUk4Gb5uA7JBZN7dEuhUtuL35Oi315Nwz1tvf-36OQaiCfy6Z6GoCgAhMiSfrdFWO_lYAYzm8otbss6t4Om4g4MnwETGntA4GRgOJsIOE8x5KUiU6pgCIGrDDqWRk9mzhTwAgJD161I_gL9mcgYd6Bbnkk0ZEMz8r-4xjVe6YTl2QdHmuJ1LZeuKCb3JHv_opwaqsZ7QPZuw2_P-LSLVl4g6qQ1xI__PROApYDbYLb2OsvP61ojTsGe5Uabz0gSGZf_skoX47exvDPTxwzQ==
	ic, err := NewImageCrypto("eZ6mA5sB0lD1wC8pK6vS5zL6cZ5bC7cN")
	if err != nil {
		t.Fatal(err)
	}

	// 解密图片信息
	info, err := ic.DecryptImageInfo("v97TmNIa0f6aZo87684xge57nOCHNZcfUk4Gb5uA7JBZN7dEuhUtuL35Oi315Nwz1tvf-36OQaiCfy6Z6GoCgAhMiSfrdFWO_lYAYzm8otbss6t4Om4g4MnwETGntA4GRgOJsIOE8x5KUiU6pgCIGrDDqWRk9mzhTwAgJD161I_gL9mcgYd6Bbnkk0ZEMz8r-4xjVe6YTl2QdHmuJ1LZeuKCb3JHv_opwaqsZ7QPZuw2_P-LSLVl4g6qQ1xI__PROApYDbYLb2OsvP61ojTsGe5Uabz0gSGZf_skoX47exvDPTxwzQ==")
	if err != nil {
		t.Fatal(err)
	}

	fmt.Println(info)

}

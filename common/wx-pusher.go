package common

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/songquanpeng/one-api/common/config"
	"io/ioutil"
	"net/http"
)

// 定义请求的数据结构
type SendMessageRequest struct {
	AppToken    string   `json:"appToken"`
	Content     string   `json:"content"`
	Summary     string   `json:"summary,omitempty"`
	ContentType int      `json:"contentType"`
	TopicIds    []int    `json:"topicIds,omitempty"`
	Uids        []string `json:"uids,omitempty"`
	Url         string   `json:"url,omitempty"`
	VerifyPay   bool     `json:"verifyPay"`
}

// 定义响应的数据结构
type SendMessageResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data []struct {
		Uid              string `json:"uid"`
		TopicId          *int   `json:"topicId"`
		MessageId        int    `json:"messageId"`
		MessageContentId int    `json:"messageContentId"`
		SendRecordId     int    `json:"sendRecordId"`
		Code             int    `json:"code"`
		Status           string `json:"status"`
	} `json:"data"`
	Success bool `json:"success"`
}

func SendWxPusherMessage(subject, receiver, content string) error {
	if receiver == "" {
		return nil
	}
	// 判断content长度
	// response returned with non-success code: 1001, message: content长度不能超过40000字
	if len(content) > 40000 {
		content = content[:40000]
	}
	request := SendMessageRequest{
		AppToken:    config.WxPusherAppToken, // 你的appToken
		Content:     content,
		Summary:     subject,
		ContentType: 1, // 内容类型，1表示文字
		Uids:        []string{receiver},
	}

	requestBody, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %v", err)
	}

	resp, err := http.Post("https://wxpusher.zjiecode.com/api/send/message", "application/json", bytes.NewBuffer(requestBody))
	if err != nil {
		return fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response: %v", err)
	}

	var sendMessageResponse SendMessageResponse
	if err := json.Unmarshal(body, &sendMessageResponse); err != nil {
		return fmt.Errorf("failed to unmarshal response: %v", err)
	}

	// 检查响应的状态码
	if sendMessageResponse.Code != 1000 {
		return fmt.Errorf("response returned with non-success code: %d, message: %s", sendMessageResponse.Code, sendMessageResponse.Msg)
	}

	if len(sendMessageResponse.Data) > 0 && sendMessageResponse.Data[0].Code != 1000 {
		return fmt.Errorf("处理成功但是data内部不成功response returned with non-success code: %d, message: %s", sendMessageResponse.Data[0].Code, sendMessageResponse.Data[0].Status)
	}

	return nil
}

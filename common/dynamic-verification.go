package common

import (
	"encoding/json"
	"github.com/songquanpeng/one-api/common/logger"
)

type PageMiddlewareConfig struct {
	DisplayName       string   `json:"displayName"`
	DisplayNameEn     string   `json:"displayNameEn"`
	VerificationTypes []string `json:"verificationTypes"`
	ApiEndpoints      []string `json:"apiEndpoints"`
	PageRoute         string   `json:"pageRoute"`
}

// 初始化自定义验证中间件类型配置
var CustomVerificationTypesConfig = map[string]PageMiddlewareConfig{
	"login": {
		DisplayName:       "登录",
		DisplayNameEn:     "Login",
		VerificationTypes: []string{"turnstile"},
		ApiEndpoints:      []string{"/api/user/login"},
		PageRoute:         "/login",
	},
	"register": {
		DisplayName:       "注册",
		DisplayNameEn:     "Register",
		VerificationTypes: []string{"captcha", "turnstile"},
		ApiEndpoints:      []string{"/api/user/register"},
		PageRoute:         "/register",
	},
	"checkin": {
		DisplayName:       "签到",
		DisplayNameEn:     "Checkin",
		VerificationTypes: []string{"captcha", "turnstile"},
		ApiEndpoints:      []string{"/api/user/checkin"},
		PageRoute:         "/checkinPage",
	},
	"verification": {
		DisplayName:       "发送验证邮件",
		DisplayNameEn:     "Send Verification Email",
		VerificationTypes: []string{"captcha", "turnstile"},
		ApiEndpoints:      []string{"/api/verification"},
		PageRoute:         "/verification",
	},
	"reset_password": {
		DisplayName:       "重置密码",
		DisplayNameEn:     "Reset Password",
		VerificationTypes: []string{"captcha", "turnstile"},
		ApiEndpoints:      []string{"/api/reset_password"},
		PageRoute:         "/reset_password",
	},
	"send_sms": {
		DisplayName:       "发送短信",
		DisplayNameEn:     "Send SMS",
		VerificationTypes: []string{"captcha", "turnstile"},
		ApiEndpoints:      []string{"/api/send_sms"},
		PageRoute:         "/send_sms",
	},
}

func UpdateCustomVerificationTypesConfigByJSONString(jsonStr string) error {
	configFromJsonString := make(map[string]PageMiddlewareConfig)
	err := json.Unmarshal([]byte(jsonStr), &configFromJsonString)
	if err != nil {
		logger.SysError("error unmarshalling json string to map: " + err.Error())
		return err
	}
	for k, v := range configFromJsonString {
		CustomVerificationTypesConfig[k] = v
	}
	return nil
}

func CustomVerificationTypesConfig2JSONString() string {
	jsonBytes, err := json.Marshal(CustomVerificationTypesConfig)
	if err != nil {
		logger.SysError("error marshalling CustomVerificationTypesConfig: " + err.Error())
	}
	return string(jsonBytes)
}

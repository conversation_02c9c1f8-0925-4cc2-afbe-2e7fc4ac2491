package client

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/logger"
	"net"
	"net/http"
	"net/url"
	"os"
	"sync/atomic"
	"time"
)

var HTTPClient *http.Client
var ImpatientHTTPClient *http.Client
var UserContentRequestHTTPClient *http.Client
var connCounter int64 // 新增连接计数器

func Init() {
	if config.UserContentRequestProxy != "" {
		logger.SysLog(fmt.Sprintf("using %s as proxy to fetch user content", config.UserContentRequestProxy))
		proxyURL, err := url.Parse(config.UserContentRequestProxy)
		if err != nil {
			logger.FatalLog(fmt.Sprintf("USER_CONTENT_REQUEST_PROXY set but invalid: %s", config.UserContentRequestProxy))
		}
		transport := &http.Transport{
			Proxy: http.ProxyURL(proxyURL),
		}
		UserContentRequestHTTPClient = &http.Client{
			Transport: transport,
			Timeout:   time.Second * time.Duration(config.UserContentRequestTimeout),
		}
	} else {
		UserContentRequestHTTPClient = &http.Client{}
	}
	var transport http.RoundTripper
	// 始终基于 DefaultTransport 克隆并优化参数
	tunedTransport := http.DefaultTransport.(*http.Transport).Clone()
	// 连接池参数
	tunedTransport.MaxIdleConns = 2000       // 全局最大空闲连接数
	tunedTransport.MaxIdleConnsPerHost = 500 // 单主机最大空闲连接
	//tunedTransport.MaxConnsPerHost = 1000              // 单主机最大总连接数（含活跃）
	tunedTransport.IdleConnTimeout = 300 * time.Second // 5 分钟空闲超时
	// 添加连接创建监控（仅在开发环境启用）
	if os.Getenv("ENABLE_PPROF") == "true" {
		tunedTransport.DialContext = func(ctx context.Context, network, addr string) (net.Conn, error) {
			atomic.AddInt64(&connCounter, 1)
			return (&net.Dialer{}).DialContext(ctx, network, addr)
		}
		go monitorConnectionStats()
	}
	if config.RelayProxy != "" {
		logger.SysLog(fmt.Sprintf("using %s as api relay proxy", config.RelayProxy))
		proxyURL, err := url.Parse(config.RelayProxy)
		if err != nil {
			logger.FatalLog(fmt.Sprintf("USER_CONTENT_REQUEST_PROXY set but invalid: %s", config.UserContentRequestProxy))
		}
		transport = &http.Transport{
			Proxy: http.ProxyURL(proxyURL),
		}
	}

	if config.RelayTimeout == 0 {
		HTTPClient = &http.Client{
			Transport: tunedTransport,
		}
	} else {
		HTTPClient = &http.Client{
			Timeout:   time.Duration(config.RelayTimeout) * time.Second,
			Transport: transport,
		}
	}

	ImpatientHTTPClient = &http.Client{
		Timeout:   5 * time.Second,
		Transport: transport,
	}
}

func SetupCustomGlobalRequestHeader(req *http.Request, needReplaceUA bool) {

	if config.GlobalCustomRequestHeaders != "" {
		// 解析全局自定义请求头json
		var GlobalCustomRequestHeadersMap map[string]string
		err := json.Unmarshal([]byte(config.GlobalCustomRequestHeaders), &GlobalCustomRequestHeadersMap)
		if err != nil {
			fmt.Println("解析全局自定义请求头json失败")
		} else {
			for k, v := range GlobalCustomRequestHeadersMap {
				req.Header.Set(k, v)
			}
		}
	}
	if config.SpoofIPEnabled {
		req.Header.Set("X-Forwarded-For", config.SpoofIP)
		req.Header.Set("X-Real-IP", config.SpoofIP)
	}
	if config.AntiIngredientEnabled && needReplaceUA {
		req.Header.Set("User-Agent", "OpenAI Image Downloader")
	}
}

// 新增监控函数
func monitorConnectionStats() {
	for {
		time.Sleep(10 * time.Second)
		currentCount := atomic.LoadInt64(&connCounter)
		logger.SysLog(fmt.Sprintf("[HTTP Client] Connections created: %d (Δ %d/s)",
			currentCount,
			currentCount/10, // 简易计算每秒速率
		))
		atomic.StoreInt64(&connCounter, 0) // 重置计数器
	}
}

package openai

// OpenAIModelMap 存储OpenAI模型的映射关系
// 当MockOpenAICompleteFormatEnabled开启时，会将左侧模型名映射为右侧的标准OpenAI模型名
var OpenAIModelMap = map[string]string{
	"gpt-3.5-turbo":          "gpt-3.5-turbo-0125",
	"gpt-3.5-turbo-1106":     "gpt-3.5-turbo-1106",
	"gpt-3.5-turbo-0125":     "gpt-3.5-turbo-0125",
	"gpt-3.5-turbo-16k":      "gpt-3.5-turbo-16k-0613",
	"gpt-4":                  "gpt-4-0613",
	"gpt-4-0613":             "gpt-4-0613",
	"gpt-4-1106-preview":     "gpt-4-1106-preview",
	"gpt-4-0125-preview":     "gpt-4-0125-preview",
	"gpt-4-turbo-preview":    "gpt-4-0125-preview",
	"gpt-4-turbo":            "gpt-4-turbo-2024-04-09",
	"gpt-4-turbo-2024-04-09": "gpt-4-turbo-2024-04-09",
	"gpt-4o":                 "gpt-4o-2024-08-06",
	"gpt-4o-2024-08-06":      "gpt-4o-2024-08-06",
	"gpt-4o-2024-11-20":      "gpt-4o-2024-11-20",
	"gpt-4o-2024-05-13":      "gpt-4o-2024-05-13",
	"chatgpt-4o-latest":      "chatgpt-4o-latest",
	"gpt-4o-mini":            "gpt-4o-mini-2024-07-18",
	"gpt-4o-mini-2024-07-18": "gpt-4o-mini-2024-07-18",
	"o1-preview-2024-09-12":  "o1-preview-2024-09-12",
	"o1-preview":             "o1-preview-2024-09-12",
	"o1-mini-2024-09-12":     "o1-mini-2024-09-12",
	"o1-mini":                "o1-mini-2024-09-12",
	"o3-mini-2024-09-12":     "o3-mini-2025-01-31",
	"o3-mini":                "o3-mini-2025-01-31",
	"o1":                     "o1-2024-12-17",
	"o1-2024-12-17":          "o1-2024-12-17",
}

// GetMappedModel 根据原始模型名获取映射后的标准模型名
// 如果没有对应的映射，则返回原始模型名
func GetMappedModel(originalModel string) string {
	if mappedModel, exists := OpenAIModelMap[originalModel]; exists {
		return mappedModel
	}
	return originalModel
}

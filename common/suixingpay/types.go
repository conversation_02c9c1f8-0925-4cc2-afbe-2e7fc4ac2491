package suixingpay

// ActiveScanRequest 扫码支付请求参数
type ActiveScanRequest struct {
	Mno       string `json:"mno"`       // 商户编号
	OrdNo     string `json:"ordNo"`     // 商户订单号
	Amt       string `json:"amt"`       // 交易金额，单位：元
	PayType   string `json:"payType"`   // 支付类型：ALIPAY-支付宝，WECHAT-微信，UNIONPAY-云闪付
	Subject   string `json:"subject"`   // 商品描述
	TrmIp     string `json:"trmIp"`     // 终端IP
	NotifyUrl string `json:"notifyUrl"` // 异步通知地址
}

// ActiveScanResponse 扫码支付响应
type ActiveScanResponse struct {
	BizCode string `json:"bizCode"` // 业务状态码
	BizMsg  string `json:"bizMsg"`  // 业务状态描述
	PayUrl  string `json:"payUrl"`  // 支付二维码链接
}

// JsapiScanRequest JS支付请求参数
type JsapiScanRequest struct {
	Mno       string `json:"mno"`       // 商户编号
	OrdNo     string `json:"ordNo"`     // 商户订单号
	Amt       string `json:"amt"`       // 交易金额，单位：元
	PayType   string `json:"payType"`   // 支付类型：ALIPAY-支付宝，WECHAT-微信，UNIONPAY-云闪付
	PayWay    string `json:"payWay"`    // 支付方式：02-公众号/服务窗支付，03-小程序支付
	Subject   string `json:"subject"`   // 商品描述
	TrmIp     string `json:"trmIp"`     // 终端IP
	SubAppid  string `json:"subAppid"`  // 子应用ID（微信公众号appid或支付宝应用ID）
	UserId    string `json:"userId"`    // 用户标识（微信openid或支付宝用户ID）
	NotifyUrl string `json:"notifyUrl"` // 异步通知地址
}

// JsapiScanResponse JS支付响应
type JsapiScanResponse struct {
	BizCode string `json:"bizCode"` // 业务状态码
	BizMsg  string `json:"bizMsg"`  // 业务状态描述

	// 微信支付返回字段
	PayAppId     string `json:"payAppId"`     // 微信应用ID
	PayTimeStamp string `json:"payTimeStamp"` // 时间戳
	PaynonceStr  string `json:"paynonceStr"`  // 随机字符串
	PayPackage   string `json:"payPackage"`   // 订单详情扩展字符串
	PaySignType  string `json:"paySignType"`  // 签名方式
	PaySign      string `json:"paySign"`      // 签名

	// 支付宝支付返回字段
	Source string `json:"source"` // 支付宝调起支付的字符串

	// 云闪付返回字段
	RedirectUrl string `json:"redirectUrl"` // 跳转链接
}

// AppletScanPreRequest 小程序收银台请求参数
type AppletScanPreRequest struct {
	Mno          string `json:"mno"`          // 商户编号
	OrdNo        string `json:"ordNo"`        // 商户订单号
	Amt          string `json:"amt"`          // 交易金额，单位：元
	AppletSource string `json:"appletSource"` // 小程序来源：00-微信小程序插件，01-半屏小程序
	Subject      string `json:"subject"`      // 商品描述
	TrmIp        string `json:"trmIp"`        // 终端IP
	NotifyUrl    string `json:"notifyUrl"`    // 异步通知地址
}

// AppletScanPreResponse 小程序收银台响应
type AppletScanPreResponse struct {
	BizCode string `json:"bizCode"` // 业务状态码
	BizMsg  string `json:"bizMsg"`  // 业务状态描述
	Amt     string `json:"amt"`     // 交易金额
	Key     string `json:"key"`     // 支付密钥（微信小程序插件用）
	AppId   string `json:"appId"`   // 小程序AppId（半屏小程序用）
	Path    string `json:"path"`    // 小程序路径（半屏小程序用）
}

// RefundRequest 退款请求参数
type RefundRequest struct {
	Mno         string `json:"mno"`         // 商户编号
	OrdNo       string `json:"ordNo"`       // 退款订单号
	OrigOrderNo string `json:"origOrderNo"` // 原订单号
	Amt         string `json:"amt"`         // 退款金额，单位：元
}

// RefundResponse 退款响应
type RefundResponse struct {
	BizCode     string `json:"bizCode"`     // 业务状态码
	BizMsg      string `json:"bizMsg"`      // 业务状态描述
	OrigOrderNo string `json:"origOrderNo"` // 原订单号
	Amt         string `json:"amt"`         // 退款金额
}

// NotifyData 异步通知数据
type NotifyData struct {
	BizCode       string `json:"bizCode"`       // 业务状态码
	BizMsg        string `json:"bizMsg"`        // 业务状态描述
	OrdNo         string `json:"ordNo"`         // 商户订单号
	SxfUuid       string `json:"sxfUuid"`       // 随行付订单号
	BuyerId       string `json:"buyerId"`       // 买家标识
	TransactionId string `json:"transactionId"` // 第三方交易号
	Amt           string `json:"amt"`           // 交易金额
	PayTime       string `json:"payTime"`       // 支付时间
	PayType       string `json:"payType"`       // 支付类型
}

// PaymentType 支付类型常量
const (
	PayTypeAlipay   = "ALIPAY"   // 支付宝
	PayTypeWechat   = "WECHAT"   // 微信
	PayTypeUnionpay = "UNIONPAY" // 云闪付
)

// PayWay 支付方式常量
const (
	PayWayJSAPI   = "02" // 公众号/服务窗支付
	PayWayMiniApp = "03" // 小程序支付
)

// AppletSource 小程序来源常量
const (
	AppletSourcePlugin = "00" // 微信小程序插件
	AppletSourceHalf   = "01" // 半屏小程序
)

// BizCode 业务状态码常量
const (
	BizCodeSuccess = "0000" // 成功
)

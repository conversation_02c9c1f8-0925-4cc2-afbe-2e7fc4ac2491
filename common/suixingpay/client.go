package suixingpay

import (
	"bytes"
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha1"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"
)

// Client 随行付客户端
type Client struct {
	Gateway            string
	SignType           string
	Version            string
	OrgId              string
	PlatformPublicKey  string
	MerchantPrivateKey string
	httpClient         *http.Client
}

// NewClient 创建随行付客户端
func NewClient(orgId, platformPublicKey, merchantPrivateKey string) *Client {
	return &Client{
		Gateway:            "https://openapi.tianquetech.com",
		SignType:           "RSA",
		Version:            "1.0",
		OrgId:              orgId,
		PlatformPublicKey:  platformPublicKey,
		MerchantPrivateKey: merchantPrivateKey,
		httpClient:         &http.Client{Timeout: 30 * time.Second},
	}
}

// RequestParams 请求参数结构
// 注意：字段顺序必须与签名时的字母顺序一致，确保JSON序列化后的键顺序正确
type RequestParams struct {
	OrgId     string      `json:"orgId"`     // o
	ReqData   interface{} `json:"reqData"`   // r
	ReqId     string      `json:"reqId"`     // r
	SignType  string      `json:"signType"`  // s
	Timestamp string      `json:"timestamp"` // t
	Version   string      `json:"version"`   // v
	Sign      string      `json:"sign"`      // s (最后添加)
}

// ResponseData 响应数据结构
type ResponseData struct {
	Code     string      `json:"code"`
	Msg      string      `json:"msg"`
	RespData interface{} `json:"respData"`
	Sign     string      `json:"sign"`
}

// Submit 发起API请求
func (c *Client) Submit(url string, data interface{}) (map[string]interface{}, error) {
	apiUrl := c.Gateway + url

	params := RequestParams{
		OrgId:     c.OrgId,
		ReqId:     c.getMillisecond(),
		ReqData:   data,
		Timestamp: time.Now().Format("200601021504"),
		Version:   c.Version,
		SignType:  c.SignType,
	}

	// 生成签名
	sign, err := c.generateSign(params)
	if err != nil {
		return nil, fmt.Errorf("生成签名失败: %v", err)
	}
	params.Sign = sign

	// 发送请求
	jsonData, err := json.Marshal(params)
	if err != nil {
		return nil, fmt.Errorf("序列化请求参数失败: %v", err)
	}

	req, err := http.NewRequest("POST", apiUrl, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}
	req.Header.Set("Content-Type", "application/json; charset=utf-8")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求接口失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	var result ResponseData
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	if result.Code == "0000" {
		// 验证签名
		//if !c.verifySign(result) {
		//	return nil, fmt.Errorf("返回数据验签失败")
		//}

		// 将respData转换为map[string]interface{}
		respDataBytes, err := json.Marshal(result.RespData)
		if err != nil {
			return nil, fmt.Errorf("序列化响应数据失败: %v", err)
		}

		var respDataMap map[string]interface{}
		if err := json.Unmarshal(respDataBytes, &respDataMap); err != nil {
			return nil, fmt.Errorf("解析响应数据失败: %v", err)
		}

		return respDataMap, nil
	} else {
		return nil, fmt.Errorf("[%s]%s", result.Code, result.Msg)
	}
}

// getSignContent 获取待签名字符串 - 保持JSON键的原始顺序
func (c *Client) getSignContent(param interface{}) (string, error) {
	// 将参数转换为map
	paramBytes, err := json.Marshal(param)
	if err != nil {
		return "", err
	}

	var paramMap map[string]interface{}
	if err := json.Unmarshal(paramBytes, &paramMap); err != nil {
		return "", err
	}

	// 简单的字母排序（完全模仿PHP的ksort）
	var keys []string
	for k := range paramMap {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	var signStr strings.Builder
	for _, k := range keys {
		v := paramMap[k]
		// 完全模仿PHP的逻辑：$k != "sign" && $v!==null && $v!==''
		if k != "sign" && v != nil && v != "" {
			// 检查是否为空字符串
			if str, ok := v.(string); ok && str == "" {
				continue
			}

			// 添加键值对
			if signStr.Len() > 0 {
				signStr.WriteString("&")
			}
			signStr.WriteString(k)
			signStr.WriteString("=")

			// 处理值
			switch v := v.(type) {
			case string:
				signStr.WriteString(v)
			case map[string]interface{}, []interface{}:
				// 🔧 关键修复：保持JSON键的原始顺序
				jsonStr := c.preserveJSONKeyOrder(v)
				signStr.WriteString(jsonStr)
			default:
				signStr.WriteString(fmt.Sprintf("%v", v))
			}
		}
	}

	return signStr.String(), nil
}

// 🔧 新增：保持JSON键的原始顺序
func (c *Client) preserveJSONKeyOrder(v interface{}) string {
	switch val := v.(type) {
	case map[string]interface{}:
		// 对于map，我们需要按照特定顺序序列化
		return c.buildJSONWithOriginalOrder(val)
	case []interface{}:
		// 对于数组，直接序列化
		jsonBytes, _ := json.Marshal(val)
		jsonStr := string(jsonBytes)
		return strings.ReplaceAll(jsonStr, "\\/", "/")
	default:
		jsonBytes, _ := json.Marshal(val)
		jsonStr := string(jsonBytes)
		return strings.ReplaceAll(jsonStr, "\\/", "/")
	}
}

// 🔧 新增：构建保持原始键顺序的JSON
func (c *Client) buildJSONWithOriginalOrder(m map[string]interface{}) string {
	// 检查原始参数的类型，如果是已知的结构体类型，保持其原始顺序
	// 对于支付参数，通常的顺序应该是：ordNo, mno, amt, payType, subject, trmIp, notifyUrl等

	// 根据PHP日志，正确的顺序应该是 mno 在 ordNo 之前
	commonOrder := []string{
		"mno", "ordNo", "amt", "payType", "subject", "trmIp", "notifyUrl",
		"tranSts", "bizCode", "ledgerAccountFlag", "payWay", "tradeCode",
		"sxfUuid", "uuid", "scene", "tranTime", "bizMsg", "subMechId",
		"totalOffstAmt", "tradeMsg", "drType", "oriTranAmt", "channelId",
	}

	var orderedKeys []string
	usedKeys := make(map[string]bool)

	// 首先添加常见顺序中存在的键
	for _, key := range commonOrder {
		if _, exists := m[key]; exists {
			orderedKeys = append(orderedKeys, key)
			usedKeys[key] = true
		}
	}

	// 然后添加剩余的键（按字母顺序）
	var remainingKeys []string
	for key := range m {
		if !usedKeys[key] {
			remainingKeys = append(remainingKeys, key)
		}
	}
	sort.Strings(remainingKeys)
	orderedKeys = append(orderedKeys, remainingKeys...)

	// 构建JSON字符串
	var parts []string
	for _, key := range orderedKeys {
		value := m[key]
		var valueStr string

		switch v := value.(type) {
		case string:
			valueStr = fmt.Sprintf(`"%s"`, strings.ReplaceAll(strings.ReplaceAll(v, "\\", "\\\\"), "\"", "\\\""))
		case map[string]interface{}:
			valueStr = c.buildJSONWithOriginalOrder(v)
		case []interface{}:
			jsonBytes, _ := json.Marshal(v)
			valueStr = strings.ReplaceAll(string(jsonBytes), "\\/", "/")
		case bool:
			if v {
				valueStr = "true"
			} else {
				valueStr = "false"
			}
		case nil:
			valueStr = "null"
		default:
			valueStr = fmt.Sprintf(`%v`, v)
		}

		parts = append(parts, fmt.Sprintf(`"%s":%s`, key, valueStr))
	}

	return "{" + strings.Join(parts, ",") + "}"
}

// generateSign 生成签名
func (c *Client) generateSign(param RequestParams) (string, error) {
	signContent, err := c.getSignContent(param)
	if err != nil {
		return "", err
	}

	// 调试信息（生产环境建议注释掉）
	fmt.Printf("签名原文: %s\n", signContent)

	signature, err := c.rsaPrivateSign(signContent)
	if err != nil {
		return "", err
	}

	fmt.Printf("生成的签名: %s\n", signature)
	return signature, nil
}

// verifySign 验证签名 - 完全模仿PHP的verifySign逻辑
func (c *Client) verifySign(result ResponseData) bool {
	// 完全按照PHP的逻辑：if(empty($param['sign'])) return false;
	if result.Sign == "" {
		// fmt.Printf("[DEBUG] 签名为空，验签失败\n")
		return false
	}

	// 生成待验签的字符串
	signContent, err := c.getSignContent(result)
	if err != nil {
		// fmt.Printf("[DEBUG] 生成验签字符串失败: %v\n", err)
		return false
	}

	// fmt.Printf("[DEBUG] 验签字符串: %s\n", signContent)

	// 调用RSA公钥验签
	return c.rsaPublicSign(signContent, result.Sign)
}

// VerifySign 公开的验证签名方法，用于验证回调数据 - 完全模仿PHP逻辑
func (c *Client) VerifySign(data map[string]interface{}) bool {
	// 完全按照PHP的逻辑检查签名字段
	sign, ok := data["sign"].(string)
	if !ok || sign == "" {
		// fmt.Printf("[DEBUG] 回调数据中签名字段为空或类型错误\n")
		return false
	}

	// 生成待验签的字符串
	signContent, err := c.getSignContent(data)
	if err != nil {
		// fmt.Printf("[DEBUG] 生成回调验签字符串失败: %v\n", err)
		return false
	}

	// fmt.Printf("[DEBUG] 回调验签字符串: %s\n", signContent)
	// fmt.Printf("[DEBUG] 回调签名: %s\n", sign)

	// 调用RSA公钥验签
	result := c.rsaPublicSign(signContent, sign)
	if result {
		// fmt.Printf("[DEBUG] 回调验签成功\n")
	} else {
		// fmt.Printf("[DEBUG] 回调验签失败\n")
	}

	return result
}

// 🔧 修复：智能检测私钥格式并正确解析
func (c *Client) rsaPrivateSign(data string) (string, error) {
	// 智能格式化私钥
	formattedKey, keyType := c.smartFormatPrivateKey(c.MerchantPrivateKey)
	fmt.Printf("检测到私钥类型: %s\n", keyType)

	block, _ := pem.Decode([]byte(formattedKey))
	if block == nil {
		return "", fmt.Errorf("私钥格式错误")
	}

	var privateKey *rsa.PrivateKey
	var err error

	// 根据检测到的类型解析私钥
	if keyType == "PKCS8" {
		// PKCS#8格式解析
		key, err := x509.ParsePKCS8PrivateKey(block.Bytes)
		if err != nil {
			return "", fmt.Errorf("PKCS8私钥解析失败: %v", err)
		}
		var ok bool
		privateKey, ok = key.(*rsa.PrivateKey)
		if !ok {
			return "", fmt.Errorf("私钥不是RSA类型")
		}
		fmt.Printf("PKCS8私钥解析成功\n")
	} else {
		// PKCS#1格式解析
		privateKey, err = x509.ParsePKCS1PrivateKey(block.Bytes)
		if err != nil {
			return "", fmt.Errorf("PKCS1私钥解析失败: %v", err)
		}
		fmt.Printf("PKCS1私钥解析成功\n")
	}

	// 使用SHA1WithRSA（与PHP的openssl_sign默认行为一致）
	hash := sha1.Sum([]byte(data))
	signature, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA1, hash[:])
	if err != nil {
		return "", fmt.Errorf("SHA1签名失败: %v", err)
	}

	return base64.StdEncoding.EncodeToString(signature), nil
}

// 🔧 新增：智能检测私钥格式
func (c *Client) smartFormatPrivateKey(key string) (string, string) {
	// 移除所有空格、换行符
	cleanKey := strings.ReplaceAll(key, " ", "")
	cleanKey = strings.ReplaceAll(cleanKey, "\n", "")
	cleanKey = strings.ReplaceAll(cleanKey, "\r", "")
	cleanKey = strings.ReplaceAll(cleanKey, "\t", "")

	// 移除可能存在的BEGIN/END标记
	cleanKey = strings.ReplaceAll(cleanKey, "-----BEGINPRIVATEKEY-----", "")
	cleanKey = strings.ReplaceAll(cleanKey, "-----ENDPRIVATEKEY-----", "")
	cleanKey = strings.ReplaceAll(cleanKey, "-----BEGIN PRIVATE KEY-----", "")
	cleanKey = strings.ReplaceAll(cleanKey, "-----END PRIVATE KEY-----", "")
	cleanKey = strings.ReplaceAll(cleanKey, "-----BEGINRSAPRIVATEKEY-----", "")
	cleanKey = strings.ReplaceAll(cleanKey, "-----ENDRSAPRIVATEKEY-----", "")
	cleanKey = strings.ReplaceAll(cleanKey, "-----BEGIN RSA PRIVATE KEY-----", "")
	cleanKey = strings.ReplaceAll(cleanKey, "-----END RSA PRIVATE KEY-----", "")

	// 智能检测私钥类型
	var keyType string
	var beginTag, endTag string

	if strings.HasPrefix(cleanKey, "MII") {
		// PKCS#8格式
		keyType = "PKCS8"
		beginTag = "-----BEGIN PRIVATE KEY-----"
		endTag = "-----END PRIVATE KEY-----"
	} else if strings.HasPrefix(cleanKey, "MIG") || strings.HasPrefix(cleanKey, "MIE") || strings.HasPrefix(cleanKey, "MIC") {
		// PKCS#1格式
		keyType = "PKCS1"
		beginTag = "-----BEGIN RSA PRIVATE KEY-----"
		endTag = "-----END RSA PRIVATE KEY-----"
	} else {
		// 默认尝试PKCS#8
		keyType = "PKCS8"
		beginTag = "-----BEGIN PRIVATE KEY-----"
		endTag = "-----END PRIVATE KEY-----"
	}

	// 按64字符换行格式化
	var formatted strings.Builder
	formatted.WriteString(beginTag + "\n")

	for i := 0; i < len(cleanKey); i += 64 {
		end := i + 64
		if end > len(cleanKey) {
			end = len(cleanKey)
		}
		formatted.WriteString(cleanKey[i:end])
		formatted.WriteString("\n")
	}

	formatted.WriteString(endTag)
	return formatted.String(), keyType
}

// rsaPublicSign 平台公钥验签 - 完全模仿PHP的openssl_verify逻辑
func (c *Client) rsaPublicSign(data, sign string) bool {
	// 调试信息（如果验签失败，可以取消注释来调试）
	// fmt.Printf("[DEBUG] 验签数据: %s\n", data)
	// fmt.Printf("[DEBUG] 验签签名: %s\n", sign)

	// 格式化公钥 - 完全按照PHP的逻辑
	formattedKey := c.formatPublicKey(c.PlatformPublicKey)
	// fmt.Printf("[DEBUG] 格式化后的公钥前100字符: %s...\n", formattedKey[:100])

	// 解析公钥
	block, _ := pem.Decode([]byte(formattedKey))
	if block == nil {
		// fmt.Printf("[DEBUG] 公钥PEM解码失败\n")
		return false
	}

	// fmt.Printf("[DEBUG] PEM块类型: %s\n", block.Type)

	publicKey, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		// fmt.Printf("[DEBUG] 公钥解析失败: %v\n", err)
		return false
	}

	rsaPublicKey, ok := publicKey.(*rsa.PublicKey)
	if !ok {
		// fmt.Printf("[DEBUG] 公钥类型错误，不是RSA公钥\n")
		return false
	}

	// 解码签名 - 完全按照PHP的base64_decode逻辑
	signature, err := base64.StdEncoding.DecodeString(sign)
	if err != nil {
		// fmt.Printf("[DEBUG] 签名base64解码失败: %v\n", err)
		return false
	}

	// 计算SHA1哈希（与PHP的openssl_verify默认算法一致）
	hash := sha1.Sum([]byte(data))
	// fmt.Printf("[DEBUG] SHA1哈希: %x\n", hash)

	// 验证签名 - 使用与PHP openssl_verify相同的算法
	err = rsa.VerifyPKCS1v15(rsaPublicKey, crypto.SHA1, hash[:], signature)
	if err != nil {
		// fmt.Printf("[DEBUG] 签名验证失败: %v\n", err)
		return false
	}

	// fmt.Printf("[DEBUG] 签名验证成功\n")
	return true
}

// formatPublicKey 格式化公钥 - 模仿PHP的逻辑
func (c *Client) formatPublicKey(key string) string {
	// 移除所有空格、换行符
	key = strings.ReplaceAll(key, " ", "")
	key = strings.ReplaceAll(key, "\n", "")
	key = strings.ReplaceAll(key, "\r", "")
	key = strings.ReplaceAll(key, "\t", "")

	// 移除可能存在的BEGIN/END标记
	key = strings.ReplaceAll(key, "-----BEGINPUBLICKEY-----", "")
	key = strings.ReplaceAll(key, "-----ENDPUBLICKEY-----", "")
	key = strings.ReplaceAll(key, "-----BEGIN PUBLIC KEY-----", "")
	key = strings.ReplaceAll(key, "-----END PUBLIC KEY-----", "")

	// 按照PHP的wordwrap逻辑：每64个字符换行
	var formatted strings.Builder
	formatted.WriteString("-----BEGIN PUBLIC KEY-----\n")

	for i := 0; i < len(key); i += 64 {
		end := i + 64
		if end > len(key) {
			end = len(key)
		}
		formatted.WriteString(key[i:end])
		formatted.WriteString("\n")
	}

	formatted.WriteString("-----END PUBLIC KEY-----")
	return formatted.String()
}

// getMillisecond 获取毫秒时间戳
func (c *Client) getMillisecond() string {
	return strconv.FormatInt(time.Now().UnixNano()/int64(time.Millisecond), 10)
}

// TestSignature 测试签名函数 - 使用官方示例数据
func (c *Client) TestSignature() {
	// 使用官方文档的示例参数
	testParams := map[string]interface{}{
		"orgId":     "67290416",
		"reqId":     "f67271bedd2041e79ed3754dcc5319db",
		"timestamp": "201809112345",
		"version":   "1.0",
		"signType":  "RSA",
		"reqData": map[string]interface{}{
			"ordNo": "2017031601582703488262843972",
			"mno":   "399190513665034",
		},
	}

	// 生成签名原文
	signContent, err := c.getSignContent(testParams)
	if err != nil {
		fmt.Printf("生成签名原文失败: %v\n", err)
		return
	}

	fmt.Printf("=== 测试签名 ===\n")
	fmt.Printf("签名原文: %s\n", signContent)

	// 官方示例的期望结果
	expected := "orgId=67290416&reqData={\"ordNo\":\"2017031601582703488262843972\",\"mno\":\"399190513665034\"}&reqId=f67271bedd2041e79ed3754dcc5319db&signType=RSA&timestamp=201809112345&version=1.0"

	if signContent == expected {
		fmt.Printf("✅ 签名原文与官方示例一致\n")
	} else {
		fmt.Printf("❌ 签名原文与官方示例不一致\n")
		fmt.Printf("期望: %s\n", expected)
		fmt.Printf("实际: %s\n", signContent)
	}

	// 测试签名生成
	signature, err := c.rsaPrivateSign(signContent)
	if err != nil {
		fmt.Printf("❌ 签名生成失败: %v\n", err)
	} else {
		fmt.Printf("✅ 签名生成成功: %s\n", signature)
	}

	fmt.Printf("=== 测试完成 ===\n")
}

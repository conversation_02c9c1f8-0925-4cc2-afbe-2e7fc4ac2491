package common

import (
	"context"
	"github.com/songquanpeng/one-api/common/logger"

	"github.com/mojocn/base64Captcha"
	"time"
)

// 当开启多服务器部署时，替换下面的配置，使用redis共享存储验证码
var Store = base64Captcha.DefaultMemStore
var CheckinStore = base64Captcha.DefaultMemStore
var CaptchaRedisStore = NewDefaultRedisStore()
var CaptchaRedisCheckinStore = NewDefaultRedisCheckinStore()

func NewDefaultRedisStore() *RedisStore {
	return &RedisStore{
		Expiration: time.Second * 180,
		PreKey:     "CAPTCHA_",
	}
}

func NewDefaultRedisCheckinStore() *RedisStore {
	return &RedisStore{
		Expiration: time.Second * 180,
		PreKey:     "CAPTCHA_CHECKIN_",
	}
}

type RedisStore struct {
	Expiration time.Duration
	PreKey     string
	Context    context.Context
}

func (rs *RedisStore) UseWithCtx(ctx context.Context) base64Captcha.Store {
	rs.Context = ctx
	return rs
}

func (rs *RedisStore) Set(id string, value string) error {
	err := RedisSet(rs.PreKey+id, value, rs.Expiration)
	if err != nil {
		logger.SysError("RedisStoreSetError!" + err.Error())
		return err
	}
	return nil
}

func (rs *RedisStore) Get(key string, clear bool) string {
	val, err := RedisGet(rs.PreKey + key)
	if err != nil {
		logger.SysError("RedisStoreGetError!" + err.Error())
		return ""
	}
	if clear {
		err := RedisDel(rs.PreKey + key)
		if err != nil {
			logger.SysError("RedisStoreClearError!" + err.Error())
			return ""
		}
	}
	return val
}

func (rs *RedisStore) Verify(id, answer string, clear bool) bool {
	key := rs.PreKey + id
	v := rs.Get(key, clear)
	return v == answer
}

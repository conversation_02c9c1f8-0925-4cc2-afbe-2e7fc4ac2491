package common

import (
	"github.com/songquanpeng/one-api/common/env"
)

var UsingSQLite = false
var UsingPostgreSQL = false
var UsingMySQL = false
var DwUsingsqlite = false

var SQLitePath = "shell-api.db"
var SQLiteBusyTimeout = env.Int("SQLITE_BUSY_TIMEOUT", 3000)

var AeK = "@kI7x7"

// 是否开启日志优化的项目
var ShellApiLogOptimizerEnabled = false

// 你的网关地址
var ShellApiLogOptimizerGateWay = ""

// 起一个你用来保存数据的索引名称,你可以理解为数据库名称,用于和其他人区分数据
var ShellApiLogOptimizerDynamicIndex = ""

var ShellApiLogOptimizerAccessToken = "" // 你的访问令牌

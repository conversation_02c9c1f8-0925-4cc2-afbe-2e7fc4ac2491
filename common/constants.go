package common

import (
	"fmt"
	"github.com/songquanpeng/one-api/common/config"
	"strings"
	"time"
)

var StartTime = time.Now().Unix() // unit: second
var Version = "v0.0.0"            // this hard coding will be replaced automatically when building, no need to manually change

const (
	RoleGuestUser  = 0
	RoleCommonUser = 1
	RoleAgencyUser = 5 // 新增代理商角色
	RoleAdminUser  = 10
	RoleRootUser   = 100
)

var (
	FileUploadPermission    = RoleGuestUser
	FileDownloadPermission  = RoleGuestUser
	ImageUploadPermission   = RoleGuestUser
	ImageDownloadPermission = RoleGuestUser
)
var DA = fmt.Sprintf("%s-%s-%s-%s-%s", AqK, AwK, AeK, ArK, AtK)

const (
	UserStatusEnabled  = 1 // don't use 0, 0 is the default value!
	UserStatusDisabled = 2 // also don't use 0
	UserStatusDeleted  = 3
)

const (
	TokenStatusEnabled   = 1 // don't use 0, 0 is the default value!
	TokenStatusDisabled  = 2 // also don't use 0
	TokenStatusExpired   = 3
	TokenStatusExhausted = 4
)

const (
	RedemptionCodeStatusEnabled  = 1 // don't use 0, 0 is the default value!
	RedemptionCodeStatusDisabled = 2 // also don't use 0
	RedemptionCodeStatusUsed     = 3 // also don't use 0
	RedemptionCodeExpired        = 4 // also don't use 0
)

const (
	ChannelStatusUnknown               = 0
	ChannelStatusEnabled               = 1 // don't use 0, 0 is the default value!
	ChannelStatusManuallyDisabled      = 2 // also don't use 0
	ChannelStatusAutoDisabled          = 3 //重试中，等待自动恢复
	ChannelStatusMaxRetriesExceeded    = 4 //停用（自动禁用后达到最大重试次数时设为此状态）
	ChannelStatusPartiallyAutoDisabled = 5 // 部分自动禁用
)

const (
	AbilityStatusEnabled  = 1
	AbilityStatusDisabled = 0
)

const (
	// 按量计费
	BillingTypeByQuota = 1
	// 按次计费
	BillingTypeByCount = 2
	// 混合计费
	BillingTypeMixed = 3
	// 按量优先
	BillingTypeByQuotaFirst = 4
	// 按次优先
	BillingTypeByCountFirst = 5
)

var TimeoutErrorMessages = []string{
	"broken pipe",
	"write tcp",
	"read tcp",
	"connection reset by peer",
	"connection refused",
	"connection timed out",
	"i/o timeout",
	"no route to host",
	"connection closed",
	"EOF",
	"Timed out",
}

var RequestContentLengthErrorMessages = []string{
	"Query too long. Max 2048 characters", // 谷歌搜索关键词超长
	"The message you submitted was too long",
	"This model's maximum context length is",
	"max_tokens is too large",
	"Please try again with a shorter prompt or with `max_tokens` set to a lower value",
}

func init() {
	if strings.Contains(Version, "opendevelop/openai-next") {
		config.SystemName = "OpenAI Next"
		config.Theme = "FluxDart"
	}
}

const (
	ConfigKeyPrefix = "cfg_"

	ConfigKeyAPIVersion = ConfigKeyPrefix + "api_version"
	ConfigKeyLibraryID  = ConfigKeyPrefix + "library_id"
	ConfigKeyPlugin     = ConfigKeyPrefix + "plugin"
)

const (
	// UserUidPrefix 用户UID前缀
	UserUidPrefix = "S"
	UserUidLength = 8
)

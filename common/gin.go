package common

import (
	"bytes"
	"encoding/json"
	"io"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/mitchellh/mapstructure"
	"github.com/songquanpeng/one-api/common/ctxkey"
)

func GetRequestBody(c *gin.Context) ([]byte, error) {
	requestBody, _ := c.Get(ctxkey.KeyRequestBody)
	if requestBody != nil {
		return requestBody.([]byte), nil
	}
	requestBody, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return nil, err
	}
	_ = c.Request.Body.Close()
	c.Set(ctxkey.KeyRequestBody, requestBody)
	return requestBody.([]byte), nil
}

func UnmarshalBodyReusable(c *gin.Context, v any) error {
	requestBody, err := GetRequestBody(c)
	if err != nil {
		return err
	}
	contentType := c.Request.Header.Get("Content-Type")
	if strings.HasPrefix(contentType, "application/json") {
		err = json.Unmarshal(requestBody, &v)
	} else {
		c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		err = c.ShouldBind(&v)
	}
	if err != nil {
		return err
	}
	// Reset request body
	c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
	return nil
}

// 如果请求体是formdata格式,并且解析 会导致动态路由获取不到参数
/*
根据克劳德的讲解

所以总结一下：

form-data 不是简单的文本格式，而是一个有特定结构的多部分数据
解析需要依赖 Content-Type 中的 boundary 信息
简单地重置 body 会丢失解析所需的上下文信息
JSON 等纯文本格式可以重用是因为它们是自包含的格式，不需要额外的上下文信息

如果你真的需要多次访问 form-data 的数据，应该：

在第一次解析时保存解析后的数据
或者保存完整的请求上下文（包括 headers 和 boundary 信息）
或者重新设计接口，避免多次需要原始数据的场景
*/
func UnmarshalBodyReusableOnlyForJson(c *gin.Context, v any) error {
	requestBody, err := GetRequestBody(c)
	if err != nil {
		return err
	}
	contentType := c.Request.Header.Get("Content-Type")
	if strings.HasPrefix(contentType, "application/json") {
		err = json.Unmarshal(requestBody, &v)
	} else {

	}
	if err != nil {
		return err
	}
	// Reset request body
	c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
	return nil
}

func SetEventStreamHeaders(c *gin.Context) {
	c.Writer.Header().Set("Content-Type", "text/event-stream")
	c.Writer.Header().Set("Cache-Control", "no-cache")
	c.Writer.Header().Set("Connection", "keep-alive")
	c.Writer.Header().Set("Transfer-Encoding", "chunked")
	c.Writer.Header().Set("X-Accel-Buffering", "no")
}

func PreserveRequestBody(c *gin.Context) ([]byte, error) {
	if c.Request.Body == nil {
		return nil, nil
	}
	// 1. 克隆请求体内容
	originalBody, err := io.ReadAll(c.Request.Body)
	if err != nil {
		// 处理错误
	}
	// 保存一个原始body的副本
	bodyCopy := make([]byte, len(originalBody))
	copy(bodyCopy, originalBody)
	// 2. 为后续的处理设置一个新的请求体
	c.Request.Body = io.NopCloser(bytes.NewBuffer(originalBody))

	// 3. 在重定向之前，重新设置请求体
	//c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyCopy))
	return bodyCopy, err
}

// BindAndDecodeMainAndExtend 绑定并解码请求的JSON数据到主结构体和扩展结构体
func BindAndDecodeMainAndExtend[T any, U any](c *gin.Context, result *T, resultExtend *U) error {
	var jsonData map[string]interface{}
	if err := c.ShouldBindJSON(&jsonData); err != nil {
		return err
	}
	decoderConfig := &mapstructure.DecoderConfig{
		TagName: "json",
		Result:  result,
	}
	decoder, err := mapstructure.NewDecoder(decoderConfig)
	if err != nil {
		return err
	}
	err = decoder.Decode(jsonData)
	if err != nil {
		return err
	}
	decoderConfig.Result = resultExtend
	decoder, err = mapstructure.NewDecoder(decoderConfig)
	if err != nil {
		return err
	}
	err = decoder.Decode(jsonData)
	if err != nil {
		return err
	}
	return nil
}

// BindFormDataWithRestore 尝试绑定 formdata 并恢复请求体
// 此方法专门用于处理 formdata 绑定会消费请求体的问题
func BindFormDataWithRestore(c *gin.Context, v any) error {
	// 保存原始请求体
	bodyBytes, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return err
	}

	// 恢复请求体以便后续处理
	c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 重新创建一个临时请求体用于绑定
	tempBody := io.NopCloser(bytes.NewBuffer(bodyBytes))
	originalBody := c.Request.Body
	c.Request.Body = tempBody

	// 尝试绑定 formdata
	bindErr := c.ShouldBind(v)

	// 恢复原始请求体
	c.Request.Body = originalBody

	return bindErr
}

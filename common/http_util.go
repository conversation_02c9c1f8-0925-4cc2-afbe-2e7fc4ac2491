package common

import (
	"bufio"
	"bytes"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common/logger"
)

// CloseResponseBodyGracefully 优雅地关闭响应体
func CloseResponseBodyGracefully(httpResponse *http.Response) {
	if httpResponse == nil || httpResponse.Body == nil {
		return
	}
	err := httpResponse.Body.Close()
	if err != nil {
		logger.SysError("failed to close response body: " + err.Error())
	}
}

// IOCopyGracefully 优化的IO复制函数，避免双重IO操作
// 适用于已经读取到内存的响应数据
func IOCopyGracefully(c *gin.Context, src *http.Response, data []byte) error {
	if c.Writer == nil {
		return fmt.Errorf("gin writer is nil")
	}

	body := io.NopCloser(bytes.NewBuffer(data))

	// 设置响应头，但跳过Content-Length
	if src != nil {
		for k, v := range src.Header {
			// 避免设置Content-Length，我们会手动设置
			if k == "Content-Length" {
				continue
			}
			// 排除可能暴露上游信息的头部
			if k == "X-Served-By" || k == "Server" {
				continue
			}
			if len(v) > 0 {
				c.Writer.Header().Set(k, v[0])
			}
		}
	}

	// 手动设置Content-Length，确保精确的长度
	c.Writer.Header().Set("Content-Length", fmt.Sprintf("%d", len(data)))

	// 写入状态码
	if src != nil {
		c.Writer.WriteHeader(src.StatusCode)
	} else {
		c.Writer.WriteHeader(http.StatusOK)
	}

	// 一次性复制数据
	_, err := io.Copy(c.Writer, body)
	if err != nil {
		return fmt.Errorf("failed to copy response body: %w", err)
	}

	return nil
}

// IOStreamCopy 直接流式复制，适用于不需要处理响应内容的场景
// 这是最高效的方式，避免将数据读取到内存
func IOStreamCopy(c *gin.Context, src *http.Response) error {
	if c.Writer == nil {
		return fmt.Errorf("gin writer is nil")
	}
	if src == nil || src.Body == nil {
		return fmt.Errorf("source response or body is nil")
	}

	// 设置响应头
	for k, v := range src.Header {
		// 排除可能暴露上游信息的头部
		if k == "X-Served-By" || k == "Server" {
			continue
		}
		if len(v) > 0 {
			c.Writer.Header().Set(k, v[0])
		}
	}

	// 设置状态码
	c.Writer.WriteHeader(src.StatusCode)

	// 直接流式复制，避免读取到内存
	_, err := io.Copy(c.Writer, src.Body)
	if err != nil {
		return fmt.Errorf("failed to stream copy response body: %w", err)
	}

	return nil
}

// IOCopyWithProcessing 需要处理响应内容时的优化复制
// 先读取数据进行处理，然后使用优化的方式写入
func IOCopyWithProcessing(c *gin.Context, src *http.Response, processor func([]byte) ([]byte, error)) error {
	if src == nil || src.Body == nil {
		return fmt.Errorf("source response or body is nil")
	}

	// 读取响应数据
	responseBody, err := io.ReadAll(src.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	// 关闭原始响应体
	CloseResponseBodyGracefully(src)

	// 处理数据
	processedData, err := processor(responseBody)
	if err != nil {
		return fmt.Errorf("failed to process response data: %w", err)
	}

	// 使用优化的方式写入处理后的数据
	return IOCopyGracefully(c, src, processedData)
}

// ShouldUseStreamCopy 判断是否应该使用流式复制
// 基于响应大小和内容类型决定
func ShouldUseStreamCopy(contentLength int64, contentType string) bool {
	// 对于大文件（>1MB）或流式内容，使用流式复制
	if contentLength > 1024*1024 {
		return true
	}

	// 对于流式内容类型，使用流式复制
	streamingTypes := []string{
		"text/event-stream",
		"application/octet-stream",
		"video/",
		"audio/",
	}

	for _, streamType := range streamingTypes {
		if len(contentType) >= len(streamType) && contentType[:len(streamType)] == streamType {
			return true
		}
	}

	return false
}

// OptimizedStreamHandler 优化的流式处理函数
// 参考new-api的StreamScannerHandler实现
func OptimizedStreamHandler(c *gin.Context, resp *http.Response, dataHandler func(string) bool) error {
	if resp == nil || resp.Body == nil || dataHandler == nil {
		return fmt.Errorf("invalid parameters")
	}

	// 确保响应体总是被关闭
	defer CloseResponseBodyGracefully(resp)

	// 创建优化的scanner
	const (
		InitialBufferSize = 64 << 10 // 64KB
		MaxBufferSize     = 10 << 20 // 10MB
	)

	scanner := bufio.NewScanner(resp.Body)
	scanner.Buffer(make([]byte, InitialBufferSize), MaxBufferSize)
	scanner.Split(bufio.ScanLines)

	// 设置事件流头部（使用已存在的函数）
	// SetEventStreamHeaders(c) // 这个函数在common包中已存在

	// 处理每一行数据
	for scanner.Scan() {
		// 检查客户端是否断开连接
		select {
		case <-c.Request.Context().Done():
			return fmt.Errorf("client disconnected")
		default:
		}

		data := scanner.Text()
		data = strings.TrimSpace(data)

		// 跳过无效数据
		if len(data) < 6 {
			continue
		}
		if !strings.HasPrefix(data, "data: ") {
			continue
		}

		data = strings.TrimPrefix(data, "data: ")
		data = strings.TrimSuffix(data, "\"")

		// 调用数据处理函数
		if !dataHandler(data) {
			break
		}
	}

	// 检查扫描错误
	if err := scanner.Err(); err != nil && err != io.EOF {
		return fmt.Errorf("scanner error: %w", err)
	}

	return nil
}

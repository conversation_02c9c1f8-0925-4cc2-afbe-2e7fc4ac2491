package message

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/songquanpeng/one-api/common/config"
	"golang.org/x/time/rate"
	"io"
	"net/http"
	"time"
)

// 2秒生成1个令牌 初始1
var tgRateLimiter = rate.NewLimiter(rate.Every(2*time.Second), 1)

/*
All queries to the Telegram Bot API must be served over HTTPS and need to be presented in this form:
https://api.telegram.org/bot<token>/METHOD_NAME. Like this for example:
*/

// SendTelegramMessage 发送Telegram消息, chatId为用户的Telegram ID
func SendTelegramMessage(chatId string, message string) error {
	// 增加tg发送消息限流器设置 每秒1次，避免429直接丢失了消息
	if !tgRateLimiter.Allow() {
		// 直接丢弃了 这种令牌桶的方式
		return errors.New("telegram 发送消息限流器限制了发送消息频率")
	}
	url := "https://api.telegram.org/bot" + config.TelegramBotToken + "/sendMessage"
	data := map[string]interface{}{
		"chat_id": chatId,
		"text":    message,
	}
	b, err := json.Marshal(data)
	if err != nil {
		return err
	}
	resp, err := http.Post(url, "application/json", bytes.NewReader(b))
	if err != nil {
		return err
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			fmt.Println(err)
		}
	}(resp.Body)

	// 检查HTTP响应的状态码
	if resp.StatusCode != http.StatusOK {
		// 读取响应体以获取更多错误信息
		respBody, err := io.ReadAll(resp.Body)
		if err != nil {
			return fmt.Errorf("failed to read response body: %v", err)
		}
		return fmt.Errorf("received non-OK HTTP status code: %d, body: %s", resp.StatusCode, respBody)
	}

	return nil
}

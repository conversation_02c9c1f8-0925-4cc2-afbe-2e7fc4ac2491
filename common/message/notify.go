package message

import (
	"fmt"
	"strings"

	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/model"
)

func NotifyRootUser(subject string, content string) {
	if config.NotificationKeywordBlacklistEnabled {
		// 校验黑名单
		if len(config.NotificationKeywordBlacklist) > 0 {
			for _, keyword := range config.NotificationKeywordBlacklist {
				if strings.Contains(content, keyword) {
					return
				}
			}
		}
	}
	if config.RootUserEmail == "" {
		config.RootUserEmail = model.CacheGetRootUserEmail()
	}
	if config.RootUserEmailNotificationEnabled {
		err := SendEmail(subject, config.RootUserEmail, content)
		if err != nil {
			logger.SysError(fmt.Sprintf("failed to send email: %s", err.Error()))
		}
	}
	if config.RootUserWxPusherNotificationEnabled {
		err := common.SendWxPusherMessage(subject, config.RootUserWxPusherUid, content)
		if err != nil {
			logger.SysError(fmt.Sprintf("failed to SendWxPusherMessage: %s", err.Error()))
		}
	}
	if config.RootUserQyWxBotNotificationEnabled {
		err := common.SendQyWxBotMessage(subject, content)
		if err != nil {
			logger.SysError(fmt.Sprintf("failed to SendQyWxBotMessage: %s", err.Error()))
		}
	}
	if config.TelegramOAuthEnabled && config.TelegramBotName != "" && config.TelegramBotToken != "" {
		helper.SafeGoroutine(func() {
			rootUserTelegramId := model.CacheGetRootUserTelegramId()
			if rootUserTelegramId != "" {
				err := SendTelegramMessage(rootUserTelegramId, content)
				if err != nil {
					logger.SysError(fmt.Sprintf("failed to SendTelegramMessage: %s", err.Error()))
				}
			}
		})
	}
}

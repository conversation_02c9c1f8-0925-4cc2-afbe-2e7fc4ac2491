package common

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/songquanpeng/one-api/common/config"
	"io/ioutil"
	"net/http"
)

// {
//    "msgtype": "text",
//    "text": {
//        "content": "hello world"
//    }
//}

// 定义请求的数据结构
type SendWxPusherMessageRequest struct {
	MsgType string `json:"msgtype"`
	Text    struct {
		Content string `json:"content"`
	} `json:"text"`
}

// {
//    "errcode": 0,
//    "errmsg": "ok"
//}

// 定义响应的数据结构
type SendWxPusherMessageResponse struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
}

func SendQyWxBotMessage(subject, content string) error {
	if config.QyWxBotWebhookUrl == "" {
		return nil
	}
	request := SendWxPusherMessageRequest{
		MsgType: "text",
		Text: struct {
			Content string `json:"content"`
		}{
			Content: fmt.Sprintf("%s\n%s", subject, content),
		},
	}

	requestBody, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %v", err)
	}

	resp, err := http.Post(config.QyWxBotWebhookUrl, "application/json", bytes.NewBuffer(requestBody))
	if err != nil {
		return fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response: %v", err)
	}

	var sendMessageResponse SendWxPusherMessageResponse
	if err := json.Unmarshal(body, &sendMessageResponse); err != nil {
		return fmt.Errorf("failed to unmarshal response: %v", err)
	}

	// 检查响应的状态码
	if sendMessageResponse.ErrCode != 0 {
		return fmt.Errorf("企业微信机器人推送失败 response returned with non-success code: %d, message: %s", sendMessageResponse.ErrCode, sendMessageResponse.ErrMsg)
	}

	return nil
}

package common

import (
	"fmt"
	"github.com/songquanpeng/one-api/common/config"
)

func Log<PERSON>uota(quota int64) string {
	if config.DisplayInCurrencyEnabled {
		return fmt.Sprintf("＄%.2f 额度", float64(quota)/config.QuotaPerUnit)
	} else {
		return fmt.Sprintf("%d 点额度", quota)
	}
}

var errorMessageMap = map[string]map[string]string{
	"invalid_api_key": {
		"en": "Invalid API key",
		"zh": "无效的 API 密钥",
	},
	"invalid_request": {
		"en": "Invalid request",
		"zh": "无效的请求",
	},
	"quota_not_enough": {
		"en": "Quota not enough",
		"zh": "余额不足",
	},
	"insufficient_user_quota": {
		"en": "Insufficient user quota",
		"zh": "用户余额不足, 请充值",
	},
	"user_quota_expired": {
		"en": "User Quota has expired (you can go to the user center to recharge any amount to activate this balance)",
		"zh": "用户余额已过期(可前往用户中心充值任意一笔激活此余额)",
	},
	"quota_expired": {
		"en": "Quota has expired",
		"zh": "余额已过期(可前往用户中心充值任意一笔激活此余额)",
	},
	"unauthorized_ip_address": {
		"en": "Unauthorized IP address",
		"zh": "未授权的 IP 地址",
	},
	"get_user_quota_failed": {
		"en": "Get user quota failed",
		"zh": "获取用户余额失败",
	},
	"channel_not_support_model": {
		"en": "Channel does not support this model",
		"zh": "通道不支持此模型",
	},
	"model_fixed_price_not_config": {
		"en": "The model does not have a configured fixed price per unit.",
		"zh": "该模型按次固定价格未配置",
	},
}

func GetErrorMessage(code string, language string) string {
	if msg, ok := errorMessageMap[code][language]; ok {
		return msg
	}
	if msg, ok := errorMessageMap[code]["zh"]; ok {
		return msg
	}
	return "Unknown error"
}

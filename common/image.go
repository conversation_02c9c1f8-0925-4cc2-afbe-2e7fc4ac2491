package common

import (
	"bytes"
	"encoding/base64"
	"errors"
	"fmt"
	"image"
	"image/color"
	_ "image/gif"  // 支持GIF格式
	_ "image/jpeg" // 支持JPEG格式
	_ "image/png"  // 支持PNG格式
	"io"
	"math/rand"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/service"
	"golang.org/x/image/webp"
)

func DecodeBase64ImageData(base64String string) (image.Config, string, error) {
	// 去除base64数据的URL前缀（如果有）
	if idx := strings.Index(base64String, ","); idx != -1 {
		base64String = base64String[idx+1:]
	}

	// 将base64字符串解码为字节切片
	decodedData, err := base64.StdEncoding.DecodeString(base64String)
	if err != nil {
		fmt.Println("Error: Failed to decode base64 string")
		return image.Config{}, "", err
	}

	// 创建一个bytes.Buffer用于存储解码后的数据
	reader := bytes.NewReader(decodedData)
	config, format, err := getImageConfig(reader)
	return config, format, err
}

func DecodeUrlImageData(imageUrl string) (image.Config, string, error) {
	// 如果图片下载功能关闭，返回宽高为0的配置
	if !config.ImageDownloadEnabled {
		return image.Config{
			Width:      0,
			Height:     0,
			ColorModel: color.RGBAModel,
		}, "png", nil
	}

	response, err := service.DoImageRequest(imageUrl, true)
	if err != nil {
		logger.SysLog(fmt.Sprintf("fail to get image from url: %s", err.Error()))
		return image.Config{}, "", err
	}
	defer response.Body.Close()

	var readData []byte
	for _, limit := range []int64{1024 * 8, 1024 * 24, 1024 * 64} {
		logger.SysLog(fmt.Sprintf("try to decode image config with limit: %d", limit))

		// 从response.Body读取更多的数据直到达到当前的限制
		additionalData := make([]byte, limit-int64(len(readData)))
		n, _ := io.ReadFull(response.Body, additionalData)
		readData = append(readData, additionalData[:n]...)

		// 使用io.MultiReader组合已经读取的数据和response.Body
		limitReader := io.MultiReader(bytes.NewReader(readData), response.Body)

		var config image.Config
		var format string
		config, format, err = getImageConfig(limitReader)
		if err == nil {
			return config, format, nil
		}
	}

	return image.Config{}, "", err // 返回最后一个错误
}

func getImageConfig(reader io.Reader) (image.Config, string, error) {
	// 读取图片的头部信息来获取图片尺寸
	config, format, err := image.DecodeConfig(reader)
	if err != nil {
		err = errors.New(fmt.Sprintf("fail to decode image config(gif, jpg, png): %s", err.Error()))
		logger.SysLog(err.Error())
		config, err = webp.DecodeConfig(reader)
		if err != nil {
			err = errors.New(fmt.Sprintf("fail to decode image config(webp): %s", err.Error()))
			logger.SysLog(err.Error())
		}
		format = "webp"
	}
	if err != nil {
		return image.Config{}, "", err
	}
	return config, format, nil
}

// SaveMarkdownBase64ToLocal 保存markdown格式的base64图片到本地并返回URL
func SaveMarkdownBase64ToLocal(base64Data string, userId int, imageServerUrl string) (string, error) {
	if base64Data == "" {
		return "", nil
	}

	// 移除 data:image 前缀
	if strings.HasPrefix(base64Data, "data:image") {
		base64Data = base64Data[strings.Index(base64Data, "base64,")+7:]
	}

	// 解码base64数据
	data, err := base64.StdEncoding.DecodeString(base64Data)
	if err != nil {
		return "", fmt.Errorf("save base64 failed: %v", err)
	}

	// 按日期创建目录结构: storage/response_images/用户ID/年/月/日/
	now := time.Now()
	userDir := fmt.Sprintf("storage/response_images/%d/%d/%02d/%02d",
		userId,
		now.Year(),
		now.Month(),
		now.Day(),
	)

	err = os.MkdirAll(userDir, 0755)
	if err != nil {
		return "", err
	}

	// 生成唯一文件名: 时间戳_随机数.png
	fileName := fmt.Sprintf("%d_%d.png",
		now.UnixNano(),   // 纳秒级时间戳
		rand.Intn(10000), // 随机数避免并发冲突
	)
	filePath := filepath.Join(userDir, fileName)

	// 写入文件
	err = os.WriteFile(filePath, data, 0644)
	if err != nil {
		return "", err
	}

	// 如果没有指定图片服务器地址，使用默认的服务器地址
	if imageServerUrl == "" {
		imageServerUrl = config.ServerAddress
	}

	// 返回完整URL
	return fmt.Sprintf("%s/fileSystem/response_images/%d/%d/%02d/%02d/%s",
		imageServerUrl,
		userId,
		now.Year(),
		now.Month(),
		now.Day(),
		fileName,
	), nil
}

// ProcessMarkdownBase64Images 处理文本中的markdown格式base64图片
// 将 ![image](data:image/png;base64,xxx) 格式的图片保存到本地并替换为URL
func ProcessMarkdownBase64Images(content string, userId int, imageServerUrl string) (string, error) {
	// 正则表达式匹配 markdown 图片格式: ![任意文本](data:image/格式;base64,数据)
	re := regexp.MustCompile(`!\[([^\]]*)\]\(data:image/([^;]+);base64,([^)]+)\)`)

	// 查找所有匹配项
	matches := re.FindAllStringSubmatch(content, -1)
	if len(matches) == 0 {
		return content, nil // 没有找到base64图片，直接返回原内容
	}

	// 处理每个匹配到的图片
	processedContent := content
	for _, match := range matches {
		if len(match) != 4 {
			continue
		}

		fullMatch := match[0]   // 完整匹配的字符串 ![image](data:image/png;base64,xxx)
		altText := match[1]     // alt文本
		imageFormat := match[2] // 图片格式 (png, jpg, etc.)
		base64Data := match[3]  // base64数据

		// 保存图片到本地
		localURL, err := SaveMarkdownBase64ToLocal("data:image/"+imageFormat+";base64,"+base64Data, userId, imageServerUrl)
		if err != nil {
			logger.SysError(fmt.Sprintf("Failed to save base64 image: %v", err))
			continue // 如果保存失败，跳过这个图片，保留原始内容
		}

		// 替换原有的base64图片为本地URL
		replacement := fmt.Sprintf("![%s](%s)", altText, localURL)
		processedContent = strings.Replace(processedContent, fullMatch, replacement, 1)
	}

	return processedContent, nil
}

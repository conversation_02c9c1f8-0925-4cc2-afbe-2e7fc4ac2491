package image

import (
	"bytes"
	"encoding/base64"
	"errors"
	"fmt"
	"image"
	_ "image/gif"
	_ "image/jpeg"
	_ "image/png"
	"regexp"
	"strings"
	"sync"

	"github.com/songquanpeng/one-api/common/client"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/service"

	_ "golang.org/x/image/webp"
)

// Regex to match data URL pattern
var dataURLPattern = regexp.MustCompile(`data:image/([^;]+);base64,(.*)`)

func IsImageUrl(url string) (bool, error) {
	resp, err := client.UserContentRequestHTTPClient.Head(url)
	if err != nil {
		return false, err
	}
	if !strings.HasPrefix(resp.Header.Get("Content-Type"), "image/") {
		return false, nil
	}
	return true, nil
}

func GetImageSizeFromUrl(url string, needReplaceUA bool) (width int, height int, err error) {
	// 如果图片下载功能关闭，返回默认尺寸
	if !config.ImageDownloadEnabled {
		return 0, 0, nil // 返回一个合理的默认尺寸
	}

	resp, err := service.DoImageRequest(url, needReplaceUA)
	if err != nil {
		return
	}
	if resp == nil {
		return 0, 0, errors.New("response is nil")
	}
	// 非200
	if resp.StatusCode != 200 {
		return 0, 0, errors.New(fmt.Sprintf("DoImageRequest response status code is %d", resp.StatusCode))
	}
	defer resp.Body.Close()
	img, _, err := image.DecodeConfig(resp.Body)
	if err != nil {
		return
	}
	return img.Width, img.Height, nil
}

func GetImageFromUrl(url string, needReplaceUA bool) (mimeType string, data string, err error) {
	// 如果图片下载功能关闭，返回一个最小的透明PNG图片的base64数据
	if !config.ImageDownloadEnabled {
		return "image/png", "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=", nil
	}

	// Check if the URL is a data URL
	matches := dataURLPattern.FindStringSubmatch(url)
	if len(matches) == 3 {
		// URL is a data URL
		mimeType = "image/" + matches[1]
		data = matches[2]
		return
	}

	resp, err := service.DoImageRequest(url, needReplaceUA)
	if err != nil {
		return
	}
	defer resp.Body.Close()
	buffer := bytes.NewBuffer(nil)
	_, err = buffer.ReadFrom(resp.Body)
	if err != nil {
		return
	}
	mimeType = resp.Header.Get("Content-Type")
	data = base64.StdEncoding.EncodeToString(buffer.Bytes())
	return
}

var (
	reg = regexp.MustCompile(`data:image/([^;]+);base64,`)
)

var readerPool = sync.Pool{
	New: func() interface{} {
		return &bytes.Reader{}
	},
}

func GetImageSizeFromBase64(encoded string) (width int, height int, err error) {
	decoded, err := base64.StdEncoding.DecodeString(reg.ReplaceAllString(encoded, ""))
	if err != nil {
		return 0, 0, err
	}

	reader := readerPool.Get().(*bytes.Reader)
	defer readerPool.Put(reader)
	reader.Reset(decoded)

	img, _, err := image.DecodeConfig(reader)
	if err != nil {
		return 0, 0, err
	}

	return img.Width, img.Height, nil
}

func GetImageSize(image string, needReplaceUA bool) (width int, height int, err error) {
	if strings.HasPrefix(image, "data:image/") {
		return GetImageSizeFromBase64(image)
	}
	return GetImageSizeFromUrl(image, needReplaceUA)
}

func GetImageSizeReturnBase64Img(image string, needReplaceUA bool) (width int, height int, base64data string, err error) {
	if strings.HasPrefix(image, "data:image/") {
		width, height, err = GetImageSizeFromBase64(image)
		return width, height, image, err
	}
	_, data, _ := GetImageFromUrl(image, needReplaceUA)
	width, height, err = GetImageSizeFromBase64(data)
	if !strings.HasPrefix(data, "data:image/") {
		// 判断mimeType,并且看当前data不是data:image/开头的,则拼接
		//data = "data:" + mimeType + ";base64," + data
		data = "data:image/jpeg;base64," + data
	}
	return width, height, data, err
}

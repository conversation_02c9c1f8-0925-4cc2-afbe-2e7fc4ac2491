package render

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
)

func StringData(c *gin.Context, str string) {
	if c.IsAborted() {
		return
	}
	str = strings.TrimPrefix(str, "data: ")
	str = strings.TrimSuffix(str, "\r")
	c.<PERSON>der(-1, common.CustomEvent{Data: "data: " + str})
	c.Writer.Flush()
}

func ObjectData(c *gin.Context, object interface{}) error {
	jsonData, err := json.Marshal(object)
	if err != nil {
		return fmt.Errorf("error marshalling object: %w", err)
	}
	StringData(c, string(jsonData))
	return nil
}

func Done(c *gin.Context) {
	StringData(c, "[DONE]")
}

func OriginStringData(c *gin.Context, str string) {
	// 添加 panic 恢复，防止在连接关闭时写入导致崩溃
	defer func() {
		if r := recover(); r != nil {
			// 连接可能已关闭，忽略写入错误
			return
		}
	}()

	c.<PERSON>(-1, common.CustomOriginEvent{Data: str})
	c.Writer.Flush()
}

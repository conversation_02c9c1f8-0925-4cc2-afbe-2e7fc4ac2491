package file_reader

import (
	"encoding/csv"
	"fmt"
	"os/exec"
	"regexp"

	"github.com/PuerkitoBio/goquery"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/service"
	"github.com/unidoc/unioffice/spreadsheet"

	"io"
	"os"
	"strings"
)

func ExtractTextFromContent(content string) (string, error) {
	// 编译正则表达式
	re, errReg := regexp.Compile(helper.SimpleUrlRegexp)
	if errReg != nil {
		fmt.Println(fmt.Sprintf("errReg==>failed to compile url regex: %s", errReg.Error()))
		return content, errReg
	}
	// 查找所有匹配的内容
	urls := re.FindAllString(content, -1)
	// 遍历所有匹配的内容
	for _, url := range urls {
		// 正则匹配是否是否为url
		if !helper.HasUrl(url) {
			continue
		}
		// 调用 extractTextFromURL 函数
		text, err := ExtractTextFromURL(url)
		if err != nil {
			fmt.Println(fmt.Sprintf("errReg==>failed to extract text from url: %s", err.Error()))
			continue
		}
		// 替换content中的url为提取的文本
		content = strings.Replace(content, url, text, -1)
	}
	return content, nil
}

func ExtractTextFromURL(url string) (string, error) {
	// 如果图片下载功能关闭，直接返回空文本
	if !config.ImageDownloadEnabled {
		return "", nil
	}

	resp, err := service.DoImageRequest(url, true)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 创建一个临时文件来存储下载的文件内容
	tempFile, err := os.CreateTemp("", "temp-file-*")
	if err != nil {
		return "", err
	}
	defer tempFile.Close()
	defer os.Remove(tempFile.Name())

	// 将文件内容写入临时文件
	_, err = io.Copy(tempFile, resp.Body)
	if err != nil {
		return "", err
	}

	// 根据文件类型选择相应的解析方法
	var text string
	contentType := resp.Header.Get("Content-Type")
	if contentType == "application/pdf" {
		text, err = extractTextFromPDF(tempFile.Name())
	} else if contentType == "application/msword" ||
		contentType == "application/vnd.openxmlformats-officedocument.wordprocessingml.document" {
		text, err = extractTextFromWord(tempFile.Name())
	} else if contentType == "application/vnd.ms-excel" ||
		contentType == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" {
		text, err = extractTextFromExcel(tempFile.Name())
	} else if strings.Contains(contentType, "text/csv") {
		text, err = extractTextFromCSV(tempFile.Name())
	} else if strings.Contains(contentType, "text/html") {
		text, err = extractTextFromHtml(tempFile.Name())
	} else if strings.Contains(contentType, "text") {
		text, err = extractTextFromTXT(tempFile.Name())
	} else {
		// 尝试根据url后缀解析类型
		if strings.HasSuffix(url, ".pdf") {
			text, err = extractTextFromPDF(tempFile.Name())
		} else if strings.HasSuffix(url, ".doc") || strings.HasSuffix(url, ".docx") {
			text, err = extractTextFromWord(tempFile.Name())
		} else if strings.HasSuffix(url, ".xls") || strings.HasSuffix(url, ".xlsx") {
			text, err = extractTextFromExcel(tempFile.Name())
		} else {
			return "", fmt.Errorf("unsupported file type: %s", contentType)
		}
	}

	return text, nil
}

func extractTextFromPDF(filename string) (string, error) {
	// 构建 pdf2txt.py 命令
	cmd := exec.Command("pdf2txt.py", filename)
	fmt.Println("pdf2txt 命令开始执行...")
	// 执行命令并捕获输出
	output, err := cmd.Output()
	if err != nil {
		fmt.Println(fmt.Sprintf("执行 pdf2txt.py 失败: %s", err.Error()))
		return "", err
	}
	// 将输出转换为字符串
	text := string(output)
	return text, nil
}

func extractTextFromWord(filename string) (string, error) {
	cmd := exec.Command("python", "word2txt.py", filename)
	fmt.Println("word2txt 命令开始执行...")
	output, err := cmd.Output()
	if err != nil {
		fmt.Println(fmt.Sprintf("执行 word2txt.py 失败: %s", err.Error()))
		return "", err
	}
	text := string(output)
	return text, nil
}
func extractTextFromExcel(filename string) (string, error) {
	doc, err := spreadsheet.Open(filename)
	if err != nil {
		return "", err
	}
	defer doc.Close()

	var text string
	for _, sheet := range doc.Sheets() {
		for _, row := range sheet.Rows() {
			for _, cell := range row.Cells() {
				text += cell.GetFormattedValue() + "\t"
			}
			text += "\n"
		}
	}

	return text, nil
}

func extractTextFromCSV(filename string) (string, error) {
	f, err := os.Open(filename)
	if err != nil {
		return "", err
	}
	defer f.Close()

	reader := csv.NewReader(f)
	var text string
	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return "", err
		}
		text += strings.Join(record, ",") + "\n"
	}

	return text, nil
}

func extractTextFromTXT(filename string) (string, error) {
	content, err := os.ReadFile(filename)
	if err != nil {
		return "", err
	}
	return string(content), nil
}

func extractTextFromHtml(filename string) (string, error) {
	content, err := os.ReadFile(filename)
	// 使用 goquery 解析 HTML
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(string(content)))
	if err != nil {
		return string(content), err
	}
	// 移除所有的 <script> 标签
	doc.Find("script").Remove()
	// 提取文本内容
	text := doc.Text()
	return text, nil
}

// CopyFileIfExist checks if the source file exists, and if so, copies it to the destination.
// If the source file does not exist, it returns nil.
func CopyFileIfExist(src, dst string) error {
	if _, err := os.Stat(src); err == nil {
		return CopyFile(src, dst)
	}
	return nil
}

// CopyFile copies the contents of the file named src to the file named
// by dst. The file will be created if it does not already exist. If the
// destination file exists, all it's contents will be replaced by the contents
// of the source file. The file mode will be copied from the source and
// the copied data is synced/flushed to stable storage.
func CopyFile(src, dst string) (err error) {
	in, err := os.Open(src)
	if err != nil {
		return
	}
	defer in.Close()

	out, err := os.Create(dst)
	if err != nil {
		return
	}
	defer func() {
		if e := out.Close(); e != nil {
			err = e
		}
	}()

	_, err = io.Copy(out, in)
	if err != nil {
		return
	}

	err = out.Sync()
	if err != nil {
		return
	}

	si, err := os.Stat(src)
	if err != nil {
		return
	}
	err = os.Chmod(dst, si.Mode())
	if err != nil {
		return
	}

	return
}

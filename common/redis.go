package common

import (
	"context"
	"encoding/json"
	"errors"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/songquanpeng/one-api/common/logger"
)

var RDB redis.Cmdable
var RedisEnabled = true

// 预编译的Lua脚本，避免每次调用都重新编译
var setIfNotExistsScript = redis.NewScript(`
	local cacheKey = KEYS[1]
	local newValue = ARGV[1]
	local expiration = ARGV[2]
	
	-- 检查key是否存在
	if redis.call('EXISTS', cacheKey) == 0 then
		-- key不存在，写入新值
		redis.call('SET', cacheKey, newValue, 'EX', expiration)
		return 1
	else
		-- key已存在，跳过写入
		return 0
	end
`)

// InitRedisClient This function is called after init()
func InitRedisClient() (err error) {
	if os.Getenv("REDIS_CONN_STRING") == "" {
		RedisEnabled = false
		logger.SysLog("REDIS_CONN_STRING not set, Redis is not enabled")
		return nil
	}
	if os.Getenv("SYNC_FREQUENCY") == "" {
		RedisEnabled = false
		logger.SysLog("SYNC_FREQUENCY not set, Redis is disabled")
		return nil
	}
	redisConnString := os.Getenv("REDIS_CONN_STRING")
	if os.Getenv("REDIS_MASTER_NAME") == "" {
		logger.SysLog("Redis is enabled")
		opt, err := redis.ParseURL(redisConnString)
		if err != nil {
			logger.FatalLog("failed to parse Redis connection string: " + err.Error())
		}
		RDB = redis.NewClient(opt)
	} else {
		// cluster mode
		logger.SysLog("Redis cluster mode enabled")
		RDB = redis.NewUniversalClient(&redis.UniversalOptions{
			Addrs:      strings.Split(redisConnString, ","),
			Password:   os.Getenv("REDIS_PASSWORD"),
			MasterName: os.Getenv("REDIS_MASTER_NAME"),
		})
	}
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err = RDB.Ping(ctx).Result()
	if err != nil {
		logger.FatalLog("Redis ping test failed: " + err.Error())
	}
	return err
}

func ParseRedisOption() *redis.Options {
	opt, err := redis.ParseURL(os.Getenv("REDIS_CONN_STRING"))
	if err != nil {
		logger.FatalLog("failed to parse Redis connection string: " + err.Error())
	}
	return opt
}

func RedisSet(key string, value string, expiration time.Duration) error {
	ctx := context.Background()
	return RDB.Set(ctx, key, value, expiration).Err()
}

func RedisGet(key string) (string, error) {
	ctx := context.Background()
	return RDB.Get(ctx, key).Result()
}

func RedisGetAndExtendExpiryUsingLua(key string, extension time.Duration) (string, error) {
	ctx := context.Background()
	script := `
        local key = KEYS[1]
        local extension = ARGV[1]
        local value = redis.call('GET', key)
        if value then
            redis.call('EXPIRE', key, extension)
        end
        return value
    `
	// 执行Lua脚本
	result, err := RDB.Eval(ctx, script, []string{key}, extension.Seconds()).Result()
	if err != nil {
		return "", err
	}

	// Lua脚本返回的结果
	if result == nil {
		return "", nil // 或其他适当的空值处理
	}
	return result.(string), nil
}

func RedisDel(key string) error {
	ctx := context.Background()
	return RDB.Del(ctx, key).Err()
}

func RedisDecrease(key string, value int64) error {
	ctx := context.Background()
	return RDB.DecrBy(ctx, key, value).Err()
}

func RedisIncrease(key string, value int64) error {
	ctx := context.Background()
	return RDB.IncrBy(ctx, key, value).Err()
}

func RedisIncreaseWithLuaCheckExists(key string, value int64) error {
	ctx := context.Background()
	luaScript := `
	local currentValue = redis.call("GET", KEYS[1])
	if currentValue == false then
		return -1
	else
		currentValue = tonumber(currentValue)
		if currentValue == nil then
			return -1
		else
			return redis.call("INCRBY", KEYS[1], ARGV[1])
		end
	end
	`
	script := redis.NewScript(luaScript)
	result, err := script.Run(ctx, RDB, []string{key}, value).Result()
	if err != nil {
		return err
	}
	if result == int64(-1) {
		return errors.New("key does not exist")
	}
	return nil
}

func RedisDecreaseWithLuaCheckExists(key string, value int64) error {
	ctx := context.Background()
	luaScript := `
	local currentValue = redis.call("GET", KEYS[1])
	if currentValue == false then
		return -1
	else
		currentValue = tonumber(currentValue)
		if (currentValue - tonumber(ARGV[1])) >= 0 then
			return redis.call("DECRBY", KEYS[1], ARGV[1])
		else
			return -1
		end
	end
	`
	script := redis.NewScript(luaScript)
	result, err := script.Run(ctx, RDB, []string{key}, value).Result()
	if err != nil {
		return err
	}
	if result == int64(-1) {
		return errors.New("not enough quota or key does not exist")
	}
	return nil
}

func RedisGetValuesWithPrefix[T any](prefix string) ([]T, error) {
	var result []T

	// 使用SCAN命令获取所有以prefix开头的键
	var keys []string
	iter := RDB.Scan(context.Background(), 0, prefix+"*", 0).Iterator()
	for iter.Next(context.Background()) {
		keys = append(keys, iter.Val())
	}
	if err := iter.Err(); err != nil {
		return nil, err
	}
	if len(keys) == 0 {
		return result, nil
	}
	// 从Redis获取数据
	values, err := RDB.MGet(context.Background(), keys...).Result()
	if err != nil {
		return nil, err
	}

	// 将数据赋值给结构体的切片
	for _, val := range values {
		var item T

		// 解析JSON字符串到item
		err := json.Unmarshal([]byte(val.(string)), &item)
		if err != nil {
			return nil, err
		}

		result = append(result, item)
	}

	return result, nil
}

func RedisMSet(data map[string]interface{}) error {
	ctx := context.Background()
	return RDB.MSet(ctx, data).Err()
}

func RedisDelByPrefix(prefix string) error {
	// 使用SCAN命令获取所有以prefix开头的键
	var keys []string
	iter := RDB.Scan(context.Background(), 0, prefix+"*", 0).Iterator()
	for iter.Next(context.Background()) {
		keys = append(keys, iter.Val())
	}
	if err := iter.Err(); err != nil {
		return err
	}

	// 删除所有以prefix开头的键
	for _, key := range keys {
		err := RDB.Del(context.Background(), key).Err()
		if err != nil {
			return err
		}
	}

	return nil
}

// 通用的List操作方法
func RedisListPush(key string, value interface{}, expiration time.Duration) error {
	if !RedisEnabled || RDB == nil {
		return errors.New("redis is not enabled")
	}

	ctx := context.Background()
	pipe := RDB.Pipeline()

	// 序列化数据
	jsonData, err := json.Marshal(value)
	if err != nil {
		return err
	}

	// 添加到列表
	pipe.LPush(ctx, key, string(jsonData))

	// 设置过期时间
	if expiration > 0 {
		pipe.Expire(ctx, key, expiration)
	}

	_, err = pipe.Exec(ctx)
	return err
}

// 获取列表所有元素
func RedisListGetAll(key string) ([]string, error) {
	if !RedisEnabled || RDB == nil {
		return nil, errors.New("redis is not enabled")
	}

	ctx := context.Background()
	return RDB.LRange(ctx, key, 0, -1).Result()
}

// 获取列表长度
func RedisListLen(key string) (int64, error) {
	if !RedisEnabled || RDB == nil {
		return 0, errors.New("redis is not enabled")
	}

	ctx := context.Background()
	return RDB.LLen(ctx, key).Result()
}

// Hash操作方法
func RedisHashSet(key string, field string, value interface{}, expiration time.Duration) error {
	if !RedisEnabled || RDB == nil {
		return errors.New("redis is not enabled")
	}

	ctx := context.Background()
	pipe := RDB.Pipeline()

	// 设置hash字段
	pipe.HSet(ctx, key, field, value)

	// 设置过期时间
	if expiration > 0 {
		pipe.Expire(ctx, key, expiration)
	}

	_, err := pipe.Exec(ctx)
	return err
}

// 获取hash字段值
func RedisHashGet(key string, field string) (string, error) {
	if !RedisEnabled || RDB == nil {
		return "", errors.New("redis is not enabled")
	}

	ctx := context.Background()
	return RDB.HGet(ctx, key, field).Result()
}

// 获取所有hash字段
func RedisHashGetAll(key string) (map[string]string, error) {
	if !RedisEnabled || RDB == nil {
		return nil, errors.New("redis is not enabled")
	}

	ctx := context.Background()
	return RDB.HGetAll(ctx, key).Result()
}

// Set集合操作方法
func RedisSetAdd(key string, members ...interface{}) error {
	if !RedisEnabled || RDB == nil {
		return errors.New("redis is not enabled")
	}

	ctx := context.Background()
	return RDB.SAdd(ctx, key, members...).Err()
}

// 获取集合所有成员
func RedisSetMembers(key string) ([]string, error) {
	if !RedisEnabled || RDB == nil {
		return nil, errors.New("redis is not enabled")
	}

	ctx := context.Background()
	return RDB.SMembers(ctx, key).Result()
}

// 检查集合成员是否存在
func RedisSetIsMember(key string, member interface{}) (bool, error) {
	if !RedisEnabled || RDB == nil {
		return false, errors.New("redis is not enabled")
	}

	ctx := context.Background()
	return RDB.SIsMember(ctx, key, member).Result()
}

// 通用的键值操作方法
func RedisKeyExists(key string) (bool, error) {
	if !RedisEnabled || RDB == nil {
		return false, errors.New("redis is not enabled")
	}

	ctx := context.Background()
	n, err := RDB.Exists(ctx, key).Result()
	return n > 0, err
}

func RedisKeyExpire(key string, expiration time.Duration) error {
	if !RedisEnabled || RDB == nil {
		return errors.New("redis is not enabled")
	}

	ctx := context.Background()
	return RDB.Expire(ctx, key, expiration).Err()
}

// 批量操作方法
func RedisMGet(keys ...string) ([]interface{}, error) {
	if !RedisEnabled || RDB == nil {
		return nil, errors.New("redis is not enabled")
	}

	ctx := context.Background()
	return RDB.MGet(ctx, keys...).Result()
}

// 模式匹配删除键
func RedisDeleteByPattern(pattern string) error {
	if !RedisEnabled || RDB == nil {
		return errors.New("redis is not enabled")
	}

	ctx := context.Background()
	iter := RDB.Scan(ctx, 0, pattern, 0).Iterator()

	for iter.Next(ctx) {
		err := RDB.Del(ctx, iter.Val()).Err()
		if err != nil {
			return err
		}
	}

	return iter.Err()
}

// 带锁的操作
func RedisWithLock(key string, timeout time.Duration, fn func() error) error {
	if !RedisEnabled || RDB == nil {
		return errors.New("redis is not enabled")
	}

	ctx := context.Background()
	lockKey := "lock:" + key

	// 尝试获取锁
	ok, err := RDB.SetNX(ctx, lockKey, "1", timeout).Result()
	if err != nil {
		return err
	}
	if !ok {
		return errors.New("failed to acquire lock")
	}

	defer RDB.Del(ctx, lockKey)

	return fn()
}

// 新增通用管道操作方法
func RedisPipelineIncr(key string, fields map[string]int, expiration time.Duration) error {
	if !RedisEnabled || RDB == nil {
		return errors.New("redis is not enabled")
	}

	ctx := context.Background()
	_, err := RDB.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		// 批量增加字段值
		for field, value := range fields {
			pipe.HIncrBy(ctx, key, field, int64(value))
		}

		// 设置过期时间
		if expiration > 0 {
			pipe.Expire(ctx, key, expiration)
		}

		// 记录最后更新时间
		pipe.HSet(ctx, key, "last_updated", time.Now().Unix())
		return nil
	})
	return err
}

// 新增批量获取方法
func RedisHGetAllWithFilter(key string, fieldSuffix string) (map[string]int, error) {
	ctx := context.Background()
	allFields, err := RDB.HGetAll(ctx, key).Result()
	if err != nil {
		return nil, err
	}

	result := make(map[string]int)
	for k, v := range allFields {
		if strings.HasSuffix(k, fieldSuffix) {
			val, _ := strconv.Atoi(v)
			result[k] = val
		}
	}
	return result, nil
}

// 新增带过期时间的管道操作（增强版）
func RedisPipelineIncrWithExpire(key string, fields map[string]int, expire time.Duration) error {
	ctx := context.Background()
	_, err := RDB.TxPipelined(ctx, func(pipe redis.Pipeliner) error {
		// 批量递增
		for field, val := range fields {
			pipe.HIncrBy(ctx, key, field, int64(val))
		}

		// 设置过期时间（仅当键不存在时）
		pipe.ExpireNX(ctx, key, expire)

		// 更新时间戳
		pipe.HSet(ctx, key, "last_updated", time.Now().Unix())
		return nil
	})
	return err
}

// RedisSetIfNotExists 只在key不存在时才写入，避免并发覆盖
// 使用预编译的Lua脚本确保原子操作和最佳性能
// 返回值：true表示写入成功，false表示key已存在跳过写入
func RedisSetIfNotExists(key string, value string, expiration time.Duration) (bool, error) {
	ctx := context.Background()

	// 使用预编译的全局脚本，避免重复编译开销
	result, err := setIfNotExistsScript.Run(ctx, RDB, []string{key}, value, int(expiration.Seconds())).Result()
	if err != nil {
		return false, err
	}

	return result.(int64) == 1, nil
}

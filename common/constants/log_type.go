package constants

import (
	"fmt"
	"strings"
)

// LogTypeExclude 需要排除的日志类型
var LogTypeExclude = []int{6, 7, 8, 15, 16}

// LogFieldsExclude 需要排除的日志字段
var LogFieldsExclude = []string{"channel_name", "channel", "channel_id", "prompt", "cost_quota", "error_code"}

// IsExcludedLogType 检查日志类型是否在排除列表中
func IsExcludedLogType(logType int) bool {
	for _, t := range LogTypeExclude {
		if t == logType {
			return true
		}
	}
	return false
}

// GetExcludeLogTypesStr 获取排除的日志类型字符串，用于SQL查询
func GetExcludeLogTypesStr() string {
	strNums := make([]string, len(LogTypeExclude))
	for i, num := range LogTypeExclude {
		strNums[i] = fmt.Sprint(num)
	}
	return "(" + strings.Join(strNums, ",") + ")"
}

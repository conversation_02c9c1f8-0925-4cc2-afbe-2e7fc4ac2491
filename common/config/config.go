package config

import (
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/songquanpeng/one-api/common/env"

	"github.com/google/uuid"
)

var SystemName = "Shell API"

var NotShowChannelBaseUrl = env.Bool("NOT_SHOW_CHANNEL_BASE_URL", false)
var NotShowChannelKey = env.Bo<PERSON>("NOT_SHOW_CHANNEL_KEY", false)
var NotShowChannelAccessToken = env.Bool("NOT_SHOW_CHANNEL_ACCESS_TOKEN", false)

var JWTSigningKey = uuid.New().String()

var JWTCrossLoginEnabled = false

var JWTAuthEnabled = true

var ImageDownloadEnabled = true
var WorkerUrl = ""
var WorkerValidKey = ""

func EnableWorker() bool {
	return WorkerUrl != ""
}

// 反制对方的[成分判定]功能
var AntiIngredientEnabled = false

// SiteDescription 站点描述,用于 SEO 以及链接预览，重启生效
var SiteDescription = ""
var ServerAddress = "http://localhost:3000"
var FileSystemServerAddress = "http://localhost:3000"
var FileSystemServerEnabled = false
var FileSystemClientInfoLogEnabled = false
var FileSystemProxyModeEnabled = false
var FileSystemProxyURL = ""
var FileSystemProxyUploadFieldName = "file"
var FileSystemProxyMethod = "POST"
var FileSystemProxyHeaders = ""
var FileSystemProxyAuthEnabled = false
var FileSystemProxyAuthType = "bearer"
var FileSystemProxyAuthValue = ""
var SwitchUIEnabled = true
var OnlineTopupEnabled = false
var PayAddress = ""
var PayAddress2 = ""
var EpayId = ""
var EpayId2 = ""
var EpayKey = ""
var EpayKey2 = ""
var Price = 7.3
var CustomUsdtRate = 7.0
var CustomPayPalUsdRate = 7.0
var PayPalMinimumFee = 1.0
var CustomEthRate = 27406.00
var MaxTopUpLimit = 10000.0         //美金
var CustomEpayCallbackAddress = ""  //自定义回调地址
var CustomEpayCallbackAddress2 = "" //自定义回调地址

var HideRelayErrorEnabled = false // 是否隐藏relay报错信息
var HideRelayErrorExceptList = []string{}

var HideUpstreamApiTypeErrorEnabled = false // 是否屏蔽上游API错误类型(统一替换为shell_api_error)

// 用于检测API错误类型的后缀匹配，默认为"_api_error"
var ApiErrorTypeSuffix = "_api_error"

// 用户自定义需要屏蔽的错误类型列表
var CustomHideApiErrorTypes = []string{}

var ResponseErrorStillChargeEnabled = true // 响应错误依旧计费(主要用于非流式超时导致的上游计费但自身未计费的改善)

var NewTiktokenEnabled = true // 启用新版tiktoken库计费

var CustomCircuitBreakerEnabled = false // 启用个性化熔断设置
var CustomDisableChannelEnabled = false // 启用个性化禁用设置

var DisableEntireChannelKeywordsEnabled = false // 是否启用禁用整个渠道的关键词列表
var DisableEntireChannelKeywords = []string{}   // 禁用整个渠道的关键词列表

var LogConsumeEnabled = true // 是否记录消费日志
var LogErrorEnabled = true   // 是否记录错误日志
var LogSysInfoEnabled = true // 是否记录系统信息日志

// Claude 消息整理功能配置
var ClaudeMessageNormalizationEnabled = false // 是否启用 Claude 消息整理功能

var DisableChannelHttpStatusCodeList = []int{
	//307,
	//308,
	//429,
	//500,
	//502,
	//503,
	//504,
} // 需要禁用的statusCode状态码

var CircuitBreakerHttpStatusCodeList = []int{
	//307,
	//308,
	//429,
	//500,
	//502,
	//503,
	//504,
} // 需要熔断的statusCode状态码

var StreamChunkTimeout = 0                           // 流式请求上游单个数据块等待响应的超时时间 单位秒
var StreamChunkTimeoutWarningLogThreshold int64 = 10 // 流式请求上游单个数据块等待响应的超时时间警告日志阈值 默认10秒

var Say1DirectSuccessEnabled = false // 拨测直接返回成功
var RequestTruncationEnabled = false // 请求截断
var RequestTruncationMap = map[string]int64{}
var RequestTruncationOriginMap = map[string]int64{}

func UpdateRequestTruncationMapByJSONString(jsonStr string) error {
	RequestTruncationMap = make(map[string]int64)
	return json.Unmarshal([]byte(jsonStr), &RequestTruncationMap)
}

func UpdateRequestTruncationOriginMapByJSONString(jsonStr string) error {
	RequestTruncationOriginMap = make(map[string]int64)
	return json.Unmarshal([]byte(jsonStr), &RequestTruncationOriginMap)
}

func GetRequestTruncationMap(modelName string) (bool, int64) {
	answer, ok := RequestTruncationMap[modelName]
	if !ok {
		return false, 0
	}
	return true, answer
}

func GetRequestTruncationOriginMap(modelName string) (bool, int64) {
	answer, ok := RequestTruncationOriginMap[modelName]
	if !ok {
		return false, 0
	}
	return true, answer
}
func RequestTruncationMap2JSONString() string {
	jsonBytes, err := json.Marshal(RequestTruncationMap)
	if err != nil {
		return ""
	}
	return string(jsonBytes)
}

func RequestTruncationOriginMap2JSONString() string {
	jsonBytes, err := json.Marshal(RequestTruncationOriginMap)
	if err != nil {
		return ""
	}
	return string(jsonBytes)
}

const (
	Alipay    = 1 << iota // 仅支付宝,二进制表示为 0001
	WeChat                // 仅微信,二进制表示为 0010
	Card                  // Visa / Mastercard,二进制表示为 0100
	Stripe                // 仅Stripe,二进制表示为 1000
	UsdtTrc20             // 二进制表示为 10000
	EvmEthEth             // 二进制表示为 100000
	PayPal                // 二进制表示为 100000
)

// var CustomAvailablePayMethods = 99 //自定义可用支付方式，99：全部，1：仅支付宝，2：仅微信，3：仅QQ钱包（示例）
var CustomAvailablePayMethods = Alipay | WeChat | Card | Stripe | UsdtTrc20 | EvmEthEth
var CustomAvailablePayMethods2 = Alipay | WeChat | Card | Stripe | UsdtTrc20 | EvmEthEth

var CheckinEnabled = false
var CheckinQuota int64 = 5000

// 签到验证码难度动态提升开启
var CheckinCaptchaDifficultyIncreaseEnabled = false

// 随机背景色
var CheckinCaptchaRandomBackgroundColorEnabled = false

// 签到验证码难度动态提升,每签到多少天,验证码位数增加多少位,噪点增加多少个
var CheckinCaptchaLengthDuration = 1
var CheckinCaptchaLengthIncrease = 1
var CheckinCaptchaNoiseDuration = 1
var CheckinCaptchaNoiseIncrease = 1

// CustomAppList 友情链接app列表
var CustomAppList = ""

// CustomThemeConfig 自定义主题
var CustomThemeConfig = ""

// CustomDarkThemeConfig 自定义暗黑模式主题
var CustomDarkThemeConfig = ""

// QuotaExpireEnabled 是否开启充值有效期
var QuotaExpireEnabled = false

// MidjourneyPlusEnabled 由于mjPlus和mj不能完全兼容,只能选择一个占用/mj路由,默认为非Plus
var MidjourneyPlusEnabled = false

// MidjourneyShowDerivedRatesEnabled 是否显示midjourney衍生费率
var MidjourneyShowDerivedRatesEnabled = false

// BatchUpdateMjMode MJ任务更新模式 (single/batch/pool_single/pool_batch)
var BatchUpdateMjMode = "single"

// BatchUpdateMjWorkerSize 工作协程数量，默认为10
var BatchUpdateMjWorkerSize = 10

// BatchUpdateMjBatchSize 批量查询任务数量，默认为10
var BatchUpdateMjBatchSize = 10

var MJSensitiveWordsRefundEnabled = false // 提交任务马上报失败的补偿

var MidjourneyRemoveSlashEnabled = false // mj去除提示词开头斜杠

// MidjourneyV7TurboEnabled 是否--v 7直接走turbo计费逻辑
var MidjourneyV7TurboEnabled = false

// MidjourneyDraftHalfPriceEnabled 是否--draft参数的任务在fast或turbo模式下的原始绘图操作按半价计费
var MidjourneyDraftHalfPriceEnabled = false

// mj任务进度轮询间隔， 单位s
var MidjourneyPollDuration = 5

// 任务提交超时时间，单位s，超过此时间未开始的任务将被标记为超时
var MidjourneySubmitTimeout = 86400

// 任务开始超时时间，单位s，超过此时间未完成的任务将被标记为超时
var MidjourneyStartTimeout = 600

// 使用自定义Midjourney图片回显地址,默认开启,如果关闭走discord的cdn
var MidjourneyCustomImageUrlEnabled = true

// 自定义Midjourney图片回显地址,留空则默认和ServerAddress一致
var MidjourneyCustomImageUrl = ""

// 自定义discord反代地址
var MjDiscordCdnProxy = ""

// MidjourneyBase64StorageAddress(只有渠道开启了base64存储才对渠道生效)
var MidjourneyBase64StorageAddress = ""

// GptImageStorageAddress GPT图片本地存储域名
var GptImageStorageAddress = ""

var MidjourneyBase64StorageRetentionDays = 30 //图片文件在本地保存的天数,超过该时间将被自动清理。设置为0表示永久保存

// QuotaExpireDays 充值后有效期顺延多少日
var QuotaExpireDays = 0

// GoogleOAuthEnabled 是否开启Google OAuth，等待 new-api 确认合入
//var GoogleOAuthEnabled = false
//var GoogleClientId = ""
//var GoogleClientSecret = ""

var Footer = ""
var HeaderScript = ""
var Logo = ""
var TopUpLink = ""
var ChatLink = ""
var QuotaPerUnit = 500 * 1000.0 // $0.002 / 1K tokens
var DisplayInCurrencyEnabled = true
var DisplayTokenStatEnabled = true

// 反反爬
var GlobalCustomRequestHeaders = ""
var SpoofIP = ""
var SpoofIPEnabled = false

// 是否完全模拟OpenAI官方响应格式，默认关闭
var MockOpenAICompleteFormatEnabled = false

// PrivacyPolicy 隐私政策内容
var PrivacyPolicy = ""

// ServiceAgreement 服务协议内容
var ServiceAgreement = ""

// UnsubscribeEnabled 是否允许用户注销
var UnsubscribeEnabled = false

//var ChartPageEnabled = true // 是否开启图表页面，现在已经废弃

var GuestChatPageEnabled = true // 是否允许游客访问聊天页面
var PptGenPageEnabled = false   // 是否显示PPT生成页面
var AgentMenuEnabled = true     // 是否显示代理菜单

var UserLogViewEnabled = true

var FloatButtonEnabled = false
var DocumentInfo = ""
var QqInfo = ""
var WechatInfo = ""
var RegisterInfo = ""

// token分组切换
var TokenGroupChangeEnabled = false

// SensitiveWordsEnabled 敏感词开关
var SensitiveWordsEnabled = false

// SensitiveWordsMap 敏感词列表
var SensitiveWordsMap = map[string]string{}

// SensitiveWordsTips 触发敏感词提示语
var SensitiveWordsTips = "互联网并非法外之地，请规范您的言行"

// UserTimeoutEnabled 用户超时功能开关
var UserTimeoutEnabled = false

// UserTimeoutConfigsMap 用户超时配置缓存 key格式: "userId:modelName"
// 使用sync.Map实现并发安全的读多写少场景优化
var UserTimeoutConfigsMap sync.Map

// UserTimeoutConfig 用户超时配置结构体（内存缓存用）
type UserTimeoutConfig struct {
	UserId            int     `json:"user_id"`
	ModelName         string  `json:"model_name"`
	FirstByteTimeout  int     `json:"first_byte_timeout"`
	TotalTimeout      int     `json:"total_timeout"`
	TpsThreshold      float64 `json:"tps_threshold"`
	TimeoutCostBearer string  `json:"timeout_cost_bearer"`
}

// GuestQueryEnabled 免登录根据sk查询消费情况
var GuestQueryEnabled = false

// MidjourneyEnabled 开启Midjourney菜单
var MidjourneyEnabled = true

// Any options with "Secret", "Token" in its key won't be return by GetOptions

var SessionSecret = uuid.New().String()

var OptionMap map[string]string
var OptionMapRWMutex sync.RWMutex

var ItemsPerPage = 10
var MaxRecentItems = 100

var PasswordLoginEnabled = true
var PasswordRegisterEnabled = true
var EmailVerificationEnabled = false
var GitHubOAuthEnabled = false
var OidcEnabled = false
var WeChatAuthEnabled = false
var TurnstileCheckEnabled = false
var SMSVerificationEnabled = false // 短信验证码总开关
var SMSRegisterEnabled = true      // 注册短信验证码开关
var SMSLoginEnabled = true         // 登录短信验证码开关

var CaptchaCheckEnabled = false               // 验证码校验开关
var CaptchaKeyLong = 4                        // 验证码长度
var CaptchaNoiseCount = 4                     // 验证码噪声
var CaptchaBgColor = "rgba(255, 255, 255, 1)" // 验证码背景色R,G,B,A

var RegisterEnabled = true

var EmailDomainRestrictionEnabled = false
var EmailDomainWhitelist = []string{
	"gmail.com",
	"163.com",
	"126.com",
	"qq.com",
	"outlook.com",
	"hotmail.com",
	"live.com",
	"icloud.com",
	"yahoo.com",
	"foxmail.com",
}

// EmailDomainQQNumberOnlyEnabled 是否只允许纯数字的 QQ 邮箱
var EmailDomainQQNumberOnlyEnabled = false

var DebugEnabled = strings.ToLower(os.Getenv("DEBUG")) == "true"
var DebugSQLEnabled = strings.ToLower(os.Getenv("DEBUG_SQL")) == "true"
var MemoryCacheEnabled = strings.ToLower(os.Getenv("MEMORY_CACHE_ENABLED")) == "true"

var EncryptImageServerEnabled = strings.ToLower(os.Getenv("ENCRYPT_IMAGE_SERVER_ENABLED")) == "true"
var EiseEnabled = false
var EiseUrl = ""
var EiseKey = ""

var MaxPromptLogLength = int64(1 * 1024) // 提示词日志记录最大长度限制,单位字节,默认1kb

var DemoEnabled = os.Getenv("DEMO") == "true"
var LicenseKey = os.Getenv("LICENSE_KEY")
var LogDisabled = os.Getenv("LOG_DISABLED") == "true"
var LogFileDisabled = os.Getenv("LOG_FILE_DISABLED") == "true" // 新增: 控制是否禁用日志文件输出,默认false

var LicenseInstanceId = ""
var LicenseGateway = fmt.Sprintf("https://license.shellapi.vip")
var LicenseGatewayBack1 = fmt.Sprintf("https://wrnnyjwsfmur.cloud.sealos.io")
var LicenseEnabled = false

var LogDetailConsumeEnabled = false
var LogUpstreamResponseEnabled = false // 是否记录上游返回的原始响应
var LogFullResponseEnabled = false     // 是否记录处理后返回给客户端的最终响应
var LogDownstreamErrorEnabled = false  // 是否记录返回给客户端的错误信息

var LogDurationType = 2                   // 日志耗时类型: 1: 请求耗时; 2: 响应首个字节耗时; 3: 总耗时 默认2
var LogDurationIncludeRetryEnabled = true // 日志耗时是否包含重试时间,默认true

var SMTPServer = ""
var SMTPPort = 587
var SMTPAccount = ""
var SMTPFrom = ""
var SMTPToken = ""

var EmailProxyServer = "" // 邮件代理服务器,用于解决本地端口被封的问题,需要单独搭建一个one-api,在里面配置好相关邮件配置,然后将域名填入此处
var EmailProxyAuth = ""   // 代理服务器的认证信息,root用户的token

// EmailProxyExecutor 是否为邮件代理执行者,是执行者则不进行代理转发,自己执行发送
var EmailProxyExecutor = false

var GitHubClientId = ""
var GitHubClientSecret = ""

var GoogleOAuthEnabled = false
var GoogleClientId = ""
var GoogleClientSecret = ""

// TelegramOAuthEnabled 是否开启Telegram OAuth
var TelegramOAuthEnabled = false
var TelegramBotToken = ""
var TelegramBotName = ""

var LarkClientId = ""
var LarkClientSecret = ""

var OidcClientId = ""
var OidcClientSecret = ""
var OidcWellKnown = ""
var OidcAuthorizationEndpoint = ""
var OidcTokenEndpoint = ""
var OidcUserinfoEndpoint = ""

var WeChatServerAddress = ""
var WeChatServerToken = ""
var WeChatAccountQRCodeImageURL = ""

var MessagePusherAddress = ""
var MessagePusherToken = ""

var TurnstileSiteKey = ""
var TurnstileSecretKey = ""

var QuotaForNewUser int64 = 0
var QuotaForInviter int64 = 0
var QuotaForInvitee int64 = 0
var ChannelDisableThreshold = 5.0
var AutomaticDisableChannelEnabled = false
var AutomaticEnableChannelEnabled = false
var QuotaRemindThreshold int64 = 1000
var PreConsumedQuota int64 = 500
var ApproximateTokenEnabled = false
var RetryTimes = 0

// 熔断后重启复活的重试次数
var ReviveRetryTimes = 5

var TransferEnabled = false

// TransferFee 手续费，扣除发送者的，单位为%，例如3表示3%
var TransferFee = 10

var SMSAccessKeyId = ""
var SMSAccessKeySecret = ""
var SMSSignName = ""
var SMSTemplateCode = ""

// RetryWithoutRedirectEnabled 失败后的重试不使用重定向的方式,默认false,使用重定向的方式
var RetryWithoutRedirectEnabled = false
var RetryWithoutFailedChannelEnabled = true

// RetryKeepBillingTypeEnabled 重试时保持计费类型一致性开关
// 开启后，重试时只会选择与第一次请求相同计费类型的渠道，避免在同一个请求中混合按量和按次计费
// 关闭后，重试时如果优先级计费类型的渠道没有可用的，会降级到其他计费类型
// 默认开启以保证计费的一致性和透明度
var RetryKeepBillingTypeEnabled = true

// OpenAIStreamStringBufferEnabled OpenAI响应时候允许用字符串缓冲区
var OpenAIStreamStringBufferEnabled = false

// 临时变量用户存储OpenAI官方的id
var TempOpenAIChatCompletionID = ""

// 临时变量用户存储Claude官方的id
var TempClaudeChatCompletionID = ""

// OpenAIStreamStringBufferSize OpenAI响应时候字符串缓冲区大小
var OpenAIStreamStringBufferSize = 5

// LogDetailsModelWhitelistEnabled 详细日志模型记录白名单,开启后只有在白名单的模型产生的详细日志才会记录
var LogDetailsModelWhitelistEnabled = false
var LogDetailsModelWhitelist = ""

var RootUserEmail = ""

// 启用推送通知关键词黑名单
var NotificationKeywordBlacklistEnabled = false

// 推送通知关键词黑名单
var NotificationKeywordBlacklist []string

// 渠道拆分模型禁用
var ChannelAbilityDisableEnabled = false

// 启用强制重试关键词
var RelayErrForceRetryKeywordEnabled = false

// 强制重试关键词列表
var RelayErrForceRetryKeywordList []string

// 强制重试模型列表
var RelayErrForceRetryModelList []string

// 强制抛出错误设置
var RelayErrForceThrowErrorEnabled = false
var RelayErrForceThrowErrorKeywordList []string
var RelayErrForceThrowErrorModelList []string

// 空提示词替换
var EmptyPromptReplaceEnabled = false

// 开启后会解析文本中的文件链接file_url,并将文件内容替换到文本中
var ParseFileUrlEnabled = false

// 替换内容
var EmptyPromptReplaceContent = ""

// RootUserEmailNotificationEnabled 是否邮件通知root用户(通常用于渠道被禁用之类的提醒)
var RootUserEmailNotificationEnabled = true

// RootUserWxPusherNotificationEnabled 是否WxPusher推送通知root用户(通常用于渠道被禁用之类的提醒)
var RootUserWxPusherNotificationEnabled = true

// RootUserQyWxBotNotificationEnabled 是否开启企业微信推送通知root用户(通常用于渠道被禁用之类的提醒)
var RootUserQyWxBotNotificationEnabled = true

// RootUserRelayErrorNotificationEnabled 报错通知root用户(通常用于非禁用的报错推送,即使不禁用也要推送relay错误信息)
var RootUserRelayErrorNotificationEnabled = false

// WxPusherAppToken  需要自己关注wxpusher公众号获取
var WxPusherAppToken = ""

// RootUserWxPusherUid  需要自己关注wxpusher公众号获取
var RootUserWxPusherUid = ""

// QyWxBotWebhookUrl 企业微信webhookUrl,用于企业微信机器人推送消息
var QyWxBotWebhookUrl = ""

// LimitedAccessURL 当访问此 URL 时，只显示有限的内容。后期可以支持一下多域名配置
var LimitedAccessURL = ""
var LimitedAccessType = "" //blank: 空白，query：仅查询页面，about：仅公告页面

// LimitedAccessConfigs
var LimitedAccessConfigs = "[]"

// NoticeVersion 公告标记，用于判断是否需要重新显示公告，如果和 localStorage 中的值相同或者为空，则不弹出公告
var NoticeVersion = ""

var IsMasterNode = os.Getenv("NODE_TYPE") != "slave"

var requestInterval, _ = strconv.Atoi(os.Getenv("POLLING_INTERVAL"))
var RequestInterval = time.Duration(requestInterval) * time.Second

var SyncFrequency = env.Int("SYNC_FREQUENCY", 10*60) // unit is second

var BatchUpdateEnabled = false
var BatchUpdateConsiderMemoryQuotaEnabled = true
var BatchUpdateInterval = env.Int("BATCH_UPDATE_INTERVAL", 5)

var RelayTimeout = env.Int("RELAY_TIMEOUT", 0) // unit is second

var GeminiSafetySetting = env.String("GEMINI_SAFETY_SETTING", "BLOCK_NONE")

// StatusPageUrl 状态页地址，填写后则在前端显示状态页链接
var StatusPageUrl = ""

// NavExtMenus 导航栏扩展菜单，格式为json字符串
var NavExtMenus = ""
var NewHomeConf = `{"subTitle":"基于 <span class='bg-gradient-to-r from-red-400 to-red-600 text-transparent bg-clip-text'>官方API</span> 的中转服务，<span class='bg-gradient-to-r from-red-400 to-red-600 text-transparent bg-clip-text'>高速、高稳定、高并发</span>","desc":["在这个开放与分享的时代","OpenAI引领了一场人工智能的革命","现在，我们骄傲地向全世界宣布","我们已经完全支持OpenAI的全模型、以及国产各种大模型","最令人激动的是，我们已经做好准备","向世界揭晓更强大、更具影响力的人工智能技术！"],"links":[{"name":"用户中心","href":"/account/profile","target":"_blank","type":"primary"},{"name":"服务监控","href":"/status","target":"_blank"}],"serviceData":[["日API调用量","100K+"],["支持大模型数","100+"],["注册会员数","120K+"]],"priceData":[["充值比例","1.7 CNY : 1 USD","普通用户组"],["合作用户充值比例","1.5 CNY : 1 USD","累计满500USD"]],"qa":[["网站是如何计算我的费用的？","这里是回答"],["为什么余额可以是负数？","这里是回答"]]}`

// PureHomePageEnabled 是否开启纯净首页，开启后将不显示任何内容，只显示 iframe
var PureHomePageEnabled = false

var Theme = env.String("THEME", "antd")

var RotateLogsEnabled = env.Bool("ROTATE_LOGS_ENABLED", true)
var LogsRotationTime = env.Int("LOGS_ROTATION_TIME", 24)   // 拆分日志的时间间隔(单位小时)默认24小时
var LogsRotationSize = env.Int64("LOGS_ROTATION_SIZE", 10) // 拆分日志文件大小(单位M)默认10M
var LogsMaxAge = env.Int("LOGS_MAX_AGE", 168)              // 日志最大存留时间(单位小时)默认7天也就是168

// PackagePlanCacheEnabled 套餐是否使用Redis缓存，默认禁用以减轻Redis负载
var PackagePlanCacheEnabled = env.Bool("PACKAGE_PLAN_CACHE_ENABLED", false)

var ValidThemes = map[string]bool{
	"antd":     true,
	"default":  true,
	"berry":    true,
	"air":      true,
	"FluxDart": true,
}

// All duration's unit is seconds
// Shouldn't larger then RateLimitKeyExpirationDuration
var (
	GlobalApiRateLimitNum            = env.Int("GLOBAL_API_RATE_LIMIT", 480)
	GlobalApiRateLimitDuration int64 = 3 * 60

	GlobalWebRateLimitNum            = env.Int("GLOBAL_WEB_RATE_LIMIT", 6000)
	GlobalWebRateLimitDuration int64 = 3 * 60

	UploadRateLimitNum            = 10
	UploadRateLimitDuration int64 = 60

	DownloadRateLimitNum            = 10
	DownloadRateLimitDuration int64 = 60

	CriticalRateLimitNum            = env.Int("GLOBAL_CRITICAL_RATE_LIMIT", 20)
	CriticalRateLimitDuration int64 = 20 * 60

	ResetCriticalRateLimitNum            = env.Int("RESET_CRITICAL_RATE_LIMIT", 90)
	ResetCriticalRateLimitDuration int64 = 3 * 60
)

var RateLimitKeyExpirationDuration = 20 * time.Minute

var SystemStartTime = time.Now().Unix()

// LogErrorSamplingRate 错误日志采样率 (0-1之间的浮点数)
var LogErrorSamplingRate float64 = 1.0

// LogIgnoredErrorCodes 需要忽略的错误码列表，以逗号分隔
var LogIgnoredErrorCodes = ""

// NewRPMEnabled 是否启用新版RPM统计功能
// 基于Redis的新版RPM统计，提供更精确的请求统计
// 需要Redis支持，会占用一定的Redis内存
var NewRPMEnabled = false

// ChannelMetricsEnabled 是否启用渠道指标统计功能
// 开启后会收集渠道的成功率、响应时间等指标，用于评估渠道性能
// 关闭则不会收集这些数据，可减少系统资源占用
var ChannelMetricsEnabled = false

// ChannelScoreRoutingEnabled 是否启用基于渠道得分的智能路由
// 开启后系统会根据渠道的历史表现自动调整请求分配优先级
// 性能更好的渠道将获得更高的请求分配概率
var ChannelScoreRoutingEnabled = false

// AlipayFaceToFaceEnabled 是否启用支付宝当面付
var AlipayFaceToFaceEnabled = false

// AlipayAppId 支付宝应用ID
var AlipayAppId = ""

// AlipayPrivateKey 支付宝应用私钥
var AlipayPrivateKey = ""

// AlipayPublicKey 支付宝公钥
var AlipayPublicKey = ""

// AlipayCallbackAddress 支付宝回调地址
var AlipayCallbackAddress = ""

// SuixingpayEnabled 是否启用随行付聚合支付
var SuixingpayEnabled = false

// SuixingpayOrgId 随行付机构编号
var SuixingpayOrgId = ""

// SuixingpayPlatformPublicKey 随行付平台公钥
var SuixingpayPlatformPublicKey = ""

// SuixingpayMerchantPrivateKey 随行付商户私钥
var SuixingpayMerchantPrivateKey = ""

// SuixingpayMerchantNo 随行付商户编号
var SuixingpayMerchantNo = ""

// SuixingpayCallbackAddress 随行付回调地址
var SuixingpayCallbackAddress = ""

// GlobalIgnoreWeightCalculationEnabled 全局忽略权重计算，适合号池模式，节约CPU开销
var GlobalIgnoreWeightCalculationEnabled = false

// GlobalIgnorePriorityEnabled 全局忽略优先级排序，直接随机选择渠道，进一步节约CPU开销
var GlobalIgnorePriorityEnabled = false

// 全局渠道筛选开关
// GlobalIgnoreBillingTypeFilteringEnabled 全局忽略按量按次的计费方式筛选
var GlobalIgnoreBillingTypeFilteringEnabled = false

// GlobalIgnoreFunctionCallFilteringEnabled 全局忽略functionCall渠道筛选
var GlobalIgnoreFunctionCallFilteringEnabled = false

// GlobalIgnoreImageSupportFilteringEnabled 全局忽略支持image渠道筛选
var GlobalIgnoreImageSupportFilteringEnabled = false

// IgnoreImageErrorButRequestEnabled 忽略图片错误但依然将错误图片请求上游API的配置
// 该配置与RemoveImageDownloadErrorEnabled冲突时，RemoveImageDownloadErrorEnabled优先级更高
var IgnoreImageErrorButRequestEnabled = false

func init() {
	SystemStartTime = time.Now().Unix()

	if DemoEnabled {
		OnlineTopupEnabled = true
		QuotaExpireEnabled = true
		QuotaExpireDays = 30
		GuestQueryEnabled = true
		QqInfo = "**********"
		WechatInfo = "akl7777777"
	}

	if LicenseKey == "" {
		LicenseKey = "COMMON_KEY"
	}

	if Theme == "FluxDart" {
		SystemName = "OpenAI Next"
	}

	// 从环境变量获取API错误类型后缀，默认为"_api_error"
	if envSuffix := os.Getenv("API_ERROR_TYPE_SUFFIX"); envSuffix != "" {
		ApiErrorTypeSuffix = envSuffix
	}

	// 从环境变量获取自定义需要屏蔽的错误类型列表，用逗号分隔
	if envCustomTypes := os.Getenv("CUSTOM_HIDE_API_ERROR_TYPES"); envCustomTypes != "" {
		CustomHideApiErrorTypes = strings.Split(envCustomTypes, ",")
		// 去除可能的空格
		for i, errType := range CustomHideApiErrorTypes {
			CustomHideApiErrorTypes[i] = strings.TrimSpace(errType)
		}
	}

	// 日志输出当前API错误类型配置信息
	fmt.Printf("API错误类型后缀设置为: %s\n", ApiErrorTypeSuffix)
	if len(CustomHideApiErrorTypes) > 0 {
		fmt.Printf("自定义屏蔽的API错误类型: %s\n", strings.Join(CustomHideApiErrorTypes, ", "))
	}
}

// 获取mj url前缀
func GetMjUrlByTaskIdDefaultOriginUrl(taskId string, originUrl string) string {
	if MidjourneyCustomImageUrlEnabled {
		if strings.HasPrefix(originUrl, "https://cdn.discordapp.com") && MjDiscordCdnProxy != "" {
			return strings.Replace(originUrl, "https://cdn.discordapp.com", MjDiscordCdnProxy, 1)
		} else {
			return fmt.Sprintf("%s/mj/image/%s", MidjourneyCustomImageUrl, taskId)
		}
	} else {
		return originUrl
	}
}

var EnableMetric = env.Bool("ENABLE_METRIC", false)
var MetricQueueSize = env.Int("METRIC_QUEUE_SIZE", 10)
var MetricSuccessRateThreshold = env.Float64("METRIC_SUCCESS_RATE_THRESHOLD", 0.8)
var MetricSuccessChanSize = env.Int("METRIC_SUCCESS_CHAN_SIZE", 1024)
var MetricFailChanSize = env.Int("METRIC_FAIL_CHAN_SIZE", 128)

var InitialRootToken = os.Getenv("INITIAL_ROOT_TOKEN")

var InitialRootAccessToken = os.Getenv("INITIAL_ROOT_ACCESS_TOKEN")

// ReturnSensitiveConfig 是否返回敏感配置（本来以token/secret结尾的配置会被过滤掉）
var ReturnSensitiveConfig = env.Bool("RETURN_SENSITIVE_CONFIG", false)

var GeminiVersion = env.String("GEMINI_VERSION", "v1")

var OnlyOneLogFile = env.Bool("ONLY_ONE_LOG_FILE", false)

var RelayProxy = env.String("RELAY_PROXY", "")
var UserContentRequestProxy = env.String("USER_CONTENT_REQUEST_PROXY", "")
var UserContentRequestTimeout = env.Int("USER_CONTENT_REQUEST_TIMEOUT", 30)

var EnforceIncludeUsage = env.Bool("ENFORCE_INCLUDE_USAGE", false)
var TestPrompt = env.String("TEST_PROMPT", "Print your model name exactly and do not output without any other text.")

var DynamicRouterModelMap = map[string]float64{}

// 新增: 存储模型ID到允许使用的角色列表的映射
var DynamicRouterModelRoles = make(map[string][]string)

//var XYHelperDBEnabled bool = true
//var XYHelperDBDSN string = "root:kimitest@tcp(103.144.245.143:13306)/cool?charset=utf8mb4&parseTime=True&loc=Local"

var XYHelperDBEnabled = env.Bool("XY_HELPER_DB_ENABLED", false)
var XYHelperDBDSN = env.String("XY_HELPER_DB_DSN", "")

// RequestMaxCompatibilityEnabled 请求最大兼容模式开关
// 用于自动修复和规范化异常的请求参数，实现最大兼容性。
// 启用后会：
// 1. 修复空的工具类型(type)字段，自动设置为"function"
// 2. 过滤掉无效的工具配置(空name的function)
// 3. 处理消息内容中的特殊格式
// 4. 验证并移除无效的图片URL（仅支持http(s)和data URI格式）
// 5. 自动转换base64图片格式
// 6. 合并多字符串消息
// 7. 规范化消息格式（支持旧版API格式转换）
//
// 具体处理内容：
// 1. 自动将传统prompt字段转换为messages格式
// 2. 支持消息内容为字符串数组格式并自动合并
// 3. 处理带role属性的旧格式消息
// 4. 支持单个image_url对象自动转换为数组格式
// 5. 自动将普通文本转换为标准的text类型格式
// 6. 移除无效的tool_choice参数（当未指定tools时）
//
// 注意：启用此模式将会
// 1. 改变原有请求内容
// 2. 对每个请求进行额外的解析和判断
// 3. 增加CPU开销
// 4. 可能移除不符合规范的图片URL
//
// 建议仅在以下场景启用：
// 1. 需要兼容非标准客户端请求时
// 2. 使用旧版API的应用迁移时
// 3. 需要自动验证和清理图片URL的场景
var RequestMaxCompatibilityEnabled = false

// MaxTokenAutoDetectionEnabled 启用maxtoken自动检测和回退功能
var MaxTokenAutoDetectionEnabled = false

// ModelMaxTokensConfig 模型最大token限制配置（JSON格式）
var ModelMaxTokensConfig = ""

// modelMaxTokensMap 模型最大token限制映射表（使用sync.Map实现无锁并发安全）
var modelMaxTokensMap sync.Map

// LoadModelMaxTokensFromConfig 从配置中加载模型token限制
func LoadModelMaxTokensFromConfig() {
	// 清空现有配置
	modelMaxTokensMap = sync.Map{}

	if ModelMaxTokensConfig == "" {
		return // 如果配置为空，不加载任何默认值
	}

	var configMap map[string]int
	if err := json.Unmarshal([]byte(ModelMaxTokensConfig), &configMap); err != nil {
		fmt.Printf("Failed to parse ModelMaxTokensConfig: %v\n", err)
		return
	}

	// 只加载用户明确配置的模型token限制
	for model, maxTokens := range configMap {
		modelMaxTokensMap.Store(model, maxTokens)
	}
}

// GetModelMaxTokens 获取指定模型的最大token限制
func GetModelMaxTokens(modelName string) int {
	if !MaxTokenAutoDetectionEnabled {
		return 0 // 如果未启用自动检测，返回0表示不限制
	}

	// 只查找用户明确配置的模型（sync.Map无锁并发安全）
	if value, exists := modelMaxTokensMap.Load(modelName); exists {
		if maxTokens, ok := value.(int); ok {
			return maxTokens
		}
	}

	// 如果没有找到配置，返回0表示不进行限制覆盖
	return 0
}

// GetAllModelMaxTokens 获取所有模型的token限制配置（用于管理界面显示）
func GetAllModelMaxTokens() map[string]int {
	result := make(map[string]int)

	// 使用sync.Map的Range方法遍历（无锁并发安全）
	modelMaxTokensMap.Range(func(key, value interface{}) bool {
		if modelName, ok := key.(string); ok {
			if maxTokens, ok := value.(int); ok {
				result[modelName] = maxTokens
			}
		}
		return true // 继续遍历
	})

	return result
}

// PreferOptimizerQueryEnabled 优先使用优化器查询
var PreferOptimizerQueryEnabled = false

// TrustUpstreamStreamUsageEnabled 是否完全信任上游流式返回的用量统计
// 开启后将完全信任上游流式返回的用量统计，关闭则重新计算
// 注意：非流式模式默认信任上游返回，如需重新计算请在特定渠道中设置
var TrustUpstreamStreamUsageEnabled = true

// ForceStreamOptionEnabled 是否强制要求上游使用流式返回
// 开启后将强制要求上游在流式响应中返回用量统计信息，可能会增加响应延迟
var ForceStreamOptionEnabled = true

// ForceDownstreamStreamUsageEnabled 是否强制在流式响应中返回用量统计给下游
var ForceDownstreamStreamUsageEnabled = false

// 数据看板
var DataExportEnabled = true              // 控制后台是否定时刷新数据
var DataExportInterval = 5                // 数据刷新间隔，单位：分钟
var DataExportDefaultTime = "hour"        // 默认时间粒度
var DataExportDisplayEnabled = false      // 控制前端是否显示数据看板功能
var IgnoreChannelDimensionEnabled = false // 统计时忽略渠道维度，减少统计表数据量

// Channel No SQL Configuration - 渠道NoSQL配置
var ChannelNoSQLEnabled = env.Bool("CHANNEL_NOSQL_ENABLED", false)
var ChannelNoSQLType = env.String("CHANNEL_NOSQL_TYPE", "mongodb")
var ChannelNoSQLConnectionString = env.String("CHANNEL_NOSQL_CONNECTION_STRING", "mongodb://localhost:27017")
var ChannelNoSQLDatabase = env.String("CHANNEL_NOSQL_DATABASE", "shell_api_channels")
var ChannelMigrationEnabled = env.Bool("CHANNEL_MIGRATION_ENABLED", true)

// Log Storage Configuration - 日志存储配置
var LogStorageType = env.String("LOG_STORAGE_TYPE", "mysql")                         // mysql, clickhouse, elasticsearch
var LogStorageConnectionString = env.String("LOG_STORAGE_CONNECTION_STRING", "")     // 日志存储连接字符串
var LogStorageDatabase = env.String("LOG_STORAGE_DATABASE", "shell_api_logs")        // 日志存储数据库名
var LogStorageTable = env.String("LOG_STORAGE_TABLE", "logs")                        // 日志存储表名
var LogStorageBatchSize = env.Int("LOG_STORAGE_BATCH_SIZE", 1000)                    // 批量写入大小
var LogStorageFlushInterval = env.Int("LOG_STORAGE_FLUSH_INTERVAL", 5)               // 刷新间隔(秒)
var LogStorageMaxRetries = env.Int("LOG_STORAGE_MAX_RETRIES", 3)                     // 最大重试次数
var LogStorageEnabled = env.Bool("LOG_STORAGE_ENABLED", false)                       // 是否启用新的日志存储系统
var LogStorageFallbackToMySQL = env.Bool("LOG_STORAGE_FALLBACK_TO_MYSQL", false)     // 是否在新存储失败时回退到MySQL
var LogStorageAsyncWrite = env.Bool("LOG_STORAGE_ASYNC_WRITE", false)                // 是否启用异步写入
var LogStorageCompressionEnabled = env.Bool("LOG_STORAGE_COMPRESSION_ENABLED", true) // 是否启用压缩(适用于ClickHouse)

// ClickHouse specific configuration
var ClickHouseHost = env.String("CLICKHOUSE_HOST", "localhost")
var ClickHousePort = env.Int("CLICKHOUSE_PORT", 9000)
var ClickHouseUsername = env.String("CLICKHOUSE_USERNAME", "default")
var ClickHousePassword = env.String("CLICKHOUSE_PASSWORD", "")
var ClickHouseDatabase = env.String("CLICKHOUSE_DATABASE", "shell_api_logs")
var ClickHouseCluster = env.String("CLICKHOUSE_CLUSTER", "") // 集群名称，为空则使用单机模式

// Elasticsearch specific configuration
var ElasticsearchHosts = env.String("ELASTICSEARCH_HOSTS", "http://localhost:9200") // 多个host用逗号分隔
var ElasticsearchUsername = env.String("ELASTICSEARCH_USERNAME", "")
var ElasticsearchPassword = env.String("ELASTICSEARCH_PASSWORD", "")
var ElasticsearchIndex = env.String("ELASTICSEARCH_INDEX", "shell-api-logs")                          // 主日志索引名
var ElasticsearchExtendIndex = env.String("ELASTICSEARCH_EXTEND_INDEX", "")                           // 日志扩展索引名，为空时使用默认格式
var ElasticsearchExtendIndexDateFormat = env.String("ELASTICSEARCH_EXTEND_INDEX_DATE_FORMAT", "none") // 扩展索引日期格式，支持：none(不使用日期)、2006-01(按月)、2006-01-02(按日)
var ElasticsearchShards = env.Int("ELASTICSEARCH_SHARDS", 3)                                          // 分片数
var ElasticsearchReplicas = env.Int("ELASTICSEARCH_REPLICAS", 1)                                      // 副本数
var ElasticsearchRefreshInterval = env.String("ELASTICSEARCH_REFRESH_INTERVAL", "5s")                 // 刷新间隔

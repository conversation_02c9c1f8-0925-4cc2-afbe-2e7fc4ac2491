package common

import (
	"sync"
	"time"
)

type SMSVerificationValue struct {
	code string
	time time.Time
}

const (
	SMSVerificationPurpose = "sv" //仅可用于注册短信验证码，因为如果手机存在，验证码不会被发送
	SMSLoginPurpose        = "sl"
	SMSBindPurpose         = "sb"
)

var smsVerificationMutex sync.Mutex
var smsVerificationMap map[string]SMSVerificationValue
var smsVerificationMapMaxSize = 100 // 假设短信验证码的最大存储量更大
var SMSVerificationValidMinutes = 5 // 短信验证码的有效时间通常比电子邮件验证码短

// 初始化函数
func init() {
	smsVerificationMutex.Lock()
	defer smsVerificationMutex.Unlock()
	smsVerificationMap = make(map[string]SMSVerificationValue)
}

// RegisterSMSVerificationCode 注册短信验证码
func RegisterSMSVerificationCode(phoneNumber string, code string, purpose string) {
	smsVerificationMutex.Lock()
	defer smsVerificationMutex.Unlock()
	smsVerificationMap[purpose+phoneNumber] = SMSVerificationValue{
		code: code,
		time: time.Now(),
	}
	// 如果短信验证码的存储量超过了最大值,移除过期的短信验证码
	if len(smsVerificationMap) > smsVerificationMapMaxSize {
		removeExpiredSMSPairs()
	}
}

// VerifySMSCodeWithKey 验证短信验证码
func VerifySMSCodeWithKey(key string, code string, purpose string) bool {
	smsVerificationMutex.Lock()
	defer smsVerificationMutex.Unlock()
	value, okay := smsVerificationMap[purpose+key]
	now := time.Now()
	if !okay || int(now.Sub(value.time).Seconds()) >= SMSVerificationValidMinutes*60 {
		return false
	}
	return code == value.code
}

// DeleteSMSCode 删除短信验证码
func DeleteSMSCode(phoneNumber string) {
	smsVerificationMutex.Lock()
	defer smsVerificationMutex.Unlock()
	delete(smsVerificationMap, phoneNumber)
}

// removeExpiredSMSPairs 移除过期的短信验证码
func removeExpiredSMSPairs() {
	now := time.Now()
	for key := range smsVerificationMap {
		if int(now.Sub(smsVerificationMap[key].time).Seconds()) >= SMSVerificationValidMinutes*60 {
			delete(smsVerificationMap, key)
		}
	}
}

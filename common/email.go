package common

// ProxyEmail 代理邮件结构体
type ProxyEmailRequest struct {
	SystemName  string `json:"system_name"`
	Email       string `json:"email"`
	Subject     string `json:"subject"`
	Content     string `json:"content"`
	SMTPServer  string `json:"smtp_server"`
	SMTPPort    int    `json:"smtp_port"`
	SMTPAccount string `json:"smtp_account"`
	SMTPFrom    string `json:"smtp_from"`
	SMTPToken   string `json:"smtp_token"`
}

package ctxkey

const (
	Config                   = "config"
	UseChannel               = "use_channel"
	Id                       = "id"
	Username                 = "username"
	Role                     = "role"
	UserQuota                = "user_quota"
	QuotaExpireTime          = "quota_expire_time"
	Status                   = "status"
	Channel                  = "channel"
	ChannelId                = "channel_id"
	SpecificChannelId        = "specific_channel_id"
	RequestModel             = "request_model"
	ConvertedRequest         = "converted_request"
	OriginalModel            = "original_model"
	ExcludedFields           = "excluded_fields"
	ExcludedResponseFields   = "excluded_response_fields"
	ExtraFields              = "extra_fields"
	Group                    = "group"
	ModelMapping             = "model_mapping"
	Base64ImagePrefixMapping = "base64_image_prefix_mapping"
	ModelMappingArr          = "model_mapping_arr"
	ChannelName              = "channel_name"
	RetryInterval            = "retryInterval"
	UndeadModeEnabled        = "undeadModeEnabled"
	TokenBillingType         = "token_billing_type"
	ModelName                = "model_name"
	BillingType              = "billing_type"
	FunctionCallEnabled      = "function_call_enabled"
	ImageSupported           = "image_supported"
	InputHasFunctionCall     = "input_has_function_call"
	InputHasImage            = "input_has_image"
	TokenId                  = "token_id"
	TokenName                = "token_name"
	TokenKey                 = "token_key"
	TokenGroup               = "token_group"
	BaseURL                  = "base_url"
	AvailableModels          = "available_models"
	KeyRequestBody           = "key_request_body"
	SystemPrompt             = "system_prompt"
	Timezone                 = "timezone"
	ErrorCode                = "error_code"
	ShouldIncludeUsage       = "should_include_usage"
	FirstStartTime           = "first_start_time"
	RetryTimestamps          = "retry_timestamps"
	IsV1MessagesPath         = "is_v1_messages_path"
	IsResponsesPath          = "is_responses_path"
	PromptTokens             = "prompt_tokens"
	FailedDuration           = "failed_duration"
)

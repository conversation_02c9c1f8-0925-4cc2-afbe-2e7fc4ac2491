package helper_test

import (
	"fmt"
	"github.com/songquanpeng/one-api/common/file_reader"
	_ "golang.org/x/image/webp"
	_ "image/gif"
	_ "image/jpeg"
	_ "image/png"
	"testing"
)

func TestExtractTextFromURL(t *testing.T) {
	// 使用现成的 URL 进行测试
	//url := "https://filesystem.shellgpt.top/fileSystem/download/20240312/7bcc98c5-cc25-4f0d-91d2-28c4be5b8613.txt"
	url := "https://filesystem.site/cdn/20240312/x7c74y3g99ziQvOKq82GCzTbSOV4l0.pdf"

	// 调用 extractTextFromURL 函数
	text, err := file_reader.ExtractTextFromURL(url)
	if err != nil {
		t.Fatal(err)
	}

	fmt.Println(text)
	// 检查提取的文本是否包含期望的内容
	//expectedText := "This is a sample PDF file."
	//if !strings.Contains(text, expectedText) {
	//	t.<PERSON><PERSON><PERSON>("Expected extracted text to contain %q, but got %q", expectedText, text)
	//}
}

package helper

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	cryptoRand "crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"html/template"
	"image/color"
	"io"
	"log"
	"math/rand"
	"net"
	"os/exec"
	"reflect"
	"regexp"
	"runtime"
	"runtime/debug"
	"strconv"
	"strings"

	"github.com/songquanpeng/one-api/common/logger"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/random"
	"golang.org/x/crypto/pbkdf2"

	"time"
	"unsafe"
)

const SimpleUrlRegexp = `(?i)\b((?:https?:\/\/)[^\s()]+)`

const urlRegex = `((https?|ftp):\/\/)?[\w.%-]+\.[a-zA-Z]{2,}(:\d+)?(\/[\w.%-]*)*`

func OpenBrowser(url string) {
	var err error

	switch runtime.GOOS {
	case "linux":
		err = exec.Command("xdg-open", url).Start()
	case "windows":
		err = exec.Command("rundll32", "url.dll,FileProtocolHandler", url).Start()
	case "darwin":
		err = exec.Command("open", url).Start()
	}
	if err != nil {
		log.Println(err)
	}
}

func GetClientRealIp(c *gin.Context) string {
	if c.GetHeader("CF-Connecting-IP") != "" {
		return c.GetHeader("CF-Connecting-IP")
	} else if c.GetHeader("X-Forwarded-For") != "" {
		ips := c.GetHeader("X-Forwarded-For")
		ipList := strings.Split(ips, ",")
		if len(ipList) > 0 {
			return ipList[0]
		}
		return c.ClientIP()
	} else if c.GetHeader("X-Real-Ip") != "" {
		return c.GetHeader("X-Real-Ip")
	} else {
		return c.ClientIP()
	}
}

// GetRequestScheme 获取请求的协议（http或https）
// 支持反向代理环境下的协议检测
func GetRequestScheme(c *gin.Context) string {
	scheme := "http"

	// 检查TLS连接
	if c.Request.TLS != nil {
		scheme = "https"
	}

	// 检查反向代理头部（如Nginx、Cloudflare等）
	if proto := c.GetHeader("X-Forwarded-Proto"); proto == "https" {
		scheme = "https"
	}

	// 检查其他常见的HTTPS头部
	if c.GetHeader("X-Forwarded-Ssl") == "on" || c.GetHeader("X-Forwarded-Scheme") == "https" {
		scheme = "https"
	}

	return scheme
}

func GetIp() (ip string) {
	ips, err := net.InterfaceAddrs()
	if err != nil {
		log.Println(err)
		return ip
	}

	for _, a := range ips {
		if ipNet, ok := a.(*net.IPNet); ok && !ipNet.IP.IsLoopback() {
			if ipNet.IP.To4() != nil {
				ip = ipNet.IP.String()
				if strings.HasPrefix(ip, "10") {
					return
				}
				if strings.HasPrefix(ip, "172") {
					return
				}
				if strings.HasPrefix(ip, "192.168") {
					return
				}
				ip = ""
			}
		}
	}
	return
}

var sizeKB = 1024
var sizeMB = sizeKB * 1024
var sizeGB = sizeMB * 1024

func Bytes2Size(num int64) string {
	numStr := ""
	unit := "B"
	if num/int64(sizeGB) > 1 {
		numStr = fmt.Sprintf("%.2f", float64(num)/float64(sizeGB))
		unit = "GB"
	} else if num/int64(sizeMB) > 1 {
		numStr = fmt.Sprintf("%d", int(float64(num)/float64(sizeMB)))
		unit = "MB"
	} else if num/int64(sizeKB) > 1 {
		numStr = fmt.Sprintf("%d", int(float64(num)/float64(sizeKB)))
		unit = "KB"
	} else {
		numStr = fmt.Sprintf("%d", num)
	}
	return numStr + " " + unit
}

func Seconds2Time(num int) (time string) {
	if num/31104000 > 0 {
		time += strconv.Itoa(num/31104000) + " 年 "
		num %= 31104000
	}
	if num/2592000 > 0 {
		time += strconv.Itoa(num/2592000) + " 个月 "
		num %= 2592000
	}
	if num/86400 > 0 {
		time += strconv.Itoa(num/86400) + " 天 "
		num %= 86400
	}
	if num/3600 > 0 {
		time += strconv.Itoa(num/3600) + " 小时 "
		num %= 3600
	}
	if num/60 > 0 {
		time += strconv.Itoa(num/60) + " 分钟 "
		num %= 60
	}
	time += strconv.Itoa(num) + " 秒"
	return
}

func Interface2String(inter interface{}) string {
	switch inter := inter.(type) {
	case string:
		return inter
	case int:
		return fmt.Sprintf("%d", inter)
	case float64:
		return fmt.Sprintf("%f", inter)
	}
	return "Not Implemented"
}

func UnescapeHTML(x string) interface{} {
	return template.HTML(x)
}

func IntMax(a int, b int) int {
	if a >= b {
		return a
	} else {
		return b
	}
}

func GetUUID() string {
	code := uuid.New().String()
	code = strings.Replace(code, "-", "", -1)
	return code
}

const keyChars = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
const keyNumbers = "0123456789"

// 纯数字
const numberKeyChars = "0123456789"

func init() {
	if !strings.Contains(runtime.Version(), "go1.2") { // go1.20之前版本需要全局 seed，其他插件无需再 seed
		rand.Seed(time.Now().UnixNano()) //nolint: staticcheck
	}
}

func GenerateKey() string {
	key := make([]byte, 48)
	for i := 0; i < 16; i++ {
		key[i] = keyChars[rand.Intn(len(keyChars))]
	}
	uuid_ := GetUUID()
	for i := 0; i < 32; i++ {
		c := uuid_[i]
		if i%2 == 0 && c >= 'a' && c <= 'z' {
			c = c - 'a' + 'A'
		}
		key[i+16] = c
	}
	return string(key)
}

func GetRandomString(length int) string {
	key := make([]byte, length)
	for i := 0; i < length; i++ {
		key[i] = keyChars[rand.Intn(len(keyChars))]
	}
	return string(key)
}

func GetRandomNumberString(length int) string {
	key := make([]byte, length)
	for i := 0; i < length; i++ {
		key[i] = keyNumbers[rand.Intn(len(keyNumbers))]
	}
	return string(key)
}

// RandomRGB 生成一个随机的 RGB 颜色
func RandomRGB() (r, g, b int) {
	r = rand.Intn(256)
	g = rand.Intn(256)
	b = rand.Intn(256)
	return
}

// RandomRGBA 生成一个随机的 RGBA 颜色
func RandomRGBA() color.RGBA {
	return color.RGBA{
		R: uint8(rand.Intn(256)),
		G: uint8(rand.Intn(256)),
		B: uint8(rand.Intn(256)),
		A: uint8(rand.Intn(256)), // 随机透明度
	}
}

func GenRequestID() string {
	return GetTimeString() + GetRandomNumberString(8)
}

func SetRequestID(ctx context.Context, id string) context.Context {
	return context.WithValue(ctx, RequestIdKey, id)
}

func GetRequestID(ctx context.Context) string {
	rawRequestId := ctx.Value(RequestIdKey)
	if rawRequestId == nil {
		return ""
	}
	return rawRequestId.(string)
}

func GetResponseID(c *gin.Context) string {
	logID := c.GetString(RequestIdKey)
	return fmt.Sprintf("chatcmpl-%s", logID)
}

func Max(a int, b int) int {
	if a >= b {
		return a
	} else {
		return b
	}
}

func AssignOrDefault(value string, defaultValue string) string {
	if len(value) != 0 {
		return value
	}
	return defaultValue
}

func MessageWithRequestId(message string, id string) string {
	return fmt.Sprintf("%s (request id: %s)", message, id)
}

func String2Int(str string) int {
	num, err := strconv.Atoi(str)
	if err != nil {
		return 0
	}
	return num
}

// []byte only read, panic on append
func StringToByteSlice(s string) []byte {
	tmp1 := (*[2]uintptr)(unsafe.Pointer(&s))
	tmp2 := [3]uintptr{tmp1[0], tmp1[1], tmp1[1]}
	return *(*[]byte)(unsafe.Pointer(&tmp2))
}

// GenerateAesKey 从一个简单的密码生成AES-256密钥
func GenerateAesKey(password string, keyLength int) ([]byte, error) {
	salt := make([]byte, 8) // 通常盐值应该保存起来以便以后解密时使用
	_, err := io.ReadFull(cryptoRand.Reader, salt)
	if err != nil {
		return nil, err
	}
	return pbkdf2.Key([]byte(password), salt, 4096, keyLength, sha256.New), nil
}

// GenerateAesKeyBySalt 从一个简单的密码生成AES-256密钥
func GenerateAesKeyBySalt(password string, keyLength int, salt []byte) ([]byte, error) {
	return pbkdf2.Key([]byte(password), salt, 4096, keyLength, sha256.New), nil
}

// EncryptString 使用给定的密钥加密字符串
func EncryptString(key []byte, text string) (string, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	plaintext := []byte(text)
	cfb := cipher.NewCFBEncrypter(block, key[:aes.BlockSize])
	ciphertext := make([]byte, len(plaintext))
	cfb.XORKeyStream(ciphertext, plaintext)

	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// DecryptString 使用给定的密钥解密字符串
func DecryptString(key []byte, cryptoText string) (string, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	ciphertext, _ := base64.StdEncoding.DecodeString(cryptoText)
	cfb := cipher.NewCFBDecrypter(block, key[:aes.BlockSize])
	plaintext := make([]byte, len(ciphertext))
	cfb.XORKeyStream(plaintext, ciphertext)

	return string(plaintext), nil
}

func GenerateSalts(id, key string, timestamp int64) (string, string, string) {
	// 假设 common.GetTimestamp() 返回的是秒级时间戳
	currentTime := time.Unix(timestamp, 0).UTC()
	yesterdayTime := currentTime.AddDate(0, 0, -1)
	tomorrowTime := currentTime.AddDate(0, 0, 1)

	// 转换为日期字符串 (YYYYMMDD)
	currentDate := currentTime.Format("20060102")
	yesterdayDate := yesterdayTime.Format("20060102")
	tomorrowDate := tomorrowTime.Format("20060102")

	if len(key) < 8 {
		key = key + "12345678"
	}
	// 生成盐值
	saltCurrent := id[len(id)-8:] + key[len(key)-8:] + currentDate
	saltYesterday := id[len(id)-8:] + key[len(key)-8:] + yesterdayDate
	saltTomorrow := id[len(id)-8:] + key[len(key)-8:] + tomorrowDate

	return saltCurrent, saltYesterday, saltTomorrow
}

// deepCopy 创建并返回一个 map 的深拷贝
func DeepCopy(originalMap map[string]float64) map[string]float64 {
	newMap := make(map[string]float64)
	for key, value := range originalMap {
		newMap[key] = value
	}
	return newMap
}

func ParseIPorCIDR(input string) (*net.IP, *net.IPNet, error) {
	ip, ipNet, err := net.ParseCIDR(input)
	if err != nil {
		ip = net.ParseIP(input)
		if ip == nil {
			return nil, nil, err
		}
		if ip.To4() != nil {
			ipNet = &net.IPNet{
				IP:   ip,
				Mask: net.CIDRMask(32, 32),
			}
		} else {
			ipNet = &net.IPNet{
				IP:   ip,
				Mask: net.CIDRMask(128, 128),
			}
		}
	}
	return &ip, ipNet, nil
}

func ParseIPList(input string) ([]*net.IP, []*net.IPNet, error) {
	items := strings.Split(input, ",")
	ips := make([]*net.IP, 0, len(items))
	ipNets := make([]*net.IPNet, 0, len(items))
	for _, item := range items {
		ip, ipNet, err := ParseIPorCIDR(strings.TrimSpace(item))
		if err != nil {
			return nil, nil, err
		}
		ips = append(ips, ip)
		ipNets = append(ipNets, ipNet)
	}
	return ips, ipNets, nil
}

func SafeGoroutine(f func()) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Println(r)
				logger.SysError(fmt.Sprintf("Fatal error Occurred: %v\nStack trace:\n%s", r, debug.Stack()))
			}
		}()
		f()
	}()
}

// GetJsonString 方法接受任意类型的参数，并尝试将其转换为JSON字符串
func GetJsonString(data interface{}) string {
	jsonString, err := json.Marshal(data)
	if err != nil {
		log.Fatalf("Error occurred during marshaling. Error: %s", err.Error())
	}
	return string(jsonString)
}

// GetTodayZeroUnixTime 返回当天零点的Unix时间戳
func GetTodayZeroUnixTime() int64 {
	currentTime := time.Now()
	year, month, day := currentTime.Date()
	location := currentTime.Location()

	zeroTime := time.Date(year, month, day, 0, 0, 0, 0, location)
	return zeroTime.Unix() // 获取Unix时间戳
}

// 两个结构体属性合并的方法
//func MergeStructs(dst, src interface{}, ignoreFields ...string) {
//	dstVal := reflect.ValueOf(dst).Elem()
//	srcVal := reflect.ValueOf(src).Elem()
//	for i := 0; i < srcVal.NumField(); i++ {
//		srcField := srcVal.Type().Field(i)
//		if !srcField.Anonymous {
//			if !strings.Contains(strings.Join(ignoreFields, ","), srcField.Name) {
//				dstField := dstVal.FieldByName(srcField.Name)
//				if dstField.IsValid() {
//					dstField.Set(srcVal.Field(i))
//				}
//			}
//		}
//	}
//}

// 把2个结构的属性写入到一个新的JSON当中
func MergeStructsToMap(s1, s2 interface{}, ignoreFields ...string) (map[string]interface{}, error) {
	s1Val := reflect.ValueOf(s1)
	s2Val := reflect.ValueOf(s2)

	// 验证 s1 和 s2 是否为有效的结构体
	if s1Val.Kind() == reflect.Ptr && s1Val.IsNil() {
		if s2Val.Kind() == reflect.Ptr && s2Val.IsNil() {
			// Both s1 and s2 are nil
			return nil, nil
		} else if s2Val.Kind() != reflect.Ptr || s2Val.Elem().Kind() != reflect.Struct {
			// s2 is not a pointer to a struct
			return nil, errors.New("s2 is not a pointer to a struct")
		} else {
			// s1 is nil, return s2
			s2Val = s2Val.Elem()
			result := make(map[string]interface{})
			for i := 0; i < s2Val.NumField(); i++ {
				jsonTag := s2Val.Type().Field(i).Tag.Get("json")
				result[jsonTag] = s2Val.Field(i).Interface()
			}
			return result, nil
		}
	}

	if s2Val.Kind() == reflect.Ptr && s2Val.IsNil() {
		if s1Val.Kind() != reflect.Ptr || s1Val.Elem().Kind() != reflect.Struct {
			// s1 is not a pointer to a struct
			return nil, errors.New("s1 is not a pointer to a struct")
		} else {
			// s2 is nil, return s1
			s1Val = s1Val.Elem()
			result := make(map[string]interface{})
			for i := 0; i < s1Val.NumField(); i++ {
				jsonTag := s1Val.Type().Field(i).Tag.Get("json")
				result[jsonTag] = s1Val.Field(i).Interface()
			}
			return result, nil
		}
	}

	if s1Val.Kind() != reflect.Ptr || s1Val.Elem().Kind() != reflect.Struct {
		return nil, errors.New("s1 is not a pointer to a struct")
	}
	if s2Val.Kind() != reflect.Ptr || s2Val.Elem().Kind() != reflect.Struct {
		return nil, errors.New("s2 is not a pointer to a struct")
	}

	s1Val = s1Val.Elem()
	s2Val = s2Val.Elem()

	result := make(map[string]interface{})

	for i := 0; i < s1Val.NumField(); i++ {
		jsonTag := s1Val.Type().Field(i).Tag.Get("json")
		result[jsonTag] = s1Val.Field(i).Interface()
	}

ignoreLoop:
	for i := 0; i < s2Val.NumField(); i++ {
		jsonTag := s2Val.Type().Field(i).Tag.Get("json")
		for _, ignoreField := range ignoreFields {
			if jsonTag == ignoreField {
				continue ignoreLoop
			}
		}
		result[jsonTag] = s2Val.Field(i).Interface()
	}

	return result, nil
}

// GetRandomNumber 生成一个指定范围内的随机数
func GetRandomNumber(min int64, max int64) int64 {
	if min == max {
		return min
	}
	if min > max {
		min, max = max, min
	}
	return rand.Int63n(max-min) + min
}

// GenerateUUID 生成UUID
func GenerateUUID() string {
	return uuid.New().String()
}

func IsPhoneNumber(phoneNumber string) bool {
	if len(phoneNumber) != 11 {
		return false
	}
	if phoneNumber[0] != '1' {
		return false
	}
	for _, c := range phoneNumber {
		if c < '0' || c > '9' {
			return false
		}
	}
	return true
}

func HasSimpleUrl(url string) bool {
	// 编译正则表达式
	re, errReg := regexp.Compile(SimpleUrlRegexp)
	if errReg != nil {
		fmt.Println(fmt.Sprintf("errReg==>failed to compile regex: %s", errReg.Error()))
		return false
	}
	matchedUrl := re.MatchString(url)
	return matchedUrl
}

func HasUrl(url string) bool {
	if len(url) < 8 {
		return false
	}
	// 支持http, https, ftp和任意域名的URL正则表达式
	urlRegex := `((https?|ftp):\/\/)?[\w.-]+\.[a-zA-Z]{2,}(:\d+)?(\/[\w.-]*)*`
	// 编译正则表达式
	re, errReg := regexp.Compile(urlRegex)
	if errReg != nil {
		fmt.Println(fmt.Sprintf("errReg==>failed to compile regex: %s", errReg.Error()))
		return false
	}
	matchedUrl := re.MatchString(url)
	// 查找所有匹配的内容
	urls := re.FindAllString(url, -1)

	isAllFile := true
	// 遍历
	for _, u := range urls {
		if !IsFile(u) {
			isAllFile = false
			break
		}
	}
	if !isAllFile && matchedUrl {
		return true
	}
	return false
}

func IsFile(str string) bool {
	var matchedFile bool
	// 校验[\w-]+\.\w+
	fileRegex := `^[\w-]+\.\w+$`
	fileRegexRe, errFileRe := regexp.Compile(fileRegex)

	if errFileRe != nil {
		fmt.Println(fmt.Sprintf("errFileRe==>failed to compile regex: %s", errFileRe.Error()))
		return false
	}
	if fileRegexRe.MatchString(str) {
		matchedFile = true
	}
	return matchedFile
}

func ReverseString(s string) string {
	runes := []rune(s)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}

func PadZeros(s string, length int) string {
	for len(s) < length {
		s = "0" + s
	}
	return s
}

// MaskMiddle masks the middle part of a string with the specified character,
// keeping the specified number of characters at the beginning and end.
// If the string is too short, it will still be masked with at least one character.
func MaskMiddle(s string, prefixLen, suffixLen int, maskChar rune) string {
	if prefixLen < 0 || suffixLen < 0 {
		return ""
	}

	if len(s) == 0 {
		return string(maskChar)
	}

	if prefixLen+suffixLen >= len(s) {
		return strings.Repeat(string(maskChar), len(s))
	}

	prefix := s[:prefixLen]
	suffix := s[len(s)-suffixLen:]
	middle := strings.Repeat(string(maskChar), len(s)-prefixLen-suffixLen)

	return prefix + middle + suffix
}

func GenerateFakeCompletionIdProMax(modelName string) string {
	if strings.Contains(modelName, "embedding") {
		return ""
	} else if strings.Contains(modelName, "claude") {
		// claude
		if config.TempClaudeChatCompletionID == "" || !strings.HasPrefix(config.TempClaudeChatCompletionID, "msg_") {
			return fmt.Sprintf("msg_%s", random.GetRandomString(34))
		} else {
			if len(config.TempClaudeChatCompletionID) < 8 {
				return fmt.Sprintf("msg_%s", random.GetRandomString(34))
			} else {
				return fmt.Sprintf("%s%s", config.TempClaudeChatCompletionID[:8], random.GetRandomString(26))
			}
		}
	} else {
		if config.TempOpenAIChatCompletionID == "" || !strings.HasPrefix(config.TempOpenAIChatCompletionID, "chatcmpl-") {
			return fmt.Sprintf("chatcmpl-%s", random.GetRandomString(29))
		} else {
			if len(config.TempOpenAIChatCompletionID) < 13 {
				return fmt.Sprintf("chatcmpl-%s", random.GetRandomString(29))
			} else {
				return fmt.Sprintf("%s%s", config.TempOpenAIChatCompletionID[:13], random.GetRandomString(25))
			}
		}
	}
}

// 获取请求类型
func GetRequestType(requestPath string, modelName string) string {
	if strings.HasPrefix(requestPath, "/v1/chat/completions") || strings.HasPrefix(requestPath, "/v1/completions") || strings.HasPrefix(requestPath, "/v1/messages") {
		return "text"
	} else if strings.HasPrefix(requestPath, "/v1/images/generations") || strings.HasPrefix(requestPath, "/v1/images/edits") || strings.HasPrefix(requestPath, "/v1/images/variations") {
		return "image"
	} else if strings.HasPrefix(requestPath, "/v1/audio/transcriptions") || strings.HasPrefix(requestPath, "/v1/audio/translations") || strings.HasPrefix(requestPath, "/v1/audio/speech") {
		return "audio"
	} else if strings.HasPrefix(requestPath, "/v1/embeddings") || strings.HasSuffix(requestPath, "embeddings") {
		return "embedding"
	} else if strings.HasPrefix(requestPath, "/v1/moderations") {
		return "moderation"
	} else if strings.HasPrefix(requestPath, "/v1/realtime") {
		return "realtime"
	} else if strings.HasPrefix(requestPath, "/mj/") {
		return "midjourney"
	} else if strings.HasPrefix(requestPath, "/search/") {
		return "search"
	} else if strings.HasPrefix(requestPath, "/fish/") {
		return "fish_audio"
	} else if strings.Contains(requestPath, "/oneapi/proxy/") {
		return "proxy"
	}

	// 基于模型名称的推断
	if modelName != "" {
		modelLower := strings.ToLower(modelName)
		if strings.Contains(modelLower, "dall-e") || strings.Contains(modelLower, "midjourney") || strings.Contains(modelLower, "stable-diffusion") {
			return "image"
		} else if strings.Contains(modelLower, "whisper") || strings.Contains(modelLower, "tts") || strings.Contains(modelLower, "audio") {
			return "audio"
		} else if strings.Contains(modelLower, "embedding") || strings.Contains(modelLower, "embed") {
			return "embedding"
		} else if strings.Contains(modelLower, "moderation") {
			return "moderation"
		}
	}

	// 默认返回text类型
	return "text"
}

// 构造log的other字段,入参是use_channel字符串类型的切片,completionRatio float64, frt int, groupRatio float64, modelRatio float64,
// 出参是 {"admin_info":{"use_channel":["1"],"retry_durations":[{"channel":1,"start_time":123,"end_time":456,"duration":333}]},"completion_ratio":3,"frt":-1000,"group_ratio":1,"model_ratio":0.25,"request_type":"text","usage":{"prompt_tokens":10,"completion_tokens":20}}
func ConstructLogOther(useChannel []int, completionRatio float64, adminUserID int, groupRatio float64,
	modelRatio float64, topupConvertRatio float64, userDiscountRatio float64, retryDurations []map[string]int64, requestType string, billingType int, usage interface{}) string {
	return ConstructLogOtherWithBilling(useChannel, completionRatio, adminUserID, groupRatio, modelRatio, topupConvertRatio, userDiscountRatio, retryDurations, requestType, billingType, usage, nil)
}

// ConstructLogOtherWithBilling 构造包含计费明细的log other字段
func ConstructLogOtherWithBilling(useChannel []int, completionRatio float64, adminUserID int, groupRatio float64,
	modelRatio float64, topupConvertRatio float64, userDiscountRatio float64, retryDurations []map[string]int64, requestType string, billingType int, usage interface{}, billingDetail interface{}) string {
	other := make(map[string]interface{})
	other["admin_info"] = map[string]interface{}{
		"use_channel":     useChannel,
		"retry_durations": retryDurations,
		"admin_user_id":   adminUserID,
	}

	// 根据计费类型设置不同的信息
	switch billingType {
	case 2: // common.BillingTypeByCount
		other["billing_type"] = "by_count"
	case 1: // common.BillingTypeByQuota
		other["billing_type"] = "by_quota"
	case 3: // common.BillingTypeMixed
		other["billing_type"] = "mixed"
	case 4: // common.BillingTypeByQuotaFirst
		other["billing_type"] = "by_quota_first"
	case 5: // common.BillingTypeByCountFirst
		other["billing_type"] = "by_count_first"
	default:
		other["billing_type"] = "by_quota" // 默认按量计费
	}

	other["completion_ratio"] = completionRatio
	other["group_ratio"] = groupRatio
	other["model_ratio"] = modelRatio
	other["topup_convert_ratio"] = topupConvertRatio
	other["user_discount"] = userDiscountRatio
	other["request_type"] = requestType

	// 添加usage信息
	if usage != nil {
		other["usage"] = usage

		// 处理cache相关的详细计费信息
		var cacheCreationTokens, cacheReadTokens int
		var hasCacheTokens bool

		// 尝试从不同类型的usage中提取cache信息
		switch u := usage.(type) {
		case map[string]interface{}:
			if val, exists := u["cache_creation_input_tokens"]; exists {
				if tokens, ok := val.(int); ok && tokens > 0 {
					cacheCreationTokens = tokens
					hasCacheTokens = true
				}
			}
			if val, exists := u["cache_read_input_tokens"]; exists {
				if tokens, ok := val.(int); ok && tokens > 0 {
					cacheReadTokens = tokens
					hasCacheTokens = true
				}
			}
		default:
			// 使用反射处理各种类型（包括指针、结构体等）
			v := reflect.ValueOf(usage)

			// 如果是指针，获取指向的值
			if v.Kind() == reflect.Ptr && !v.IsNil() {
				v = v.Elem()
			}

			// 处理结构体类型
			if v.Kind() == reflect.Struct {
				// 优先从直接字段获取
				if field := v.FieldByName("CacheCreationInputTokens"); field.IsValid() && field.CanInterface() {
					if tokens, ok := field.Interface().(int); ok && tokens > 0 {
						cacheCreationTokens = tokens
						hasCacheTokens = true
					}
				}
				if field := v.FieldByName("CacheReadInputTokens"); field.IsValid() && field.CanInterface() {
					if tokens, ok := field.Interface().(int); ok && tokens > 0 {
						cacheReadTokens = tokens
						hasCacheTokens = true
					}
				}

				// 如果直接字段没有值，尝试从PromptTokensDetails获取（OpenAI格式）
				if cacheCreationTokens == 0 || cacheReadTokens == 0 {
					if promptDetailsField := v.FieldByName("PromptTokensDetails"); promptDetailsField.IsValid() && promptDetailsField.CanInterface() {
						promptDetails := promptDetailsField.Interface()
						detailsValue := reflect.ValueOf(promptDetails)

						if detailsValue.Kind() == reflect.Struct {
							// 获取CachedCreationTokens
							if cacheCreationTokens == 0 {
								if cachedCreationField := detailsValue.FieldByName("CachedCreationTokens"); cachedCreationField.IsValid() && cachedCreationField.CanInterface() {
									if ptr, ok := cachedCreationField.Interface().(*int); ok && ptr != nil && *ptr > 0 {
										cacheCreationTokens = *ptr
										hasCacheTokens = true
									}
								}
							}

							// 获取CachedTokens
							if cacheReadTokens == 0 {
								if cachedField := detailsValue.FieldByName("CachedTokens"); cachedField.IsValid() && cachedField.CanInterface() {
									if ptr, ok := cachedField.Interface().(*int); ok && ptr != nil && *ptr > 0 {
										cacheReadTokens = *ptr
										hasCacheTokens = true
									}
								}
							}
						}
					}
				}
			}
		}

		// 如果有cache相关token，添加详细的计费信息
		if hasCacheTokens && (cacheCreationTokens > 0 || cacheReadTokens > 0) {
			cacheCreationRatio := 1.25
			cacheReadRatio := 0.1
			cacheCreationCost := float64(cacheCreationTokens) * cacheCreationRatio
			cacheReadCost := float64(cacheReadTokens) * cacheReadRatio
			totalCacheCost := cacheCreationCost + cacheReadCost

			// 添加cache费率详情
			cacheDetail := map[string]interface{}{
				"cache_creation_tokens": cacheCreationTokens,
				"cache_creation_ratio":  cacheCreationRatio,
				"cache_creation_cost":   cacheCreationCost,
				"cache_read_tokens":     cacheReadTokens,
				"cache_read_ratio":      cacheReadRatio,
				"cache_read_cost":       cacheReadCost,
				"total_cache_cost":      totalCacheCost,
				"cache_enabled":         true,
			}
			other["cache_billing"] = cacheDetail
		} else {
			// 即使没有cache token，也标记cache功能状态
			other["cache_billing"] = map[string]interface{}{
				"cache_enabled": false,
			}
		}
	}

	// 添加动态路由计费明细信息
	if billingDetail != nil {
		other["billing_detail"] = billingDetail
	}

	jsonBytes, err := json.Marshal(other)
	if err != nil {
		logger.SysError("error marshalling other: " + err.Error())
		return ""
	}
	return string(jsonBytes)
}

// 获取 int 切片的辅助函数
func GetIntSlice(c *gin.Context, key string) ([]int, bool) {
	if intSlice, exists := c.Get(key); exists {
		if slice, ok := intSlice.([]int); ok {
			return slice, true
		}
	}
	return nil, false
}

func Float64PtrMax(p *float64, maxValue float64) *float64 {
	if p == nil {
		return nil
	}
	if *p > maxValue {
		return &maxValue
	}
	return p
}

func Float64PtrMin(p *float64, minValue float64) *float64 {
	if p == nil {
		return nil
	}
	if *p < minValue {
		return &minValue
	}
	return p
}

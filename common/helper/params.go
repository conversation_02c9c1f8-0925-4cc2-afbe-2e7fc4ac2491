package helper

import (
	"fmt"
	"strconv"
)

// ParamsBuilder 用于构建请求参数的工具结构体
type ParamsBuilder struct {
	params map[string]interface{}
}

// NewParamsBuilder 创建一个新的参数构建器
func NewParamsBuilder() *ParamsBuilder {
	return &ParamsBuilder{
		params: make(map[string]interface{}),
	}
}

// GetParams 获取构建的参数map
func (p *ParamsBuilder) GetParams() map[string]interface{} {
	return p.params
}

// AddIfNotEmpty 添加非空的基础参数
func (p *ParamsBuilder) AddIfNotEmpty(key string, value interface{}) *ParamsBuilder {
	if value != nil && value != "" && value != 0 && len(fmt.Sprint(value)) > 0 {
		p.params[key] = value
	}
	return p
}

// AddIfNotNil 添加非nil的参数
func (p *ParamsBuilder) AddIfNotNil(key string, value interface{}) *ParamsBuilder {
	if value != nil {
		p.params[key] = value
	}
	return p
}

// AddIfNotEmptySlice 添加非空的数组参数
func (p *ParamsBuilder) AddIfNotEmptySlice(key string, value interface{}) *ParamsBuilder {
	if value != nil {
		switch v := value.(type) {
		case []string:
			if len(v) > 0 {
				p.params[key] = v
			}
		case []int:
			if len(v) > 0 {
				p.params[key] = v
			}
		}
	}
	return p
}

// Add 直接添加参数
func (p *ParamsBuilder) Add(key string, value interface{}) *ParamsBuilder {
	p.params[key] = value
	return p
}

// ParseIntPtr 将字符串解析为整型指针
func ParseIntPtr(s string) *int {
	if s != "" {
		if v, err := strconv.Atoi(s); err == nil {
			return &v
		}
	}
	return nil
}

// ParseFloatPtr 将字符串解析为浮点型指针
func ParseFloatPtr(s string) *float64 {
	if s != "" {
		if v, err := strconv.ParseFloat(s, 64); err == nil {
			return &v
		}
	}
	return nil
}

// ParseInt64Ptr 将字符串解析为int64指针
func ParseInt64Ptr(s string) *int64 {
	if s != "" {
		if v, err := strconv.ParseInt(s, 10, 64); err == nil {
			return &v
		}
	}
	return nil
}

package helper_test

import (
	"github.com/songquanpeng/one-api/common/helper"
	"testing"
)

func TestMaskMiddle(t *testing.T) {
	t.Run("mask middle with empty string", func(t *testing.T) {
		result := helper.MaskMiddle("", 0, 0, '*')
		expected := "*"
		if result != expected {
			t.<PERSON>("Expected '%s', but got '%s'", expected, result)
		}
	})

	t.Run("mask middle with prefix and suffix length equal to string length", func(t *testing.T) {
		result := helper.MaskMiddle("abc", 3, 3, '*')
		expected := "***"
		if result != expected {
			t.<PERSON>("Expected '%s', but got '%s'", expected, result)
		}
	})

	t.Run("mask middle with non-zero prefix and suffix length", func(t *testing.T) {
		result := helper.MaskMiddle("abcdefghi", 2, 3, '*')
		expected := "ab****ghi"
		if result != expected {
			t.<PERSON>("Expected '%s', but got '%s'", expected, result)
		}
	})
}

package helper

import (
	"context"
	"github.com/go-redis/redis/v8"
	"time"
)

type RedisLock struct {
	rdb     redis.Cmdable // Changed from *redis.Client to redis.Cmdable
	lockKey string
}

// NewRedisLock creates a new RedisLock instance
// rdb can be either *redis.Client or *redis.ClusterClient since both implement redis.Cmdable
func NewRedisLock(rdb redis.Cmdable, key string) *RedisLock {
	return &RedisLock{
		rdb:     rdb,
		lockKey: "lock:" + key,
	}
}

// Lock attempts to acquire the lock
// timeout specifies how long the lock should be held
func (l *RedisLock) Lock(timeout time.Duration) bool {
	ctx := context.Background()
	for {
		// Use SetNX for atomic lock acquisition
		ok, err := l.rdb.SetNX(ctx, l.lockKey, "1", timeout).Result()
		if err != nil {
			// Log error if needed
			return false
		}
		if ok {
			return true
		}
		// Wait before retrying
		time.Sleep(time.Millisecond * 100) // Reduced sleep time for better responsiveness
	}
}

// Unlock releases the lock
func (l *RedisLock) Unlock() {
	ctx := context.Background()
	// Use Lua script for atomic unlock operation
	script := redis.NewScript(`
        if redis.call("GET", KEYS[1]) == ARGV[1] then
            return redis.call("DEL", KEYS[1])
        else
            return 0
        end
    `)

	// Execute the unlock script
	script.Run(ctx, l.rdb, []string{l.lockKey}, "1")
}

// TryLock attempts to acquire the lock once without retrying
func (l *RedisLock) TryLock(timeout time.Duration) bool {
	ctx := context.Background()
	ok, _ := l.rdb.SetNX(ctx, l.lockKey, "1", timeout).Result()
	return ok
}

// IsLocked checks if the lock is currently held
func (l *RedisLock) IsLocked() bool {
	ctx := context.Background()
	exists, _ := l.rdb.Exists(ctx, l.lockKey).Result()
	return exists > 0
}

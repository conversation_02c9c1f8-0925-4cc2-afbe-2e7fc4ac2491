package helper

import (
	"fmt"
	"sync"
	"time"
)

// 为了避免循环导入，我们先使用一个简化的版本
// 后续可以考虑将Redis相关功能移到独立的包中

// 添加全局变量用于限流（改进版本，支持清理过期记录）
var (
	lastReminderTimes = make(map[int]time.Time)
	reminderMutex     sync.RWMutex
	lastCleanupTime   time.Time
)

// CanSendQuotaReminder 检查是否可以发送余额提醒（改进版本，支持按用户区分）
func CanSendQuotaReminder(userId int) bool {
	reminderMutex.Lock()
	defer reminderMutex.Unlock()

	// 定期清理过期记录（每小时清理一次）
	now := time.Now()
	if now.Sub(lastCleanupTime) > time.Hour {
		cleanupExpiredReminders(now)
		lastCleanupTime = now
	}

	lastTime, exists := lastReminderTimes[userId]

	if !exists || now.Sub(lastTime) > 2*time.Hour {
		lastReminderTimes[userId] = now
		return true
	}
	return false
}

// cleanupExpiredReminders 清理过期的提醒记录
func cleanupExpiredReminders(now time.Time) {
	for userId, lastTime := range lastReminderTimes {
		if now.Sub(lastTime) > 24*time.Hour { // 清理24小时前的记录
			delete(lastReminderTimes, userId)
		}
	}
}

// RecordQuotaReminderSent 记录已发送余额提醒（在实际发送成功后调用）
func RecordQuotaReminderSent(userId int) {
	reminderMutex.Lock()
	defer reminderMutex.Unlock()
	lastReminderTimes[userId] = time.Now()
}

// GetLastQuotaReminderTime 获取用户最后一次发送提醒的时间
func GetLastQuotaReminderTime(userId int) (time.Time, error) {
	reminderMutex.RLock()
	defer reminderMutex.RUnlock()

	lastTime, exists := lastReminderTimes[userId]
	if !exists {
		return time.Time{}, fmt.Errorf("no reminder record found for user %d", userId)
	}
	return lastTime, nil
}

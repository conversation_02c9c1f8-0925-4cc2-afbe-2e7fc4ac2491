package logger

import (
	"encoding/json"
	"fmt"
	"time"
)

// NotificationLogLevel 通知日志级别
type NotificationLogLevel string

const (
	NotificationLogInfo    NotificationLogLevel = "INFO"
	NotificationLogWarning NotificationLogLevel = "WARNING"
	NotificationLogError   NotificationLogLevel = "ERROR"
	NotificationLogSuccess NotificationLogLevel = "SUCCESS"
)

// NotificationType 通知类型
type NotificationType string

const (
	NotificationTypeEmail    NotificationType = "email"
	NotificationTypeWebhook  NotificationType = "webhook"
	NotificationTypeQyWxBot  NotificationType = "qywxbot"
	NotificationTypeDingtalk NotificationType = "dingtalk"
	NotificationTypeFeishu   NotificationType = "feishu"
	NotificationTypeWxPusher NotificationType = "wxpusher"
	NotificationTypeTelegram NotificationType = "telegram"
)

// NotificationLogEntry 通知日志条目结构
type NotificationLogEntry struct {
	Timestamp        time.Time              `json:"timestamp"`         // 时间戳
	UserId           int                    `json:"user_id"`           // 用户ID
	NotificationType NotificationType       `json:"notification_type"` // 通知类型
	EventType        string                 `json:"event_type"`        // 事件类型（如：balance_warning, test_notification）
	Level            NotificationLogLevel   `json:"level"`             // 日志级别
	Status           string                 `json:"status"`            // 发送状态（success, failed, skipped）
	Message          string                 `json:"message"`           // 日志消息
	RequestParams    map[string]interface{} `json:"request_params"`    // 请求参数
	ResponseData     map[string]interface{} `json:"response_data"`     // 响应数据
	ErrorDetails     string                 `json:"error_details"`     // 错误详情
	Duration         int64                  `json:"duration_ms"`       // 发送耗时（毫秒）
}

// LogNotification 记录通知日志的主函数
func LogNotification(entry NotificationLogEntry) {
	// 设置时间戳
	if entry.Timestamp.IsZero() {
		entry.Timestamp = time.Now()
	}

	// 序列化为JSON
	jsonData, err := json.Marshal(entry)
	if err != nil {
		SysError(fmt.Sprintf("Failed to marshal notification log entry: %v", err))
		return
	}

	// 根据日志级别选择合适的日志函数
	logMessage := fmt.Sprintf("[通知系统] %s", string(jsonData))

	switch entry.Level {
	case NotificationLogSuccess:
		SysLog(logMessage)
	case NotificationLogInfo:
		SysLog(logMessage)
	case NotificationLogWarning:
		SysWarn(logMessage)
	case NotificationLogError:
		SysError(logMessage)
	default:
		SysLog(logMessage)
	}
}

// LogNotificationSuccess 记录通知发送成功日志
func LogNotificationSuccess(userId int, notificationType NotificationType, eventType string, message string, requestParams map[string]interface{}, responseData map[string]interface{}, duration int64) {
	entry := NotificationLogEntry{
		UserId:           userId,
		NotificationType: notificationType,
		EventType:        eventType,
		Level:            NotificationLogSuccess,
		Status:           "success",
		Message:          message,
		RequestParams:    requestParams,
		ResponseData:     responseData,
		Duration:         duration,
	}
	LogNotification(entry)
}

// LogNotificationError 记录通知发送失败日志
func LogNotificationError(userId int, notificationType NotificationType, eventType string, message string, requestParams map[string]interface{}, errorDetails string, duration int64) {
	entry := NotificationLogEntry{
		UserId:           userId,
		NotificationType: notificationType,
		EventType:        eventType,
		Level:            NotificationLogError,
		Status:           "failed",
		Message:          message,
		RequestParams:    requestParams,
		ErrorDetails:     errorDetails,
		Duration:         duration,
	}
	LogNotification(entry)
}

// LogNotificationSkipped 记录通知跳过日志
func LogNotificationSkipped(userId int, notificationType NotificationType, eventType string, reason string) {
	entry := NotificationLogEntry{
		UserId:           userId,
		NotificationType: notificationType,
		EventType:        eventType,
		Level:            NotificationLogInfo,
		Status:           "skipped",
		Message:          fmt.Sprintf("通知跳过: %s", reason),
	}
	LogNotification(entry)
}

// LogNotificationWarning 记录通知警告日志
func LogNotificationWarning(userId int, notificationType NotificationType, eventType string, message string, requestParams map[string]interface{}) {
	entry := NotificationLogEntry{
		UserId:           userId,
		NotificationType: notificationType,
		EventType:        eventType,
		Level:            NotificationLogWarning,
		Status:           "warning",
		Message:          message,
		RequestParams:    requestParams,
	}
	LogNotification(entry)
}

// CreateRequestParams 创建请求参数映射的辅助函数
func CreateRequestParams(params map[string]interface{}) map[string]interface{} {
	if params == nil {
		return make(map[string]interface{})
	}
	return params
}

// CreateResponseData 创建响应数据映射的辅助函数
func CreateResponseData(data map[string]interface{}) map[string]interface{} {
	if data == nil {
		return make(map[string]interface{})
	}
	return data
}

// SanitizeRequestParams 清理敏感信息的辅助函数
func SanitizeRequestParams(params map[string]interface{}) map[string]interface{} {
	if params == nil {
		return make(map[string]interface{})
	}

	sanitized := make(map[string]interface{})
	sensitiveKeys := []string{"token", "secret", "password", "key", "authorization"}

	for k, v := range params {
		// 检查是否为敏感字段
		isSensitive := false
		for _, sensitiveKey := range sensitiveKeys {
			if len(k) >= len(sensitiveKey) &&
				(k == sensitiveKey ||
					k[len(k)-len(sensitiveKey):] == sensitiveKey ||
					k[:len(sensitiveKey)] == sensitiveKey) {
				isSensitive = true
				break
			}
		}

		if isSensitive {
			if str, ok := v.(string); ok && len(str) > 0 {
				// 只显示前3个字符和后3个字符，中间用*替代
				if len(str) <= 6 {
					sanitized[k] = "***"
				} else {
					sanitized[k] = str[:3] + "***" + str[len(str)-3:]
				}
			} else {
				sanitized[k] = "***"
			}
		} else {
			sanitized[k] = v
		}
	}

	return sanitized
}

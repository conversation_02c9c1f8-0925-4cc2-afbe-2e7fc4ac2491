package dto

import (
	"encoding/json"
	"time"
)

// TaskError 任务错误结构
type TaskError struct {
	Error      TaskErrorDetail `json:"error"`
	StatusCode int             `json:"-"`
}

// TaskErrorDetail 任务错误详情
type TaskErrorDetail struct {
	Message string `json:"message"`
	Type    string `json:"type"`
	Code    string `json:"code"`
}

// TaskResponse 通用任务响应结构
type TaskResponse[T any] struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Data    T      `json:"data"`
}

func (r *TaskResponse[T]) IsSuccess() bool {
	return r.Code == "success" || r.Code == ""
}

// TaskDto 任务数据传输对象
type TaskDto struct {
	TaskID     string          `json:"task_id"`
	Action     string          `json:"action"`
	Status     string          `json:"status"`
	FailReason string          `json:"fail_reason,omitempty"`
	ResultUrl  string          `json:"result_url,omitempty"`
	SubmitTime int64           `json:"submit_time"`
	StartTime  int64           `json:"start_time,omitempty"`
	FinishTime int64           `json:"finish_time,omitempty"`
	Progress   string          `json:"progress,omitempty"`
	Data       json.RawMessage `json:"data,omitempty"`
}

// VideoGenerationRequest 视频生成请求
type VideoGenerationRequest struct {
	Model            string  `json:"model" binding:"required"`
	Prompt           string  `json:"prompt" binding:"required"`
	DurationSeconds  int     `json:"duration_seconds,omitempty"`
	AspectRatio      string  `json:"aspect_ratio,omitempty"`
	SampleCount      int     `json:"sample_count,omitempty"`
	PersonGeneration string  `json:"person_generation,omitempty"`
	EnhancePrompt    *bool   `json:"enhance_prompt,omitempty"`
	AddWatermark     *bool   `json:"add_watermark,omitempty"`
	IncludeRaiReason *bool   `json:"include_rai_reason,omitempty"`
	GenerateAudio    *bool   `json:"generate_audio,omitempty"`
	Image            *string `json:"image,omitempty"`
}

// VideoGenerationResponse 视频生成响应
type VideoGenerationResponse struct {
	ID        string `json:"id"`
	Object    string `json:"object"`
	Created   int64  `json:"created"`
	Model     string `json:"model"`
	Status    string `json:"status"`
	TaskType  string `json:"task_type"`
	ExpiresAt int64  `json:"expires_at"`
}

// GetCurrentTimestamp 获取当前时间戳
func GetCurrentTimestamp() int64 {
	return time.Now().Unix()
}

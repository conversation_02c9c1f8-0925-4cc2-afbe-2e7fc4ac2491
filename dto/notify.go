package dto

// Notify 通知数据结构，参考 new-api 标准
type Notify struct {
	Type    string        `json:"type"`
	Title   string        `json:"title"`
	Content string        `json:"content"`
	Values  []interface{} `json:"values"`
}

const ContentValueParam = "{{value}}"

// 通知类型常量，参考 new-api 标准
const (
	NotifyTypeQuotaExceed         = "quota_exceed"
	NotifyTypeChannelUpdate       = "channel_update"
	NotifyTypeChannelTest         = "channel_test"
	NotifyTypeBalanceLow          = "balance_low"
	NotifyTypeSecurityAlert       = "security_alert"
	NotifyTypeSystemAnnouncement  = "system_announcement"
	NotifyTypePromotionalActivity = "promotional_activity"
	NotifyTypeModelPricingUpdate  = "model_pricing_update"
	NotifyTypeAntiLossContact     = "anti_loss_contact"
)

// NewNotify 创建新的通知对象
func NewNotify(t string, title string, content string, values []interface{}) Notify {
	return Notify{
		Type:    t,
		Title:   title,
		Content: content,
		Values:  values,
	}
}

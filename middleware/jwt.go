package middleware

import (
	"encoding/json"
	"fmt"
	"github.com/gin-contrib/sessions"
	"github.com/golang-jwt/jwt/v4"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/utils"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

func JWTAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		if config.JWTAuthEnabled == false {
			// 没有开启jwt认证
			c.Next()
			return
		}
		// 我们这里jwt鉴权取头部信息 x-token 登录时回返回token信息 这里前端需要把token存储到cookie或者本地localStorage中 不过需要跟后端协商过期时间 可以约定刷新令牌或者重新登录
		token := c.Request.Header.Get("X-S-Token")
		if token == "" {
			// 没有token直接交给下级中间件处理
			c.Next()
			return
		}
		var claims *utils.CustomClaims
		// redis中是否存在
		if common.RedisEnabled {
			claimsStr, err := common.RedisGet(token)
			if err != nil {
				fmt.Printf("redis get token failed: %v", err)
			}
			if claimsStr != "" {
				err := json.Unmarshal([]byte(claimsStr), &claims)
				if err != nil {
					fmt.Printf("json unmarshal failed: %v", err)
				}
			}
		}
		j := utils.NewJWT()
		if claims == nil {
			// parseToken 解析token包含的信息
			claimsParsed, err := j.ParseToken(token)
			claims = claimsParsed
			if err != nil {
				/*if errors.Is(err, utils.TokenExpired) {
					c.JSON(http.StatusUnauthorized, gin.H{
						"success": false,
						"message": "无权进行此操作，授权已过期",
					})
					c.Abort()
				} else {
					c.JSON(http.StatusUnauthorized, gin.H{
						"success": false,
						"message": "无权进行此操作，" + err.Error(),
					})
					c.Abort()
				}*/
				// 报错还是交给下级,不影响之前的逻辑
				c.Next()
				return
			}
		}

		// 已登录用户被管理员禁用 需要使该用户的jwt失效 此处比较消耗性能 如果需要 请自行打开
		// 用户被删除的逻辑 需要优化 此处比较消耗性能 如果需要 请自行打开

		//if user, err := userService.FindUserByUuid(claims.UUID.String()); err != nil || user.Enable == 2 {
		//	_ = jwtService.JsonInBlacklist(system.JwtBlacklist{Jwt: token})
		//	response.FailWithDetailed(gin.H{"reload": true}, err.Error(), c)
		//	c.Abort()
		//}
		if claims.ExpiresAt.Unix()-time.Now().Unix() < claims.BufferTime {
			dr, _ := utils.ParseDuration("7d")
			claims.ExpiresAt = jwt.NewNumericDate(time.Now().Add(dr))
			newToken, _ := j.CreateTokenByOldToken(token, *claims)
			newClaims, _ := j.ParseToken(newToken)
			c.Header("new-token", newToken)
			c.Header("new-expires-at", strconv.FormatInt(newClaims.ExpiresAt.Unix(), 10))
		}

		initialToken, err := model.GetUserInitialToken(claims.BaseClaims.ID)
		if err != nil {
			fmt.Printf("获取用户初始token失败: %v", err)
		}
		c.Set("claims", claims)
		c.Set("jwt_user_initial_token", initialToken.Key)
		session := sessions.Default(c)
		session.Set("id", claims.BaseClaims.ID)
		session.Set("username", claims.Username)
		session.Set("role", claims.Role)
		session.Set("admin_access_flags", claims.AdminAccessFlags)
		session.Set("timezone", claims.Timezone)
		//session.Set("status", claims.Status)
		err = session.Save()
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"message": "无法保存会话信息，请重试",
				"success": false,
			})
			return
		}
		c.Next()
	}
}

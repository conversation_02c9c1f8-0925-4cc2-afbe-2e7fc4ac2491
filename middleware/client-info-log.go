package middleware

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/songquanpeng/one-api/common"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/logger"
)

func ClientInfoMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestURL := c.Request.URL.String()

		// 创建一个包含所有需要记录的客户端信息的结构
		clientInfo := map[string]interface{}{
			"user_agent":       c.Request.UserAgent(),
			"ip":               c.ClientIP(),
			"remote_addr":      c.Request.RemoteAddr,
			"method":           c.Request.Method,
			"host":             c.Request.Host,
			"referer":          c.Request.Referer(),
			"timestamp":        time.Now().Unix(),
			"cf_connecting_ip": c.Request.Header.Get("CF-Connecting-IP"),
		}

		// 检查是否是特殊的测试URL
		if strings.Contains(requestURL, "do_not_delete_this/c322968d-0a91-4a54-b255-59c9b5544af5.jpg") && common.RedisEnabled {
			// 使用Redis Hash结构存储信息，使用时间戳作为field
			redisKey := fmt.Sprintf("url_info:%s", requestURL)

			// 将map转换为JSON字符串
			jsonData, err := json.Marshal(clientInfo)
			if err != nil {
				logger.SysError(fmt.Sprintf("Failed to marshal client info: %v", err))
				return
			}

			// 使用纳秒级时间戳作为field
			field := fmt.Sprintf("%d", time.Now().UnixNano())
			err = common.RedisHashSet(redisKey, field, string(jsonData), 24*time.Hour)
			if err != nil {
				logger.SysError(fmt.Sprintf("Failed to store client info in Redis: %v", err))
			}
		}

		// 保持原有的文件系统日志逻辑
		if config.FileSystemClientInfoLogEnabled {
			logger.SysLog("========= File System Middleware =========")
			logger.SysLog(fmt.Sprintf("Request URL: %s", c.Request.URL))
			logger.SysLog(fmt.Sprintf("Request URI: %s", c.Request.RequestURI))
			logger.SysLog(fmt.Sprintf("Client IP: %s", c.ClientIP()))
			logger.SysLog(fmt.Sprintf("Remote Addr: %s", c.Request.RemoteAddr))
			logger.SysLog(fmt.Sprintf("Method: %s", c.Request.Method))
			logger.SysLog(fmt.Sprintf("Host: %s", c.Request.Host))
			logger.SysLog(fmt.Sprintf("Proto: %s", c.Request.Proto))
			logger.SysLog(fmt.Sprintf("User-Agent: %s", c.Request.UserAgent()))
			logger.SysLog(fmt.Sprintf("Headers: %v", c.Request.Header))
			logger.SysLog(fmt.Sprintf("Referer: %s", c.Request.Referer()))
			logger.SysLog("=========================================")
		}

		c.Next()
	}
}

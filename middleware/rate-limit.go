package middleware

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
)

var timeFormat = "2006-01-02T15:04:05.000Z"

var MyInMemoryRateLimiter common.InMemoryRateLimiter

func redisRateLimiter(c *gin.Context, maxRequestNum int, duration int64, mark string, userRateLimitExceededMessage string) {
	ctx := context.Background()
	rdb := common.RDB
	key := "rateLimit:" + mark + c.ClientIP()
	listLength, err := rdb.LLen(ctx, key).Result()
	if err != nil {
		fmt.Println(err.Error())
		c.Status(http.StatusInternalServerError)
		c.Abort()
		return
	}
	if listLength < int64(maxRequestNum) {
		rdb.LPush(ctx, key, time.Now().Format(timeFormat))
		rdb.Expire(ctx, key, config.RateLimitKeyExpirationDuration)
	} else {
		oldTimeStr, _ := rdb.LIndex(ctx, key, -1).Result()
		oldTime, err := time.Parse(timeFormat, oldTimeStr)
		if err != nil {
			fmt.Println(err)
			c.Status(http.StatusInternalServerError)
			c.Abort()
			return
		}
		nowTimeStr := time.Now().Format(timeFormat)
		nowTime, err := time.Parse(timeFormat, nowTimeStr)
		if err != nil {
			fmt.Println(err)
			c.Status(http.StatusInternalServerError)
			c.Abort()
			return
		}
		// time.Since will return negative number!
		// See: https://stackoverflow.com/questions/50970900/why-is-time-since-returning-negative-durations-on-windows
		if int64(nowTime.Sub(oldTime).Seconds()) < duration {
			rdb.Expire(ctx, key, config.RateLimitKeyExpirationDuration)
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "Too many requests",
				"data":  userRateLimitExceededMessage,
			})
			c.Abort()
			return
		} else {
			rdb.LPush(ctx, key, time.Now().Format(timeFormat))
			rdb.LTrim(ctx, key, 0, int64(maxRequestNum-1))
			rdb.Expire(ctx, key, config.RateLimitKeyExpirationDuration)
		}
	}
}

func redisRateLimiterNoConsiderIp(c *gin.Context, maxRequestNum int, duration int64, mark string, uniqueKey string, userRateLimitExceededMessage string) {
	ctx := context.Background()
	rdb := common.RDB
	key := fmt.Sprintf("rateLimit:%s:%s", mark, uniqueKey)
	listLength, err := rdb.LLen(ctx, key).Result()
	if err != nil {
		fmt.Println(err.Error())
		c.Status(http.StatusInternalServerError)
		c.Abort()
		return
	}
	if listLength < int64(maxRequestNum) {
		rdb.LPush(ctx, key, time.Now().Format(timeFormat))
		rdb.Expire(ctx, key, config.RateLimitKeyExpirationDuration)
	} else {
		oldTimeStr, _ := rdb.LIndex(ctx, key, -1).Result()
		oldTime, err := time.Parse(timeFormat, oldTimeStr)
		if err != nil {
			fmt.Println(err)
			c.Status(http.StatusInternalServerError)
			c.Abort()
			return
		}
		nowTimeStr := time.Now().Format(timeFormat)
		nowTime, err := time.Parse(timeFormat, nowTimeStr)
		if err != nil {
			fmt.Println(err)
			c.Status(http.StatusInternalServerError)
			c.Abort()
			return
		}
		// time.Since will return negative number!
		// See: https://stackoverflow.com/questions/50970900/why-is-time-since-returning-negative-durations-on-windows
		if int64(nowTime.Sub(oldTime).Seconds()) < duration {
			rdb.Expire(ctx, key, config.RateLimitKeyExpirationDuration)
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "Too many requests",
				"data":  userRateLimitExceededMessage,
			})
			c.Abort()
			return
		} else {
			rdb.LPush(ctx, key, time.Now().Format(timeFormat))
			rdb.LTrim(ctx, key, 0, int64(maxRequestNum-1))
			rdb.Expire(ctx, key, config.RateLimitKeyExpirationDuration)
		}
	}
}

func memoryRateLimiterNoConsiderIp(c *gin.Context, maxRequestNum int, duration int64, mark string, uniqueKey string, userRateLimitExceededMessage string) {
	key := mark + uniqueKey
	if !MyInMemoryRateLimiter.Request(key, maxRequestNum, duration) {
		c.JSON(http.StatusTooManyRequests, gin.H{
			"error": "Too many requests",
			"data":  userRateLimitExceededMessage,
		})
		c.Abort()
		return
	}
}

func memoryRateLimiter(c *gin.Context, maxRequestNum int, duration int64, mark string, userRateLimitExceededMessage string) {
	key := mark + c.ClientIP()
	if !MyInMemoryRateLimiter.Request(key, maxRequestNum, duration) {
		c.JSON(http.StatusTooManyRequests, gin.H{
			"error": "Too many requests",
			"data":  userRateLimitExceededMessage,
		})
		c.Abort()
		return
	}
}

func rateLimitFactory(maxRequestNum int, duration int64, mark string) func(c *gin.Context) {
	if maxRequestNum == 0 || config.DebugEnabled {
		return func(c *gin.Context) {
			c.Next()
		}
	}
	if common.RedisEnabled {
		return func(c *gin.Context) {
			userRateLimitExceededMessage := c.GetString("user_rate_limit_exceeded_message")
			redisRateLimiter(c, maxRequestNum, duration, mark, userRateLimitExceededMessage)
		}
	} else {
		// It's safe to call multi times.
		MyInMemoryRateLimiter.Init(config.RateLimitKeyExpirationDuration)
		return func(c *gin.Context) {
			userRateLimitExceededMessage := c.GetString("user_rate_limit_exceeded_message")
			memoryRateLimiter(c, maxRequestNum, duration, mark, userRateLimitExceededMessage)
		}
	}
}

func rateLimitFactoryNoConsiderIp(maxRequestNum int, duration int64, mark string, uniqueKey string) func(c *gin.Context) {
	if common.RedisEnabled {
		return func(c *gin.Context) {
			userRateLimitExceededMessage := c.GetString("token_rate_limit_exceeded_message")
			redisRateLimiterNoConsiderIp(c, maxRequestNum, duration, mark, uniqueKey, userRateLimitExceededMessage)
		}
	} else {
		// It's safe to call multi times.
		MyInMemoryRateLimiter.Init(config.RateLimitKeyExpirationDuration)
		return func(c *gin.Context) {
			userRateLimitExceededMessage := c.GetString("token_rate_limit_exceeded_message")
			memoryRateLimiterNoConsiderIp(c, maxRequestNum, duration, mark, uniqueKey, userRateLimitExceededMessage)
		}
	}
}

func GlobalWebRateLimit() func(c *gin.Context) {
	return rateLimitFactory(config.GlobalWebRateLimitNum, config.GlobalWebRateLimitDuration, "GW")
}

func GlobalAPIRateLimit() func(c *gin.Context) {
	return rateLimitFactory(config.GlobalApiRateLimitNum, config.GlobalApiRateLimitDuration, "GA")
}

func CriticalRateLimit() func(c *gin.Context) {
	return rateLimitFactory(config.CriticalRateLimitNum, config.CriticalRateLimitDuration, "CT")
}

func ResetCriticalRateLimit() func(c *gin.Context) {
	return rateLimitFactory(config.ResetCriticalRateLimitNum, config.ResetCriticalRateLimitDuration, "RCR")
}

func DownloadRateLimit() func(c *gin.Context) {
	return rateLimitFactory(config.DownloadRateLimitNum, config.DownloadRateLimitDuration, "DW")
}

func UploadRateLimit() func(c *gin.Context) {
	return rateLimitFactory(config.UploadRateLimitNum, config.UploadRateLimitDuration, "UP")
}

func UserRelayRateLimit() func(c *gin.Context) {
	return func(c *gin.Context) {
		userRateLimit := c.GetInt("user_rate_limit")
		userId := c.GetInt("id")
		if userRateLimit > 0 {
			rateLimit := rateLimitFactory(userRateLimit, config.GlobalApiRateLimitDuration, fmt.Sprintf("UR:%d", userId))
			rateLimit(c) // 应用速率限制
		} else {
			c.Next()
		}
	}
}

func TokenRelayRateLimit() func(c *gin.Context) {
	return func(c *gin.Context) {
		tokenRateLimitNum := c.GetInt("token_rate_limit_num")
		tokenRateLimitDuration := c.GetInt("token_rate_limit_duration")
		tokenId := c.GetInt("token_id")
		if tokenRateLimitNum > 0 {
			rateLimit := rateLimitFactoryNoConsiderIp(tokenRateLimitNum, int64(tokenRateLimitDuration), fmt.Sprintf("TR:TokenId:%d", tokenId), "")
			rateLimit(c) // 应用速率限制
		} else {
			c.Next()
		}
	}
}

package middleware

import (
	"encoding/json"
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/relay/channeltype"
)

type Property struct {
	Type        string   `json:"type"`
	Description string   `json:"description"`
	Enum        []string `json:"enum,omitempty"`
}

type Parameter struct {
	Type       string              `json:"type"`
	Properties map[string]Property `json:"properties"`
	Required   []string            `json:"required"`
}

type Function struct {
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Parameters  Parameter `json:"parameters"`
}

type FunctionCall struct {
	Name string `json:"name"`
}

type ImageURL struct {
	URL    string `json:"url,omitempty"`
	Detail string `json:"detail,omitempty"`
}

type MessageContent struct {
	Type     string    `json:"type,omitempty"`
	Text     string    `json:"text,omitempty"`
	ImageURL *ImageURL `json:"image_url,omitempty"`
}

type Message struct {
	Role    string      `json:"role,omitempty"`
	Content interface{} `json:"content,omitempty"`
}

type ModelRequest struct {
	Model         string         `json:"model" form:"model"`
	Messages      []Message      `json:"messages,omitempty"`
	MaxTokens     int            `json:"max_tokens,omitempty"`
	Stream        bool           `json:"stream,omitempty"`
	Functions     *[]Function    `json:"functions,omitempty"`
	FunctionCall  interface{}    `json:"function_call,omitempty"`
	Tools         *[]Function    `json:"tools,omitempty"`
	StreamOptions *StreamOptions `json:"stream_options,omitempty"`
	// edits 请求相关字段
	Input       any         `json:"input,omitempty" form:"input"`
	Instruction interface{} `json:"instruction,omitempty" form:"instruction"`
	// whisper 音频相关字段
	File           string      `json:"file,omitempty" form:"file"`                       // 音频文件（如果是上传）
	URL            interface{} `json:"url,omitempty" form:"url"`                         // 音频文件URL（支持Base64URL）
	Language       string      `json:"language,omitempty" form:"language"`               // 输入音频的语言
	Prompt         string      `json:"prompt,omitempty" form:"prompt"`                   // 指导模型风格或如何拼写生僻词的提示
	ResponseFormat interface{} `json:"response_format,omitempty" form:"response_format"` // 输出响应格式，支持字符串和复杂对象
	Temperature    float64     `json:"temperature,omitempty" form:"temperature"`         // 采样温度
}

type StreamOptions struct {
	IncludeUsage bool `json:"include_usage,omitempty"`
}

// parseRequestWithFormDataSupport 通用函数，支持JSON和FormData两种格式的解析
func parseRequestWithFormDataSupport(c *gin.Context, modelRequest *ModelRequest, defaultModel string) error {
	contentType := c.Request.Header.Get("Content-Type")

	// 检查是否为 formdata 或 urlencoded 格式
	if contentType != "" &&
		(strings.Contains(contentType, "multipart/form-data") ||
			strings.Contains(contentType, "application/x-www-form-urlencoded")) {

		// 先尝试 JSON 解析
		err := common.UnmarshalBodyReusable(c, modelRequest)
		if err != nil || modelRequest.Model == "" {
			// JSON 解析失败或模型为空，尝试解析 formdata（并恢复请求体）
			if parseErr := common.BindFormDataWithRestore(c, modelRequest); parseErr == nil {
				// formdata 解析成功，将错误重置为 nil
				err = nil
			}
		}
		if err != nil {
			return err
		}
	} else {
		// 普通 JSON 格式
		err := common.UnmarshalBodyReusable(c, modelRequest)
		if err != nil {
			return err
		}
	}

	// 设置默认模型
	if modelRequest.Model == "" {
		modelRequest.Model = defaultModel
	}

	return nil
}

func Distribute() func(c *gin.Context) {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		userId := c.GetInt(ctxkey.Id)
		userGroup, _ := model.CacheGetUserGroup(userId)
		c.Set(ctxkey.Group, userGroup)
		if config.TokenGroupChangeEnabled {
			tokenGroup := c.GetString(ctxkey.TokenGroup)
			if tokenGroup != "" {
				userGroup = tokenGroup
			}
		}
		var channel *model.Channel
		channelId, ok := c.Get(ctxkey.SpecificChannelId)
		if ok {
			id, err := strconv.Atoi(channelId.(string))
			c.Set(ctxkey.ChannelId, id)
			if err != nil {
				LogAndAbortWithError(c, http.StatusBadRequest, "无效的渠道 Id")
				return
			}
			channel, err = model.GetChannelById(id, true)
			if err != nil {
				LogAndAbortWithError(c, http.StatusBadRequest, "无效的渠道 Id")
				return
			}
			c.Set(ctxkey.ChannelName, channel.Name)
			if channel.Status != model.ChannelStatusEnabled {
				LogAndAbortWithError(c, http.StatusForbidden, "该渠道已被禁用")
				return
			}
		}
		// Select a channel for the user
		var modelRequest ModelRequest
		var err error
		if strings.Contains(c.Request.URL.Path, "/mj") {
			// Midjourney
			if modelRequest.Model == "" {
				// 检查是否是视频提交路径
				if strings.Contains(c.Request.URL.Path, "/submit/video") {
					modelRequest.Model = "midjourney-video"
				} else {
					modelRequest.Model = "midjourney"
				}
			}
			mode := c.Param("mode")
			mode = strings.TrimPrefix(mode, "mj-")
			if mode == "" {
				mode = model.DefaultMJMode
			} else {
				// 根据是否是视频路径来设置模型名
				if strings.Contains(c.Request.URL.Path, "/submit/video") {
					modelRequest.Model = fmt.Sprintf("midjourney-video-%s", mode)
				} else {
					modelRequest.Model = fmt.Sprintf("midjourney-%s", mode)
				}
			}
			c.Set("mode", mode)
			c.Set(ctxkey.RequestModel, modelRequest.Model)
		} else {
			err = common.UnmarshalBodyReusable(c, &modelRequest)
		}
		if err != nil {
			logger.SysError(fmt.Sprintf("无效的请求:解析请求体失败：%s", err.Error()))
			LogAndAbortWithError(c, http.StatusBadRequest, "无效的请求")
			return
		}
		// 存入ShouldIncludeUsage
		if modelRequest.StreamOptions != nil {
			c.Set(ctxkey.ShouldIncludeUsage, modelRequest.StreamOptions.IncludeUsage)
		}
		if strings.HasPrefix(c.Request.URL.Path, "/v1/moderations") {
			if modelRequest.Model == "" {
				modelRequest.Model = "text-moderation-stable"
			}
		}
		if strings.HasSuffix(c.Request.URL.Path, "embeddings") {
			if modelRequest.Model == "" {
				modelRequest.Model = c.Param("model")
			}
		}
		if strings.HasPrefix(c.Request.URL.Path, "/v1/images/generations") {
			if modelRequest.Model == "" {
				modelRequest.Model = "dall-e-2"
			}
		}
		if strings.HasPrefix(c.Request.URL.Path, "/v1/images/edits") {
			// 使用通用解析函数处理 /v1/images/edits 请求
			err = parseRequestWithFormDataSupport(c, &modelRequest, "gpt-image-1")
			if err != nil {
				logger.SysError(fmt.Sprintf("无效的请求:解析请求体失败：%s", err.Error()))
				LogAndAbortWithError(c, http.StatusBadRequest, "无效的请求")
				return
			}
		}
		if strings.HasPrefix(c.Request.URL.Path, "/v1/videos/generations") {
			// 处理视频生成请求
			if modelRequest.Model == "" {
				modelRequest.Model = "veo-3.0-generate-preview" // 默认模型
			}
		}

		if strings.HasPrefix(c.Request.URL.Path, "/v1/audio/transcriptions") || strings.HasPrefix(c.Request.URL.Path, "/v1/audio/translations") {
			// 使用通用解析函数处理 whisper-1 相关请求，支持 formdata 格式
			err = parseRequestWithFormDataSupport(c, &modelRequest, "whisper-1")
			if err != nil {
				logger.SysError(fmt.Sprintf("无效的请求:解析请求体失败：%s", err.Error()))
				LogAndAbortWithError(c, http.StatusBadRequest, "无效的请求")
				return
			}
		}
		if strings.HasPrefix(c.Request.URL.Path, "/search/serper") {
			if modelRequest.Model == "" {
				modelRequest.Model = "search-serper"
			}
		}
		c.Set(ctxkey.RequestModel, modelRequest.Model)
		// 判断请求url是否为/v1/realtime 如果是则获取url上面的?model=gpt-4o-realtime-preview-2024-10-01
		if strings.HasPrefix(c.Request.URL.Path, "/v1/realtime") {
			model := c.Query("model")
			if model == "" {
				requestBody, _ := json.Marshal(modelRequest)
				c.Set("request_body", string(requestBody))
				LogAndAbortWithError(c, http.StatusBadRequest, "无效的请求")
				return
			}
			c.Set(ctxkey.RequestModel, model)
			modelRequest.Model = model
		}
		// 检测是否是Fish Audio API请求
		if strings.HasPrefix(c.Request.URL.Path, "/fish") {
			// 从header获取model
			headerModel := c.Request.Header.Get("model")
			if headerModel != "" {
				modelRequest.Model = headerModel
				c.Set(ctxkey.RequestModel, headerModel)
			} else {
				// 没有设置model，使用默认的speech-1.5
				modelRequest.Model = "speech-1.5"
				c.Set(ctxkey.RequestModel, "speech-1.5")
			}
		}
		// 判断是否是联网模型,如果是,需要暂时去掉net-
		if strings.HasPrefix(modelRequest.Model, "net-") {
			modelRequest.Model = strings.TrimPrefix(modelRequest.Model, "net-")
		}
		if strings.HasSuffix(c.Request.URL.Path, "/v1/messages") {
			c.Set(ctxkey.IsV1MessagesPath, true)
		}
		// IsResponsesPath
		if strings.HasSuffix(c.Request.URL.Path, "/v1/responses") {
			c.Set(ctxkey.IsResponsesPath, true)
		}
		if !ok {
			done := false
			matched := false
			message := ""
			var plans []model.PackagePlanInstance
			// 获取用户名下有效的套餐
			plans, _ = model.CacheGetUserActivePackagePlans(userId)
			if plans != nil {
				// 遍历套餐
				for _, plan := range plans {
					isContains := false
					// 获取套餐实例下配置的模型
					availableModels := plan.AvailableModels
					if availableModels == "" {
						isContains = true
					} else {
						models := strings.Split(availableModels, ",")
						// 判断models里面是否包含modelRequest.Model
						for _, m := range models {
							if strings.Contains(m, "*") {
								// 如果包含通配符
								// 将通配符 '*' 转换为正则表达式的 '.*'
								pattern := "^" + strings.Replace(regexp.QuoteMeta(m), "\\*", ".*", -1) + "$"
								matched, err := regexp.MatchString(pattern, modelRequest.Model)
								if err != nil {
									logger.SysError("regexp error: " + err.Error())
									LogAndAbortWithError(c, http.StatusInternalServerError, "regexp error")
								}
								if matched {
									isContains = true
									break
								}
							} else {
								// 否则直接等于
								if m == modelRequest.Model {
									isContains = true
									break
								}
							}
						}
					}
					if isContains {
						// 获取套餐分组下的渠道
						isV1MessagesPath := c.GetBool(ctxkey.IsV1MessagesPath)
						channel, _, message, done, matched = getChannelByGroupAndBillingType(c, plan.Group, modelRequest, isV1MessagesPath)
						if matched {
							c.Set("package_plan_instance", plan)
							c.Set("is_package_plan", true)
							break
						}
					}
				}
			}
			if !matched {
				// 获取用户分组下的渠道
				isV1MessagesPath := c.GetBool(ctxkey.IsV1MessagesPath)
				channel, _, message, done, matched = getChannelByGroupAndBillingType(c, userGroup, modelRequest, isV1MessagesPath)
				// 这里判断如果是Midjourney模型找不到渠道,则需要再找midjourney-fast
				if !matched && modelRequest.Model == "midjourney" {
					modelRequest.Model = "midjourney-fast"
					c.Set(ctxkey.RequestModel, "midjourney-fast")
					channel, _, message, done, matched = getChannelByGroupAndBillingType(c, userGroup, modelRequest, isV1MessagesPath)
				}
				// 这里判断如果是Midjourney视频模型找不到渠道,则需要再找midjourney-video-fast
				if !matched && modelRequest.Model == "midjourney-video" {
					modelRequest.Model = "midjourney-video-fast"
					c.Set(ctxkey.RequestModel, "midjourney-video-fast")
					channel, _, message, done, matched = getChannelByGroupAndBillingType(c, userGroup, modelRequest, isV1MessagesPath)
				}
			}
			if done {
				requestBody, _ := json.Marshal(modelRequest)
				c.Set("request_body", string(requestBody))
				LogAndAbortWithError(c, http.StatusServiceUnavailable, message)
				return
			}
		}
		// 校验key下面配置的模型是否支持
		tokenAbilityModels := c.GetString("token_ability_models")
		if tokenAbilityModels != "" {
			models_ := strings.Split(tokenAbilityModels, ",")
			// 判断models里面是否包含modelRequest.Model
			isContains := false
			for _, m := range models_ {
				if strings.Contains(m, "*") {
					// 如果包含通配符
					// 将通配符 '*' 转换为正则表达式的 '.*'
					pattern := "^" + strings.Replace(regexp.QuoteMeta(m), "\\*", ".*", -1) + "$"
					matched, err := regexp.MatchString(pattern, modelRequest.Model)
					if err != nil {
						logger.SysError("regexp error: " + err.Error())
						LogAndAbortWithError(c, http.StatusInternalServerError, "regexp error")
					}
					if matched {
						isContains = true
						break
					}
				} else {
					// 否则直接等于
					if m == modelRequest.Model {
						isContains = true
						break
					}
				}
			}
			if !isContains {
				LogAndAbortWithError(c, http.StatusUnauthorized, fmt.Sprintf("当前令牌下面配置的模型不包含 [%s]", modelRequest.Model))
				return
			}
		}
		logger.Debugf(ctx, "user id %d, user group: %s, request model: %s, using channel #%d", userId, userGroup, modelRequest.Model, channel.Id)
		SetupContextForSelectedChannel(c, channel, modelRequest.Model)
		c.Next()
	}
}

func SetupContextForSelectedChannel(c *gin.Context, channel *model.Channel, modelName string) {
	c.Set(ctxkey.Channel, channel.Type)
	c.Set(ctxkey.ChannelId, channel.Id)
	c.Set(ctxkey.ChannelName, channel.Name)
	c.Set(ctxkey.BillingType, channel.BillingType)
	c.Set(ctxkey.FunctionCallEnabled, channel.GetFunctionCallEnabled())
	c.Set(ctxkey.ImageSupported, channel.GetImageSupported())
	if channel.SystemPrompt != nil && *channel.SystemPrompt != "" {
		c.Set(ctxkey.SystemPrompt, *channel.SystemPrompt)
	}
	c.Set(ctxkey.ModelMapping, channel.GetModelMapping())
	c.Set(ctxkey.ModelMappingArr, channel.GetModelMappingArr())
	c.Set(ctxkey.OriginalModel, modelName) // for retry
	c.Set(ctxkey.ExcludedFields, channel.GetExcludedFields())
	c.Set(ctxkey.ExcludedResponseFields, channel.GetExcludedResponseFields())
	c.Set(ctxkey.ExtraFields, channel.GetExtraFields())
	c.Set(ctxkey.BaseURL, channel.GetBaseURL())
	cfg, _ := channel.LoadConfig()
	c.Set("retryInterval", channel.GetRetryInterval())
	c.Set("undeadModeEnabled", channel.GetUndeadModeEnabled())
	if channel.OverFrequencyAutoDisable != nil {
		c.Set("overFrequencyAutoDisable", *channel.OverFrequencyAutoDisable)
	}
	if channel.ImageInMarkdown != nil {
		c.Set("image_in_markdown", *channel.ImageInMarkdown)
	}
	if channel.Key != "" {
		c.Request.Header.Set("Authorization", fmt.Sprintf("Bearer %s", channel.Key))
	} else {
		c.Request.Header.Del("Authorization")
	}
	if channel.OpenAIOrganization != nil {
		c.Request.Header.Set("OpenAI-Organization", *channel.OpenAIOrganization)
	}
	c.Set("base_url", channel.GetBaseURL())
	// this is for backward compatibility
	if channel.Other != nil {
		switch channel.Type {
		case channeltype.Azure:
			if cfg.APIVersion == "" {
				cfg.APIVersion = *channel.Other
			}
		case channeltype.Xunfei:
			if cfg.APIVersion == "" {
				cfg.APIVersion = *channel.Other
			}
		case channeltype.Gemini:
			if cfg.APIVersion == "" {
				cfg.APIVersion = *channel.Other
			}
		case channeltype.AIProxyLibrary:
			if cfg.LibraryID == "" {
				cfg.LibraryID = *channel.Other
			}
		case channeltype.Ali:
			if cfg.Plugin == "" {
				cfg.Plugin = *channel.Other
			}
		}
	}
	c.Set(ctxkey.Config, cfg)
	// 根据channelId获取channelExtend
	channelExtend, _ := model.CacheGetChannelExByChannelId(channel.Id)
	if channelExtend != nil {
		c.Set("filter_stream_ad", channelExtend.FilterStreamAd)
		c.Set("filter_stream_ad_min_size", channelExtend.FilterStreamAdMinSize)
		c.Set("filter_non_stream_ad", channelExtend.FilterNonStreamAd)
		c.Set("filter_non_stream_ad_regex", channelExtend.FilterNonStreamAdRegex)
		c.Set("filter_system_prompt", channelExtend.FilterSystemPrompt)
		c.Set("custom_system_prompt", channelExtend.CustomSystemPrompt)
		c.Set("extra_headers", channelExtend.GetExtraHeaders())
		c.Set("platform_access_token", channelExtend.PlatformAccessToken)
		c.Set("parse_url_to_content", channelExtend.ParseUrlToContent)
		c.Set("parse_url_prefix_enabled", channelExtend.ParseUrlPrefixEnabled)
		c.Set("parse_url_prefix", channelExtend.ParseUrlPrefix)
		c.Set("custom_full_url_enabled", channelExtend.CustomFullUrlEnabled)
		c.Set("arrange_messages", channelExtend.ArrangeMessages)
		c.Set("original_model_pricing", channelExtend.OriginalModelPricing)
		c.Set("negative_optimization_enabled", channelExtend.NegativeOptimizationEnabled)
		c.Set("negative_optimization_time", channelExtend.NegativeOptimizationTime)
		c.Set("negative_random_offset", channelExtend.NegativeRandomOffset)
		c.Set("original_model_fake_resp_enabled", channelExtend.OriginalModelFakeRespEnabled)
		c.Set("fake_completion_id_enabled", channelExtend.FakeCompletionIdEnabled)
		c.Set("exclude_custom_prompt_cost_enabled", channelExtend.ExcludeCustomPromptCostEnabled)
		c.Set("force_chat_url_enabled", channelExtend.ForceChatUrlEnabled)
		c.Set("ignore_fc_tc_enabled", channelExtend.IgnoreFcTcEnabled)
		c.Set("channel_timeout_breaker_time", channelExtend.ChannelTimeoutBreakerTime)
		c.Set("usage_recalculation_enabled", channelExtend.UsageRecalculationEnabled)
		c.Set("empty_response_error_enabled", channelExtend.EmptyResponseErrorEnabled)
		c.Set("remove_image_download_error_enabled", channelExtend.RemoveImageDownloadErrorEnabled)
		c.Set(ctxkey.Base64ImagePrefixMapping, channelExtend.GetBase64ImagePrefixMapping())
		c.Set("request_token_limit_enabled", channelExtend.RequestTokenLimitEnabled)
		c.Set("min_request_token_count", channelExtend.MinRequestTokenCount)
		c.Set("max_request_token_count", channelExtend.MaxRequestTokenCount)
		c.Set("claude_stream_enabled", channelExtend.ClaudeStreamEnabled)
		c.Set("keyword_error_enabled", channelExtend.KeywordErrorEnabled)
		c.Set("keyword_error", channelExtend.KeywordError)
		c.Set("transparent_proxy_enabled", channelExtend.TransparentProxyEnabled)
		c.Set("force_o1_stream_enabled", channelExtend.ForceO1StreamEnabled)
		c.Set("cost_per_unit", channelExtend.CostPerUnit)
		c.Set("think_tag_processing_enabled", channelExtend.ThinkTagProcessingEnabled)
	}
}

func getChannelByGroupAndBillingType(c *gin.Context, userGroup string, modelRequest ModelRequest, isV1MessagesPath bool) (*model.Channel, error, string, bool, bool) {
	if userGroup == "" {
		return nil, nil, "", true, false
	}
	var channel *model.Channel
	var err error
	inputHasFunctionCall := modelRequest.FunctionCall != nil || modelRequest.Functions != nil || modelRequest.Tools != nil
	inputHasImage := hasImageInput(&modelRequest)
	c.Set(ctxkey.InputHasFunctionCall, inputHasFunctionCall)
	c.Set(ctxkey.InputHasImage, inputHasImage)
	userId := c.GetInt(ctxkey.Id)
	tokenBillingType := c.GetInt("token_billing_type")
	tokenBillingTypeStr := "按量计费"
	if tokenBillingType == common.BillingTypeByCount {
		tokenBillingTypeStr = "按次计费"
	} else if tokenBillingType == common.BillingTypeMixed {
		tokenBillingTypeStr = "混合计费"
	}
	if tokenBillingType == common.BillingTypeByQuotaFirst {
		// 如果是按量优先,则优先选择按量计费的渠道
		channel, err = model.CacheGetRandomSatisfiedChannel(userGroup, modelRequest.Model, false, common.BillingTypeByQuota, inputHasFunctionCall, inputHasImage, nil, isV1MessagesPath, userId)
		if err != nil {
			// 如果按量计费的渠道没有,则选择按次计费的渠道
			channel, err = model.CacheGetRandomSatisfiedChannel(userGroup, modelRequest.Model, false, common.BillingTypeByCount, inputHasFunctionCall, inputHasImage, nil, isV1MessagesPath, userId)
			if err != nil {
				message := fmt.Sprintf("当前分组 %s 下对于模型 %s 计费模式 [按量计费,按次计费] 无可用渠道", userGroup, modelRequest.Model)
				if channel != nil {
					logger.SysError(fmt.Sprintf("渠道不存在：%d", channel.Id))
					message = "数据库一致性已被破坏，请联系管理员"
				}
				return nil, err, message, true, false
			}
		}
	} else if tokenBillingType == common.BillingTypeByCountFirst {
		// 如果是按次优先,则优先选择按次计费的渠道
		channel, err = model.CacheGetRandomSatisfiedChannel(userGroup, modelRequest.Model, false, common.BillingTypeByCount, inputHasFunctionCall, inputHasImage, nil, isV1MessagesPath, userId)
		if err != nil {
			// 如果按次计费的渠道没有,则选择按量计费的渠道
			channel, err = model.CacheGetRandomSatisfiedChannel(userGroup, modelRequest.Model, false, common.BillingTypeByQuota, inputHasFunctionCall, inputHasImage, nil, isV1MessagesPath, userId)
			if err != nil {
				message := fmt.Sprintf("当前分组 %s 下对于模型 %s 计费模式 [按次计费,按量计费] 无可用渠道", userGroup, modelRequest.Model)
				if channel != nil {
					logger.SysError(fmt.Sprintf("渠道不存在：%d", channel.Id))
					message = "数据库一致性已被破坏，请联系管理员"
				}
				return nil, err, message, true, false
			}
		}
	} else {
		// 如果不是以上两种情况,则直接选择对应的计费模式
		channel, err = model.CacheGetRandomSatisfiedChannel(userGroup, modelRequest.Model, false, tokenBillingType, inputHasFunctionCall, inputHasImage, nil, isV1MessagesPath, userId)
		if err != nil {
			message := fmt.Sprintf("当前分组 %s 下对于模型 %s 计费模式 [%s] 无可用渠道", userGroup, modelRequest.Model, tokenBillingTypeStr)
			if channel != nil {
				logger.SysError(fmt.Sprintf("渠道不存在：%d", channel.Id))
				message = "数据库一致性已被破坏，请联系管理员"
			}
			return nil, err, message, true, false
		}
	}
	return channel, nil, "", false, true
}

// checkForImageContent 检查消息中是否包含图片内容
func checkForImageContent(content interface{}) bool {
	switch v := content.(type) {
	case string:
		return false // 纯文本消息，不包含图片
	case []interface{}:
		for _, item := range v {
			if m, ok := item.(map[string]interface{}); ok {
				if m["type"] == "image_url" || m["type"] == "image" {
					return true
				}
			}
		}
	case map[string]interface{}:
		if v["type"] == "image_url" || v["type"] == "image" {
			return true
		}
	}
	return false
}

// hasImageInput 检查整个请求中是否包含图片输入
func hasImageInput(modelRequest *ModelRequest) bool {
	if modelRequest.Messages == nil {
		return false
	}
	for _, message := range modelRequest.Messages {
		if checkForImageContent(message.Content) {
			return true
		}
	}
	return false
}

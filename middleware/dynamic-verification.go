package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
)

func DynamicVerificationMiddleware(config *map[string]common.PageMiddlewareConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		var pageConfig common.PageMiddlewareConfig
		var found bool
		// 遍历配置，检查当前请求的URL是否匹配任何配置中的ApiEndpoints
		for _, cfg := range *config {
			if stringInSlice(c.Request.URL.Path, cfg.ApiEndpoints) {
				pageConfig = cfg
				found = true
				break
			}
		}
		// 如果没有找到匹配的配置，应用默认的两种验证码验证
		if !found {
			CaptchaCheck()(c)
			TurnstileCheck()(c)
			if c.IsAborted() {
				return
			}
			c.Next()
			return
		}

		// 如果找到了匹配的配置，根据配置应用相应的验证类型
		for _, verificationType := range pageConfig.VerificationTypes {
			var middleware gin.HandlerFunc
			switch verificationType {
			case "captcha":
				middleware = CaptchaCheck()
			case "turnstile":
				middleware = TurnstileCheck()
				// 可以添加更多的验证类型
			}

			// 应用中间件
			middleware(c)

			// 检查中间件是否设置了响应，如有，终止后续处理
			if c.IsAborted() {
				return
			}
		}

		c.Next()
	}
}

func CheckinDynamicVerificationMiddleware(config *map[string]common.PageMiddlewareConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		var pageConfig common.PageMiddlewareConfig
		var found bool
		// 遍历配置，检查当前请求的URL是否匹配任何配置中的ApiEndpoints
		for _, cfg := range *config {
			if stringInSlice(c.Request.URL.Path, cfg.ApiEndpoints) {
				pageConfig = cfg
				found = true
				break
			}
		}
		// 如果没有找到匹配的配置，应用默认的两种验证码验证
		if !found {
			CheckinCaptchaCheck()(c)
			TurnstileCheck()(c)
			if c.IsAborted() {
				return
			}
			c.Next()
			return
		}

		// 如果找到了匹配的配置，根据配置应用相应的验证类型
		for _, verificationType := range pageConfig.VerificationTypes {
			var middleware gin.HandlerFunc
			switch verificationType {
			case "captcha":
				middleware = CheckinCaptchaCheck()
			case "turnstile":
				middleware = TurnstileCheck()
				// 可以添加更多的验证类型
			}

			// 应用中间件
			middleware(c)

			// 检查中间件是否设置了响应，如有，终止后续处理
			if c.IsAborted() {
				return
			}
		}

		c.Next()
	}
}

func RegisterDynamicVerificationMiddleware(conf *map[string]common.PageMiddlewareConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		var pageConfig common.PageMiddlewareConfig
		var found bool
		// 遍历配置，检查当前请求的URL是否匹配任何配置中的ApiEndpoints
		for _, cfg := range *conf {
			if stringInSlice(c.Request.URL.Path, cfg.ApiEndpoints) {
				pageConfig = cfg
				found = true
				break
			}
		}
		// 如果没有找到匹配的配置，应用默认的两种验证码验证
		if !found {
			// 邮箱开启验证则不需要再校验验证码,只校验邮箱验证码,避免2次输入验证码的尴尬
			if config.CaptchaCheckEnabled && !config.EmailVerificationEnabled {
				CaptchaCheck()(c)
			}
			// 手机开启验证码验证，不需要再校验图形验证码
			//if config.CaptchaCheckEnabled && !config.SMSRegisterEnabled {
			//	CaptchaCheck()(c)
			//}
			TurnstileCheck()(c)
			if c.IsAborted() {
				return
			}
			c.Next()
			return
		}

		// 如果找到了匹配的配置，根据配置应用相应的验证类型
		for _, verificationType := range pageConfig.VerificationTypes {
			var middleware gin.HandlerFunc
			switch verificationType {
			case "captcha":
				// 邮箱开启验证则不需要再校验验证码,只校验邮箱验证码,避免2次输入验证码的尴尬
				if config.CaptchaCheckEnabled && !config.EmailVerificationEnabled {
					middleware = CaptchaCheck()
				} else {
					continue
				}
			case "turnstile":
				middleware = TurnstileCheck()
				// 可以添加更多的验证类型
			}
			// 应用中间件
			middleware(c)
			// 检查中间件是否设置了响应，如有，终止后续处理
			if c.IsAborted() {
				return
			}
		}
		c.Next()
	}
}

// stringInSlice 检查字符串是否在切片中
func stringInSlice(a string, list []string) bool {
	for _, b := range list {
		if b == a {
			return true
		}
	}
	return false
}

package redis_armor

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/logger"
)

// Shield 创建一个缓存中间件，像盾牌一样保护后端免受重复请求的冲击
// duration: 缓存时间（秒）
// keyGenerator: 缓存键生成函数，根据请求生成唯一的缓存键
func Shield(duration int, keyGenerator func(*gin.Context) string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 如果Redis未启用，直接跳过
		if !common.RedisEnabled {
			c.Next()
			return
		}

		// 生成缓存键
		cacheKey := keyGenerator(c)

		// 尝试从缓存获取
		cachedResponse, err := common.RedisGet(cacheKey)
		if err == nil {
			// 缓存命中
			var response gin.H
			if err := json.Unmarshal([]byte(cachedResponse), &response); err == nil {
				// 添加缓存标记
				if response["data"] != nil {
					if dataMap, ok := response["data"].(map[string]interface{}); ok {
						dataMap["from_cache"] = true
					}
				}
				c.JSON(http.StatusOK, response)
				c.Abort() // 中止后续处理
				return
			}
		}

		// 创建一个自定义的ResponseWriter来捕获响应
		writer := &responseBodyWriter{body: &strings.Builder{}, ResponseWriter: c.Writer}
		c.Writer = writer

		// 处理请求
		c.Next()

		// 如果状态码是200，缓存响应
		if c.Writer.Status() == http.StatusOK {
			responseBody := writer.body.String()

			// 确保响应是JSON格式
			if strings.HasPrefix(responseBody, "{") {
				// 缓存响应
				err := common.RedisSet(cacheKey, responseBody, time.Duration(duration)*time.Second)
				if err != nil {
					logger.SysError("Redis set cache error: " + err.Error())
				}
			}
		}
	}
}

// 自定义ResponseWriter来捕获响应体
type responseBodyWriter struct {
	gin.ResponseWriter
	body *strings.Builder
}

func (r *responseBodyWriter) Write(b []byte) (int, error) {
	r.body.Write(b)
	return r.ResponseWriter.Write(b)
}

// 为日志计数生成缓存键的函数
func LogsCountCacheKey(c *gin.Context) string {
	userId := c.GetInt("id")
	queryParams := c.Request.URL.Query()

	// 构建包含所有查询参数的键
	var keyBuilder strings.Builder
	keyBuilder.WriteString(fmt.Sprintf("logs_count:%d", userId))

	// 添加所有查询参数
	for key, values := range queryParams {
		for _, value := range values {
			keyBuilder.WriteString(fmt.Sprintf(":%s=%s", key, value))
		}
	}

	return keyBuilder.String()
}

// 为用户日志计数生成缓存键的函数
func UserLogsCountCacheKey(c *gin.Context) string {
	userId := c.GetInt("id")
	queryParams := c.Request.URL.Query()

	// 构建包含所有查询参数的键
	var keyBuilder strings.Builder
	keyBuilder.WriteString(fmt.Sprintf("user_logs_count:%d", userId))

	// 添加所有查询参数
	for key, values := range queryParams {
		for _, value := range values {
			keyBuilder.WriteString(fmt.Sprintf(":%s=%s", key, value))
		}
	}

	return keyBuilder.String()
}

// MidjourneyCountCacheKey 为Midjourney计数生成缓存键的函数
func MidjourneyCountCacheKey(c *gin.Context) string {
	queryParams := c.Request.URL.Query()

	// 构建包含所有查询参数的键
	var keyBuilder strings.Builder
	keyBuilder.WriteString("midjourney_count")

	// 添加所有查询参数
	for key, values := range queryParams {
		for _, value := range values {
			keyBuilder.WriteString(fmt.Sprintf(":%s=%s", key, value))
		}
	}

	return keyBuilder.String()
}

// UserMidjourneyCountCacheKey 为用户Midjourney计数生成缓存键的函数
func UserMidjourneyCountCacheKey(c *gin.Context) string {
	userId := c.GetInt("id")
	queryParams := c.Request.URL.Query()

	// 构建包含所有查询参数的键
	var keyBuilder strings.Builder
	keyBuilder.WriteString(fmt.Sprintf("user_midjourney_count:%d", userId))

	// 添加所有查询参数
	for key, values := range queryParams {
		for _, value := range values {
			keyBuilder.WriteString(fmt.Sprintf(":%s=%s", key, value))
		}
	}

	return keyBuilder.String()
}

// DailyUsageStatsCacheKey 为日常使用统计生成缓存键的函数
func DailyUsageStatsCacheKey(c *gin.Context) string {
	queryParams := c.Request.URL.Query()
	timezone := c.GetString("timezone")

	// 构建包含所有查询参数的键
	var keyBuilder strings.Builder
	keyBuilder.WriteString(fmt.Sprintf("daily_usage_stats:%s", timezone))

	// 添加所有查询参数
	for key, values := range queryParams {
		for _, value := range values {
			keyBuilder.WriteString(fmt.Sprintf(":%s=%s", key, value))
		}
	}

	return keyBuilder.String()
}

// UserDailyUsageStatsCacheKey 为用户日常使用统计生成缓存键的函数
func UserDailyUsageStatsCacheKey(c *gin.Context) string {
	userId := c.GetInt("id")
	timezone := c.GetString("timezone")
	queryParams := c.Request.URL.Query()

	// 构建包含所有查询参数的键
	var keyBuilder strings.Builder
	keyBuilder.WriteString(fmt.Sprintf("user_daily_usage_stats:%d:%s", userId, timezone))

	// 添加所有查询参数
	for key, values := range queryParams {
		for _, value := range values {
			keyBuilder.WriteString(fmt.Sprintf(":%s=%s", key, value))
		}
	}

	return keyBuilder.String()
}

// LogsModelUsageCacheKey 为日志模型使用统计生成缓存键的函数
func LogsModelUsageCacheKey(c *gin.Context) string {
	timezone := c.GetString("timezone")
	queryParams := c.Request.URL.Query()

	// 构建包含所有查询参数的键
	var keyBuilder strings.Builder
	keyBuilder.WriteString(fmt.Sprintf("logs_model_usage:%s", timezone))

	// 添加所有查询参数
	for key, values := range queryParams {
		for _, value := range values {
			keyBuilder.WriteString(fmt.Sprintf(":%s=%s", key, value))
		}
	}

	return keyBuilder.String()
}

// AdminStatusCacheKey 为管理员状态生成缓存键的函数
func AdminStatusCacheKey(c *gin.Context) string {
	// adminStatus 接口不依赖用户ID或查询参数，是全局数据
	// 但可以考虑加上时间戳来定期刷新
	return "admin_status:global"
}

// InvalidateAdminStatusCache 清除管理员状态缓存
func InvalidateAdminStatusCache() error {
	if !common.RedisEnabled {
		return nil
	}

	cacheKey := "admin_status:global"
	return common.RDB.Del(context.Background(), cacheKey).Err()
}

// UserLogsModelUsageCacheKey 为用户日志模型使用统计生成缓存键的函数
func UserLogsModelUsageCacheKey(c *gin.Context) string {
	userId := c.GetInt("id")
	queryParams := c.Request.URL.Query()

	// 构建包含所有查询参数的键
	var keyBuilder strings.Builder
	keyBuilder.WriteString(fmt.Sprintf("user_logs_model_usage:%d", userId))

	// 添加所有查询参数
	for key, values := range queryParams {
		for _, value := range values {
			keyBuilder.WriteString(fmt.Sprintf(":%s=%s", key, value))
		}
	}

	return keyBuilder.String()
}

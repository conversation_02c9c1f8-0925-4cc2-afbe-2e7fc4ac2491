package middleware

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/utils"
)

func abortWithMessage(c *gin.Context, statusCode int, message string) {
	originalMessage := message
	message = utils.SanitizeErrorMessage(message)

	if config.LogDownstreamErrorEnabled {
		logMessage := &model.ErrorLogMessage{
			Description: "抛出到下游错误",
			DownstreamError: &model.ErrorInfo{
				StatusCode: statusCode,
				Error: model.ErrorDetailInfo{
					Message: helper.MessageWithRequestId(message, c.GetString(helper.RequestIdKey)),
					Type:    "shell_api_error",
				},
			},
			RequestParams: message,
		}
		logJson, _ := json.Marshal(logMessage)
		model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeDownstreamError, string(logJson), message)
	}

	c.JSON(statusCode, gin.H{
		"error": gin.H{
			"message": helper.MessageWithRequestId(message, c.GetString(helper.RequestIdKey)),
			"type":    "shell_api_error",
		},
	})
	c.Abort()
	logger.Error(c.Request.Context(), originalMessage)
}

// abortWithMessageAndCode aborts the request with a message and a code
func abortWithMessageAndCode(c *gin.Context, statusCode int, code string, message string) {
	originalMessage := message
	message = utils.SanitizeErrorMessage(message)
	c.JSON(statusCode, gin.H{
		"error": gin.H{
			"message":           helper.MessageWithRequestId(message, c.GetString(helper.RequestIdKey)),
			"localized_message": common.GetErrorMessage(code, "zh"),
			"type":              "shell_api_error",
			"code":              code,
		},
	})
	c.Abort()
	logger.Error(c.Request.Context(), originalMessage)
}

func abortWithFullErrorMessage(c *gin.Context, statusCode int, message string, errCode string, errType string) {
	if errType == "" {
		errType = "shell_api_error"
	}
	c.JSON(statusCode, gin.H{
		"error": gin.H{
			"message": helper.MessageWithRequestId(message, c.GetString(helper.RequestIdKey)),
			"type":    errType,
			"code":    errCode,
		},
	})
	c.Abort()
	logger.Error(c.Request.Context(), message)
}

func getRequestModel(c *gin.Context) (string, error) {
	var modelRequest ModelRequest
	err := common.UnmarshalBodyReusable(c, &modelRequest)
	if err != nil {
		return "", fmt.Errorf("common.UnmarshalBodyReusable failed: %w", err)
	}
	if strings.HasPrefix(c.Request.URL.Path, "/v1/moderations") {
		if modelRequest.Model == "" {
			modelRequest.Model = "text-moderation-stable"
		}
	}
	if strings.HasSuffix(c.Request.URL.Path, "embeddings") {
		if modelRequest.Model == "" {
			modelRequest.Model = c.Param("model")
		}
	}
	if strings.HasPrefix(c.Request.URL.Path, "/v1/images/generations") {
		if modelRequest.Model == "" {
			modelRequest.Model = "dall-e-2"
		}
	}
	if strings.HasPrefix(c.Request.URL.Path, "/v1/audio/transcriptions") || strings.HasPrefix(c.Request.URL.Path, "/v1/audio/translations") {
		// 使用通用解析函数处理 whisper 相关请求，支持 formdata 格式
		err = parseRequestWithFormDataSupport(c, &modelRequest, "whisper-1")
		if err != nil {
			return "", fmt.Errorf("parseRequestWithFormDataSupport failed: %w", err)
		}
	}
	return modelRequest.Model, nil
}

func getRequestModelForDr(c *gin.Context) (string, error) {
	var modelRequest ModelRequest
	err := common.UnmarshalBodyReusableOnlyForJson(c, &modelRequest)
	if err != nil {
		return "", fmt.Errorf("common.UnmarshalBodyReusable failed: %w", err)
	}
	if strings.HasPrefix(c.Request.URL.Path, "/v1/moderations") {
		if modelRequest.Model == "" {
			modelRequest.Model = "text-moderation-stable"
		}
	}
	if strings.HasSuffix(c.Request.URL.Path, "embeddings") {
		if modelRequest.Model == "" {
			modelRequest.Model = c.Param("model")
		}
	}
	if strings.HasPrefix(c.Request.URL.Path, "/v1/images/generations") {
		if modelRequest.Model == "" {
			modelRequest.Model = "dall-e-2"
		}
	}
	if strings.HasPrefix(c.Request.URL.Path, "/v1/audio/transcriptions") || strings.HasPrefix(c.Request.URL.Path, "/v1/audio/translations") {
		// 对于 Dr (动态路由) 场景，仍然使用简单的默认值设置，避免复杂的 formdata 解析
		if modelRequest.Model == "" {
			modelRequest.Model = "whisper-1"
		}
	}
	return modelRequest.Model, nil
}

func isModelInList(modelName string, models string) bool {
	modelList := strings.Split(models, ",")
	for _, model := range modelList {
		if modelName == model {
			return true
		}
	}
	return false
}

// LogAndAbortWithError 记录错误日志并中止请求
func LogAndAbortWithError(c *gin.Context, statusCode int, message string) {
	// 原始错误信息
	originalMessage := message
	message = utils.SanitizeErrorMessage(message)

	// 记录下游错误日志
	if config.LogDownstreamErrorEnabled {
		reqBodyBytes, _ := common.GetRequestBody(c)
		reqBody := string(reqBodyBytes)
		logMessage := &model.ErrorLogMessage{
			Description: "抛出到下游错误",
			DownstreamError: &model.ErrorInfo{
				StatusCode: statusCode,
				Error: model.ErrorDetailInfo{
					Message: helper.MessageWithRequestId(message, c.GetString(helper.RequestIdKey)),
					Type:    "shell_api_error",
				},
			},
			RequestParams: reqBody,
		}
		logJson, _ := json.Marshal(logMessage)
		model.RecordSysLogToDBAndFileByGinContext(c, model.LogTypeDownstreamError, string(logJson), reqBody)
	}

	// 返回错误响应
	c.JSON(statusCode, gin.H{
		"error": gin.H{
			"message": helper.MessageWithRequestId(message, c.GetString(helper.RequestIdKey)),
			"type":    "shell_api_error",
		},
	})
	c.Abort()
	logger.Error(c.Request.Context(), originalMessage)
}

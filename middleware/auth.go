package middleware

import (
	"fmt"
	"net"
	"net/http"
	"strings"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/blacklist"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/common/network"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/utils"
)

func authHelper(c *gin.Context, minRole int, permissions ...int64) {
	var username interface{}
	var role interface{}
	var id interface{}
	var status interface{}
	var adminAccessFlags int64
	var requiredPermission int64
	var tokenPermissions int64
	var timezone string
	var userQuota int64
	if len(permissions) > 0 {
		requiredPermission = permissions[0]
	} else {
		requiredPermission = 0
	}
	claims, jwtUserExists := c.Get("claims")
	if jwtUserExists {
		// JWT登录
		claimsMap := claims.(*utils.CustomClaims)
		role = claimsMap.BaseClaims.Role
		id = claimsMap.BaseClaims.ID
		username = claimsMap.BaseClaims.Username
		adminAccessFlags = claimsMap.BaseClaims.AdminAccessFlags
		timezone = claimsMap.BaseClaims.Timezone
	} else {
		session := sessions.Default(c)
		username = session.Get("username")
		role = session.Get("role")
		id = session.Get("id")
		status = session.Get("status")
		adminAccessFlagsInterface := session.Get("admin_access_flags")
		if af, ok := adminAccessFlagsInterface.(int64); ok {
			adminAccessFlags = af
		} else {
			// 如果转换失败,可以设置一个默认值或者返回错误
			adminAccessFlags = 0 // 或者根据实际情况处理
		}
	}
	if username == nil {
		// Check access token
		accessToken := c.Request.Header.Get("Authorization")
		if accessToken == "" {
			// 打印详细的日志信息
			logger.SysDebug("无权进行此操作,未登录且未提供 access token")
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "无权进行此操作,未登录且未提供 access token",
			})
			c.Abort()
			return
		}

		// 先尝试使用新的访问令牌系统
		user, permissions, err := model.ValidateAccessTokenPermissions(accessToken)
		if err == nil && user != nil && user.Username != "" {
			// Token is valid
			username = user.Username
			role = user.Role
			id = user.Id
			status = user.Status
			adminAccessFlags = user.AdminAccessFlags
			timezone = user.Timezone
			userQuota = user.Quota
			tokenPermissions = permissions

			// 检查新令牌系统的权限
			// 对于基本操作权限的检查
			pathRequiresWrite := false
			method := c.Request.Method
			if method == "POST" || method == "PUT" || method == "PATCH" || method == "DELETE" {
				pathRequiresWrite = true
			}

			if pathRequiresWrite && !model.HasTokenPermission(permissions, model.PermWrite) {
				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"message": "无权进行此操作,当前访问令牌不具备写入权限",
				})
				c.Abort()
				return
			} else if !pathRequiresWrite && !model.HasTokenPermission(permissions, model.PermRead) {
				// 非写入操作需要检查读取权限
				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"message": "无权进行此操作,当前访问令牌不具备读取权限",
				})
				c.Abort()
				return
			}

			// 检查是否具有特定资源的权限
			path := c.Request.URL.Path
			if strings.Contains(path, "/api/token") && !model.HasTokenPermission(permissions, model.PermToken) {
				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"message": "无权进行此操作,当前访问令牌不具备令牌管理权限",
				})
				c.Abort()
				return
			} else if strings.Contains(path, "/api/channel") && !model.HasTokenPermission(permissions, model.PermChannel) {
				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"message": "无权进行此操作,当前访问令牌不具备渠道管理权限",
				})
				c.Abort()
				return
			} else if strings.Contains(path, "/api/log") && !model.HasTokenPermission(permissions, model.PermLog) {
				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"message": "无权进行此操作,当前访问令牌不具备日志查看权限",
				})
				c.Abort()
				return
			} else if strings.Contains(path, "/api/stat") && !model.HasTokenPermission(permissions, model.PermStatistics) {
				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"message": "无权进行此操作,当前访问令牌不具备统计信息查看权限",
				})
				c.Abort()
				return
			} else if strings.Contains(path, "/api/user") && path != "/api/user/self" && !model.HasTokenPermission(permissions, model.PermUserAdmin) {
				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"message": "无权进行此操作,当前访问令牌不具备用户管理权限",
				})
				c.Abort()
				return
			} else if strings.Contains(path, "/api/topup") && !model.HasTokenPermission(permissions, model.PermQuota) {
				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"message": "无权进行此操作,当前访问令牌不具备额度管理权限",
				})
				c.Abort()
				return
			}
		} else {
			// 回退到旧系统
			user := model.ValidateAccessToken(accessToken)
			if user != nil && user.Username != "" {
				// Token is valid
				username = user.Username
				role = user.Role
				id = user.Id
				status = user.Status
				adminAccessFlags = user.AdminAccessFlags
				timezone = user.Timezone
				userQuota = user.Quota
				tokenPermissions = model.PermFull // 旧系统令牌拥有全部权限
			} else {
				c.JSON(http.StatusOK, gin.H{
					"success": false,
					"message": "无权进行此操作,access token 无效",
				})
				c.Abort()
				return
			}
		}
	} else {
		// 不是accessToken登录,是用session维护登录状态,则需要校验当前用户在数据库中的角色,避免管理员修改用户角色或状态后导致session延迟
		user, _ := model.CacheGetUserById(id.(int), false)
		if user != nil {
			role = user.Role
			status = user.Status
			adminAccessFlags = user.AdminAccessFlags
			timezone = user.Timezone
			userQuota = user.Quota
		}
	}
	if status.(int) == model.UserStatusDisabled || blacklist.IsUserBanned(id.(int)) {
		// 打印详细的日志信息
		if status.(int) == model.UserStatusDisabled {
			logger.SysDebug(fmt.Sprintf("用户已被禁用model.UserStatusDisabled, id: %d, username: %s", id, username))
		} else {
			logger.SysDebug(fmt.Sprintf("用户已被封禁blacklist.IsUserBanned, id: %d, username: %s", id, username))
		}
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户已被封禁",
		})
		session := sessions.Default(c)
		session.Clear()
		_ = session.Save()
		c.Abort()
		return
	}
	// 优化权限检查逻辑
	if role.(int) < minRole {
		if role.(int) == model.RoleAdminUser && requiredPermission != 0 {
			if !HasPermission(adminAccessFlags, requiredPermission) {
				// 打印详细的日志信息
				logger.SysDebug(fmt.Sprintf("用户权限不足, id: %d, username: %s, role: %d, requiredPermission: %d", id, username, role, requiredPermission))
				c.JSON(http.StatusUnauthorized, gin.H{
					"success": false,
					"message": "无权进行此操作,权限不足",
				})
				c.Abort()
				return
			}
		} else {
			// 打印详细的日志信息
			logger.SysDebug(fmt.Sprintf("用户权限不足, id: %d, username: %s, role: %d, minRole: %d", id, username, role, minRole))
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "无权进行此操作,权限不足",
			})
			c.Abort()
			return
		}
	}
	c.Set(ctxkey.Username, username)
	c.Set("role", role)
	c.Set("id", id)
	c.Set("timezone", timezone)
	c.Set(ctxkey.UserQuota, userQuota)
	c.Set("token_permissions", tokenPermissions) // 设置令牌权限
	c.Set("admin_access_flags", adminAccessFlags)
	c.Next()
}

func UserAuth() func(c *gin.Context) {
	return func(c *gin.Context) {
		authHelper(c, model.RoleCommonUser)
	}
}

func AdminAuth() func(c *gin.Context) {
	return func(c *gin.Context) {
		authHelper(c, model.RoleAdminUser)
	}
}

func RootAuth(adminAccessFlags ...int64) func(c *gin.Context) {
	return func(c *gin.Context) {
		authHelper(c, model.RoleRootUser, adminAccessFlags...)
	}
}

func AgencyAuth() func(c *gin.Context) {
	return func(c *gin.Context) {
		authHelper(c, model.RoleAgencyUser)
	}
}

func TokenAuth() func(c *gin.Context) {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		jwtUserInitialToken := c.GetString("jwt_user_initial_token")
		key := c.Request.Header.Get("Authorization")
		if strings.HasPrefix(c.Request.URL.Path, "/mj") {
			// 兼容mj传头Mj-Api-Secret
			if key == "" {
				key = c.Request.Header.Get("Mj-Api-Secret")
			}
		} else if strings.HasSuffix(c.Request.URL.Path, "/v1/messages") {
			// 兼容claude传头x-api-key
			if key == "" {
				key = c.Request.Header.Get("x-api-key")
			}
			c.Set(ctxkey.IsV1MessagesPath, true)
		} else if strings.HasSuffix(c.Request.URL.Path, "/search/serper") {
			// 兼容serper传头x-api-key
			if key == "" {
				key = c.Request.Header.Get("x-api-key")
			}
		} else if strings.HasSuffix(c.Request.URL.Path, "/v1/realtime") {
			// 考虑从sec-websocket-protocol: realtime, openai-insecure-api-key.sk-xxxxx, openai-beta.realtime-v1 获取
			// 从sec-websocket-protocol: openai-insecure-api-key.sk-xxxx 获取
			secWebSocketProtocol := c.Request.Header.Get("Sec-WebSocket-Protocol")
			if secWebSocketProtocol != "" {
				protocols := strings.Split(secWebSocketProtocol, ",")
				for _, protocol := range protocols {
					protocol = strings.TrimSpace(protocol)
					if strings.HasPrefix(protocol, "openai-insecure-api-key") {
						tmpKey := strings.TrimPrefix(protocol, "openai-insecure-api-key.")
						if tmpKey != "" {
							key = tmpKey
							// 添加到请求头
							c.Request.Header.Set("Authorization", "Bearer "+key)
							break
						}
					}
				}
			}

		}
		isJwtToken := false
		if jwtUserInitialToken != "" {
			// 未提供令牌,使用jwt用户的初始令牌
			key = jwtUserInitialToken
			isJwtToken = true
		}
		key = strings.TrimPrefix(key, "Bearer ")
		key = strings.TrimPrefix(key, "sk-")
		parts := strings.Split(key, "-")
		key = parts[0]
		token, tokenExtend, err := model.ValidateUserToken2(c, key, jwtUserInitialToken, isJwtToken)
		if err != nil {
			LogAndAbortWithError(c, http.StatusUnauthorized, err.Error())
			return
		}
		if token.Subnet != nil && *token.Subnet != "" {
			if !network.IsIpInSubnets(ctx, c.ClientIP(), *token.Subnet) {
				LogAndAbortWithError(c, http.StatusForbidden, fmt.Sprintf("该令牌只能在指定网段使用：%s，当前 ip：%s", *token.Subnet, c.ClientIP()))
				return
			}
		}
		userEnabled, err := model.CacheIsUserEnabled(token.UserId)
		// 校验sk上配置的ip白名单
		if token.IpWhitelist != "" && token.IpWhitelist != "*" {
			ip := helper.GetClientRealIp(c)
			flag := false
			ips, ipNets, err := helper.ParseIPList(token.IpWhitelist)
			if err != nil {
				LogAndAbortWithError(c, http.StatusUnauthorized, "Parse CIDR error")
				return
			}
			for i, ipItem := range ips {
				fmt.Println("IP:", *ipItem)
				fmt.Println("IPNet:", ipNets[i])
				// 对比ip和ipItem的值是否相同
				if ipItem.Equal(net.ParseIP(ip)) {
					flag = true
					break
				}
			}
			if !flag {
				abortWithMessageAndCode(c, http.StatusUnauthorized, "unauthorized_ip_address", "Unauthorized IP address")
				return
			}
		}
		user, err := model.GetCacheUser(token.UserId)
		if err != nil {
			LogAndAbortWithError(c, http.StatusInternalServerError, err.Error())
			return
		}
		if !userEnabled || blacklist.IsUserBanned(token.UserId) {
			LogAndAbortWithError(c, http.StatusForbidden, "用户已被封禁")
			return
		}
		c.Set(ctxkey.TokenName, token.Name)
		c.Set(ctxkey.TokenGroup, token.Group)
		c.Set(ctxkey.TokenKey, token.Key)
		requestModel, err := getRequestModel(c)
		if err != nil && shouldCheckModel(c) {
			LogAndAbortWithError(c, http.StatusBadRequest, err.Error())
			return
		}
		c.Set(ctxkey.RequestModel, requestModel)
		if token.Models != "" {
			c.Set(ctxkey.AvailableModels, token.Models)
			if requestModel != "" && !isModelInList(requestModel, token.Models) {
				LogAndAbortWithError(c, http.StatusForbidden, fmt.Sprintf("该令牌无权使用模型：%s", requestModel))
				return
			}
		}
		// 在 TokenAuth 函数中，在设置 token 相关的上下文之后添加以下代码
		if token.Group != "" {
			group, err := model.CacheGetGroupByName(token.Group)
			if err != nil {
				LogAndAbortWithError(c, http.StatusInternalServerError, "获取用户组信息失败")
				return
			}
			if group == nil {
				LogAndAbortWithError(c, http.StatusForbidden, "令牌所属用户组不存在")
				return
			}
			if !group.GetIsSelectable() {
				LogAndAbortWithError(c, http.StatusForbidden, "令牌所属用户组不可选")
				return
			}
			// 获取当前用户的分组倍率和充值倍率
			userGroup, err := model.CacheGetGroupByName(user.Group)

			// 如果当前用户是0倍率分组，但是请求的模型是有倍率的，那么就不允许
			if (userGroup.GroupRatio == 0 || userGroup.TopupGroupRatio == 0) && group.GroupRatio != 0 && group.TopupGroupRatio != 0 {
				LogAndAbortWithError(c, http.StatusForbidden, "当前用户组倍率为0,不支持切换使用有倍率的用户组")
				return
			}
			// 如果验证通过，可以将 group 信息设置到上下文中
			c.Set(ctxkey.TokenGroup, group.Name)
		}
		c.Set(ctxkey.Id, token.UserId)
		c.Set("user_rate_limit", user.RateLimit)
		c.Set("user_rate_limit_exceeded_message", user.RateLimitExceededMessage)
		c.Set(ctxkey.TokenId, token.Id)
		c.Set("token_key", token.Key)
		c.Set(ctxkey.TokenName, token.Name)
		c.Set("token_ability_models", token.Models)
		c.Set("token_billing_type", token.BillingType)
		c.Set("token_ad_position", token.AdPosition)
		c.Set("token_advertisement", token.Advertisement)
		c.Set("token_rate_limit_num", tokenExtend.RateLimitNum)
		c.Set("token_rate_limit_duration", tokenExtend.RateLimitDuration)
		c.Set("token_rate_limit_exceeded_message", tokenExtend.RateLimitExceededMessage)
		c.Set("mj_discord_proxy_url", tokenExtend.MjDiscordProxyUrl)
		// 设置 MJ 翻译相关配置
		c.Set("mj_translate_enabled", tokenExtend.MjTranslateEnabled)
		c.Set("mj_translate_base_url", tokenExtend.MjTranslateBaseUrl)
		c.Set("mj_translate_api_key", tokenExtend.MjTranslateApiKey)
		c.Set("mj_translate_model", tokenExtend.MjTranslateModel)
		// 设置token级别的重试计费类型一致性配置
		c.Set("token_retry_keep_billing_type_enabled", tokenExtend.RetryKeepBillingTypeEnabled)
		// 设置整个tokenExtend对象到context，方便控制器调用方法
		c.Set("token_extend", tokenExtend)
		if token.AdPosition != 0 && token.AdPosition != -1 && token.Advertisement != "" {
			c.Set("has_token_ad", 1)
		}
		if len(parts) > 1 {
			if model.IsAdmin(token.UserId) {
				c.Set("channelId", parts[1])
				c.Set(ctxkey.SpecificChannelId, parts[1])
			} else {
				LogAndAbortWithError(c, http.StatusForbidden, "普通用户不支持指定渠道")
				return
			}
		}

		// set channel id for proxy relay
		if channelId := c.Param("channelid"); channelId != "" {
			c.Set(ctxkey.SpecificChannelId, channelId)
		}

		c.Next()
	}
}

func DynamicRouterTokenAuth() func(c *gin.Context) {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		jwtUserInitialToken := c.GetString("jwt_user_initial_token")
		key := c.Request.Header.Get("Authorization")
		if strings.HasPrefix(c.Request.URL.Path, "/mj") {
			// 兼容mj传头Mj-Api-Secret
			if key == "" {
				key = c.Request.Header.Get("Mj-Api-Secret")
			}
		} else if strings.HasSuffix(c.Request.URL.Path, "/v1/messages") {
			// 兼容claude传头x-api-key
			if key == "" {
				key = c.Request.Header.Get("x-api-key")
			}
			c.Set(ctxkey.IsV1MessagesPath, true)
		} else if strings.HasSuffix(c.Request.URL.Path, "/v1/realtime") {
			// 考虑从sec-websocket-protocol: realtime, openai-insecure-api-key.sk-xxxxx, openai-beta.realtime-v1 获取
			// 从sec-websocket-protocol: openai-insecure-api-key.sk-xxxx 获取
			secWebSocketProtocol := c.Request.Header.Get("Sec-WebSocket-Protocol")
			if secWebSocketProtocol != "" {
				protocols := strings.Split(secWebSocketProtocol, ",")
				for _, protocol := range protocols {
					protocol = strings.TrimSpace(protocol)
					if strings.HasPrefix(protocol, "openai-insecure-api-key") {
						tmpKey := strings.TrimPrefix(protocol, "openai-insecure-api-key.")
						if tmpKey != "" {
							key = tmpKey
							// 添加到请求头
							c.Request.Header.Set("Authorization", "Bearer "+key)
							break
						}
					}
				}
			}

		}
		isJwtToken := false
		if jwtUserInitialToken != "" {
			// 未提供令牌,使用jwt用户的初始令牌
			key = jwtUserInitialToken
			isJwtToken = true
		}
		key = strings.TrimPrefix(key, "Bearer ")
		key = strings.TrimPrefix(key, "sk-")
		parts := strings.Split(key, "-")
		key = parts[0]
		token, tokenExtend, err := model.ValidateUserToken2(c, key, jwtUserInitialToken, isJwtToken)
		if err != nil {
			LogAndAbortWithError(c, http.StatusUnauthorized, err.Error())
			return
		}
		if token.Subnet != nil && *token.Subnet != "" {
			if !network.IsIpInSubnets(ctx, c.ClientIP(), *token.Subnet) {
				LogAndAbortWithError(c, http.StatusForbidden, fmt.Sprintf("该令牌只能在指定网段使用：%s，当前 ip：%s", *token.Subnet, c.ClientIP()))
				return
			}
		}
		userEnabled, err := model.CacheIsUserEnabled(token.UserId)
		// 校验sk上配置的ip白名单
		if token.IpWhitelist != "" && token.IpWhitelist != "*" {
			ip := helper.GetClientRealIp(c)
			flag := false
			ips, ipNets, err := helper.ParseIPList(token.IpWhitelist)
			if err != nil {
				LogAndAbortWithError(c, http.StatusUnauthorized, "Parse CIDR error")
				return
			}
			for i, ipItem := range ips {
				fmt.Println("IP:", *ipItem)
				fmt.Println("IPNet:", ipNets[i])
				// 对比ip和ipItem的值是否相同
				if ipItem.Equal(net.ParseIP(ip)) {
					flag = true
					break
				}
			}
			if !flag {
				abortWithMessageAndCode(c, http.StatusUnauthorized, "unauthorized_ip_address", "Unauthorized IP address")
				return
			}
		}
		user, err := model.GetCacheUser(token.UserId)
		if err != nil {
			LogAndAbortWithError(c, http.StatusInternalServerError, err.Error())
			return
		}
		if !userEnabled || blacklist.IsUserBanned(token.UserId) {
			LogAndAbortWithError(c, http.StatusForbidden, "用户已被封禁")
			return
		}
		requestModel, err := getRequestModelForDr(c)
		if err != nil && shouldCheckModel(c) {
			LogAndAbortWithError(c, http.StatusBadRequest, err.Error())
			return
		}
		c.Set(ctxkey.RequestModel, requestModel)
		if token.Models != "" {
			c.Set(ctxkey.AvailableModels, token.Models)
			if requestModel != "" && !isModelInList(requestModel, token.Models) {
				LogAndAbortWithError(c, http.StatusForbidden, fmt.Sprintf("该令牌无权使用模型：%s", requestModel))
				return
			}
		}
		// 在 TokenAuth 函数中，在设置 token 相关的上下文之后添加以下代码
		if token.Group != "" {
			group, err := model.CacheGetGroupByName(token.Group)
			if err != nil {
				LogAndAbortWithError(c, http.StatusInternalServerError, "获取用户组信息失败")
				return
			}
			if group == nil {
				LogAndAbortWithError(c, http.StatusForbidden, "令牌所属用户组不存在")
				return
			}
			if !group.GetIsSelectable() {
				LogAndAbortWithError(c, http.StatusForbidden, "令牌所属用户组不可选")
				return
			}
			// 获取当前用户的分组倍率和充值倍率
			userGroup, err := model.CacheGetGroupByName(user.Group)

			// 如果当前用户是0倍率分组，但是请求的模型是有倍率的，那么就不允许
			if (userGroup.GroupRatio == 0 || userGroup.TopupGroupRatio == 0) && group.GroupRatio != 0 && group.TopupGroupRatio != 0 {
				LogAndAbortWithError(c, http.StatusForbidden, "当前用户组倍率为0,不支持切换使用有倍率的用户组")
				return
			}
			// 如果验证通过，可以将 group 信息设置到上下文中
			c.Set(ctxkey.TokenGroup, group.Name)
		}
		c.Set(ctxkey.Id, token.UserId)
		c.Set("user_rate_limit", user.RateLimit)
		c.Set("user_rate_limit_exceeded_message", user.RateLimitExceededMessage)
		c.Set(ctxkey.TokenId, token.Id)
		c.Set("token_key", token.Key)
		c.Set(ctxkey.TokenName, token.Name)
		c.Set("token_ability_models", token.Models)
		c.Set("token_billing_type", token.BillingType)
		c.Set("token_ad_position", token.AdPosition)
		c.Set("token_advertisement", token.Advertisement)
		c.Set("token_rate_limit_num", tokenExtend.RateLimitNum)
		c.Set("token_rate_limit_duration", tokenExtend.RateLimitDuration)
		c.Set("token_rate_limit_exceeded_message", tokenExtend.RateLimitExceededMessage)
		c.Set("mj_discord_proxy_url", tokenExtend.MjDiscordProxyUrl)
		// 设置 MJ 翻译相关配置
		c.Set("mj_translate_enabled", tokenExtend.MjTranslateEnabled)
		c.Set("mj_translate_base_url", tokenExtend.MjTranslateBaseUrl)
		c.Set("mj_translate_api_key", tokenExtend.MjTranslateApiKey)
		c.Set("mj_translate_model", tokenExtend.MjTranslateModel)
		// 设置token级别的重试计费类型一致性配置
		c.Set("token_retry_keep_billing_type_enabled", tokenExtend.RetryKeepBillingTypeEnabled)
		// 设置整个tokenExtend对象到context，方便控制器调用方法
		c.Set("token_extend", tokenExtend)
		if token.AdPosition != 0 && token.AdPosition != -1 && token.Advertisement != "" {
			c.Set("has_token_ad", 1)
		}
		if len(parts) > 1 {
			if model.IsAdmin(token.UserId) {
				c.Set("channelId", parts[1])
				c.Set(ctxkey.SpecificChannelId, parts[1])
			} else {
				LogAndAbortWithError(c, http.StatusForbidden, "普通用户不支持指定渠道")
				return
			}
		}

		// set channel id for proxy relay
		if channelId := c.Param("channelid"); channelId != "" {
			c.Set(ctxkey.SpecificChannelId, channelId)
		}

		c.Next()
	}
}

func PublicTokenAuthForQuery() func(c *gin.Context) {
	return func(c *gin.Context) {
		key := c.Request.Header.Get("Authorization")
		if strings.HasPrefix(c.Request.URL.Path, "/mj") {
			// 兼容mj传头Mj-Api-Secret
			if key == "" {
				key = c.Request.Header.Get("Mj-Api-Secret")
			}
		}
		key = strings.TrimPrefix(key, "Bearer ")
		key = strings.TrimPrefix(key, "sk-")
		parts := strings.Split(key, "-")
		key = parts[0]
		token, _, err := model.CacheGetTokenByKey(key)
		if err != nil {
			LogAndAbortWithError(c, http.StatusUnauthorized, err.Error())
			return
		}
		// 校验sk上配置的ip白名单
		if token.IpWhitelist != "" && token.IpWhitelist != "*" {
			ip := helper.GetClientRealIp(c)
			flag := false
			ips, ipNets, err := helper.ParseIPList(token.IpWhitelist)
			if err != nil {
				LogAndAbortWithError(c, http.StatusUnauthorized, "Parse CIDR error")
				return
			}
			for i, ipItem := range ips {
				fmt.Println("IP:", *ipItem)
				fmt.Println("IPNet:", ipNets[i])
				// 对比ip和ipItem的值是否相同
				if ipItem.Equal(net.ParseIP(ip)) {
					flag = true
					break
				}
			}
			if !flag {
				abortWithMessageAndCode(c, http.StatusUnauthorized, "unauthorized_ip_address", "Unauthorized IP address")
				return
			}
		}
		user, err := model.GetCacheUser(token.UserId)
		if err != nil {
			LogAndAbortWithError(c, http.StatusInternalServerError, err.Error())
			return
		}
		userEnabled := user.Status == common.UserStatusEnabled
		if err != nil {
			LogAndAbortWithError(c, http.StatusInternalServerError, err.Error())
			return
		}
		if !userEnabled {
			LogAndAbortWithError(c, http.StatusForbidden, "用户已被封禁")
			return
		}
		c.Set("id", token.UserId)
		c.Set("user_rate_limit", user.RateLimit)
		c.Set("user_rate_limit_exceeded_message", user.RateLimitExceededMessage)
		c.Set("token_id", token.Id)
		c.Set("token_key", token.Key)
		c.Set("token_name", token.Name)
		c.Set("token_ability_models", token.Models)
		c.Set("token_billing_type", token.BillingType)
		c.Set("token_ad_position", token.AdPosition)
		c.Set("token_advertisement", token.Advertisement)
		if token.AdPosition != 0 && token.AdPosition != -1 && token.Advertisement != "" {
			c.Set("has_token_ad", 1)
		}
		if len(parts) > 1 {
			if user.Role >= common.RoleAdminUser {
				c.Set("channelId", parts[1])
			} else {
				LogAndAbortWithError(c, http.StatusForbidden, "普通用户不支持指定渠道")
				return
			}
		}
		c.Next()
	}
}

func LicenseAuth() func(c *gin.Context) {
	return func(c *gin.Context) {
		if !config.LicenseEnabled {
			abortWithFullErrorMessage(c, http.StatusForbidden, " Shell API 授权码无效或已过期 ", "shell_api_license_invalid", "")
			return
		}
		c.Next()
	}
}

// OptionalUserAuth 可选的用户认证中间件，如果用户已登录则设置用户信息，未登录则继续执行
func OptionalUserAuth() func(c *gin.Context) {
	return func(c *gin.Context) {
		var username interface{}
		var role interface{}
		var id interface{}
		var status interface{}
		var adminAccessFlags int64
		var timezone interface{}
		var userQuota int64
		var tokenPermissions int64 = model.PermFull

		// 检查JWT登录
		claims, jwtUserExists := c.Get("claims")
		if jwtUserExists {
			// JWT登录
			claimsMap := claims.(*utils.CustomClaims)
			role = claimsMap.BaseClaims.Role
			id = claimsMap.BaseClaims.ID
			username = claimsMap.BaseClaims.Username
			adminAccessFlags = claimsMap.BaseClaims.AdminAccessFlags
			timezone = claimsMap.BaseClaims.Timezone
		} else {
			// 检查Session登录
			session := sessions.Default(c)
			username = session.Get("username")
			role = session.Get("role")
			id = session.Get("id")
			status = session.Get("status")
			adminAccessFlagsInterface := session.Get("adminAccessFlags")
			if af, ok := adminAccessFlagsInterface.(int64); ok {
				adminAccessFlags = af
			} else {
				adminAccessFlags = 0
			}
		}

		// 如果没有session登录，检查access token
		if username == nil {
			accessToken := c.Request.Header.Get("Authorization")
			if accessToken != "" {
				// 先尝试使用新的访问令牌系统
				user, permissions, err := model.ValidateAccessTokenPermissions(accessToken)
				if err == nil && user != nil && user.Username != "" {
					// Token is valid
					username = user.Username
					role = user.Role
					id = user.Id
					status = user.Status
					adminAccessFlags = user.AdminAccessFlags
					timezone = user.Timezone
					userQuota = user.Quota
					tokenPermissions = permissions
				} else {
					// 回退到旧系统
					user := model.ValidateAccessToken(accessToken)
					if user != nil && user.Username != "" {
						// Token is valid
						username = user.Username
						role = user.Role
						id = user.Id
						status = user.Status
						adminAccessFlags = user.AdminAccessFlags
						timezone = user.Timezone
						userQuota = user.Quota
						tokenPermissions = model.PermFull // 旧系统令牌拥有全部权限
					}
				}
			}
		} else {
			// 不是accessToken登录,是用session维护登录状态,则需要校验当前用户在数据库中的角色,避免管理员修改用户角色或状态后导致session延迟
			user, _ := model.CacheGetUserById(id.(int), false)
			if user != nil {
				role = user.Role
				status = user.Status
				adminAccessFlags = user.AdminAccessFlags
				timezone = user.Timezone
				userQuota = user.Quota
			}
		}

		// 如果用户已登录，设置上下文信息
		if username != nil && id != nil {
			// 检查用户状态（如果已登录）
			if status != nil && (status.(int) == model.UserStatusDisabled || blacklist.IsUserBanned(id.(int))) {
				// 用户被禁用，清除登录状态但不阻止请求
				session := sessions.Default(c)
				session.Clear()
				_ = session.Save()
			} else {
				// 设置用户信息到上下文
				c.Set(ctxkey.Username, username)
				c.Set("role", role)
				c.Set("id", id)
				c.Set("timezone", timezone)
				c.Set(ctxkey.UserQuota, userQuota)
				c.Set("token_permissions", tokenPermissions)
				c.Set("admin_access_flags", adminAccessFlags)
			}
		}

		c.Next()
	}
}

func shouldCheckModel(c *gin.Context) bool {
	if strings.HasPrefix(c.Request.URL.Path, "/v1/completions") {
		return true
	}
	if strings.HasPrefix(c.Request.URL.Path, "/v1/messages") {
		return true
	}
	if strings.HasPrefix(c.Request.URL.Path, "/v1/chat/completions") {
		return true
	}
	if strings.HasPrefix(c.Request.URL.Path, "/v1/images") {
		return true
	}
	if strings.HasPrefix(c.Request.URL.Path, "/v1/audio") {
		return true
	}
	return false
}

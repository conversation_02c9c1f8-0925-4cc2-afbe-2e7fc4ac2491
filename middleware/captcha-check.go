package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"net/http"
	"strings"
)

type captchaCheckResponse struct {
	Success bool `json:"success"`
}

func CaptchaCheck() gin.HandlerFunc {
	return func(c *gin.Context) {
		if config.CaptchaCheckEnabled && !config.DebugEnabled {
			if checkCaptchaAndAborted(c, "default") {
				return
			}
		}
		c.Next()
	}
}

func CheckinCaptchaCheck() gin.HandlerFunc {
	return func(c *gin.Context) {
		if config.CaptchaCheckEnabled && !config.DebugEnabled {
			if checkCaptchaAndAborted(c, "checkin") {
				return
			}
		}
		c.Next()
	}
}

func RegisterCaptchaCheck() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 邮箱开启验证则不需要再校验验证码,只校验邮箱验证码,避免2次输入验证码的尴尬
		if config.CaptchaCheckEnabled && !config.EmailVerificationEnabled {
			if checkCaptchaAndAborted(c, "register") {
				return
			}
		}
		c.Next()
	}
}

func checkCaptchaAndAborted(c *gin.Context, actionType string) bool {
	// 获取验证码参数
	captchaId := c.Query("captchaId")
	captcha := c.Query("captcha")
	if captchaId == "" || captcha == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "验证码为空",
		})
		c.Abort()
		return true
	}
	// 验证码校验
	storedCapt := ""
	switch actionType {
	case "checkin":
		if common.RedisEnabled {
			storedCapt = common.CaptchaRedisCheckinStore.Get(captchaId, true)
		} else {
			storedCapt = common.CheckinStore.Get(captchaId, true)
		}
		break
	default:
		if common.RedisEnabled {
			storedCapt = common.CaptchaRedisStore.Get(captchaId, true)
		} else {
			storedCapt = common.Store.Get(captchaId, true)
		}
	}
	if storedCapt == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "验证码不存在或已过期",
		})
		c.Abort()
		return true
	}
	// 判定验证码是否正确,不区分 大小写
	if strings.ToLower(captcha) != strings.ToLower(storedCapt) {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "验证码错误",
		})
		c.Abort()
		return true
	}
	return false
}

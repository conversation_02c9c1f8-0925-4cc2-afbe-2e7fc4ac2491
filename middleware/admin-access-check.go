// Package middleware admin-access-check.go - 管理员授权权限检查中间件
package middleware

const (
	// 原有权限常量 - 保持不变，确保向后兼容
	PermissionChannel     = 1 << 0 // 000001 渠道&渠道组权限
	PermissionLog         = 1 << 1 // 000010 日志权限
	PermissionLogDetail   = 1 << 2 // 000100 详细日志权限
	PermissionRedemption  = 1 << 3 // 001000 兑换码权限
	PermissionUser        = 1 << 4 // 010000 用户权限
	PermissionPackagePlan = 1 << 5 // 100000 套餐权限
	PermissionSetting     = 1 << 6 // 1000000 系统配置权限
	PermissionAbility     = 1 << 7 // 10000000 能力权限
	PermissionChannelKey  = 1 << 8 // 100000000 渠道密钥查看权限

	// 新增读写分离权限 - 从第9位开始，避免与现有权限冲突
	// 渠道读写权限
	PermissionChannelRead  = 1 << 9  // 渠道查看权限
	PermissionChannelWrite = 1 << 10 // 渠道编辑权限

	// 兑换码读写权限
	PermissionRedemptionRead  = 1 << 11 // 兑换码查看权限
	PermissionRedemptionWrite = 1 << 12 // 兑换码编辑权限

	// 用户读写权限
	PermissionUserRead  = 1 << 13 // 用户查看权限
	PermissionUserWrite = 1 << 14 // 用户编辑权限

	// 套餐读写权限
	PermissionPackagePlanRead  = 1 << 15 // 套餐查看权限
	PermissionPackagePlanWrite = 1 << 16 // 套餐编辑权限

	// 系统配置读写权限
	PermissionSettingRead  = 1 << 17 // 系统配置查看权限
	PermissionSettingWrite = 1 << 18 // 系统配置编辑权限

	// 能力读写权限
	PermissionAbilityRead  = 1 << 19 // 能力查看权限
	PermissionAbilityWrite = 1 << 20 // 能力编辑权限
)

// HasPermission 是否有权限
func HasPermission(adminAccessFlags int64, permission int64) bool {
	return adminAccessFlags&permission == permission
}

// HasReadPermission 检查是否有读权限
// 优先级：如果设置了新的读写权限，则忽略旧权限；否则使用旧权限
func HasReadPermission(adminAccessFlags int64, oldPermission, readPermission int64) bool {
	// 检查是否设置了任何新的读写权限（读权限或写权限）
	hasNewPermissions := HasPermission(adminAccessFlags, readPermission) ||
		HasPermission(adminAccessFlags, getCorrespondingWritePermission(readPermission))

	if hasNewPermissions {
		// 如果设置了新权限，只检查新的读权限
		return HasPermission(adminAccessFlags, readPermission)
	} else {
		// 如果没有设置新权限，使用旧权限（向后兼容）
		return HasPermission(adminAccessFlags, oldPermission)
	}
}

// HasWritePermission 检查是否有写权限
// 优先级：如果设置了新的读写权限，则忽略旧权限；否则使用旧权限
func HasWritePermission(adminAccessFlags int64, oldPermission, writePermission int64) bool {
	// 检查是否设置了任何新的读写权限（读权限或写权限）
	hasNewPermissions := HasPermission(adminAccessFlags, writePermission) ||
		HasPermission(adminAccessFlags, getCorrespondingReadPermission(writePermission))

	if hasNewPermissions {
		// 如果设置了新权限，只检查新的写权限
		return HasPermission(adminAccessFlags, writePermission)
	} else {
		// 如果没有设置新权限，使用旧权限（向后兼容）
		return HasPermission(adminAccessFlags, oldPermission)
	}
}

// getCorrespondingWritePermission 根据读权限获取对应的写权限
func getCorrespondingWritePermission(readPermission int64) int64 {
	switch readPermission {
	case PermissionChannelRead:
		return PermissionChannelWrite
	case PermissionRedemptionRead:
		return PermissionRedemptionWrite
	case PermissionUserRead:
		return PermissionUserWrite
	case PermissionPackagePlanRead:
		return PermissionPackagePlanWrite
	case PermissionSettingRead:
		return PermissionSettingWrite
	case PermissionAbilityRead:
		return PermissionAbilityWrite
	default:
		return 0
	}
}

// getCorrespondingReadPermission 根据写权限获取对应的读权限
func getCorrespondingReadPermission(writePermission int64) int64 {
	switch writePermission {
	case PermissionChannelWrite:
		return PermissionChannelRead
	case PermissionRedemptionWrite:
		return PermissionRedemptionRead
	case PermissionUserWrite:
		return PermissionUserRead
	case PermissionPackagePlanWrite:
		return PermissionPackagePlanRead
	case PermissionSettingWrite:
		return PermissionSettingRead
	case PermissionAbilityWrite:
		return PermissionAbilityRead
	default:
		return 0
	}
}

package model

import (
	"errors"

	"github.com/songquanpeng/one-api/common/helper"
)

type Agency struct {
	Id                      int     `json:"id" gorm:"primaryKey"`
	UserId                  int     `json:"user_id" gorm:"uniqueIndex"`
	Name                    string  `json:"name"`
	Logo                    string  `json:"logo"`
	Domain                  string  `json:"domain" gorm:"type:varchar(255);uniqueIndex"`
	ServerAddress           string  `json:"server_address"`
	FileSystemServerAddress string  `json:"file_system_server_address"`
	HomepageContent         string  `json:"homepage_content"`
	AboutContent            string  `json:"about_content"`
	NewHomeConf             string  `json:"new_home_conf"`
	TopupGroupRatio         string  `json:"topup_group_ratio"` // 充值分组费率,用来取代默认的充值费率,需要校验不得低于默认费率
	CommissionRate          float64 `json:"commission_rate"`
	Notice                  string  `json:"notice"`                            // 代理商自定义公告内容
	CommissionMoney         float64 `json:"commission_money"`                  // 佣金金额
	HistoryCommissionMoney  float64 `json:"history_commission_money"`          // 历史佣金金额
	SalesVolume             float64 `json:"sales_volume"`                      // 销售额
	UserCount               int64   `json:"user_count" gorm:"-"`               // user_count 代理商下的用户数量 用于统计 不存数据库
	SystemTopupGroupRatio   string  `json:"system_topup_group_ratio" gorm:"-"` // 系统默认充值费率,不存数据库
}

// CreateAgency 创建新的代理商
func CreateAgency(agency *Agency) error {
	return DB.Create(agency).Error
}

// GetAgencyById 通过ID获取代理商信息
func GetAgencyById(id int) (*Agency, error) {
	var agency Agency
	err := DB.First(&agency, id).Error
	if err != nil {
		return nil, err
	}
	return &agency, nil
}

// GetAgencyByUserId 通过用户ID获取代理商信息
func GetAgencyByUserId(userId int) (*Agency, error) {
	var agency Agency
	err := DB.Where("user_id = ?", userId).First(&agency).Error
	if err != nil {
		return nil, err
	}
	return &agency, nil
}

// CountUsersByAgencyId
func CountUsersByAgencyId(agencyId int) (int64, error) {
	var count int64
	err := DB.Model(&User{}).Where("agency_id = ?", agencyId).Count(&count).Error
	return count, err
}

// GetAgencyByDomain 通过域名获取代理商信息
func GetAgencyByDomain(domain string) (*Agency, error) {
	var agency Agency
	err := DB.Where("domain = ?", domain).First(&agency).Error
	if err != nil {
		return nil, err
	}
	return &agency, nil
}

// UpdateAgency 更新代理商信息
func UpdateAgency(agency *Agency) error {
	err := DB.Save(agency).Error
	if err != nil {
		return err
	}

	// 更新代理商信息后，需要清除该代理商下所有用户的服务器地址缓存
	users, err := GetUsersByAgencyId(agency.Id)
	if err == nil {
		for _, user := range users {
			// 异步清除用户代理商服务器地址缓存
			helper.SafeGoroutine(func() {
				DeleteUserAgencyServerAddressCache(user.Id)
			})
		}
	}

	return nil
}

// DeleteAgency 删除代理商
func DeleteAgency(id int) error {
	return DB.Delete(&Agency{}, id).Error
}

// IsUserAgent 判断用户是否为代理商
func IsUserAgent(userId int) (bool, error) {
	var count int64
	err := DB.Model(&Agency{}).Where("user_id = ?", userId).Count(&count).Error
	return count > 0, err
}

// ListAgencies 获取代理商列表，支持分页
func ListAgencies(page, pageSize int) ([]Agency, int64, error) {
	var agencies []Agency
	var total int64

	offset := (page - 1) * pageSize

	err := DB.Model(&Agency{}).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = DB.Offset(offset).Limit(pageSize).Find(&agencies).Error
	if err != nil {
		return nil, 0, err
	}

	return agencies, total, nil
}

// UpdateAgencyPriceMultiplier 更新代理商的价格倍率
func UpdateAgencyPriceMultiplier(agencyId int, multiplier float64) error {
	return DB.Model(&Agency{}).Where("id = ?", agencyId).Update("price_multiplier", multiplier).Error
}

// ToggleAgencyAbout 切换代理商的关于页面启用状态
func ToggleAgencyAbout(agencyId int) error {
	var agency Agency
	err := DB.First(&agency, agencyId).Error
	if err != nil {
		return err
	}

	//agency.AboutEnabled = !agency.AboutEnabled
	return DB.Save(&agency).Error
}

// UpdateAgencyDomain 更新代理商的域名
func UpdateAgencyDomain(agencyId int, newDomain string) error {
	// 首先检查新域名是否已被使用
	var count int64
	err := DB.Model(&Agency{}).Where("domain = ? AND id != ?", newDomain, agencyId).Count(&count).Error
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("domain already in use")
	}

	// 更新域名
	err = DB.Model(&Agency{}).Where("id = ?", agencyId).Update("domain", newDomain).Error
	if err != nil {
		return err
	}

	// 更新域名后，需要清除该代理商下所有用户的服务器地址缓存
	users, err := GetUsersByAgencyId(agencyId)
	if err == nil {
		for _, user := range users {
			// 异步清除用户代理商服务器地址缓存
			helper.SafeGoroutine(func() {
				DeleteUserAgencyServerAddressCache(user.Id)
			})
		}
	}

	return nil
}

// GetUsersByAgencyId 获取代理商下的所有用户
func GetUsersByAgencyId(agencyId int) ([]User, error) {
	var users []User
	err := DB.Where("agency_id = ?", agencyId).Find(&users).Error
	return users, err
}

// CountAgencies 获取代理商总数
func CountAgencies() (int64, error) {
	var count int64
	err := DB.Model(&Agency{}).Count(&count).Error
	return count, err
}

// SetAgencyModelPrice 设置代理商特定模型的价格
func SetAgencyModelPrice(agencyId int, modelName string, price float64) error {
	// 这里我们假设有一个 AgencyPrices 表来存储代理商的自定义价格
	//agencyPrice := AgencyPrice{
	//	AgencyId:  agencyId,
	//	ModelName: modelName,
	//	Price:     price,
	//}
	//
	//// 使用 upsert 操作，如果记录存在则更新，不存在则插入
	//return DB.Where(AgencyPrice{AgencyId: agencyId, ModelName: modelName}).
	//	Assign(AgencyPrice{Price: price}).
	//	FirstOrCreate(&agencyPrice).Error
	return nil
}

// UpdateAgencyUser
func UpdateAgencyUser(user *User) error {
	return DB.Save(user).Error
}

// DeleteAgencyUser
func DeleteAgencyUser(userId int) error {
	return DB.Delete(&User{}, userId).Error
}

// SettleAgencyCommission 代理商佣金结算，使用原子操作避免并发问题
func SettleAgencyCommission(agencyId int, settleAmount float64) error {
	// 使用原子操作扣减佣金，同时检查余额是否足够
	result := DB.Model(&Agency{}).
		Where("id = ? AND commission_money >= ?", agencyId, settleAmount).
		Update("commission_money", DB.Raw("commission_money - ?", settleAmount))

	if result.Error != nil {
		return result.Error
	}

	// 检查是否有记录被更新（即余额是否足够）
	if result.RowsAffected == 0 {
		return errors.New("佣金余额不足或代理商不存在")
	}

	return nil
}

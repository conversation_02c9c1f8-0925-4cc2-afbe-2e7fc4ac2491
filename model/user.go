package model

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/blacklist"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/common/random"
	"gorm.io/gorm"
)

const (
	RoleGuestUser  = 0
	RoleCommonUser = 1
	RoleAgencyUser = 5 // 新增代理商角色
	RoleAdminUser  = 10
	RoleRootUser   = 100
)

const (
	UserStatusEnabled  = 1 // don't use 0, 0 is the default value!
	UserStatusDisabled = 2 // also don't use 0
	UserStatusDeleted  = 3
)

// 用户类型常量
const (
	UserTypeUsername = 1 // 用户名密码注册/登录（传统方式）
	UserTypeEmail    = 2 // 邮箱密码注册/登录
	UserTypePhone    = 3 // 手机号验证码注册/登录（手机号作为用户名）
	UserTypeGoogle   = 4 // Google OAuth登录
	UserTypeGitHub   = 5 // GitHub OAuth登录
	UserTypeWechat   = 6 // 微信登录
	UserTypeLark     = 7 // 飞书登录
	UserTypeOIDC     = 8 // OIDC登录
	UserTypeTelegram = 9 // Telegram登录
)

// User if you add sensitive fields, don't forget to clean them in setupLogin function.
// Otherwise, the sensitive information will be saved on local storage in plain text!
type User struct {
	Id                       int                `json:"id"`
	Username                 string             `json:"username" gorm:"unique;index" validate:"max=12"`
	Password                 string             `json:"password" gorm:"not null;" validate:"min=8,max=20"`
	DisplayName              string             `json:"display_name" gorm:"index" validate:"max=20"`
	Role                     int                `json:"role" gorm:"type:int;default:1"`   // admin, util
	Status                   int                `json:"status" gorm:"type:int;default:1"` // enabled, disabled
	Email                    string             `json:"email" gorm:"index" validate:"max=50"`
	UserType                 int                `json:"user_type" gorm:"type:int;default:1;index"` // 用户类型：1-用户名 2-邮箱 3-手机 4-Google 5-GitHub 6-微信 7-飞书 8-OIDC 9-Telegram
	GitHubId                 string             `json:"github_id" gorm:"column:github_id;index"`
	GoogleId                 string             `json:"google_id" gorm:"column:google_id;index"`
	TelegramId               string             `json:"telegram_id" gorm:"column:telegram_id;index"`
	WeChatId                 string             `json:"wechat_id" gorm:"column:wechat_id;index"`
	LarkId                   string             `json:"lark_id" gorm:"column:lark_id;index"`
	OidcId                   string             `json:"oidc_id" gorm:"column:oidc_id;index"`
	AccessToken              string             `json:"access_token" gorm:"type:char(32);column:access_token;uniqueIndex"` // this token is for system management
	Quota                    int64              `json:"quota" gorm:"bigint;default:0"`
	UsedQuota                int64              `json:"used_quota" gorm:"bigint;default:0;column:used_quota"` // used quota
	RequestCount             int                `json:"request_count" gorm:"type:int;default:0;"`             // request number
	Group                    string             `json:"group" gorm:"type:varchar(32);default:'default'"`
	AffCode                  string             `json:"aff_code" gorm:"type:varchar(32);column:aff_code;uniqueIndex"`
	InviterId                int                `json:"inviter_id" gorm:"type:int;column:inviter_id;index"`
	LastLoginTime            int64              `json:"last_login_time" gorm:"type:bigint;column:last_login_time;index"`
	RegisterTime             int64              `json:"register_time" gorm:"type:bigint;column:register_time;index"`
	LastLoginIp              string             `json:"last_login_ip" gorm:"type:varchar(45);column:last_login_ip;index"`
	RateLimit                int                `json:"rate_limit" gorm:"type:int;default:0;column:rate_limit"`                                  // 限速每三分钟访问次数
	RateLimitExceededMessage string             `json:"rate_limit_exceeded_message" gorm:"type:varchar(500);column:rate_limit_exceeded_message"` // 限速超过次数后的提示信息
	Remark                   string             `json:"remark" gorm:"type:varchar(500);column:remark;"`                                          // 备注
	QuotaExpireTime          *int64             `json:"quota_expire_time" gorm:"bigint"`                                                         // 余额过期时间，nil表示无限期
	PhoneNumber              string             `json:"phone_number" gorm:"type:varchar(20);column:phone_number;index" validate:"max=11"`        // 手机号码
	SMSVerificationCode      string             `json:"sms_verification_code" gorm:"-:all"`                                                      // 短信验证码
	AdminAccessFlags         int64              `json:"admin_access_flags" gorm:"type:bigint;default:0;column:admin_access_flags"`               // 管理员授权标志
	AgencyId                 int                `json:"agency_id" gorm:"type:int;default:0;column:agency_id;index"`                              // 代理商ID
	Timezone                 string             `json:"timezone" gorm:"type:varchar(50);default:'UTC'"`
	VerificationCode         string             `json:"verification_code" gorm:"-:all"`  // this field is only for Email verification, don't save it to database!
	InviteUserNumber         int64              `json:"inviteUserNumber" gorm:"-:all"`   // 该用户邀请注册的其他用户个数
	AffQuota                 int64              `json:"aff_quota" gorm:"-:all"`          // 邀请剩余额度
	AffHistoryQuota          int64              `json:"aff_history_quota" gorm:"-:all"`  // 邀请历史额度
	TopupGroupMinLimit       int                `json:"TopupGroupMinLimit" gorm:"-:all"` // 最低充值限制
	ModelFixedPrice          map[string]float64 `json:"ModelFixedPrice" gorm:"-:all"`    // 模型固定费率配置信息
	InviteBonusRatio         float64            `json:"InviteBonusRatio" gorm:"-:all"`   // 邀请返利比例
	ShowPackagePlan          bool               `json:"ShowPackagePlan" gorm:"-:all"`    // 是否显示套餐
	GroupDisplayName         string             `json:"group_display_name" gorm:"-:all"` // 分组显示名称

}

func GetMaxUserId() int {
	var user User
	DB.Last(&user)
	return user.Id
}

func GetAllUsers(startIdx int, num int, order string, keyword string, username string, displayName string, group string, email string, id int, role int, status int, remark string, hasSpecialSettings *bool) (users []*User, err error) {
	groupCol := "`group`"
	if common.UsingPostgreSQL {
		groupCol = `"group"`
	}
	tx := DB
	if keyword != "" {
		tx = tx.Where("username LIKE ? OR remark LIKE ? OR email LIKE ? OR phone_number LIKE ? OR last_login_ip LIKE ?",
			"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
	}
	if username != "" {
		tx = tx.Where("username LIKE ?", "%"+username+"%")
	}
	if displayName != "" {
		tx = tx.Where("display_name LIKE ?", "%"+displayName+"%")
	}
	if group != "" {
		tx = tx.Where(groupCol+" = ?", group)
	}
	if email != "" {
		tx = tx.Where("email LIKE ?", "%"+email+"%")
	}
	if id != 0 {
		tx = tx.Where("id = ?", id)
	}
	if role != 0 {
		tx = tx.Where("role = ?", role)
	}
	if status != 0 {
		tx = tx.Where("status = ?", status)
	}
	if remark != "" {
		tx = tx.Where("remark LIKE ?", "%"+remark+"%")
	}
	tx = tx.Where("status != ?", common.UserStatusDeleted)

	// 应用特殊设置过滤
	if hasSpecialSettings != nil {
		tx = FilterUsersWithSpecialSettings(tx, *hasSpecialSettings)
	}

	switch order {
	case "quota":
		tx = tx.Order("quota desc")
	case "used_quota":
		tx = tx.Order("used_quota desc")
	case "request_count":
		tx = tx.Order("request_count desc")
	default:
		tx = tx.Order("id desc")
	}
	err = tx.Limit(num).Offset(startIdx).Omit("password").Find(&users).Error
	return users, err
}

func CountAllUsers(keyword string, username string, displayName string, group string, email string, id int, role int, status int, remark string, hasSpecialSettings *bool) (count int64, err error) {
	groupCol := "`group`"
	if common.UsingPostgreSQL {
		groupCol = `"group"`
	}
	tx := DB.Model(&User{})
	if keyword != "" {
		tx = tx.Where("username LIKE ? OR remark LIKE ? OR email LIKE ? OR phone_number LIKE ? OR last_login_ip LIKE ?",
			"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
	}
	if username != "" {
		tx = tx.Where("username LIKE ?", "%"+username+"%")
	}
	if displayName != "" {
		tx = tx.Where("display_name LIKE ?", "%"+displayName+"%")
	}
	if group != "" {
		tx = tx.Where(groupCol+" = ?", group)
	}
	if email != "" {
		tx = tx.Where("email LIKE ?", "%"+email+"%")
	}
	if id != 0 {
		tx = tx.Where("id = ?", id)
	}
	if role != 0 {
		tx = tx.Where("role = ?", role)
	}
	if status != 0 {
		tx = tx.Where("status = ?", status)
	}
	if remark != "" {
		tx = tx.Where("remark LIKE ?", "%"+remark+"%")
	}
	tx = tx.Where("status != ?", common.UserStatusDeleted)

	// 应用特殊设置过滤
	if hasSpecialSettings != nil {
		tx = FilterUsersWithSpecialSettings(tx, *hasSpecialSettings)
	}

	err = tx.Count(&count).Error
	return count, err
}

func SearchUsers(keyword string) (users []*User, err error) {
	if !common.UsingPostgreSQL {
		err = DB.Omit("password").Where("id = ? or username LIKE ? or email LIKE ? or display_name LIKE ?", keyword, keyword+"%", keyword+"%", keyword+"%").Find(&users).Error
	} else {
		err = DB.Omit("password").Where("username LIKE ? or email LIKE ? or display_name LIKE ?", keyword+"%", keyword+"%", keyword+"%").Find(&users).Error
	}
	return users, err
}

func GetUserById(id int, selectAll bool) (*User, error) {
	if id == 0 {
		return nil, errors.New("id 为空！")
	}
	user := User{Id: id}
	var err error = nil
	if selectAll {
		err = DB.First(&user, "id = ?", id).Error
	} else {
		err = DB.Omit("password", "access_token").First(&user, "id = ?", id).Error
	}
	return &user, err
}

func GetUserByName(username string) (*User, error) {
	if username == "" {
		return nil, errors.New("username 为空！")
	}
	user := User{Username: username}
	err := DB.Omit("password").First(&user, "username = ?", username).Error
	return &user, err
}

func GetUserIdByAffCode(affCode string) (int, error) {
	if affCode == "" {
		return 0, errors.New("affCode 为空！")
	}
	var user User
	err := DB.Select("id").First(&user, "aff_code = ?", affCode).Error
	return user.Id, err
}

func GetInviteUserCntByUserId(userId int) (int64, error) {
	var user User
	err := DB.Model(&User{}).Where("inviter_id = ?", userId).Count(&user.InviteUserNumber).Error
	return user.InviteUserNumber, err
}

func DeleteUserById(id int) (err error) {
	if id == 0 {
		return errors.New("id 为空！")
	}
	user := User{Id: id}
	return user.Delete()
}

// GenerateUsernameFromEmail 从邮箱生成唯一用户名
// 提取邮箱前缀，如果重复则添加数字后缀
func GenerateUsernameFromEmail(email string) string {
	if email == "" {
		return ""
	}

	// 提取邮箱前缀
	parts := strings.Split(email, "@")
	if len(parts) == 0 {
		return ""
	}

	baseUsername := parts[0]
	// 清理用户名，只保留字母数字和下划线
	reg := regexp.MustCompile(`[^a-zA-Z0-9_]`)
	baseUsername = reg.ReplaceAllString(baseUsername, "")

	// 限制长度（调整为更合理的长度）
	if len(baseUsername) > 20 {
		baseUsername = baseUsername[:20]
	}

	// 如果为空，使用默认前缀
	if baseUsername == "" {
		baseUsername = "user"
	}

	// 检查用户名是否已存在
	username := baseUsername
	counter := 1

	for IsUsernameAlreadyTaken(username) {
		counter++
		username = fmt.Sprintf("%s%d", baseUsername, counter)
		// 防止无限循环
		if counter > 9999 {
			username = fmt.Sprintf("%s%d", baseUsername, time.Now().Unix()%10000)
			break
		}
	}

	return username
}

// GetUserTypeDescription 获取用户类型的描述
func GetUserTypeDescription(userType int) string {
	switch userType {
	case UserTypeUsername:
		return "用户名登录"
	case UserTypeEmail:
		return "邮箱登录"
	case UserTypePhone:
		return "手机登录"
	case UserTypeGoogle:
		return "Google登录"
	case UserTypeGitHub:
		return "GitHub登录"
	case UserTypeWechat:
		return "微信登录"
	case UserTypeLark:
		return "飞书登录"
	case UserTypeOIDC:
		return "OIDC登录"
	case UserTypeTelegram:
		return "Telegram登录"
	default:
		return "未知类型"
	}
}

// GetUserTypeDescription 获取当前用户类型的描述
func (user *User) GetUserTypeDescription() string {
	return GetUserTypeDescription(user.UserType)
}

func (user *User) Insert(ctx context.Context, inviterId int) error {
	var err error
	if user.Password != "" {
		user.Password, err = common.Password2Hash(user.Password)
		if err != nil {
			return err
		}
	}
	user.Quota = config.QuotaForNewUser
	user.AccessToken = random.GetUUID()
	user.AffCode = random.GetRandomString(4)
	result := DB.Create(user)
	if result.Error != nil {
		return result.Error
	}
	if config.QuotaForNewUser > 0 {
		RecordLog(ctx, user.Id, LogTypeSystem, fmt.Sprintf("新用户注册赠送 %s", common.LogQuota(config.QuotaForNewUser)))
	}
	if inviterId != 0 {
		if config.QuotaForInvitee > 0 {
			_ = IncreaseUserQuotaAndRedis(user.Id, config.QuotaForInvitee)
			RecordLog(ctx, user.Id, LogTypeSystem, fmt.Sprintf("使用邀请码赠送 %s", common.LogQuota(config.QuotaForInvitee)))
		}
		if config.QuotaForInviter > 0 {
			_ = IncreaseUserQuotaAndRedis(inviterId, config.QuotaForInviter)
			RecordLog(ctx, inviterId, LogTypeSystem, fmt.Sprintf("邀请用户赠送 %s", common.LogQuota(config.QuotaForInviter)))
		}
	}
	return nil
}

func (user *User) Update(updatePassword bool) error {
	var err error
	if updatePassword {
		user.Password, err = common.Password2Hash(user.Password)
		if err != nil {
			return err
		}
	}
	if user.Status == UserStatusDisabled {
		blacklist.BanUser(user.Id)
	} else if user.Status == UserStatusEnabled {
		blacklist.UnbanUser(user.Id)
	}
	err = DB.Model(user).Updates(user).Error
	if err != nil {
		return err
	}
	// 只有当 AdminAccessFlags 为 0 时才执行单独的更新语句
	if user.AdminAccessFlags == 0 {
		err = DB.Model(user).Update("AdminAccessFlags", user.AdminAccessFlags).Error
	}
	// 当Remark或者RateLimitExceededMessage为空时，更新为空字符串
	if user.Remark == "" {
		err = DB.Model(user).Update("Remark", "").Error
	}
	if user.RateLimitExceededMessage == "" {
		err = DB.Model(user).Update("RateLimitExceededMessage", "").Error
	}

	// 清除用户信息缓存
	DeleteUserInfoCache(user.Id)

	return err
}

func (user *User) UpdateLastLoginInfo() error {
	var err error
	err = DB.Model(user).Select("last_login_time", "last_login_ip").Updates(User{
		LastLoginTime: helper.GetTimestamp(),
		LastLoginIp:   user.LastLoginIp,
	}).Error
	return err
}

func (user *User) Delete() error {
	if user.Id == 0 {
		return errors.New("id 为空！")
	}
	blacklist.BanUser(user.Id)
	user.Username = fmt.Sprintf("deleted_%s", random.GetUUID())
	user.Status = UserStatusDeleted
	err := DB.Model(user).Updates(user).Error
	return err
}

func (user *User) ValidateAndFillWithPhoneNumber() (err error) {
	phoneNumber := user.PhoneNumber
	if phoneNumber == "" {
		return errors.New("手机号码为空")
	}

	// 查询数据库中是否存在该手机号码的用户
	result := DB.Where(&User{PhoneNumber: phoneNumber}).First(user)
	if result.Error != nil {
		return errors.New("该手机号尚未注册")
	}

	// 由于短信验证码已验证，此处不再验证密码，直接检查用户状态
	if user.Status != common.UserStatusEnabled {
		return errors.New("该用户已被封禁")
	}

	return nil
}

// ValidatePassword 对比输入的密码与数据库是否一致，用于修改密码和敏感操作前的验证
func (user *User) ValidatePassword(inputPassword string) (err error) {
	//查询用户信息，如果查询失败，返回错误
	if DB.Where(User{Id: user.Id}).First(user).Error != nil {
		return errors.New("用户不存在")
	}
	//DB.Where(User{Id: user.Id}).First(user)
	//但是更建议您区分"验证密码"和"设置密码"的逻辑，确保每个功能都有明确且安全的行为。
	if user.Password == "" {
		return nil
	}
	okay := common.ValidatePasswordAndHash(inputPassword, user.Password)
	if !okay {
		return errors.New("密码错误")
	}
	return nil
}

// ValidateAndFill check password & user status
func (user *User) ValidateAndFill() (err error) {
	// When querying with struct, GORM will only query with non-zero fields,
	// that means if your field's value is 0, '', false or other zero values,
	// it won't be used to build query conditions
	password := user.Password
	if user.Username == "" || password == "" {
		return errors.New("用户名或密码为空")
	}
	// 判断是否是邮箱登录

	// 使用正则表达式检查用户名是否符合电子邮件格式
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	isEmail := emailRegex.MatchString(user.Username)
	if isEmail {
		user.Email = user.Username
		// 先判断该邮箱是否存在重复
		if !IsEmailAlreadyTaken(user.Email) {
			return errors.New("该邮箱尚未注册或存在多个注册账号，请使用用户名登录")
		}
		err = user.FillUserByEmail()
	} else {
		DB.Where(User{Username: user.Username}).First(user)
	}
	okay := common.ValidatePasswordAndHash(password, user.Password)
	//if !okay || user.Status != common.UserStatusEnabled {
	//	return errors.New("用户名或密码错误，或用户已被封禁")
	//}
	//分离出来，防止密码错误时，返回用户已被封禁
	if !okay {
		return errors.New("用户名或密码错误")
	}
	if user.Status == UserStatusDisabled { //用户已被封禁,不使用!=Enabled ,因为后续可能会有其他状态
		return errors.New("该用户已被封禁")
	}
	return nil
}

func (user *User) FillUserById() error {
	if user.Id == 0 {
		return errors.New("id 为空！")
	}
	DB.Where(User{Id: user.Id}).First(user)
	return nil
}

func (user *User) FillUserByEmail() error {
	if user.Email == "" {
		return errors.New("email 为空！")
	}
	DB.Where(User{Email: user.Email}).First(user)
	return nil
}

func (user *User) FillUserByGitHubId() error {
	if user.GitHubId == "" {
		return errors.New("GitHub id 为空！")
	}
	DB.Where(User{GitHubId: user.GitHubId}).First(user)
	return nil
}

func (user *User) FillUserByGoogleId() error {
	if user.GoogleId == "" {
		return errors.New("Google id 为空！")
	}
	DB.Where(User{GoogleId: user.GoogleId}).First(user)
	return nil
}

func (user *User) FillUserByLarkId() error {
	if user.LarkId == "" {
		return errors.New("lark id 为空！")
	}
	DB.Where(User{LarkId: user.LarkId}).First(user)
	return nil
}

func (user *User) FillUserByOidcId() error {
	if user.OidcId == "" {
		return errors.New("oidc id 为空！")
	}
	DB.Where(User{OidcId: user.OidcId}).First(user)
	return nil
}

func (user *User) FillUserByWeChatId() error {
	if user.WeChatId == "" {
		return errors.New("WeChat id 为空！")
	}
	DB.Where(User{WeChatId: user.WeChatId}).First(user)
	return nil
}

func (user *User) FillUserByUsername() error {
	if user.Username == "" {
		return errors.New("username 为空！")
	}
	DB.Where(User{Username: user.Username}).First(user)
	return nil
}

func (user *User) FillUserByTelegramId() error {
	if user.TelegramId == "" {
		return errors.New("用户 Telegram ID 为空")
	}
	err := DB.Where(User{TelegramId: user.TelegramId}).First(user).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return errors.New("该 Telegram 账户未绑定")
	}
	return nil
}

func IsEmailAlreadyTaken(email string) bool {
	return DB.Where("email = ?", email).Find(&User{}).RowsAffected == 1
}

func IsWeChatIdAlreadyTaken(wechatId string) bool {
	return DB.Where("wechat_id = ?", wechatId).Find(&User{}).RowsAffected == 1
}

func IsGitHubIdAlreadyTaken(githubId string) bool {
	return DB.Where("github_id = ?", githubId).Find(&User{}).RowsAffected == 1
}

func IsGoogleIdAlreadyTaken(googleId string) bool {
	return DB.Where("google_id = ?", googleId).Find(&User{}).RowsAffected == 1
}

func IsTelegramIdAlreadyTaken(telegramId string) bool {
	return DB.Where("telegram_id = ?", telegramId).Find(&User{}).RowsAffected == 1
}

func IsLarkIdAlreadyTaken(githubId string) bool {
	return DB.Where("lark_id = ?", githubId).Find(&User{}).RowsAffected == 1
}

func IsOidcIdAlreadyTaken(oidcId string) bool {
	return DB.Where("oidc_id = ?", oidcId).Find(&User{}).RowsAffected == 1
}

func IsUsernameAlreadyTaken(username string) bool {
	return DB.Where("username = ?", username).Find(&User{}).RowsAffected == 1
}

func IsPhoneNumberAlreadyTaken(phoneNumber string) bool {
	return DB.Where("phone_number = ?", phoneNumber).Find(&User{}).RowsAffected == 1
}

func ResetUserPasswordByEmail(email string, password string) error {
	if email == "" || password == "" {
		return errors.New("邮箱地址或密码为空！")
	}
	hashedPassword, err := common.Password2Hash(password)
	if err != nil {
		return err
	}
	err = DB.Model(&User{}).Where("email = ?", email).Update("password", hashedPassword).Error
	return err
}

func IsAdmin(userId int) bool {
	if userId == 0 {
		return false
	}
	var user User
	err := DB.Where("id = ?", userId).Select("role").Find(&user).Error
	if err != nil {
		logger.SysError("no such user " + err.Error())
		return false
	}
	return user.Role >= RoleAdminUser
}

func IsUserEnabled(userId int) (bool, error) {
	if userId == 0 {
		return false, errors.New("user id is empty")
	}
	var user User
	err := DB.Where("id = ?", userId).Select("status").Find(&user).Error
	if err != nil {
		return false, err
	}
	return user.Status == UserStatusEnabled, nil
}

func ValidateAccessToken(token string) (user *User) {
	if token == "" {
		return nil
	}
	token = strings.Replace(token, "Bearer ", "", 1)
	user = &User{}
	if DB.Where("access_token = ?", token).First(user).RowsAffected == 1 {
		return user
	}
	return nil
}

func GetUserQuota(id int) (quota int64, err error) {
	// 定义一个匿名结构体来接收查询结果
	var result struct {
		Quota           int64
		QuotaExpireTime int64
	}
	err = DB.Model(&User{}).Where("id = ?", id).Select("quota,quota_expire_time").Scan(&result).Error
	if config.QuotaExpireEnabled {
		if result.QuotaExpireTime != 0 && result.QuotaExpireTime < helper.GetTimestamp() {
			return 0, nil
		}
	}
	return result.Quota, err
}

func GetUserQuotaAndExpireTime(id int) (quota int64, quotaExpireTime int64, err error) {
	// 定义一个匿名结构体来接收查询结果
	var result struct {
		Quota           int64
		QuotaExpireTime int64
	}
	err = DB.Model(&User{}).Where("id = ?", id).Select("quota,quota_expire_time").Scan(&result).Error
	return result.Quota, result.QuotaExpireTime, err
}

func GetUserUsedQuota(id int) (quota int64, err error) {
	err = DB.Model(&User{}).Where("id = ?", id).Select("used_quota").Find(&quota).Error
	return quota, err
}

func GetUserEmail(id int) (email string, err error) {
	err = DB.Model(&User{}).Where("id = ?", id).Select("email").Find(&email).Error
	return email, err
}

func GetUserGroup(id int) (group string, err error) {
	groupCol := "`group`"
	if common.UsingPostgreSQL {
		groupCol = `"group"`
	}
	err = DB.Model(&User{}).Where("id = ?", id).Select(groupCol).First(&group).Error
	return group, err
}

func IncreaseUserQuota(id int, quota int64) (err error) {
	if quota < 0 {
		return errors.New("quota 不能为负数！")
	}
	if config.BatchUpdateEnabled {
		addNewRecord(BatchUpdateTypeUserQuota, id, quota)
		return nil
	}
	return increaseUserQuota(id, quota)
}

func IncreaseUserQuotaAndRedis(id int, quota int64) (err error) {
	if quota < 0 {
		return errors.New("quota 不能为负数！")
	}
	// 确保缓存双写一致
	if common.RedisEnabled {
		redisErr := common.RedisIncreaseWithLuaCheckExists(fmt.Sprintf("user_quota:%d", id), quota)
		if redisErr != nil {
			logger.SysError(fmt.Sprintf("IncreaseUserQuotaAndRedis 缓存双写一致失败 - 用户ID: %d, 增加配额: %d, 错误: %s", id, quota, redisErr.Error()))
		}
	}
	if config.BatchUpdateEnabled {
		addNewRecord(BatchUpdateTypeUserQuota, id, quota)
		return nil
	}
	return increaseUserQuota(id, quota)
}

func IncreaseUserQuotaAndRedisByTx(tx *gorm.DB, id int, quota int64) (err error) {
	if quota < 0 {
		return errors.New("quota 不能为负数！")
	}
	// 确保缓存双写一致
	if common.RedisEnabled {
		redisErr := common.RedisIncreaseWithLuaCheckExists(fmt.Sprintf("user_quota:%d", id), quota)
		if redisErr != nil {
			logger.SysError("IncreaseUserQuotaAndRedis 缓存双写一致 error: " + redisErr.Error())
		}
	}
	return tx.Model(&User{}).Where("id = ?", id).Update("quota", gorm.Expr("quota + ?", quota)).Error
}

func IncreaseUserQuotaByTx(tx *gorm.DB, id int, quota int64) (err error) {
	if quota < 0 {
		return errors.New("quota 不能为负数！")
	}
	return tx.Model(&User{}).Where("id = ?", id).Update("quota", gorm.Expr("quota + ?", quota)).Error
}

func increaseUserQuota(id int, quota int64) (err error) {
	err = DB.Model(&User{}).Where("id = ?", id).Update("quota", gorm.Expr("quota + ?", quota)).Error
	return err
}

func DecreaseUserQuota(id int, quota int64) (err error) {
	if quota < 0 {
		return errors.New("quota 不能为负数！")
	}
	if config.BatchUpdateEnabled {
		addNewRecord(BatchUpdateTypeUserQuota, id, -quota)
		return nil
	}
	return decreaseUserQuota(id, quota)
}

func DecreaseUserQuotaAndRedis(id int, quota int64) (err error) {
	if quota < 0 {
		return errors.New("quota 不能为负数！")
	}
	// 确保缓存双写一致
	if common.RedisEnabled {
		redisErr := common.RedisDecreaseWithLuaCheckExists(fmt.Sprintf("user_quota:%d", id), quota)
		if redisErr != nil {
			// todo 这里需要考虑到 Redis 的异常情况 log_20250517.log.49:[ERROR] 2025/05/17 - 02:45:56 | /build/model/user.go:658 [DecreaseUserQuotaAndRedis] DecreaseUserQuotaAndRedis 缓存双写一致 error: redis: connection pool timeout
			// 这里需要考虑到 Redis 的异常情况 如果异常了，可能会导致数据库的余额和 Redis 的余额不一致
			logger.SysError(fmt.Sprintf("DecreaseUserQuotaAndRedis 缓存双写一致失败 - 用户ID: %d, 扣减配额: %d, 错误: %s", id, quota, redisErr.Error()))
		}
	}
	if config.BatchUpdateEnabled {
		addNewRecord(BatchUpdateTypeUserQuota, id, -quota)
		return nil
	}
	return decreaseUserQuota(id, quota)
}

func decreaseUserQuota(id int, quota int64) (err error) {
	err = DB.Model(&User{}).Where("id = ?", id).Update("quota", gorm.Expr("quota - ?", quota)).Error
	return err
}

func GetRootUserEmail() (email string) {
	DB.Model(&User{}).Where("role = ?", RoleRootUser).Select("email").Find(&email)
	return email
}

func GetRootUserTelegramId() (telegramId string) {
	DB.Model(&User{}).Where("role = ?", RoleRootUser).Select("telegram_id").Find(&telegramId)
	return telegramId
}

// GetRootUserAccessToken() 获取 Root 用户的 AccessToken
func GetRootUserAccessToken() (accessToken string) {
	DB.Model(&User{}).Where("role = ?", RoleRootUser).Select("access_token").Find(&accessToken)
	return accessToken
}

func UpdateUserUsedQuotaAndRequestCount(id int, quota int64) {
	if config.BatchUpdateEnabled {
		addNewRecord(BatchUpdateTypeUsedQuota, id, quota)
		addNewRecord(BatchUpdateTypeRequestCount, id, 1)
		return
	}
	updateUserUsedQuotaAndRequestCount(id, quota, 1)
}

func UpdateOnlyUserRequestCount(id int) {
	if config.BatchUpdateEnabled {
		addNewRecord(BatchUpdateTypeRequestCount, id, 1)
		return
	}
	updateUserRequestCount(id, 1)
}

func updateUserUsedQuotaAndRequestCount(id int, quota int64, count int) {
	err := DB.Model(&User{}).Where("id = ?", id).Updates(
		map[string]interface{}{
			"used_quota":    gorm.Expr("used_quota + ?", quota),
			"request_count": gorm.Expr("request_count + ?", count),
		},
	).Error
	if err != nil {
		logger.SysError("failed to update user used quota and request count: " + err.Error())
	}
}

func updateUserUsedQuota(id int, quota int64) {
	err := DB.Model(&User{}).Where("id = ?", id).Updates(
		map[string]interface{}{
			"used_quota": gorm.Expr("used_quota + ?", quota),
		},
	).Error
	if err != nil {
		logger.SysError("failed to update user used quota: " + err.Error())
	}
}

func updateUserRequestCount(id int, count int) {
	err := DB.Model(&User{}).Where("id = ?", id).Update("request_count", gorm.Expr("request_count + ?", count)).Error
	if err != nil {
		logger.SysError("failed to update user request count: " + err.Error())
	}
}

func GetUsernameById(id int) (username string) {
	DB.Model(&User{}).Where("id = ?", id).Select("username").Find(&username)
	return username
}

//// SendTelegramMessage 发送telegram消息
//func SendTelegramMessage(userId int, message string) error { // 注意这里修改了返回类型，从(err error)变为error
//	var user User
//	// 假设DB已经被定义并正确配置
//	result := DB.Where("id = ?", userId).Select("telegram_id").First(&user)
//	if result.Error != nil {
//		return result.Error // 如果查询出错，直接返回错误
//	}
//	if user.TelegramId == "" {
//		return fmt.Errorf("no TelegramId found for user ID %d", userId) // 如果没有找到TelegramId，返回一个新的错误
//	}
//	// 假设controller.SendTelegramMessage已经正确配置
//	err := controller.SendTelegramMessage(user.TelegramId, message)
//	if err != nil {
//		return err // 如果发送消息时出错，返回错误
//	}
//	// 如果一切顺利，返回nil表示没有错误发生
//	return nil
//}

func UpdateAdminAccessFlags(id int, adminAccessFlags int64) error {
	err := DB.Model(&User{}).Where("id = ?", id).Update("admin_access_flags", adminAccessFlags).Error
	return err
}

func UpdateUserTimezone(userId int, timezone string) error {
	return DB.Model(&User{}).Where("id = ?", userId).Update("timezone", timezone).Error
}

func GetAllEmails() []string {
	var emails []string
	DB.Model(&User{}).Where("email != ''").Pluck("email", &emails)
	return emails
}

// GetRoleName 获取角色的可读名称
func GetRoleName(role int) string {
	switch role {
	case RoleGuestUser:
		return "访客"
	case RoleCommonUser:
		return "普通用户"
	case RoleAgencyUser:
		return "代理商"
	case RoleAdminUser:
		return "管理员"
	case RoleRootUser:
		return "超级管理员"
	default:
		return fmt.Sprintf("未知角色(%d)", role)
	}
}

// GetStatusName 获取状态的可读名称
func GetStatusName(status int) string {
	switch status {
	case UserStatusEnabled:
		return "正常"
	case UserStatusDisabled:
		return "禁用"
	case UserStatusDeleted:
		return "已删除"
	default:
		return fmt.Sprintf("未知状态(%d)", status)
	}
}

// GetQuotaExpireTime 获取用户余额过期时间
// 返回值: 过期时间戳(秒)，如果是无限期则返回0
func (user *User) GetQuotaExpireTime() int64 {
	if user.QuotaExpireTime == nil {
		return 0 // 无限期
	}
	return *user.QuotaExpireTime
}

// IsQuotaExpired 检查用户余额是否已过期
func (user *User) IsQuotaExpired() bool {
	if user.QuotaExpireTime == nil {
		return false // 无限期永不过期
	}
	return *user.QuotaExpireTime < helper.GetTimestamp()
}

// GetQuotaExpireDuration 获取余额过期剩余时间(秒)
// 返回值: 剩余秒数，如果是无限期则返回-1，如果已过期则返回0
func (user *User) GetQuotaExpireDuration() int64 {
	if user.QuotaExpireTime == nil {
		return -1 // 无限期
	}

	now := helper.GetTimestamp()
	if *user.QuotaExpireTime <= now {
		return 0 // 已过期
	}

	return *user.QuotaExpireTime - now
}

// SetQuotaExpireTime 设置用户余额过期时间
// 参数: expireTime - 过期时间戳(秒)，如果为0则设置为无限期
func (user *User) SetQuotaExpireTime(expireTime int64) {
	if expireTime == 0 {
		user.QuotaExpireTime = nil // 无限期
	} else {
		user.QuotaExpireTime = &expireTime
	}
}

// SetQuotaExpireDays 设置用户余额过期天数
// 参数: days - 从现在开始的有效天数
func (user *User) SetQuotaExpireDays(days int) {
	if days <= 0 {
		user.QuotaExpireTime = nil // 无限期
		return
	}

	expireTime := helper.GetTimestamp() + int64(days*86400) // 一天86400秒
	user.QuotaExpireTime = &expireTime
}

// FormatQuotaExpireTime 格式化余额过期时间为可读字符串
// 返回值: 格式化后的字符串，如"永久有效"、"已过期"或"2023-12-31 23:59:59"
func (user *User) FormatQuotaExpireTime() string {
	if user.QuotaExpireTime == nil {
		return "永久有效"
	}

	now := helper.GetTimestamp()
	if *user.QuotaExpireTime <= now {
		return "已过期"
	}

	// 转换为时间格式
	t := time.Unix(*user.QuotaExpireTime, 0)
	return t.Format("2006-01-02 15:04:05")
}

// GetUserAgencyServerAddress 根据用户ID获取对应的代理商服务器地址
// 优先使用ServerAddress，如果不存在则使用domain进行拼接
func GetUserAgencyServerAddress(userId int) (string, error) {
	user, err := GetUserById(userId, false)
	if err != nil {
		return "", err
	}

	// 如果用户没有代理商ID，返回空字符串
	if user.AgencyId == 0 {
		return "", nil
	}

	// 根据代理商ID获取代理商信息
	agency, err := GetAgencyByUserId(user.AgencyId)
	if err != nil {
		return "", err
	}

	// 优先使用ServerAddress
	if agency.ServerAddress != "" {
		return agency.ServerAddress, nil
	}

	// 如果ServerAddress为空，使用domain进行拼接
	if agency.Domain != "" {
		return "https://" + agency.Domain, nil
	}

	// 如果都为空，返回空字符串
	return "", nil
}

package model

import (
	"github.com/songquanpeng/one-api/common/helper"
	"strconv"
	"strings"
)

// RedPacket 红包，保存红包的基本信息，只允许管理员创建红包
type RedPacket struct {
	Id              int    `json:"id"`                                                    // 红包id
	RedPacketUUID   string `json:"red_packet_uuid"`                                       // 红包uuid
	Type            int    `json:"type"`                                                  // 红包类型，1-普通红包，2-拼手气红包
	Quota           int64  `json:"quota"`                                                 // 红包总金额
	Count           int    `json:"count"`                                                 // 红包个数
	Blessing        string `json:"blessing"`                                              // 祝福语
	RemainCount     int    `json:"remain_count"`                                          // 剩余红包个数
	RemainQuota     int64  `json:"remain_quota"`                                          // 剩余红包金额
	CreatedAt       int64  `json:"created_at"`                                            // 创建时间
	Status          int    `json:"status"`                                                // 红包状态，1-正常，2-失效
	ReceivedUserIds string `json:"received_user_ids" gorm:"type:varchar(255);default:''"` // 已领取用户id
}

// Insert 插入红包
func (redPacket *RedPacket) Insert() error {
	var err error
	err = DB.Create(redPacket).Error
	if err != nil {
		return err
	}
	return nil
}

// Update 更新红包
func (redPacket *RedPacket) Update() error {
	var err error
	err = DB.Save(redPacket).Error
	if err != nil {
		return err
	}
	return nil
}

// Delete 删除红包
func (redPacket *RedPacket) Delete() error {
	var err error
	err = DB.Delete(redPacket).Error
	if err != nil {
		return err
	}
	return nil
}

// Get 获取红包
func (redPacket *RedPacket) Get() error {
	var err error
	err = DB.First(redPacket).Error
	if err != nil {
		return err
	}
	return nil
}

// GetRedPacketByUUID 根据红包UUID获取红包
func GetRedPacketByUUID(redPacketUUID string) (*RedPacket, error) {
	var redPacket *RedPacket
	var err error
	err = DB.Where("red_packet_uuid = ?", redPacketUUID).First(&redPacket).Error
	if err != nil {
		return nil, err
	}
	return redPacket, nil
}

// CheckUserReceivedRedPacket 校验传入的红包 UUID 是否已经被某个用户领取过,返回 bool
func CheckUserReceivedRedPacket(redPacketUUID string, userId int) (bool, error) {
	var redPacket *RedPacket
	var err error
	err = DB.Where("red_packet_uuid = ?", redPacketUUID).First(&redPacket).Error
	if err != nil {
		return false, err
	}
	if redPacket.ReceivedUserIds == "" {
		return false, nil
	}
	if !strings.Contains(redPacket.ReceivedUserIds, strconv.Itoa(userId)) {
		return false, nil
	}
	return true, nil
}

// GetRandomRedPacketQuota 传入 uuid，生成随机红包金额，根据当前红包总数和剩余金额计算随机金额
func GetRandomRedPacketQuota(redPacketUUID string) (int64, error) {
	var redPacket *RedPacket
	var err error
	err = DB.Where("red_packet_uuid = ?", redPacketUUID).First(&redPacket).Error
	if err != nil {
		return 0, err
	}
	if redPacket.RemainCount == 1 {
		return redPacket.RemainQuota, nil
	}
	// 生成随机金额int64
	minQuota := 1
	maxQuota := redPacket.RemainQuota/int64(redPacket.RemainCount*2) - 1
	if maxQuota < 1 {
		maxQuota = 1
	}
	quota := helper.GetRandomNumber(int64(minQuota), maxQuota)
	return quota, nil
}

// UpdateReceivedUserIds 更新已经领取的用户id
func UpdateReceivedUserIds(redPacketUUID string, userId int) error {
	var redPacket *RedPacket
	var err error
	err = DB.Where("red_packet_uuid = ?", redPacketUUID).First(&redPacket).Error
	if err != nil {
		return err
	}
	if redPacket.ReceivedUserIds == "" {
		redPacket.ReceivedUserIds = strconv.Itoa(userId)
	} else {
		redPacket.ReceivedUserIds = redPacket.ReceivedUserIds + "," + strconv.Itoa(userId)
	}
	err = redPacket.Update()
	if err != nil {
		return err
	}
	return nil
}

// GetAllRedPackets 获取所有红包
func GetAllRedPackets() ([]RedPacket, error) {
	//如果没有这个表，需要创建表
	err1 := DB.AutoMigrate(&RedPacket{})
	if err1 != nil {
		return nil, err1
	}
	var redPackets []RedPacket
	var err error
	err = DB.Find(&redPackets).Error
	if err != nil {
		return nil, err
	}
	return redPackets, nil
}

// GetRedPacketStatus 获取红包状态，传入红包uuid 和用户 id，返回红包状态（已领完/已领取），优先监测是否已领取
func GetRedPacketStatus(redPacketUUID string, userId int) (int, error) {
	var redPacket *RedPacket
	var err error
	err = DB.Where("red_packet_uuid = ?", redPacketUUID).First(&redPacket).Error
	if err != nil {
		return 0, err
	}
	if redPacket.ReceivedUserIds == "" {
		return 1, nil //未领取
	}
	if !strings.Contains(redPacket.ReceivedUserIds, strconv.Itoa(userId)) {
		return 1, nil //未领取
	}
	//如果剩余额度或者剩余个数 = 0，返回已领完
	if redPacket.RemainCount == 0 || redPacket.RemainQuota == 0 {
		return 2, nil //已领完
	}
	return 3, nil //已领取
}

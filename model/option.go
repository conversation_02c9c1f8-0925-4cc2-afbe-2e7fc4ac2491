package model

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/logger"
	billingratio "github.com/songquanpeng/one-api/relay/billing/ratio"
	"github.com/songquanpeng/one-api/relay/say1"
)

type Option struct {
	Key   string `json:"key" gorm:"primaryKey"`
	Value string `json:"value"`
}

func AllOption() ([]*Option, error) {
	var options []*Option
	var err error
	err = DB.Find(&options).Error
	return options, err
}

// InitOptionMap 函数将所有的配置选项初始化到一个全局的 OptionMap 中，这个 OptionMap 是一个 map[string]string 类型的变量，用于在内存中存储所有的配置选项，以便快速访问。
func InitOptionMap() {
	config.OptionMapRWMutex.Lock()
	config.OptionMap = make(map[string]string)
	config.OptionMap["FileUploadPermission"] = strconv.Itoa(common.FileUploadPermission)
	config.OptionMap["FileDownloadPermission"] = strconv.Itoa(common.FileDownloadPermission)
	config.OptionMap["ImageUploadPermission"] = strconv.Itoa(common.ImageUploadPermission)
	config.OptionMap["ImageDownloadPermission"] = strconv.Itoa(common.ImageDownloadPermission)
	config.OptionMap["PasswordLoginEnabled"] = strconv.FormatBool(config.PasswordLoginEnabled)
	config.OptionMap["PasswordRegisterEnabled"] = strconv.FormatBool(config.PasswordRegisterEnabled)
	config.OptionMap["EmailVerificationEnabled"] = strconv.FormatBool(config.EmailVerificationEnabled)
	config.OptionMap["GitHubOAuthEnabled"] = strconv.FormatBool(config.GitHubOAuthEnabled)
	config.OptionMap["GoogleOAuthEnabled"] = strconv.FormatBool(config.GoogleOAuthEnabled)
	config.OptionMap["TelegramOAuthEnabled"] = strconv.FormatBool(config.TelegramOAuthEnabled)
	config.OptionMap["OidcEnabled"] = strconv.FormatBool(config.OidcEnabled)
	config.OptionMap["WeChatAuthEnabled"] = strconv.FormatBool(config.WeChatAuthEnabled)
	config.OptionMap["TurnstileCheckEnabled"] = strconv.FormatBool(config.TurnstileCheckEnabled)
	config.OptionMap["CaptchaCheckEnabled"] = strconv.FormatBool(config.CaptchaCheckEnabled)
	config.OptionMap["RegisterEnabled"] = strconv.FormatBool(config.RegisterEnabled)
	config.OptionMap["AutomaticDisableChannelEnabled"] = strconv.FormatBool(config.AutomaticDisableChannelEnabled)
	config.OptionMap["AutomaticEnableChannelEnabled"] = strconv.FormatBool(config.AutomaticEnableChannelEnabled)
	config.OptionMap["ApproximateTokenEnabled"] = strconv.FormatBool(config.ApproximateTokenEnabled)
	config.OptionMap["LogConsumeEnabled"] = strconv.FormatBool(config.LogConsumeEnabled)
	config.OptionMap["LogDetailConsumeEnabled"] = strconv.FormatBool(config.LogDetailConsumeEnabled)
	config.OptionMap["LogUpstreamResponseEnabled"] = strconv.FormatBool(config.LogUpstreamResponseEnabled)
	config.OptionMap["LogFullResponseEnabled"] = strconv.FormatBool(config.LogFullResponseEnabled)
	config.OptionMap["LogDownstreamErrorEnabled"] = strconv.FormatBool(config.LogDownstreamErrorEnabled)
	config.OptionMap["LogErrorEnabled"] = strconv.FormatBool(config.LogErrorEnabled)
	config.OptionMap["LogSysInfoEnabled"] = strconv.FormatBool(config.LogSysInfoEnabled)
	config.OptionMap["ClaudeMessageNormalizationEnabled"] = strconv.FormatBool(config.ClaudeMessageNormalizationEnabled)
	config.OptionMap["LogDurationType"] = strconv.Itoa(config.LogDurationType)
	config.OptionMap["LogDurationIncludeRetryEnabled"] = strconv.FormatBool(config.LogDurationIncludeRetryEnabled)
	config.OptionMap["DisplayInCurrencyEnabled"] = strconv.FormatBool(config.DisplayInCurrencyEnabled)
	config.OptionMap["DisplayTokenStatEnabled"] = strconv.FormatBool(config.DisplayTokenStatEnabled)
	config.OptionMap["FloatButtonEnabled"] = strconv.FormatBool(config.FloatButtonEnabled)
	config.OptionMap["UnsubscribeEnabled"] = strconv.FormatBool(config.UnsubscribeEnabled)
	config.OptionMap["UserLogViewEnabled"] = strconv.FormatBool(config.UserLogViewEnabled)
	config.OptionMap["GuestChatPageEnabled"] = strconv.FormatBool(config.GuestChatPageEnabled)
	config.OptionMap["PptGenPageEnabled"] = strconv.FormatBool(config.PptGenPageEnabled)
	config.OptionMap["AgentMenuEnabled"] = strconv.FormatBool(config.AgentMenuEnabled)
	config.OptionMap["GuestQueryEnabled"] = strconv.FormatBool(config.GuestQueryEnabled)
	config.OptionMap["MidjourneyEnabled"] = strconv.FormatBool(config.MidjourneyEnabled)
	config.OptionMap["MidjourneyPlusEnabled"] = strconv.FormatBool(config.MidjourneyPlusEnabled)
	config.OptionMap["MJSensitiveWordsRefundEnabled"] = strconv.FormatBool(config.MJSensitiveWordsRefundEnabled)
	config.OptionMap["MidjourneyRemoveSlashEnabled"] = strconv.FormatBool(config.MidjourneyRemoveSlashEnabled)
	config.OptionMap["MidjourneyV7TurboEnabled"] = strconv.FormatBool(config.MidjourneyV7TurboEnabled)
	config.OptionMap["MidjourneyDraftHalfPriceEnabled"] = strconv.FormatBool(config.MidjourneyDraftHalfPriceEnabled)
	config.OptionMap["MidjourneyCustomImageUrlEnabled"] = strconv.FormatBool(config.MidjourneyCustomImageUrlEnabled)
	config.OptionMap["MidjourneyShowDerivedRatesEnabled"] = strconv.FormatBool(config.MidjourneyShowDerivedRatesEnabled)
	config.OptionMap["ChannelDisableThreshold"] = strconv.FormatFloat(config.ChannelDisableThreshold, 'f', -1, 64)
	config.OptionMap["EmailDomainRestrictionEnabled"] = strconv.FormatBool(config.EmailDomainRestrictionEnabled)
	config.OptionMap["EmailDomainQQNumberOnlyEnabled"] = strconv.FormatBool(config.EmailDomainQQNumberOnlyEnabled)
	config.OptionMap["EmailDomainWhitelist"] = strings.Join(config.EmailDomainWhitelist, ",")
	config.OptionMap["NotificationKeywordBlacklist"] = strings.Join(config.NotificationKeywordBlacklist, ",")
	config.OptionMap["RelayErrForceRetryKeywordList"] = strings.Join(config.RelayErrForceRetryKeywordList, ",")
	config.OptionMap["HideRelayErrorExceptList"] = strings.Join(config.HideRelayErrorExceptList, ",")
	config.OptionMap["RelayErrForceRetryModelList"] = strings.Join(config.RelayErrForceRetryModelList, ",")
	config.OptionMap["DisableChannelHttpStatusCodeList"] = strings.Trim(strings.Replace(fmt.Sprint(config.DisableChannelHttpStatusCodeList), " ", ",", -1), "[]")
	config.OptionMap["CircuitBreakerHttpStatusCodeList"] = strings.Trim(strings.Replace(fmt.Sprint(config.CircuitBreakerHttpStatusCodeList), " ", ",", -1), "[]")
	config.OptionMap["MaxPromptLogLength"] = strconv.FormatInt(config.MaxPromptLogLength, 10)
	config.OptionMap["SMTPServer"] = ""
	config.OptionMap["SMTPFrom"] = ""
	config.OptionMap["SMTPPort"] = strconv.Itoa(config.SMTPPort)
	config.OptionMap["SMTPAccount"] = ""
	config.OptionMap["SMTPToken"] = ""
	config.OptionMap["Notice"] = ""
	config.OptionMap["About"] = ""
	config.OptionMap["HomePageContent"] = ""
	config.OptionMap["Footer"] = config.Footer
	config.OptionMap["HeaderScript"] = config.HeaderScript
	config.OptionMap["SystemName"] = config.SystemName
	config.OptionMap["Logo"] = config.Logo
	config.OptionMap["ServerAddress"] = ""
	config.OptionMap["ImageDownloadEnabled"] = strconv.FormatBool(config.ImageDownloadEnabled)
	config.OptionMap["AntiIngredientEnabled"] = strconv.FormatBool(config.AntiIngredientEnabled)
	config.OptionMap["WorkerUrl"] = config.WorkerUrl
	config.OptionMap["WorkerValidKey"] = config.WorkerValidKey
	config.OptionMap["JWTCrossLoginEnabled"] = strconv.FormatBool(config.JWTCrossLoginEnabled)
	config.OptionMap["JWTAuthEnabled"] = strconv.FormatBool(config.JWTAuthEnabled)
	config.OptionMap["FileSystemServerAddress"] = ""
	config.OptionMap["FileSystemServerEnabled"] = strconv.FormatBool(config.FileSystemServerEnabled)
	config.OptionMap["FileSystemClientInfoLogEnabled"] = strconv.FormatBool(config.FileSystemClientInfoLogEnabled)
	config.OptionMap["FileSystemProxyModeEnabled"] = strconv.FormatBool(config.FileSystemProxyModeEnabled)
	config.OptionMap["FileSystemProxyURL"] = config.FileSystemProxyURL
	config.OptionMap["FileSystemProxyUploadFieldName"] = config.FileSystemProxyUploadFieldName
	config.OptionMap["FileSystemProxyMethod"] = config.FileSystemProxyMethod
	config.OptionMap["FileSystemProxyHeaders"] = config.FileSystemProxyHeaders
	config.OptionMap["FileSystemProxyAuthEnabled"] = strconv.FormatBool(config.FileSystemProxyAuthEnabled)
	config.OptionMap["FileSystemProxyAuthType"] = config.FileSystemProxyAuthType
	config.OptionMap["FileSystemProxyAuthValue"] = config.FileSystemProxyAuthValue
	config.OptionMap["CheckinEnabled"] = strconv.FormatBool(config.CheckinEnabled)
	config.OptionMap["CheckinQuota"] = strconv.FormatInt(config.CheckinQuota, 10)
	config.OptionMap["CheckinCaptchaDifficultyIncreaseEnabled"] = strconv.FormatBool(config.CheckinCaptchaDifficultyIncreaseEnabled)
	config.OptionMap["CheckinCaptchaRandomBackgroundColorEnabled"] = strconv.FormatBool(config.CheckinCaptchaRandomBackgroundColorEnabled)
	config.OptionMap["CheckinCaptchaLengthDuration"] = strconv.Itoa(config.CheckinCaptchaLengthDuration)
	config.OptionMap["CheckinCaptchaLengthIncrease"] = strconv.Itoa(config.CheckinCaptchaLengthIncrease)
	config.OptionMap["CheckinCaptchaNoiseDuration"] = strconv.Itoa(config.CheckinCaptchaNoiseDuration)
	config.OptionMap["CheckinCaptchaNoiseIncrease"] = strconv.Itoa(config.CheckinCaptchaNoiseIncrease)
	config.OptionMap["TransferFee"] = strconv.Itoa(config.TransferFee)
	config.OptionMap["SwitchUIEnabled"] = strconv.FormatBool(config.SwitchUIEnabled)
	config.OptionMap["OnlineTopupEnabled"] = strconv.FormatBool(config.OnlineTopupEnabled)
	config.OptionMap["HideRelayErrorEnabled"] = strconv.FormatBool(config.HideRelayErrorEnabled)
	config.OptionMap["ResponseErrorStillChargeEnabled"] = strconv.FormatBool(config.ResponseErrorStillChargeEnabled)
	config.OptionMap["NewTiktokenEnabled"] = strconv.FormatBool(config.NewTiktokenEnabled)
	config.OptionMap["CustomCircuitBreakerEnabled"] = strconv.FormatBool(config.CustomCircuitBreakerEnabled)
	config.OptionMap["CustomDisableChannelEnabled"] = strconv.FormatBool(config.CustomDisableChannelEnabled)
	config.OptionMap["Say1DirectSuccessEnabled"] = strconv.FormatBool(config.Say1DirectSuccessEnabled)
	config.OptionMap["RequestTruncationEnabled"] = strconv.FormatBool(config.RequestTruncationEnabled)
	config.OptionMap["PayAddress"] = ""
	config.OptionMap["EpayId"] = ""
	config.OptionMap["EpayKey"] = ""
	config.OptionMap["CustomAvailablePayMethods"] = strconv.Itoa(config.CustomAvailablePayMethods)
	config.OptionMap["Price"] = strconv.FormatFloat(config.Price, 'f', -1, 64)
	config.OptionMap["CustomUsdtRate"] = strconv.FormatFloat(config.CustomUsdtRate, 'f', -1, 64)
	config.OptionMap["CustomPayPalUsdRate"] = strconv.FormatFloat(config.CustomPayPalUsdRate, 'f', -1, 64)
	config.OptionMap["PayPalMinimumFee"] = strconv.FormatFloat(config.PayPalMinimumFee, 'f', -1, 64)
	config.OptionMap["CustomEthRate"] = strconv.FormatFloat(config.CustomEthRate, 'f', -1, 64)
	config.OptionMap["MaxTopUpLimit"] = strconv.FormatFloat(config.MaxTopUpLimit, 'f', -1, 64)
	config.OptionMap["CustomEpayCallbackAddress"] = ""
	config.OptionMap["QuotaExpireEnabled"] = strconv.FormatBool(config.QuotaExpireEnabled)
	config.OptionMap["QuotaExpireDays"] = strconv.Itoa(config.QuotaExpireDays)
	config.OptionMap["TopupGroupRatio"] = billingratio.TopupGroupRatio2JSONString()
	config.OptionMap["TopupGroupMinLimit"] = billingratio.TopupGroupMinLimit2JSONString()
	config.OptionMap["Say1DirectWordsMap"] = say1.Say1DirectWordsMap2JSONString()
	config.OptionMap["GitHubClientId"] = ""
	config.OptionMap["GitHubClientSecret"] = ""
	config.OptionMap["TelegramBotToken"] = ""
	config.OptionMap["TelegramBotName"] = ""
	config.OptionMap["SMSAccessKeyId"] = ""
	config.OptionMap["SMSAccessKeySecret"] = ""
	config.OptionMap["SMSSignName"] = ""
	config.OptionMap["SMSTemplateCode"] = ""
	config.OptionMap["WeChatServerAddress"] = ""
	config.OptionMap["WeChatServerToken"] = ""
	config.OptionMap["WeChatAccountQRCodeImageURL"] = ""
	config.OptionMap["MessagePusherAddress"] = ""
	config.OptionMap["MessagePusherToken"] = ""
	config.OptionMap["TurnstileSiteKey"] = ""
	config.OptionMap["TurnstileSecretKey"] = ""
	config.OptionMap["StreamChunkTimeout"] = strconv.Itoa(config.StreamChunkTimeout)
	config.OptionMap["StreamChunkTimeoutWarningLogThreshold"] = strconv.FormatInt(config.StreamChunkTimeoutWarningLogThreshold, 10)
	config.OptionMap["QuotaForNewUser"] = strconv.FormatInt(config.QuotaForNewUser, 10)
	config.OptionMap["QuotaForInviter"] = strconv.FormatInt(config.QuotaForInviter, 10)
	config.OptionMap["QuotaForInvitee"] = strconv.FormatInt(config.QuotaForInvitee, 10)
	config.OptionMap["QuotaRemindThreshold"] = strconv.FormatInt(config.QuotaRemindThreshold, 10)
	config.OptionMap["PreConsumedQuota"] = strconv.FormatInt(config.PreConsumedQuota, 10)
	config.OptionMap["ModelRatio"] = billingratio.ModelRatio2JSONString()
	config.OptionMap["ModelFixedPrice"] = billingratio.ModelFixedPrice2JSONString()
	config.OptionMap["GroupRatio"] = billingratio.GroupRatio2JSONString()
	config.OptionMap["InviteBonusRatio"] = billingratio.InviteBonusRatio2JSONString()
	config.OptionMap["GroupColorMapping"] = billingratio.GroupColorMapping2JSONString()
	config.OptionMap["CustomVerificationTypesConfig"] = common.CustomVerificationTypesConfig2JSONString()
	config.OptionMap["CompletionRatio"] = billingratio.CompletionRatio2JSONString()
	config.OptionMap["TopUpLink"] = config.TopUpLink
	config.OptionMap["StatusPageUrl"] = config.StatusPageUrl
	config.OptionMap["NewHomeConf"] = config.NewHomeConf
	config.OptionMap["ChatLink"] = config.ChatLink
	config.OptionMap["DocumentInfo"] = config.DocumentInfo
	config.OptionMap["QqInfo"] = config.QqInfo
	config.OptionMap["WechatInfo"] = config.WechatInfo
	config.OptionMap["RegisterInfo"] = config.RegisterInfo
	config.OptionMap["QuotaPerUnit"] = strconv.FormatFloat(config.QuotaPerUnit, 'f', -1, 64)
	config.OptionMap["RetryTimes"] = strconv.Itoa(config.RetryTimes)
	config.OptionMap["ReviveRetryTimes"] = strconv.Itoa(config.ReviveRetryTimes)
	config.OptionMap["Theme"] = config.Theme
	config.OptionMap["RetryWithoutRedirectEnabled"] = strconv.FormatBool(config.RetryWithoutRedirectEnabled)
	config.OptionMap["RetryWithoutFailedChannelEnabled"] = strconv.FormatBool(config.RetryWithoutFailedChannelEnabled)
	config.OptionMap["RetryKeepBillingTypeEnabled"] = strconv.FormatBool(config.RetryKeepBillingTypeEnabled)
	config.OptionMap["LogDetailsModelWhitelistEnabled"] = strconv.FormatBool(config.LogDetailsModelWhitelistEnabled)
	config.OptionMap["OpenAIStreamStringBufferEnabled"] = strconv.FormatBool(config.OpenAIStreamStringBufferEnabled)
	config.OptionMap["OpenAIStreamStringBufferSize"] = strconv.Itoa(config.OpenAIStreamStringBufferSize)
	config.OptionMap["RootUserWxPusherUid"] = config.RootUserWxPusherUid
	config.OptionMap["WxPusherAppToken"] = config.WxPusherAppToken
	config.OptionMap["QyWxBotWebhookUrl"] = config.QyWxBotWebhookUrl
	config.OptionMap["CustomAppList"] = config.CustomAppList
	config.OptionMap["CustomThemeConfig"] = config.CustomThemeConfig
	config.OptionMap["CustomDarkThemeConfig"] = config.CustomDarkThemeConfig
	config.OptionMap["SensitiveWordsTips"] = config.SensitiveWordsTips
	config.OptionMap["SensitiveWordsEnabled"] = strconv.FormatBool(config.SensitiveWordsEnabled)
	config.OptionMap["UserTimeoutEnabled"] = strconv.FormatBool(config.UserTimeoutEnabled)
	config.OptionMap["TokenGroupChangeEnabled"] = strconv.FormatBool(config.TokenGroupChangeEnabled)
	config.OptionMap["EmptyPromptReplaceEnabled"] = strconv.FormatBool(config.EmptyPromptReplaceEnabled)
	config.OptionMap["ParseFileUrlEnabled"] = strconv.FormatBool(config.ParseFileUrlEnabled)
	config.OptionMap["NotificationKeywordBlacklistEnabled"] = strconv.FormatBool(config.NotificationKeywordBlacklistEnabled)
	config.OptionMap["RelayErrForceRetryKeywordEnabled"] = strconv.FormatBool(config.RelayErrForceRetryKeywordEnabled)
	config.OptionMap["ChannelAbilityDisableEnabled"] = strconv.FormatBool(config.ChannelAbilityDisableEnabled)
	config.OptionMap["RootUserEmailNotificationEnabled"] = strconv.FormatBool(config.RootUserEmailNotificationEnabled)
	config.OptionMap["RootUserWxPusherNotificationEnabled"] = strconv.FormatBool(config.RootUserWxPusherNotificationEnabled)
	config.OptionMap["RootUserQyWxBotNotificationEnabled"] = strconv.FormatBool(config.RootUserQyWxBotNotificationEnabled)
	config.OptionMap["RootUserRelayErrorNotificationEnabled"] = strconv.FormatBool(config.RootUserRelayErrorNotificationEnabled)
	config.OptionMap["PureHomePageEnabled"] = strconv.FormatBool(config.PureHomePageEnabled)
	config.OptionMap["SiteDescription"] = config.SiteDescription
	config.OptionMap["CaptchaKeyLong"] = strconv.Itoa(config.CaptchaKeyLong)
	config.OptionMap["CaptchaNoiseCount"] = strconv.Itoa(config.CaptchaNoiseCount)
	config.OptionMap["CaptchaBgColor"] = config.CaptchaBgColor
	config.OptionMap["UsingShellApiLogOptimizer"] = strconv.FormatBool(common.ShellApiLogOptimizerEnabled)
	config.OptionMap["PreferOptimizerQueryEnabled"] = strconv.FormatBool(config.PreferOptimizerQueryEnabled)
	config.OptionMap["ShellApiLogOptimizerGateWay"] = common.ShellApiLogOptimizerGateWay
	config.OptionMap["ShellApiLogOptimizerDynamicIndex"] = common.ShellApiLogOptimizerDynamicIndex
	config.OptionMap["ShellApiLogOptimizerAccessToken"] = common.ShellApiLogOptimizerAccessToken
	config.OptionMap["TransferEnabled"] = strconv.FormatBool(config.TransferEnabled)
	config.OptionMap["LimitedAccessURL"] = ""
	config.OptionMap["LimitedAccessType"] = ""
	config.OptionMap["PrivacyPolicy"] = ""
	config.OptionMap["ServiceAgreement"] = ""
	config.OptionMap["NoticeVersion"] = ""
	config.OptionMap["GlobalCustomRequestHeaders"] = config.GlobalCustomRequestHeaders
	config.OptionMap["SpoofIP"] = config.SpoofIP
	config.OptionMap["SpoofIPEnabled"] = strconv.FormatBool(config.SpoofIPEnabled)
	config.OptionMap["EiseEnabled"] = strconv.FormatBool(config.EiseEnabled)
	config.OptionMap["EiseUrl"] = config.EiseUrl
	config.OptionMap["EiseKey"] = config.EiseKey
	config.OptionMap["BatchUpdateMjMode"] = config.BatchUpdateMjMode
	config.OptionMap["BatchUpdateConsiderMemoryQuotaEnabled"] = strconv.FormatBool(config.BatchUpdateConsiderMemoryQuotaEnabled)
	if config.BatchUpdateMjMode == "" {
		config.BatchUpdateMjMode = "single" // 默认使用单个更新模式
	}
	config.OptionMap["BatchUpdateMjWorkerSize"] = strconv.Itoa(config.BatchUpdateMjWorkerSize)
	if config.BatchUpdateMjWorkerSize <= 0 {
		config.BatchUpdateMjWorkerSize = 20
	}
	config.OptionMap["BatchUpdateMjBatchSize"] = strconv.Itoa(config.BatchUpdateMjBatchSize)
	if config.BatchUpdateMjBatchSize <= 0 {
		config.BatchUpdateMjBatchSize = 20
	}
	config.OptionMap["RequestMaxCompatibilityEnabled"] = strconv.FormatBool(config.RequestMaxCompatibilityEnabled)
	config.OptionMap["MaxTokenAutoDetectionEnabled"] = strconv.FormatBool(config.MaxTokenAutoDetectionEnabled)
	config.OptionMap["ModelMaxTokensConfig"] = config.ModelMaxTokensConfig
	config.OptionMap["HideUpstreamApiTypeErrorEnabled"] = strconv.FormatBool(config.HideUpstreamApiTypeErrorEnabled)
	config.OptionMap["LogErrorSamplingRate"] = strconv.FormatFloat(config.LogErrorSamplingRate, 'f', -1, 64)
	config.OptionMap["LogIgnoredErrorCodes"] = config.LogIgnoredErrorCodes
	config.OptionMap["NewRPMEnabled"] = strconv.FormatBool(config.NewRPMEnabled)
	config.OptionMap["TrustUpstreamStreamUsageEnabled"] = strconv.FormatBool(config.TrustUpstreamStreamUsageEnabled)
	config.OptionMap["ForceStreamOptionEnabled"] = strconv.FormatBool(config.ForceStreamOptionEnabled)
	config.OptionMap["ForceDownstreamStreamUsageEnabled"] = strconv.FormatBool(config.ForceDownstreamStreamUsageEnabled)
	config.OptionMap["ChannelMetricsEnabled"] = strconv.FormatBool(config.ChannelMetricsEnabled)
	config.OptionMap["ChannelScoreRoutingEnabled"] = strconv.FormatBool(config.ChannelScoreRoutingEnabled)
	config.OptionMap["GlobalIgnoreWeightCalculationEnabled"] = strconv.FormatBool(config.GlobalIgnoreWeightCalculationEnabled)
	config.OptionMap["GlobalIgnorePriorityEnabled"] = strconv.FormatBool(config.GlobalIgnorePriorityEnabled)
	config.OptionMap["GlobalIgnoreBillingTypeFilteringEnabled"] = strconv.FormatBool(config.GlobalIgnoreBillingTypeFilteringEnabled)
	config.OptionMap["GlobalIgnoreFunctionCallFilteringEnabled"] = strconv.FormatBool(config.GlobalIgnoreFunctionCallFilteringEnabled)
	config.OptionMap["GlobalIgnoreImageSupportFilteringEnabled"] = strconv.FormatBool(config.GlobalIgnoreImageSupportFilteringEnabled)
	config.OptionMap["IgnoreImageErrorButRequestEnabled"] = strconv.FormatBool(config.IgnoreImageErrorButRequestEnabled)
	config.OptionMap["DataExportEnabled"] = strconv.FormatBool(config.DataExportEnabled)
	config.OptionMap["DataExportInterval"] = strconv.Itoa(config.DataExportInterval)
	config.OptionMap["DataExportDefaultTime"] = config.DataExportDefaultTime
	config.OptionMap["DataExportDisplayEnabled"] = strconv.FormatBool(config.DataExportDisplayEnabled)
	config.OptionMap["IgnoreChannelDimensionEnabled"] = strconv.FormatBool(config.IgnoreChannelDimensionEnabled)
	config.OptionMap["AlipayFaceToFaceEnabled"] = strconv.FormatBool(config.AlipayFaceToFaceEnabled)
	config.OptionMap["AlipayAppId"] = config.AlipayAppId
	config.OptionMap["AlipayPrivateKey"] = config.AlipayPrivateKey
	config.OptionMap["AlipayPublicKey"] = config.AlipayPublicKey
	config.OptionMap["AlipayCallbackAddress"] = config.AlipayCallbackAddress
	config.OptionMap["SuixingpayEnabled"] = strconv.FormatBool(config.SuixingpayEnabled)
	config.OptionMap["SuixingpayOrgId"] = config.SuixingpayOrgId
	config.OptionMap["SuixingpayPlatformPublicKey"] = config.SuixingpayPlatformPublicKey
	config.OptionMap["SuixingpayMerchantPrivateKey"] = config.SuixingpayMerchantPrivateKey
	config.OptionMap["SuixingpayMerchantNo"] = config.SuixingpayMerchantNo
	config.OptionMap["SuixingpayCallbackAddress"] = config.SuixingpayCallbackAddress
	config.OptionMap["MidjourneySubmitTimeout"] = strconv.Itoa(config.MidjourneySubmitTimeout)
	config.OptionMap["MidjourneyStartTimeout"] = strconv.Itoa(config.MidjourneyStartTimeout)
	config.OptionMap["MockOpenAICompleteFormatEnabled"] = strconv.FormatBool(config.MockOpenAICompleteFormatEnabled)
	config.OptionMap["PackagePlanCacheEnabled"] = strconv.FormatBool(config.PackagePlanCacheEnabled)
	config.OptionMap["RelayErrForceThrowErrorEnabled"] = strconv.FormatBool(config.RelayErrForceThrowErrorEnabled)
	config.OptionMap["RelayErrForceThrowErrorKeywordList"] = strings.Join(config.RelayErrForceThrowErrorKeywordList, ",")
	config.OptionMap["RelayErrForceThrowErrorModelList"] = strings.Join(config.RelayErrForceThrowErrorModelList, ",")
	config.OptionMap["DisableEntireChannelKeywordsEnabled"] = strconv.FormatBool(config.DisableEntireChannelKeywordsEnabled)
	config.OptionMap["DisableEntireChannelKeywords"] = strings.Join(config.DisableEntireChannelKeywords, ",")
	config.OptionMap["ApiErrorTypeSuffix"] = config.ApiErrorTypeSuffix
	config.OptionMap["CustomHideApiErrorTypes"] = strings.Join(config.CustomHideApiErrorTypes, ",")
	config.OptionMap["MidjourneyBase64StorageAddress"] = config.MidjourneyBase64StorageAddress
	config.OptionMap["GptImageStorageAddress"] = config.GptImageStorageAddress
	config.OptionMap["MidjourneyCustomImageUrl"] = config.MidjourneyCustomImageUrl
	config.OptionMap["MidjourneyBase64StorageRetentionDays"] = strconv.Itoa(config.MidjourneyBase64StorageRetentionDays)
	config.OptionMap["MidjourneyPollDuration"] = strconv.Itoa(config.MidjourneyPollDuration)
	config.OptionMap["MjDiscordCdnProxy"] = config.MjDiscordCdnProxy
	config.OptionMap["ChannelDisableThreshold"] = strconv.FormatFloat(config.ChannelDisableThreshold, 'f', -1, 64)

	// 日志存储配置
	config.OptionMap["LogStorageEnabled"] = strconv.FormatBool(config.LogStorageEnabled)
	config.OptionMap["LogStorageType"] = config.LogStorageType
	config.OptionMap["LogStorageConnectionString"] = config.LogStorageConnectionString
	config.OptionMap["LogStorageDatabase"] = config.LogStorageDatabase
	config.OptionMap["LogStorageTable"] = config.LogStorageTable
	config.OptionMap["LogStorageBatchSize"] = strconv.Itoa(config.LogStorageBatchSize)
	config.OptionMap["LogStorageFlushInterval"] = strconv.Itoa(config.LogStorageFlushInterval)
	config.OptionMap["LogStorageMaxRetries"] = strconv.Itoa(config.LogStorageMaxRetries)
	config.OptionMap["LogStorageFallbackToMySQL"] = strconv.FormatBool(config.LogStorageFallbackToMySQL)
	config.OptionMap["LogStorageAsyncWrite"] = strconv.FormatBool(config.LogStorageAsyncWrite)
	config.OptionMap["LogStorageCompressionEnabled"] = strconv.FormatBool(config.LogStorageCompressionEnabled)

	// ClickHouse配置
	config.OptionMap["ClickHouseHost"] = config.ClickHouseHost
	config.OptionMap["ClickHousePort"] = strconv.Itoa(config.ClickHousePort)
	config.OptionMap["ClickHouseUsername"] = config.ClickHouseUsername
	config.OptionMap["ClickHousePassword"] = config.ClickHousePassword
	config.OptionMap["ClickHouseDatabase"] = config.ClickHouseDatabase
	config.OptionMap["ClickHouseCluster"] = config.ClickHouseCluster

	// Elasticsearch配置
	config.OptionMap["ElasticsearchHosts"] = config.ElasticsearchHosts
	config.OptionMap["ElasticsearchUsername"] = config.ElasticsearchUsername
	config.OptionMap["ElasticsearchPassword"] = config.ElasticsearchPassword
	config.OptionMap["ElasticsearchIndex"] = config.ElasticsearchIndex
	config.OptionMap["ElasticsearchShards"] = strconv.Itoa(config.ElasticsearchShards)
	config.OptionMap["ElasticsearchReplicas"] = strconv.Itoa(config.ElasticsearchReplicas)
	config.OptionMap["ElasticsearchRefreshInterval"] = config.ElasticsearchRefreshInterval

	config.OptionMapRWMutex.Unlock()
	loadOptionsFromDatabase()
}

// loadOptionsFromDatabase 函数从数据库中加载所有的配置选项，并更新 OptionMap
func loadOptionsFromDatabase() {
	options, _ := AllOption()
	for _, option := range options {
		if option.Key == "ModelRatio" {
			option.Value = billingratio.AddNewMissingRatio(option.Value)
		}
		err := updateOptionMap(option.Key, option.Value)
		if err != nil {
			logger.SysError("failed to update option map: " + err.Error())
		}
	}
	// mj自定义url如果为空默认用服务器url
	if config.MidjourneyCustomImageUrl == "" {
		config.MidjourneyCustomImageUrl = config.ServerAddress
	}
}

// SyncOptions 函数是一个无限循环，每隔一段时间就从数据库中同步配置选项。
func SyncOptions(frequency int) {
	for {
		time.Sleep(time.Duration(frequency) * time.Second)
		logger.SysLog("syncing options from database")
		loadOptionsFromDatabase()
	}
}

// UpdateOption 函数用于更新一个配置选项。它首先将新的配置选项保存到数据库，然后更新 OptionMap。
func UpdateOption(key string, value string) error {
	// 对 ShellApiLogOptimizerDynamicIndex 进行特殊校验
	if key == "ShellApiLogOptimizerDynamicIndex" {
		if err := validateIndexName(value); err != nil {
			return err
		}
	}

	// Save to database first
	option := Option{
		Key: key,
	}
	// https://gorm.io/docs/update.html#Save-All-Fields
	DB.FirstOrCreate(&option, Option{Key: key})
	option.Value = value
	// Save is a combination function.
	// If save value does not contain primary key, it will execute Create,
	// otherwise it will execute Update (with all fields).
	DB.Save(&option)
	// Update OptionMap
	return updateOptionMap(key, value)
}

// validateIndexName 验证索引名称是否只包含大小写字母和下划线
func validateIndexName(name string) error {
	if name == "" {
		return nil // 允许空值
	}
	matched, err := regexp.MatchString("^[a-zA-Z_]+$", name)
	if err != nil {
		return fmt.Errorf("验证索引名称时发生错误: %v", err)
	}
	if !matched {
		return fmt.Errorf("索引名称只能包含大小写字母和下划线")
	}
	return nil
}

// updateOptionMap 函数用于更新 OptionMap。它会根据配置选项的键来确定如何处理配置选项的值。
// 如果键以 "Permission" 结尾，那么它会将值转换为整数并更新相应的全局变量。
// 如果键以 "Enabled" 结尾，那么它会将值转换为布尔值并更新相应的全局变量。
// 对于其他的键，它会直接更新相应的全局变量。
func updateOptionMap(key string, value string) (err error) {
	config.OptionMapRWMutex.Lock()
	defer config.OptionMapRWMutex.Unlock()
	config.OptionMap[key] = value
	if strings.HasSuffix(key, "Permission") {
		intValue, _ := strconv.Atoi(value)
		switch key {
		case "FileUploadPermission":
			common.FileUploadPermission = intValue
		case "FileDownloadPermission":
			common.FileDownloadPermission = intValue
		case "ImageUploadPermission":
			common.ImageUploadPermission = intValue
		case "ImageDownloadPermission":
			common.ImageDownloadPermission = intValue
		}
	}
	if strings.HasSuffix(key, "Enabled") {
		boolValue := value == "true"
		switch key {
		case "JWTCrossLoginEnabled":
			config.JWTCrossLoginEnabled = boolValue
		case "JWTAuthEnabled":
			config.JWTAuthEnabled = boolValue
		case "PasswordRegisterEnabled":
			config.PasswordRegisterEnabled = boolValue
		case "PasswordLoginEnabled":
			config.PasswordLoginEnabled = boolValue
		case "EmailVerificationEnabled":
			config.EmailVerificationEnabled = boolValue
		case "GitHubOAuthEnabled":
			config.GitHubOAuthEnabled = boolValue
		case "GoogleOAuthEnabled":
			config.GoogleOAuthEnabled = boolValue
		case "OidcEnabled":
			config.OidcEnabled = boolValue
		case "TelegramOAuthEnabled":
			config.TelegramOAuthEnabled = boolValue
		case "SMSVerificationEnabled":
			config.SMSVerificationEnabled = boolValue
		case "SMSLoginEnabled":
			config.SMSLoginEnabled = boolValue
		case "SMSRegisterEnabled":
			config.SMSRegisterEnabled = boolValue
		case "WeChatAuthEnabled":
			config.WeChatAuthEnabled = boolValue
		case "TurnstileCheckEnabled":
			config.TurnstileCheckEnabled = boolValue
		case "CaptchaCheckEnabled":
			config.CaptchaCheckEnabled = boolValue
		case "RegisterEnabled":
			config.RegisterEnabled = boolValue
		case "EmailDomainRestrictionEnabled":
			config.EmailDomainRestrictionEnabled = boolValue
		case "EmailDomainQQNumberOnlyEnabled":
			config.EmailDomainQQNumberOnlyEnabled = boolValue
		case "AutomaticDisableChannelEnabled":
			config.AutomaticDisableChannelEnabled = boolValue
		case "AutomaticEnableChannelEnabled":
			config.AutomaticEnableChannelEnabled = boolValue
		case "ApproximateTokenEnabled":
			config.ApproximateTokenEnabled = boolValue
		case "LogConsumeEnabled":
			config.LogConsumeEnabled = boolValue
		case "LogDetailConsumeEnabled":
			config.LogDetailConsumeEnabled = boolValue
		case "LogUpstreamResponseEnabled":
			config.LogUpstreamResponseEnabled = boolValue
		case "LogFullResponseEnabled":
			config.LogFullResponseEnabled = boolValue
		case "LogDownstreamErrorEnabled":
			config.LogDownstreamErrorEnabled = boolValue
		case "LogErrorEnabled":
			config.LogErrorEnabled = boolValue
		case "LogSysInfoEnabled":
			config.LogSysInfoEnabled = boolValue
		case "ClaudeMessageNormalizationEnabled":
			config.ClaudeMessageNormalizationEnabled = boolValue
		case "DisplayInCurrencyEnabled":
			config.DisplayInCurrencyEnabled = boolValue
		case "DisplayTokenStatEnabled":
			config.DisplayTokenStatEnabled = boolValue
		case "FloatButtonEnabled":
			config.FloatButtonEnabled = boolValue
		case "UnsubscribeEnabled":
			config.UnsubscribeEnabled = boolValue
		case "UserLogViewEnabled":
			config.UserLogViewEnabled = boolValue
		case "GuestChatPageEnabled":
			config.GuestChatPageEnabled = boolValue
		case "PptGenPageEnabled":
			config.PptGenPageEnabled = boolValue
		case "AgentMenuEnabled":
			config.AgentMenuEnabled = boolValue
		case "GuestQueryEnabled":
			config.GuestQueryEnabled = boolValue
		case "MidjourneyEnabled":
			config.MidjourneyEnabled = boolValue
		case "MidjourneyPlusEnabled":
			config.MidjourneyPlusEnabled = boolValue
		case "MJSensitiveWordsRefundEnabled":
			config.MJSensitiveWordsRefundEnabled = boolValue
		case "RetryWithoutRedirectEnabled":
			config.RetryWithoutRedirectEnabled = boolValue
		case "RetryWithoutFailedChannelEnabled":
			config.RetryWithoutFailedChannelEnabled = boolValue
		case "RetryKeepBillingTypeEnabled":
			config.RetryKeepBillingTypeEnabled = boolValue
		case "OpenAIStreamStringBufferEnabled":
			config.OpenAIStreamStringBufferEnabled = boolValue
		case "LogDetailsModelWhitelistEnabled":
			config.LogDetailsModelWhitelistEnabled = boolValue
		case "FileSystemServerEnabled":
			config.FileSystemServerEnabled = boolValue
		case "FileSystemClientInfoLogEnabled":
			config.FileSystemClientInfoLogEnabled = boolValue
		case "FileSystemProxyModeEnabled":
			config.FileSystemProxyModeEnabled = boolValue
		case "FileSystemProxyAuthEnabled":
			config.FileSystemProxyAuthEnabled = boolValue
		case "CheckinEnabled":
			config.CheckinEnabled = boolValue
		case "CheckinCaptchaDifficultyIncreaseEnabled":
			config.CheckinCaptchaDifficultyIncreaseEnabled = boolValue
		case "CheckinCaptchaRandomBackgroundColorEnabled":
			config.CheckinCaptchaRandomBackgroundColorEnabled = boolValue
		case "OnlineTopupEnabled":
			config.OnlineTopupEnabled = boolValue
		case "HideRelayErrorEnabled":
			config.HideRelayErrorEnabled = boolValue
		case "ResponseErrorStillChargeEnabled":
			config.ResponseErrorStillChargeEnabled = boolValue
		case "NewTiktokenEnabled":
			config.NewTiktokenEnabled = boolValue
		case "CustomCircuitBreakerEnabled":
			config.CustomCircuitBreakerEnabled = boolValue
		case "CustomDisableChannelEnabled":
			config.CustomDisableChannelEnabled = boolValue
		case "Say1DirectSuccessEnabled":
			config.Say1DirectSuccessEnabled = boolValue
		case "RequestTruncationEnabled":
			config.RequestTruncationEnabled = boolValue
		case "SwitchUIEnabled":
			config.SwitchUIEnabled = boolValue
		case "QuotaExpireEnabled":
			config.QuotaExpireEnabled = boolValue
		case "MidjourneyRemoveSlashEnabled":
			config.MidjourneyRemoveSlashEnabled = boolValue
		case "MidjourneyV7TurboEnabled":
			config.MidjourneyV7TurboEnabled = boolValue
		case "MidjourneyDraftHalfPriceEnabled":
			config.MidjourneyDraftHalfPriceEnabled = boolValue
		case "MidjourneyCustomImageUrlEnabled":
			config.MidjourneyCustomImageUrlEnabled = boolValue
		case "MidjourneyShowDerivedRatesEnabled":
			config.MidjourneyShowDerivedRatesEnabled = boolValue
		case "SensitiveWordsEnabled":
			config.SensitiveWordsEnabled = boolValue
		case "TokenGroupChangeEnabled":
			config.TokenGroupChangeEnabled = boolValue
		case "RootUserEmailNotificationEnabled":
			config.RootUserEmailNotificationEnabled = boolValue
		case "NotificationKeywordBlacklistEnabled":
			config.NotificationKeywordBlacklistEnabled = boolValue
		case "RelayErrForceRetryKeywordEnabled":
			config.RelayErrForceRetryKeywordEnabled = boolValue
		case "ChannelAbilityDisableEnabled":
			config.ChannelAbilityDisableEnabled = boolValue
		case "DisableEntireChannelKeywordsEnabled":
			config.DisableEntireChannelKeywordsEnabled = boolValue
		case "EmptyPromptReplaceEnabled":
			config.EmptyPromptReplaceEnabled = boolValue
		case "ParseFileUrlEnabled":
			config.ParseFileUrlEnabled = boolValue
		case "RootUserWxPusherNotificationEnabled":
			config.RootUserWxPusherNotificationEnabled = boolValue
		case "RootUserQyWxBotNotificationEnabled":
			config.RootUserQyWxBotNotificationEnabled = boolValue
		case "RootUserRelayErrorNotificationEnabled":
			config.RootUserRelayErrorNotificationEnabled = boolValue
		case "PureHomePageEnabled":
			config.PureHomePageEnabled = boolValue
		case "ShellApiLogOptimizerEnabled":
			common.ShellApiLogOptimizerEnabled = boolValue
		case "PreferOptimizerQueryEnabled":
			config.PreferOptimizerQueryEnabled = boolValue
		case "TransferEnabled":
			config.TransferEnabled = boolValue
		case "ImageDownloadEnabled":
			config.ImageDownloadEnabled = boolValue
		case "AntiIngredientEnabled":
			config.AntiIngredientEnabled = boolValue
		case "SpoofIPEnabled":
			config.SpoofIPEnabled = boolValue
		case "EiseEnabled":
			config.EiseEnabled = boolValue
		case "RequestMaxCompatibilityEnabled":
			config.RequestMaxCompatibilityEnabled = boolValue
		case "MaxTokenAutoDetectionEnabled":
			config.MaxTokenAutoDetectionEnabled = boolValue
		case "HideUpstreamApiTypeErrorEnabled":
			config.HideUpstreamApiTypeErrorEnabled = boolValue
		case "NewRPMEnabled":
			config.NewRPMEnabled = boolValue
		case "LogDurationIncludeRetryEnabled":
			config.LogDurationIncludeRetryEnabled = boolValue
		case "TrustUpstreamStreamUsageEnabled":
			config.TrustUpstreamStreamUsageEnabled = boolValue
		case "ForceStreamOptionEnabled":
			config.ForceStreamOptionEnabled = boolValue
		case "ForceDownstreamStreamUsageEnabled":
			config.ForceDownstreamStreamUsageEnabled = boolValue
		case "ChannelMetricsEnabled":
			config.ChannelMetricsEnabled = boolValue
		case "ChannelScoreRoutingEnabled":
			config.ChannelScoreRoutingEnabled = boolValue
		case "DataExportEnabled":
			config.DataExportEnabled = boolValue
		case "DataExportDisplayEnabled":
			config.DataExportDisplayEnabled = boolValue
		case "IgnoreChannelDimensionEnabled":
			config.IgnoreChannelDimensionEnabled = boolValue
		case "AlipayFaceToFaceEnabled":
			config.AlipayFaceToFaceEnabled = boolValue
		case "SuixingpayEnabled":
			config.SuixingpayEnabled = boolValue
		case "MockOpenAICompleteFormatEnabled":
			config.MockOpenAICompleteFormatEnabled = boolValue
		case "PackagePlanCacheEnabled":
			config.PackagePlanCacheEnabled = boolValue
		case "GlobalIgnoreWeightCalculationEnabled":
			config.GlobalIgnoreWeightCalculationEnabled = boolValue
		case "GlobalIgnorePriorityEnabled":
			config.GlobalIgnorePriorityEnabled = boolValue
			// 如果开启了全局忽略优先级，自动开启全局忽略权重计算
			if boolValue && !config.GlobalIgnoreWeightCalculationEnabled {
				config.GlobalIgnoreWeightCalculationEnabled = true
				config.OptionMap["GlobalIgnoreWeightCalculationEnabled"] = "true"
			}
		case "GlobalIgnoreBillingTypeFilteringEnabled":
			config.GlobalIgnoreBillingTypeFilteringEnabled = boolValue
		case "GlobalIgnoreFunctionCallFilteringEnabled":
			config.GlobalIgnoreFunctionCallFilteringEnabled = boolValue
		case "GlobalIgnoreImageSupportFilteringEnabled":
			config.GlobalIgnoreImageSupportFilteringEnabled = boolValue
		case "IgnoreImageErrorButRequestEnabled":
			config.IgnoreImageErrorButRequestEnabled = boolValue
		case "BatchUpdateConsiderMemoryQuotaEnabled":
			config.BatchUpdateConsiderMemoryQuotaEnabled = boolValue
		case "UserTimeoutEnabled":
			config.UserTimeoutEnabled = boolValue
		case "LogStorageEnabled":
			config.LogStorageEnabled = boolValue
			// 如果启用状态发生变化，重新初始化LogManager
			if boolValue {
				logManager := GetLogManager()
				if err := logManager.Initialize(); err != nil {
					logger.SysError("Failed to reinitialize log manager: " + err.Error())
				}
			}
		case "LogStorageFallbackToMySQL":
			config.LogStorageFallbackToMySQL = boolValue
		case "LogStorageAsyncWrite":
			config.LogStorageAsyncWrite = boolValue
		case "LogStorageCompressionEnabled":
			config.LogStorageCompressionEnabled = boolValue
		}
	}
	switch key {
	case "EmailDomainWhitelist":
		config.EmailDomainWhitelist = strings.Split(value, ",")
	case "NotificationKeywordBlacklist":
		config.NotificationKeywordBlacklist = strings.Split(value, ",")
	case "RelayErrForceRetryKeywordList":
		config.RelayErrForceRetryKeywordList = strings.Split(value, ",")
	case "HideRelayErrorExceptList":
		config.HideRelayErrorExceptList = strings.Split(value, ",")
	case "RelayErrForceRetryModelList":
		config.RelayErrForceRetryModelList = strings.Split(value, ",")
		if len(config.RelayErrForceRetryModelList) == 1 && config.RelayErrForceRetryModelList[0] == "" {
			config.RelayErrForceRetryModelList = []string{}
		}
	case "ApiErrorTypeSuffix":
		config.ApiErrorTypeSuffix = value
	case "CustomHideApiErrorTypes":
		var errorTypes []string
		if value != "" {
			err = json.Unmarshal([]byte(value), &errorTypes)
			if err == nil {
				config.CustomHideApiErrorTypes = errorTypes
			}
		} else {
			config.CustomHideApiErrorTypes = []string{}
		}
	case "DisableChannelHttpStatusCodeList":
		config.DisableChannelHttpStatusCodeList = func(s string) []int {
			var result []int
			for _, v := range strings.Split(s, ",") {
				i, _ := strconv.Atoi(v)
				result = append(result, i)
			}
			return result
		}(value)
	case "CircuitBreakerHttpStatusCodeList":
		config.CircuitBreakerHttpStatusCodeList = func(s string) []int {
			var result []int
			for _, v := range strings.Split(s, ",") {
				i, _ := strconv.Atoi(v)
				result = append(result, i)
			}
			return result
		}(value)
	case "LogDurationType":
		intValue, _ := strconv.Atoi(value)
		if intValue <= 0 || intValue > 3 {
			config.LogDurationType = 2
		} else {
			config.LogDurationType = intValue
		}
	case "MaxPromptLogLength":
		intValue, _ := strconv.ParseInt(value, 10, 64)
		config.MaxPromptLogLength = intValue
	case "SMTPServer":
		config.SMTPServer = value
	case "SMTPPort":
		intValue, _ := strconv.Atoi(value)
		config.SMTPPort = intValue
	case "SMTPAccount":
		config.SMTPAccount = value
	case "SMTPFrom":
		config.SMTPFrom = value
	case "SMTPToken":
		config.SMTPToken = value
	case "EmailProxyServer":
		config.EmailProxyServer = value
	case "EmailProxyAuth":
		config.EmailProxyAuth = value
	case "ServerAddress":
		config.ServerAddress = value
	case "WorkerUrl":
		config.WorkerUrl = value
	case "WorkerValidKey":
		config.WorkerValidKey = value
	case "MidjourneyCustomImageUrl":
		config.MidjourneyCustomImageUrl = value
	case "LogStorageType":
		config.LogStorageType = value
		// 如果存储类型发生变化，重新初始化LogManager
		if config.LogStorageEnabled {
			logManager := GetLogManager()
			if err := logManager.SwitchStorage(value, config.LogStorageConnectionString); err != nil {
				logger.SysError("Failed to switch log storage: " + err.Error())
			}
		}
	case "LogStorageConnectionString":
		config.LogStorageConnectionString = value
	case "LogStorageDatabase":
		config.LogStorageDatabase = value
	case "LogStorageTable":
		config.LogStorageTable = value
	case "LogStorageBatchSize":
		intValue, _ := strconv.Atoi(value)
		if intValue > 0 {
			config.LogStorageBatchSize = intValue
		}
	case "LogStorageFlushInterval":
		intValue, _ := strconv.Atoi(value)
		if intValue > 0 {
			config.LogStorageFlushInterval = intValue
		}
	case "LogStorageMaxRetries":
		intValue, _ := strconv.Atoi(value)
		if intValue > 0 {
			config.LogStorageMaxRetries = intValue
		}
	case "ClickHouseHost":
		config.ClickHouseHost = value
	case "ClickHousePort":
		intValue, _ := strconv.Atoi(value)
		if intValue > 0 {
			config.ClickHousePort = intValue
		}
	case "ClickHouseUsername":
		config.ClickHouseUsername = value
	case "ClickHousePassword":
		config.ClickHousePassword = value
	case "ClickHouseDatabase":
		config.ClickHouseDatabase = value
	case "ClickHouseCluster":
		config.ClickHouseCluster = value
	case "ElasticsearchHosts":
		config.ElasticsearchHosts = value
	case "ElasticsearchUsername":
		config.ElasticsearchUsername = value
	case "ElasticsearchPassword":
		config.ElasticsearchPassword = value
	case "ElasticsearchIndex":
		config.ElasticsearchIndex = value
	case "ElasticsearchShards":
		intValue, _ := strconv.Atoi(value)
		if intValue > 0 {
			config.ElasticsearchShards = intValue
		}
	case "ElasticsearchReplicas":
		intValue, _ := strconv.Atoi(value)
		if intValue >= 0 {
			config.ElasticsearchReplicas = intValue
		}
	case "ElasticsearchRefreshInterval":
		config.ElasticsearchRefreshInterval = value
	case "MjDiscordCdnProxy":
		config.MjDiscordCdnProxy = value
	case "MidjourneyBase64StorageAddress":
		config.MidjourneyBase64StorageAddress = value
	case "GptImageStorageAddress":
		config.GptImageStorageAddress = value
	case "MidjourneyBase64StorageRetentionDays":
		config.MidjourneyBase64StorageRetentionDays, _ = strconv.Atoi(value)
	case "FileSystemServerAddress":
		config.FileSystemServerAddress = value
	case "PayAddress":
		config.PayAddress = value
	case "PayAddress2":
		config.PayAddress2 = value
	case "EpayId":
		config.EpayId = value
	case "EpayId2":
		config.EpayId2 = value
	case "EpayKey":
		config.EpayKey = value
	case "EpayKey2":
		config.EpayKey2 = value
	case "CustomAvailablePayMethods":
		config.CustomAvailablePayMethods, _ = strconv.Atoi(value)
	case "CustomAvailablePayMethods2":
		config.CustomAvailablePayMethods2, _ = strconv.Atoi(value)
	case "Price":
		config.Price, _ = strconv.ParseFloat(value, 64)
	case "CustomUsdtRate":
		config.CustomUsdtRate, _ = strconv.ParseFloat(value, 64)
	case "CustomPayPalUsdRate":
		config.CustomPayPalUsdRate, _ = strconv.ParseFloat(value, 64)
	case "PayPalMinimumFee":
		config.PayPalMinimumFee, _ = strconv.ParseFloat(value, 64)
	case "CustomEthRate":
		config.CustomEthRate, _ = strconv.ParseFloat(value, 64)
	case "MaxTopUpLimit":
		config.MaxTopUpLimit, _ = strconv.ParseFloat(value, 64)
	case "CustomEpayCallbackAddress":
		config.CustomEpayCallbackAddress = value
	case "CustomEpayCallbackAddress2":
		config.CustomEpayCallbackAddress2 = value
	case "QuotaExpireDays":
		config.QuotaExpireDays, _ = strconv.Atoi(value)
	case "TopupGroupRatio":
		err = billingratio.UpdateTopupGroupRatioByJSONString(value)
	case "TopupGroupMinLimit":
		err = billingratio.UpdateTopupGroupMinLimitByJSONString(value)
	case "Say1DirectWordsMap":
		err = say1.UpdateSay1DirectWordsMapByJSONString(value)
	case "RequestTruncationMap":
		err = config.UpdateRequestTruncationMapByJSONString(value)
	case "RequestTruncationOriginMap":
		err = config.UpdateRequestTruncationOriginMapByJSONString(value)
	case "GitHubClientId":
		config.GitHubClientId = value
	case "GitHubClientSecret":
		config.GitHubClientSecret = value
	case "GoogleClientId":
		config.GoogleClientId = value
	case "GoogleClientSecret":
		config.GoogleClientSecret = value
	case "LarkClientId":
		config.LarkClientId = value
	case "LarkClientSecret":
		config.LarkClientSecret = value
	case "OidcClientId":
		config.OidcClientId = value
	case "OidcClientSecret":
		config.OidcClientSecret = value
	case "OidcWellKnown":
		config.OidcWellKnown = value
	case "OidcAuthorizationEndpoint":
		config.OidcAuthorizationEndpoint = value
	case "OidcTokenEndpoint":
		config.OidcTokenEndpoint = value
	case "OidcUserinfoEndpoint":
		config.OidcUserinfoEndpoint = value
	case "TelegramBotToken":
		config.TelegramBotToken = value
	case "TelegramBotName":
		config.TelegramBotName = value
	case "StatusPageUrl":
		config.StatusPageUrl = value
	case "NavExtMenus":
		config.NavExtMenus = value
	case "NewHomeConf":
		config.NewHomeConf = value
	case "SMSAccessKeyId":
		config.SMSAccessKeyId = value
	case "SMSAccessKeySecret":
		config.SMSAccessKeySecret = value
	case "SMSSignName":
		config.SMSSignName = value
	case "SMSTemplateCode":
		config.SMSTemplateCode = value
	case "Footer":
		config.Footer = value
	case "HeaderScript":
		config.HeaderScript = value
	case "SystemName":
		config.SystemName = value
	case "Logo":
		config.Logo = value
	case "WeChatServerAddress":
		config.WeChatServerAddress = value
	case "WeChatServerToken":
		config.WeChatServerToken = value
	case "WeChatAccountQRCodeImageURL":
		config.WeChatAccountQRCodeImageURL = value
	case "MessagePusherAddress":
		config.MessagePusherAddress = value
	case "MessagePusherToken":
		config.MessagePusherToken = value
	case "TurnstileSiteKey":
		config.TurnstileSiteKey = value
	case "TurnstileSecretKey":
		config.TurnstileSecretKey = value
	case "StreamChunkTimeout":
		config.StreamChunkTimeout, _ = strconv.Atoi(value)
	case "StreamChunkTimeoutWarningLogThreshold":
		config.StreamChunkTimeoutWarningLogThreshold, _ = strconv.ParseInt(value, 10, 64)
	case "QuotaForNewUser":
		config.QuotaForNewUser, _ = strconv.ParseInt(value, 10, 64)
	case "QuotaForInviter":
		config.QuotaForInviter, _ = strconv.ParseInt(value, 10, 64)
	case "QuotaForInvitee":
		config.QuotaForInvitee, _ = strconv.ParseInt(value, 10, 64)
	case "QuotaRemindThreshold":
		config.QuotaRemindThreshold, _ = strconv.ParseInt(value, 10, 64)
	case "PreConsumedQuota":
		config.PreConsumedQuota, _ = strconv.ParseInt(value, 10, 64)
	case "RetryTimes":
		config.RetryTimes, _ = strconv.Atoi(value)
	case "ReviveRetryTimes":
		config.ReviveRetryTimes, _ = strconv.Atoi(value)
	case "ModelRatio":
		err = billingratio.UpdateModelRatioByJSONString(value)
	case "ModelFixedPrice":
		err = billingratio.UpdateModelFixedPriceByJSONString(value)
	case "GroupRatio":
		err = billingratio.UpdateGroupRatioByJSONString(value)
	case "InviteBonusRatio":
		err = billingratio.UpdateInviteBonusRatioByJSONString(value)
	case "GroupColorMapping":
		err = billingratio.UpdateGroupColorMappingByJSONString(value)
	case "CustomVerificationTypesConfig":
		err = common.UpdateCustomVerificationTypesConfigByJSONString(value)
	case "CompletionRatio":
		err = billingratio.UpdateCompletionRatioByJSONString(value)
	case "TopUpLink":
		config.TopUpLink = value
	case "ChatLink":
		config.ChatLink = value
	case "ChannelDisableThreshold":
		config.ChannelDisableThreshold, _ = strconv.ParseFloat(value, 64)
	case "QuotaPerUnit":
		config.QuotaPerUnit, _ = strconv.ParseFloat(value, 64)
	case "Theme":
		config.Theme = value
	case "LogDetailsModelWhitelist":
		config.LogDetailsModelWhitelist = value
	case "LicenseInstanceId":
		config.LicenseInstanceId = value
	case "OpenAIStreamStringBufferSize":
		config.OpenAIStreamStringBufferSize, _ = strconv.Atoi(value)
	case "RootUserWxPusherUid":
		config.RootUserWxPusherUid = value
	case "WxPusherAppToken":
		config.WxPusherAppToken = value
	case "QyWxBotWebhookUrl":
		config.QyWxBotWebhookUrl = value
	case "CustomThemeConfig":
		config.CustomThemeConfig = value
	case "CustomDarkThemeConfig":
		config.CustomDarkThemeConfig = value
	case "SensitiveWordsTips":
		config.SensitiveWordsTips = value
	case "CustomAppList":
		config.CustomAppList = value
	case "SiteDescription":
		config.SiteDescription = value
	case "CaptchaKeyLong":
		config.CaptchaKeyLong, _ = strconv.Atoi(value)
	case "CaptchaNoiseCount":
		config.CaptchaNoiseCount, _ = strconv.Atoi(value)
	case "CaptchaBgColor":
		config.CaptchaBgColor = value
	case "ShellApiLogOptimizerGateWay":
		common.ShellApiLogOptimizerGateWay = value
	case "ShellApiLogOptimizerDynamicIndex":
		common.ShellApiLogOptimizerDynamicIndex = value
	case "ShellApiLogOptimizerAccessToken":
		common.ShellApiLogOptimizerAccessToken = value
	case "CheckinQuota":
		config.CheckinQuota, _ = strconv.ParseInt(value, 10, 64)
	case "CheckinCaptchaLengthDuration":
		config.CheckinCaptchaLengthDuration, _ = strconv.Atoi(value)
	case "CheckinCaptchaLengthIncrease":
		config.CheckinCaptchaLengthIncrease, _ = strconv.Atoi(value)
	case "CheckinCaptchaNoiseDuration":
		config.CheckinCaptchaNoiseDuration, _ = strconv.Atoi(value)
	case "CheckinCaptchaNoiseIncrease":
		config.CheckinCaptchaNoiseIncrease, _ = strconv.Atoi(value)
	case "LimitedAccessURL":
		config.LimitedAccessURL = value
	case "LimitedAccessConfigs":
		config.LimitedAccessConfigs = value
	case "NoticeVersion":
		config.NoticeVersion = value
	case "LimitedAccessType":
		config.LimitedAccessType = value
	case "PrivacyPolicy":
		config.PrivacyPolicy = value
	case "ServiceAgreement":
		config.ServiceAgreement = value
	case "EmptyPromptReplaceContent":
		config.EmptyPromptReplaceContent = value
	case "GlobalCustomRequestHeaders":
		config.GlobalCustomRequestHeaders = value
	case "SpoofIP":
		config.SpoofIP = value
	case "EiseUrl":
		config.EiseUrl = value
	case "EiseKey":
		config.EiseKey = value
	case "BatchUpdateMjMode":
		switch value {
		case "single", "batch", "pool_single", "pool_batch", "full_concurrent":
			config.BatchUpdateMjMode = value
		default:
			config.BatchUpdateMjMode = "single"
			logger.SysLog(fmt.Sprintf("无效的MJ任务更新模式：%s，已设置为默认的单个更新模式", value))
		}
	case "BatchUpdateMjWorkerSize":
		size, err := strconv.Atoi(value)
		if err != nil {
			return err
		}
		if size <= 0 {
			size = 20
		} else if size > 200 {
			size = 200
		}
		config.BatchUpdateMjWorkerSize = size
	case "BatchUpdateMjBatchSize":
		size, err := strconv.Atoi(value)
		if err != nil {
			return err
		}
		if size <= 0 {
			size = 20
		} else if size > 100 {
			size = 100
		}
		config.BatchUpdateMjBatchSize = size
	case "LogErrorSamplingRate":
		config.LogErrorSamplingRate, _ = strconv.ParseFloat(value, 64)
		if config.LogErrorSamplingRate < 0 {
			config.LogErrorSamplingRate = 0
		} else if config.LogErrorSamplingRate > 1 {
			config.LogErrorSamplingRate = 1
		}
	case "LogIgnoredErrorCodes":
		config.LogIgnoredErrorCodes = value
	case "ModelMaxTokensConfig":
		config.ModelMaxTokensConfig = value
		// 重新加载模型token限制配置
		config.LoadModelMaxTokensFromConfig()
	case "DataExportInterval":
		intValue, _ := strconv.Atoi(value)
		if intValue > 0 {
			config.DataExportInterval = intValue
		}
	case "DataExportDefaultTime":
		config.DataExportDefaultTime = value
	case "AlipayAppId":
		config.AlipayAppId = value
	case "AlipayPrivateKey":
		config.AlipayPrivateKey = value
	case "AlipayPublicKey":
		config.AlipayPublicKey = value
	case "AlipayCallbackAddress":
		config.AlipayCallbackAddress = value
	case "SuixingpayOrgId":
		config.SuixingpayOrgId = value
	case "SuixingpayPlatformPublicKey":
		config.SuixingpayPlatformPublicKey = value
	case "SuixingpayMerchantPrivateKey":
		config.SuixingpayMerchantPrivateKey = value
	case "SuixingpayMerchantNo":
		config.SuixingpayMerchantNo = value
	case "SuixingpayCallbackAddress":
		config.SuixingpayCallbackAddress = value
	case "MidjourneySubmitTimeout":
		timeout, err := strconv.Atoi(value)
		if err != nil {
			return err
		}
		if timeout <= 0 {
			timeout = 86400 // 默认24小时
		}
		config.MidjourneySubmitTimeout = timeout
	case "MidjourneyStartTimeout":
		timeout, err := strconv.Atoi(value)
		if err != nil {
			return err
		}
		if timeout <= 0 {
			timeout = 600 // 默认10分钟
		}
		config.MidjourneyStartTimeout = timeout
	case "RelayErrForceThrowErrorEnabled":
		config.RelayErrForceThrowErrorEnabled = value == "true"
	case "RelayErrForceThrowErrorKeywordList":
		config.RelayErrForceThrowErrorKeywordList = strings.Split(value, ",")
		if len(config.RelayErrForceThrowErrorKeywordList) == 1 && config.RelayErrForceThrowErrorKeywordList[0] == "" {
			config.RelayErrForceThrowErrorKeywordList = []string{}
		}
	case "RelayErrForceThrowErrorModelList":
		config.RelayErrForceThrowErrorModelList = strings.Split(value, ",")
		if len(config.RelayErrForceThrowErrorModelList) == 1 && config.RelayErrForceThrowErrorModelList[0] == "" {
			config.RelayErrForceThrowErrorModelList = []string{}
		}
	case "DisableEntireChannelKeywords":
		config.DisableEntireChannelKeywords = strings.Split(value, ",")
		if len(config.DisableEntireChannelKeywords) == 1 && config.DisableEntireChannelKeywords[0] == "" {
			config.DisableEntireChannelKeywords = []string{}
		}
		// 自动开启禁用整个渠道的关键词列表
		if len(config.DisableEntireChannelKeywords) > 0 {
			config.DisableEntireChannelKeywordsEnabled = true
			config.OptionMap["DisableEntireChannelKeywordsEnabled"] = "true"
		}
	case "FileSystemProxyURL":
		config.FileSystemProxyURL = value
	case "FileSystemProxyUploadFieldName":
		config.FileSystemProxyUploadFieldName = value
	case "FileSystemProxyMethod":
		config.FileSystemProxyMethod = value
	case "FileSystemProxyHeaders":
		config.FileSystemProxyHeaders = value
	case "FileSystemProxyAuthType":
		config.FileSystemProxyAuthType = value
	case "FileSystemProxyAuthValue":
		config.FileSystemProxyAuthValue = value
	case "DocumentInfo":
		config.DocumentInfo = value
	case "QqInfo":
		config.QqInfo = value
	case "WechatInfo":
		config.WechatInfo = value
	case "RegisterInfo":
		config.RegisterInfo = value
	}

	return err
}

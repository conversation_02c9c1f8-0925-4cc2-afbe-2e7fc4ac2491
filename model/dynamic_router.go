package model

// DynamicRouterGroup 对应router.json中的顶级组
type DynamicRouterGroup struct {
	ID        uint   `json:"id" gorm:"primaryKey"`
	GroupID   string `json:"group_id" gorm:"uniqueIndex:idx_group_id,length:191"` // 对应router.json中的id，指定索引长度
	Title     string `json:"title"`
	Base      string `json:"base"`
	Roles     string `json:"roles"` // 存储为JSON字符串
	CreatedAt int64  `json:"created_at" gorm:"bigint;index"`
	UpdatedAt int64  `json:"updated_at" gorm:"bigint"`
	Enabled   bool   `json:"enabled" gorm:"default:true"`
}

// DynamicRouterUpstream 对应router.json中的upstream
type DynamicRouterUpstream struct {
	ID         uint   `json:"id" gorm:"primaryKey"`
	GroupID    uint   `json:"group_id" gorm:"index"`                               // 关联到DynamicRouterGroup
	UpstreamID string `json:"upstream_id" gorm:"index:idx_upstream_id,length:191"` // 对应router.json中的id
	Host       string `json:"host"`
	TrimPrefix string `json:"trim_prefix"`
	Header     string `json:"header"` // 存储为JSON字符串
	Transform  string `json:"transform"`
	Priority   int    `json:"priority" gorm:"default:0"` // 优先级，数字越大优先级越高
	Weight     int    `json:"weight" gorm:"default:1"`   // 权重，用于负载均衡
	Enabled    bool   `json:"enabled" gorm:"default:true"`
	CreatedAt  int64  `json:"created_at" gorm:"bigint;index"`
	UpdatedAt  int64  `json:"updated_at" gorm:"bigint"`
}

// DynamicRouterEndpoint 对应router.json中的endpoint
type DynamicRouterEndpoint struct {
	ID                  uint    `json:"id" gorm:"primaryKey"`
	GroupID             uint    `json:"group_id" gorm:"index"` // 关联到DynamicRouterGroup
	Title               string  `json:"title"`
	Path                string  `json:"path"`
	Method              string  `json:"method"`
	Sub                 float64 `json:"sub" gorm:"default:0"`
	SubMatch            string  `json:"sub_match"`
	SubPriceMatches     string  `json:"sub_price_matches"`
	OkMatch             string  `json:"ok_match"`
	QuotaFields         string  `json:"quota_fields"` // 存储为JSON字符串，支持多字段计费配置
	Desc                string  `json:"desc"`
	Header              string  `json:"header"`
	UseClientHeader     string  `json:"use_client_header"`
	UseClientHeaderOver bool    `json:"use_client_header_over" gorm:"default:false"`
	Sse                 bool    `json:"sse" gorm:"default:false"`
	EnableTransform     bool    `json:"enable_transform" gorm:"default:false"`
	Check               string  `json:"check"`
	Enabled             bool    `json:"enabled" gorm:"default:true"`
	CreatedAt           int64   `json:"created_at" gorm:"bigint;index"`
	UpdatedAt           int64   `json:"updated_at" gorm:"bigint"`
}

package model

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/songquanpeng/one-api/common/config"

	"github.com/songquanpeng/one-api/common"
	"go.mongodb.org/mongo-driver/bson"
)

// RPM统计的数据结构
type RPMStats struct {
	Requests               int   `json:"requests"`
	Quota                  int   `json:"quota"`
	Tokens                 int   `json:"tokens"`
	TotalDuration          int64 `json:"total_duration"`
	TotalRequestDuration   int64 `json:"total_request_duration"`
	TotalFirstByteDuration int64 `json:"total_first_byte_duration"`
	RequestCount           int   `json:"request_count"`
}

// 通用的Redis统计数据存储方法
func StoreRedisStats(userId int, modelName string, channelId int, tokenName string, tokenKey string, tokenGroup string, ip string,
	quota int, tokens int, requestDuration int64, responseFirstByteDuration int64, totalDuration int64, other string) error {
	if !common.RedisEnabled || !config.NewRPMEnabled {
		return nil
	}

	now := time.Now()
	minKey := now.Format("200601021504")

	// 构建复合键
	compositeKey := fmt.Sprintf(
		"user:%d|model:%s|channel:%d|token_name:%s|token_key:%s|token_group:%s|ip:%s",
		userId, modelName, channelId, tokenName, tokenKey, tokenGroup, ip,
	)

	ctx := context.Background()
	pipe := common.RDB.Pipeline()

	// 主统计数据
	statsKey := "log_stats:" + minKey
	pipe.HIncrBy(ctx, statsKey, compositeKey+":requests", 1)
	pipe.HIncrBy(ctx, statsKey, compositeKey+":quota", int64(quota))
	pipe.HIncrBy(ctx, statsKey, compositeKey+":tokens", int64(tokens))
	pipe.HIncrBy(ctx, statsKey, compositeKey+":duration", totalDuration)
	pipe.HSet(ctx, statsKey, compositeKey+":timestamp", now.Unix())
	if other != "" {
		pipe.HSet(ctx, statsKey, compositeKey+":other", other)
	}
	pipe.Expire(ctx, statsKey, 2*time.Minute)

	// 按模型统计
	modelKey := fmt.Sprintf("model_stats:%s:%s", modelName, minKey)
	pipe.HIncrBy(ctx, modelKey, "requests", 1)
	pipe.HIncrBy(ctx, modelKey, "tokens", int64(tokens))
	pipe.HIncrBy(ctx, modelKey, "quota", int64(quota))
	pipe.HIncrBy(ctx, modelKey, "total_duration", totalDuration)
	pipe.Expire(ctx, modelKey, 2*time.Minute)

	// 按用户统计
	userKey := fmt.Sprintf("user_stats:%d:%s", userId, minKey)
	pipe.HIncrBy(ctx, userKey, "requests", 1)
	pipe.HIncrBy(ctx, userKey, "tokens", int64(tokens))
	pipe.HIncrBy(ctx, userKey, "quota", int64(quota))
	pipe.HIncrBy(ctx, userKey, "total_duration", totalDuration)
	pipe.Expire(ctx, userKey, 2*time.Minute)

	// 按IP统计
	ipKey := fmt.Sprintf("ip_stats:%s:%s", ip, minKey)
	pipe.HIncrBy(ctx, ipKey, "requests", 1)
	pipe.HIncrBy(ctx, ipKey, "tokens", int64(tokens))
	pipe.HIncrBy(ctx, ipKey, "quota", int64(quota))
	pipe.HIncrBy(ctx, ipKey, "total_duration", totalDuration)
	pipe.Expire(ctx, ipKey, 2*time.Minute)

	// 性能统计
	perfKey := fmt.Sprintf("perf_stats:%s:%s", modelName, minKey)
	pipe.HIncrBy(ctx, perfKey, "total_request_duration", requestDuration)
	pipe.HIncrBy(ctx, perfKey, "total_first_byte_duration", responseFirstByteDuration)
	pipe.HIncrBy(ctx, perfKey, "total_duration", totalDuration)
	pipe.HIncrBy(ctx, perfKey, "request_count", 1)
	pipe.Expire(ctx, perfKey, 2*time.Minute)

	_, err := pipe.Exec(ctx)
	return err
}

// 通用的Redis统计数据查询方法
func GetRedisStats(conditions map[string]string) (RPMStats, error) {
	stats := RPMStats{}

	if !common.RedisEnabled || !config.NewRPMEnabled {
		return stats, nil
	}

	now := time.Now().Unix()
	oneMinuteAgo := now - 60

	currentTime := time.Now()
	currentKey := currentTime.Format("200601021504")
	prevKey := currentTime.Add(-1 * time.Minute).Format("200601021504")

	ctx := context.Background()
	for _, key := range []string{currentKey, prevKey} {
		statsKey := "log_stats:" + key

		data, err := common.RDB.HGetAll(ctx, statsKey).Result()
		if err != nil {
			continue
		}

		for field, value := range data {
			parts := strings.Split(field, "|")
			matches := true

			for condKey, condValue := range conditions {
				found := false
				for _, part := range parts {
					if strings.HasPrefix(part, condKey+":") && strings.TrimPrefix(part, condKey+":") == condValue {
						found = true
						break
					}
				}
				if !found {
					matches = false
					break
				}
			}

			if !matches {
				continue
			}

			timestampField := strings.TrimSuffix(field, ":requests")
			timestampField = strings.TrimSuffix(timestampField, ":quota")
			timestampField = strings.TrimSuffix(timestampField, ":tokens")
			timestampField = strings.TrimSuffix(timestampField, ":duration")
			timestampField += ":timestamp"

			timestamp, err := strconv.ParseInt(data[timestampField], 10, 64)
			if err != nil || timestamp < oneMinuteAgo || timestamp > now {
				continue
			}

			if strings.HasSuffix(field, ":requests") {
				count, _ := strconv.Atoi(value)
				stats.Requests += count
			} else if strings.HasSuffix(field, ":quota") {
				q, _ := strconv.Atoi(value)
				stats.Quota += q
			} else if strings.HasSuffix(field, ":tokens") {
				t, _ := strconv.Atoi(value)
				stats.Tokens += t
			} else if strings.HasSuffix(field, ":duration") {
				d, _ := strconv.ParseInt(value, 10, 64)
				stats.TotalDuration += d
			}
		}

		// 获取性能统计数据
		if modelName, ok := conditions["model"]; ok {
			perfKey := fmt.Sprintf("perf_stats:%s:%s", modelName, key)
			perfData, err := common.RDB.HGetAll(ctx, perfKey).Result()
			if err == nil {
				if reqDur, ok := perfData["total_request_duration"]; ok {
					d, _ := strconv.ParseInt(reqDur, 10, 64)
					stats.TotalRequestDuration += d
				}
				if firstByteDur, ok := perfData["total_first_byte_duration"]; ok {
					d, _ := strconv.ParseInt(firstByteDur, 10, 64)
					stats.TotalFirstByteDuration += d
				}
				if reqCount, ok := perfData["request_count"]; ok {
					c, _ := strconv.Atoi(reqCount)
					stats.RequestCount += c
				}
			}
		}
	}

	return stats, nil
}

// 获取特定维度的统计数据
func GetDimensionStats(dimension string, value string, minKey string) (RPMStats, error) {
	stats := RPMStats{}
	if !common.RedisEnabled || !config.NewRPMEnabled {
		return stats, nil
	}

	ctx := context.Background()
	var statsKey string

	switch dimension {
	case "model":
		statsKey = fmt.Sprintf("model_stats:%s:%s", value, minKey)
	case "user":
		statsKey = fmt.Sprintf("user_stats:%s:%s", value, minKey)
	case "ip":
		statsKey = fmt.Sprintf("ip_stats:%s:%s", value, minKey)
	default:
		return stats, fmt.Errorf("unsupported dimension: %s", dimension)
	}

	data, err := common.RDB.HGetAll(ctx, statsKey).Result()
	if err != nil {
		return stats, err
	}

	stats.Requests, _ = strconv.Atoi(data["requests"])
	stats.Quota, _ = strconv.Atoi(data["quota"])
	stats.Tokens, _ = strconv.Atoi(data["tokens"])
	stats.TotalDuration, _ = strconv.ParseInt(data["total_duration"], 10, 64)

	return stats, nil
}

// RecordChannelCreationRequest 记录渠道创建请求
func RecordChannelCreationRequest(ip string, userId int) error {
	if !common.RedisEnabled || !config.NewRPMEnabled {
		return nil
	}

	now := time.Now()
	minKey := now.Format("200601021504")

	ctx := context.Background()
	pipe := common.RDB.Pipeline()

	// 全局渠道创建统计
	globalKey := fmt.Sprintf("channel_creation_stats:%s", minKey)
	pipe.HIncrBy(ctx, globalKey, "total_requests", 1)
	pipe.HSet(ctx, globalKey, "timestamp", now.Unix())
	pipe.Expire(ctx, globalKey, 2*time.Minute)

	// 按用户统计渠道创建
	if userId > 0 {
		userKey := fmt.Sprintf("channel_creation_user_stats:%d:%s", userId, minKey)
		pipe.HIncrBy(ctx, userKey, "requests", 1)
		pipe.HSet(ctx, userKey, "timestamp", now.Unix())
		pipe.Expire(ctx, userKey, 2*time.Minute)
	}

	// 按IP统计渠道创建
	if ip != "" {
		ipKey := fmt.Sprintf("channel_creation_ip_stats:%s:%s", ip, minKey)
		pipe.HIncrBy(ctx, ipKey, "requests", 1)
		pipe.HSet(ctx, ipKey, "timestamp", now.Unix())
		pipe.Expire(ctx, ipKey, 2*time.Minute)
	}

	_, err := pipe.Exec(ctx)
	return err
}

// GetChannelCreationRPM 获取渠道创建RPM统计
func GetChannelCreationRPM(userId int, ip string) (int, error) {
	// 优先使用Redis统计
	if common.RedisEnabled && config.NewRPMEnabled {
		return getChannelCreationRPMFromRedis(userId, ip)
	}

	// 回退到数据库查询
	return getChannelCreationRPMFromDB(userId, ip)
}

// getChannelCreationRPMFromRedis 从Redis获取渠道创建RPM统计
func getChannelCreationRPMFromRedis(userId int, ip string) (int, error) {
	now := time.Now().Unix()
	oneMinuteAgo := now - 60

	currentTime := time.Now()
	currentKey := currentTime.Format("200601021504")
	prevKey := currentTime.Add(-1 * time.Minute).Format("200601021504")

	ctx := context.Background()
	totalRequests := 0

	// 根据查询条件选择不同的统计维度
	var keys []string
	if userId > 0 {
		// 按用户查询
		keys = []string{
			fmt.Sprintf("channel_creation_user_stats:%d:%s", userId, currentKey),
			fmt.Sprintf("channel_creation_user_stats:%d:%s", userId, prevKey),
		}
	} else if ip != "" {
		// 按IP查询
		keys = []string{
			fmt.Sprintf("channel_creation_ip_stats:%s:%s", ip, currentKey),
			fmt.Sprintf("channel_creation_ip_stats:%s:%s", ip, prevKey),
		}
	} else {
		// 全局查询
		keys = []string{
			fmt.Sprintf("channel_creation_stats:%s", currentKey),
			fmt.Sprintf("channel_creation_stats:%s", prevKey),
		}
	}

	for _, key := range keys {
		data, err := common.RDB.HGetAll(ctx, key).Result()
		if err != nil {
			continue
		}

		// 检查时间戳是否在最近一分钟内
		timestampStr, exists := data["timestamp"]
		if !exists {
			continue
		}

		timestamp, err := strconv.ParseInt(timestampStr, 10, 64)
		if err != nil || timestamp < oneMinuteAgo || timestamp > now {
			continue
		}

		// 累加请求数
		if userId > 0 {
			if requestsStr, exists := data["requests"]; exists {
				requests, _ := strconv.Atoi(requestsStr)
				totalRequests += requests
			}
		} else {
			if requestsStr, exists := data["total_requests"]; exists {
				requests, _ := strconv.Atoi(requestsStr)
				totalRequests += requests
			}
		}
	}

	return totalRequests, nil
}

// getChannelCreationRPMFromDB 从数据库获取渠道创建RPM统计
func getChannelCreationRPMFromDB(userId int, ip string) (int, error) {
	now := time.Now().Unix()
	oneMinuteAgo := now - 60

	// 先尝试从NoSQL数据库查询
	if count, err := getChannelCreationRPMFromNoSQL(userId, ip, oneMinuteAgo, now); err == nil {
		return count, nil
	}

	// 回退到SQL数据库查询
	return getChannelCreationRPMFromSQL(userId, ip, oneMinuteAgo, now)
}

// getChannelCreationRPMFromNoSQL 从NoSQL数据库获取渠道创建RPM统计
func getChannelCreationRPMFromNoSQL(userId int, ip string, startTime, endTime int64) (int, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db == nil {
		return 0, fmt.Errorf("NoSQL database not available")
	}

	// 根据查询参数决定是否能查询
	if userId > 0 || ip != "" {
		// Channel表中没有创建者ID和IP字段，按用户和IP查询不可行
		return 0, nil
	}

	// 检查是否是MongoDB实现
	if mongoImpl, ok := db.(*MongoDatabaseImpl); ok {
		return getChannelCreationRPMFromMongoDB(mongoImpl, startTime, endTime)
	}

	// 其他NoSQL数据库暂不支持
	return 0, fmt.Errorf("NoSQL database does not support time-based channel creation RPM queries")
}

// getChannelCreationRPMFromMongoDB 从MongoDB获取渠道创建RPM统计
func getChannelCreationRPMFromMongoDB(mongoImpl *MongoDatabaseImpl, startTime, endTime int64) (int, error) {
	ctx := context.Background()

	// 构建时间范围查询条件
	filter := bson.M{
		"created_time": bson.M{
			"$gte": startTime,
			"$lte": endTime,
		},
	}

	// 统计符合条件的渠道数量
	count, err := mongoImpl.channels.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}

	return int(count), nil
}

// getChannelCreationRPMFromSQL 从SQL数据库获取渠道创建RPM统计
func getChannelCreationRPMFromSQL(userId int, ip string, startTime, endTime int64) (int, error) {
	var count int64

	// 根据查询参数构建查询
	if userId > 0 {
		// 由于Channel表中没有创建者ID字段，按用户查询不可行
		// 可以考虑从日志表中查询，但这里简化处理，返回0
		return 0, nil
	}

	if ip != "" {
		// 同样，Channel表中没有IP字段，按IP查询不可行
		return 0, nil
	}

	// 全局查询 - 统计最近一分钟创建的渠道数量
	err := DB.Model(&Channel{}).
		Where("created_time >= ? AND created_time <= ?", startTime, endTime).
		Count(&count).Error

	if err != nil {
		return 0, err
	}

	return int(count), nil
}

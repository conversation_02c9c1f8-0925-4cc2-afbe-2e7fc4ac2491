package model

import (
	"fmt"
	"sync"

	"github.com/songquanpeng/one-api/common/logger"
)

var migrationService *MigrationService

// MigrationService 一次性数据迁移服务
type MigrationService struct {
	migrated bool
	mu       sync.RWMutex
}

// InitMigrationService 初始化迁移服务
func InitMigrationService() {
	migrationService = &MigrationService{
		migrated: false,
	}
}

// CheckAndMigrateOnce 检查并执行一次性迁移
func (m *MigrationService) CheckAndMigrateOnce() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.migrated {
		logger.SysLog("Data already migrated, skipping")
		return nil
	}

	// 检查MongoDB是否已有数据
	if DBManager == nil || DBManager.GetNoSQLDB() == nil {
		return fmt.Errorf("NoSQL database not available")
	}

	nosqlDB := DBManager.GetNoSQLDB()

	// 检查MongoDB中是否已有渠道数据
	channels, err := nosqlDB.GetAllChannels(0, 1, "", false, 0, "", "", "", "", 0, 0, 0, "", "", "", "")
	if err == nil && len(channels) > 0 {
		logger.SysLog("MongoDB already contains data, skipping migration")
		m.migrated = true
		return nil
	}

	logger.SysLog("MongoDB is empty, starting one-time migration from MySQL")

	// 执行一次性迁移
	if err := m.performFullMigration(); err != nil {
		return fmt.Errorf("migration failed: %v", err)
	}

	m.migrated = true
	logger.SysLog("One-time migration completed successfully")
	return nil
}

// performFullMigration 执行完整的数据迁移
func (m *MigrationService) performFullMigration() error {
	sqlDB := DBManager.GetSQLDB()
	nosqlDB := DBManager.GetNoSQLDB()

	if sqlDB == nil || nosqlDB == nil {
		return fmt.Errorf("database connections not available")
	}

	// 1. 迁移所有渠道数据
	logger.SysLog("Migrating channels...")
	if err := m.migrateAllChannels(sqlDB, nosqlDB); err != nil {
		return fmt.Errorf("failed to migrate channels: %v", err)
	}

	// 2. 迁移所有能力数据 因为迁移渠道的时候已经自动插入了ability所以这里不用迁移了
	//logger.SysLog("Migrating abilities...")
	//if err := m.migrateAllAbilities(sqlDB, nosqlDB); err != nil {
	//	return fmt.Errorf("failed to migrate abilities: %v", err)
	//}

	// 3. 迁移所有渠道扩展数据
	logger.SysLog("Migrating channel extends...")
	if err := m.migrateAllChannelExtends(sqlDB, nosqlDB); err != nil {
		return fmt.Errorf("failed to migrate channel extends: %v", err)
	}

	// 4. 迁移所有渠道组数据
	logger.SysLog("Migrating channel groups...")
	if err := m.migrateAllChannelGroups(sqlDB, nosqlDB); err != nil {
		return fmt.Errorf("failed to migrate channel groups: %v", err)
	}

	// 5. 初始化所有counter到正确的值
	logger.SysLog("Initializing counters...")
	if mongoImpl, ok := nosqlDB.(*MongoDatabaseImpl); ok {
		if err := mongoImpl.InitializeCounters(); err != nil {
			logger.SysError("Failed to initialize counters, this may cause ID conflicts in future inserts: " + err.Error())
			// 不返回错误，允许迁移继续完成，但记录警告
		}
	} else {
		logger.SysLog("Counter initialization skipped (not using MongoDB implementation)")
	}

	return nil
}

// migrateAllChannels 迁移所有渠道数据
func (m *MigrationService) migrateAllChannels(sqlDB, nosqlDB DatabaseInterface) error {
	// 分批获取所有渠道数据
	batchSize := 5000
	offset := 0
	totalMigrated := 0

	logger.SysLog(fmt.Sprintf("Starting channel migration with batch size %d", batchSize))

	for {
		logger.SysLog(fmt.Sprintf("Fetching channels from offset %d, batch size %d", offset, batchSize))

		// 直接使用全局DB变量查询MySQL数据
		var channels []*Channel
		err := DB.Limit(batchSize).Offset(offset).Find(&channels).Error
		if err != nil {
			logger.SysError(fmt.Sprintf("Failed to fetch channels from MySQL: %v", err))
			return err
		}

		logger.SysLog(fmt.Sprintf("Fetched %d channels from MySQL database", len(channels)))

		if len(channels) == 0 {
			logger.SysLog("No more channels to migrate, breaking loop")
			break
		}

		// 批量插入到MongoDB
		var channelSlice []Channel
		for _, ch := range channels {
			channelSlice = append(channelSlice, *ch)
		}

		logger.SysLog(fmt.Sprintf("Attempting to batch insert %d channels to MongoDB", len(channelSlice)))
		if err := nosqlDB.BatchInsertChannels(channelSlice); err != nil {
			logger.SysError(fmt.Sprintf("Failed to batch insert channels: %v", err))
			return fmt.Errorf("failed to batch insert channels: %v", err)
		}

		totalMigrated += len(channels)
		logger.SysLog(fmt.Sprintf("Successfully migrated %d channels (offset: %d, total: %d)", len(channels), offset, totalMigrated))
		offset += batchSize

		// 如果返回的数据少于批次大小，说明已经是最后一批
		if len(channels) < batchSize {
			logger.SysLog("Last batch processed, migration complete")
			break
		}
	}

	logger.SysLog(fmt.Sprintf("Channel migration completed. Total migrated: %d", totalMigrated))
	return nil
}

// migrateAllAbilities 迁移所有能力数据
func (m *MigrationService) migrateAllAbilities(sqlDB, nosqlDB DatabaseInterface) error {
	batchSize := 5000
	offset := 0
	totalMigrated := 0

	logger.SysLog(fmt.Sprintf("Starting ability migration with batch size %d", batchSize))

	for {
		logger.SysLog(fmt.Sprintf("Fetching abilities from offset %d, batch size %d", offset, batchSize))

		// 直接使用全局DB变量查询MySQL数据
		var abilities []*Ability
		err := DB.Limit(batchSize).Offset(offset).Find(&abilities).Error
		if err != nil {
			logger.SysError(fmt.Sprintf("Failed to fetch abilities from MySQL: %v", err))
			return err
		}

		logger.SysLog(fmt.Sprintf("Fetched %d abilities from MySQL database", len(abilities)))

		if len(abilities) == 0 {
			logger.SysLog("No more abilities to migrate, breaking loop")
			break
		}

		// 逐个插入能力数据
		successCount := 0
		for _, ability := range abilities {
			if err := nosqlDB.InsertAbility(ability); err != nil {
				logger.SysError(fmt.Sprintf("Failed to insert ability %s-%s-%d: %v", ability.Group, ability.Model, ability.ChannelId, err))
				// 继续处理其他数据，不中断整个迁移过程
			} else {
				successCount++
			}
		}

		totalMigrated += successCount
		logger.SysLog(fmt.Sprintf("Successfully migrated %d/%d abilities (offset: %d, total: %d)", successCount, len(abilities), offset, totalMigrated))
		offset += batchSize

		if len(abilities) < batchSize {
			logger.SysLog("Last batch processed, ability migration complete")
			break
		}
	}

	logger.SysLog(fmt.Sprintf("Ability migration completed. Total migrated: %d", totalMigrated))
	return nil
}

// migrateAllChannelExtends 迁移所有渠道扩展数据
func (m *MigrationService) migrateAllChannelExtends(sqlDB, nosqlDB DatabaseInterface) error {
	batchSize := 5000
	offset := 0
	totalMigrated := 0

	logger.SysLog(fmt.Sprintf("Starting channel extends migration with batch size %d", batchSize))

	for {
		logger.SysLog(fmt.Sprintf("Fetching channel extends from offset %d, batch size %d", offset, batchSize))

		// 直接使用全局DB变量查询MySQL数据
		var channelExtends []ChannelExtend
		err := DB.Limit(batchSize).Offset(offset).Find(&channelExtends).Error
		if err != nil {
			logger.SysError(fmt.Sprintf("Failed to fetch channel extends from MySQL: %v", err))
			return err
		}

		logger.SysLog(fmt.Sprintf("Fetched %d channel extends from MySQL database", len(channelExtends)))

		if len(channelExtends) == 0 {
			logger.SysLog("No more channel extends to migrate, breaking loop")
			break
		}

		logger.SysLog(fmt.Sprintf("Attempting to batch insert %d channel extends to MongoDB", len(channelExtends)))
		if err := nosqlDB.BatchInsertChannelExtends(channelExtends, false); err != nil {
			logger.SysError(fmt.Sprintf("Failed to batch insert channel extends: %v", err))
			return fmt.Errorf("failed to batch insert channel extends: %v", err)
		}

		totalMigrated += len(channelExtends)
		logger.SysLog(fmt.Sprintf("Successfully migrated %d channel extends (offset: %d, total: %d)", len(channelExtends), offset, totalMigrated))
		offset += batchSize

		// 如果返回的数据少于批次大小，说明已经是最后一批
		if len(channelExtends) < batchSize {
			logger.SysLog("Last batch processed, channel extends migration complete")
			break
		}
	}

	logger.SysLog(fmt.Sprintf("Channel extends migration completed. Total migrated: %d", totalMigrated))
	return nil
}

// migrateAllChannelGroups 迁移所有渠道组数据
func (m *MigrationService) migrateAllChannelGroups(sqlDB, nosqlDB DatabaseInterface) error {
	batchSize := 5000
	offset := 0
	totalMigrated := 0

	logger.SysLog(fmt.Sprintf("Starting channel groups migration with batch size %d", batchSize))

	for {
		logger.SysLog(fmt.Sprintf("Fetching channel groups from offset %d, batch size %d", offset, batchSize))

		// 直接使用全局DB变量查询MySQL数据
		var channelGroups []ChannelGroup
		err := DB.Limit(batchSize).Offset(offset).Find(&channelGroups).Error
		if err != nil {
			logger.SysError(fmt.Sprintf("Failed to fetch channel groups from MySQL: %v", err))
			return err
		}

		logger.SysLog(fmt.Sprintf("Fetched %d channel groups from MySQL database", len(channelGroups)))

		if len(channelGroups) == 0 {
			logger.SysLog("No more channel groups to migrate, breaking loop")
			break
		}

		logger.SysLog(fmt.Sprintf("Attempting to batch insert %d channel groups to MongoDB", len(channelGroups)))
		if err := nosqlDB.BatchInsertChannelGroups(channelGroups); err != nil {
			logger.SysError(fmt.Sprintf("Failed to batch insert channel groups: %v", err))
			return fmt.Errorf("failed to batch insert channel groups: %v", err)
		}

		totalMigrated += len(channelGroups)
		logger.SysLog(fmt.Sprintf("Successfully migrated %d channel groups (offset: %d, total: %d)", len(channelGroups), offset, totalMigrated))
		offset += batchSize

		// 如果返回的数据少于批次大小，说明已经是最后一批
		if len(channelGroups) < batchSize {
			logger.SysLog("Last batch processed, channel groups migration complete")
			break
		}
	}

	logger.SysLog(fmt.Sprintf("Channel groups migration completed. Total migrated: %d", totalMigrated))
	return nil
}

// GetMigrationService 获取迁移服务实例
func GetMigrationService() *MigrationService {
	return migrationService
}

// IsMigrated 检查是否已经迁移
func IsMigrated() bool {
	if migrationService == nil {
		return false
	}
	migrationService.mu.RLock()
	defer migrationService.mu.RUnlock()
	return migrationService.migrated
}

// ForceSetMigrated 强制设置为已迁移状态（用于测试或手动设置）
func ForceSetMigrated(migrated bool) {
	if migrationService != nil {
		migrationService.mu.Lock()
		migrationService.migrated = migrated
		migrationService.mu.Unlock()
	}
}

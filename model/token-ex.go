package model

import (
	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common/config"
	"gorm.io/gorm"
)

type TokenExtend struct {
	Id                          int    `json:"id"`
	TokenId                     int    `json:"token_id"`
	RateLimitNum                int    `json:"rate_limit_num" gorm:"default:0"`                     // 限制次数
	RateLimitDuration           int    `json:"rate_limit_duration" gorm:"default:0"`                // 限制周期
	RateLimitExceededMessage    string `json:"rate_limit_exceeded_message" gorm:"default:''"`       // 限制超出时的提示信息
	ActivateOnFirstUse          bool   `json:"activate_on_first_use" gorm:"default:0"`              // 是否初次启用时开始计算有效期,1.是 0.否
	ValidDuration               int    `json:"valid_duration" gorm:"default:0"`                     // 有效期 单位：天，-1为无限制
	DurationActivated           bool   `json:"duration_activated" gorm:"default:0"`                 // 1.有效期已激活 0.有效期未激活
	MjDiscordProxyUrl           string `json:"mj_discord_proxy_url" gorm:"default:''"`              // mj discord proxy url 直接替换https://cdn.discordapp.com
	MjTranslateEnabled          bool   `json:"mj_translate_enabled" gorm:"default:0"`               // 是否启用MJ翻译
	MjTranslateBaseUrl          string `json:"mj_translate_base_url" gorm:"default:''"`             // MJ翻译服务基础URL
	MjTranslateApiKey           string `json:"mj_translate_api_key" gorm:"default:''"`              // MJ翻译服务API密钥
	MjTranslateModel            string `json:"mj_translate_model" gorm:"default:''"`                // MJ翻译服务使用的模型
	RetryKeepBillingTypeEnabled *bool  `json:"retry_keep_billing_type_enabled" gorm:"default:null"` // 重试保持计费类型一致性，null表示使用全局配置，true/false表示强制开启/关闭
}

func (tokenExtend *TokenExtend) Insert() error {
	var err error
	err = DB.Create(tokenExtend).Error
	if err != nil {
		return err
	}
	return nil
}

func (tokenExtend *TokenExtend) InsertByTx(tx *gorm.DB) error {
	var err error
	err = tx.Create(tokenExtend).Error
	if err != nil {
		return err
	}
	return nil
}

func (tokenExtend *TokenExtend) Update() error {
	var err error
	err = DB.Save(tokenExtend).Error
	if err != nil {
		return err
	}
	return nil
}

func (tokenExtend *TokenExtend) UpdateByTx(tx *gorm.DB) error {
	// 先删除关联的tokenExtend,再插入新的tokenExtend
	var err error
	err = tokenExtend.DeleteTokenExtendByTokenIdByTx(tx)
	if err != nil {
		return err
	}
	err = tx.Save(tokenExtend).Error
	if err != nil {
		return err
	}
	return nil
}

func (tokenExtend *TokenExtend) Delete() error {
	var err error
	err = DB.Delete(tokenExtend).Error
	if err != nil {
		return err
	}
	return nil
}

func (tokenExtend *TokenExtend) DeleteByTx(tx *gorm.DB) error {
	var err error
	err = tx.Delete(tokenExtend).Error
	if err != nil {
		return err
	}
	return nil

}

func (tokenExtend *TokenExtend) Get() error {
	var err error
	err = DB.First(tokenExtend).Error
	if err != nil {
		return err
	}
	return nil
}

func GetTokenExtendByTokenId(tokenId int) (*TokenExtend, error) {
	var tokenExtend *TokenExtend
	var err error
	err = DB.Where("token_id = ?", tokenId).First(&tokenExtend).Error
	if err != nil {
		return nil, err
	}
	return tokenExtend, nil
}

func (tokenExtend *TokenExtend) SaveTokenExtendByTokenId() error {
	var err error
	// 先判断是否存在tokenExtend记录
	err = tokenExtend.Get()
	if err != nil {
		// 不存在,则插入
		err = tokenExtend.Insert()
		if err != nil {
			return err
		}
		return nil
	}
	return tokenExtend.Update()
}

func (tokenExtend *TokenExtend) SaveTokenExtendByTokenIdByTx(tx *gorm.DB) error {
	var err error
	// 先判断是否存在tokenExtend记录
	err = tokenExtend.Get()
	if err != nil {
		// 不存在,则插入
		err = tokenExtend.InsertByTx(tx)
		if err != nil {
			return err
		}
		return nil
	}
	return tokenExtend.UpdateByTx(tx)

}

func (tokenExtend *TokenExtend) DeleteTokenExtendByTokenId() error {
	var err error
	err = DB.Where("token_id = ?", tokenExtend.TokenId).Delete(tokenExtend).Error
	if err != nil {
		return err
	}
	return nil
}

func (tokenExtend *TokenExtend) DeleteTokenExtendByTokenIdByTx(tx *gorm.DB) error {
	var err error
	err = tx.Where("token_id = ?", tokenExtend.TokenId).Delete(tokenExtend).Error
	if err != nil {
		return err
	}
	return nil

}

func (tokenExtend *TokenExtend) GetTokenExtendByTokenId() error {
	var err error
	err = DB.Where("token_id = ?", tokenExtend.TokenId).First(tokenExtend).Error
	if err != nil {
		return err
	}
	return nil
}

// ShouldKeepRetryBillingType 判断是否应该保持重试计费类型一致性
// 优先使用token级别的配置，如果token没有设置则使用全局配置
func (tokenExtend *TokenExtend) ShouldKeepRetryBillingType() bool {
	if tokenExtend.RetryKeepBillingTypeEnabled != nil {
		return *tokenExtend.RetryKeepBillingTypeEnabled
	}
	// 如果token级别没有设置，则使用全局配置
	return config.RetryKeepBillingTypeEnabled
}

// ShouldKeepRetryBillingTypeFromContext 从gin.Context中获取TokenExtend并判断是否应该保持重试计费类型一致性
// 这是一个便捷函数，避免在控制器中重复相同的逻辑
func ShouldKeepRetryBillingTypeFromContext(c *gin.Context) bool {
	if tokenExtendInterface, exists := c.Get("token_extend"); exists {
		if tokenExtend, ok := tokenExtendInterface.(*TokenExtend); ok && tokenExtend != nil {
			return tokenExtend.ShouldKeepRetryBillingType()
		}
	}
	// 如果无法获取TokenExtend，则使用全局配置作为后备
	return config.RetryKeepBillingTypeEnabled
}

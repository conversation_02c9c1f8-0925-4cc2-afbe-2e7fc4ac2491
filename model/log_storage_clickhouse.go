package model

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	_ "github.com/ClickHouse/clickhouse-go/v2"
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/env"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
)

// ClickHouseLogStorage ClickHouse日志存储实现
type ClickHouseLogStorage struct {
	db                       *sql.DB
	batchLogs                []*Log
	batchMu                  sync.Mutex
	stopCh                   chan struct{}
	wg                       sync.WaitGroup
	supportsGenerateSerialID bool // 缓存generateSerialID支持状态，启动时检测一次
}

// NewClickHouseLogStorage 创建ClickHouse日志存储实例
func NewClickHouseLogStorage(connectionString string) (LogStorage, error) {

	var baseConnectionString string
	if connectionString == "" {
		// 先连接到默认数据库（不指定数据库名）
		baseConnectionString = fmt.Sprintf("tcp://%s:%d?username=%s&password=%s",
			config.ClickHouseHost, config.ClickHousePort,
			config.ClickHouseUsername, config.ClickHousePassword)
	} else {
		baseConnectionString = connectionString
	}

	// 尝试连接ClickHouse（不指定数据库）
	db, err := sql.Open("clickhouse", baseConnectionString)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to ClickHouse: %v (请确保已安装驱动: go get github.com/ClickHouse/clickhouse-go/v2)", err)
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping ClickHouse: %v", err)
	}

	storage := &ClickHouseLogStorage{
		db:        db,
		batchLogs: make([]*Log, 0, config.LogStorageBatchSize),
		stopCh:    make(chan struct{}),
	}

	// 先创建数据库（不创建表）
	if err := storage.createDatabase(); err != nil {
		return nil, fmt.Errorf("failed to create database: %v", err)
	}

	// 关闭初始连接
	db.Close()

	// 重新连接到指定的数据库，添加超时配置
	var finalConnectionString string
	if connectionString == "" {
		finalConnectionString = fmt.Sprintf("tcp://%s:%d?database=%s&username=%s&password=%s&compress=1",
			config.ClickHouseHost, config.ClickHousePort, config.ClickHouseDatabase,
			config.ClickHouseUsername, config.ClickHousePassword)
	} else {
		finalConnectionString = connectionString
	}

	finalDB, err := sql.Open("clickhouse", finalConnectionString)
	if err != nil {
		return nil, fmt.Errorf("failed to reconnect to ClickHouse database: %v", err)
	}

	// 配置连接池 - 复用MySQL的连接池配置
	finalDB.SetMaxOpenConns(env.Int("SQL_MAX_OPEN_CONNS", 1000))                             // 复用MySQL的最大打开连接数配置
	finalDB.SetMaxIdleConns(env.Int("SQL_MAX_IDLE_CONNS", 100))                              // 复用MySQL的最大空闲连接数配置
	finalDB.SetConnMaxLifetime(time.Second * time.Duration(env.Int("SQL_MAX_LIFETIME", 60))) // 复用MySQL的连接生存时间配置

	// 测试最终连接
	if err := finalDB.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping ClickHouse database: %v", err)
	}

	// 更新存储实例的数据库连接
	storage.db = finalDB

	// 检测并缓存generateSerialID支持状态（在创建表之前执行）
	storage.supportsGenerateSerialID = storage.detectGenerateSerialIDSupport()
	if storage.supportsGenerateSerialID {
		logger.SysLog("ClickHouse generateSerialID feature detected and cached: SUPPORTED")
	} else {
		logger.SysLog("ClickHouse generateSerialID feature detected and cached: NOT SUPPORTED")
	}

	// 现在在正确的数据库连接上创建表（使用缓存的特性检测结果）
	if err := storage.createTables(); err != nil {
		return nil, fmt.Errorf("failed to create tables: %v", err)
	}
	logger.SysLog(fmt.Sprintf("Successfully connected to ClickHouse database: %s", config.ClickHouseDatabase))

	// 启动批量写入协程
	if config.LogStorageAsyncWrite {
		storage.wg.Add(1)
		go storage.batchWriter()
	}

	return storage, nil
}

// createDatabase 只创建数据库
func (c *ClickHouseLogStorage) createDatabase() error {
	// 创建数据库
	createDBSQL := fmt.Sprintf("CREATE DATABASE IF NOT EXISTS %s", config.ClickHouseDatabase)
	logger.SysLog(fmt.Sprintf("Creating database with SQL: %s", createDBSQL))
	if _, err := c.db.Exec(createDBSQL); err != nil {
		return fmt.Errorf("failed to create database: %v", err)
	}
	logger.SysLog(fmt.Sprintf("Database %s created successfully", config.ClickHouseDatabase))
	return nil
}

// createTables 创建表（在正确的数据库连接上）
func (c *ClickHouseLogStorage) createTables() error {
	// 创建日志表
	createTableSQL := c.getCreateLogsTableSQL()
	logger.SysLog("Creating logs table...")
	if _, err := c.db.Exec(createTableSQL); err != nil {
		return fmt.Errorf("failed to create logs table: %v", err)
	}
	logger.SysLog("Logs table created successfully")

	// 创建日志扩展表
	createLogExtendTableSQL := c.getCreateLogExtendsTableSQL()
	if _, err := c.db.Exec(createLogExtendTableSQL); err != nil {
		return fmt.Errorf("failed to create log_extends table: %v", err)
	}
	logger.SysLog("Log extends table created successfully")

	logger.SysLog("ClickHouse tables created successfully")
	return nil
}

// createDatabaseAndTable 创建数据库和表（保留用于兼容性）
func (c *ClickHouseLogStorage) createDatabaseAndTable() error {
	if err := c.createDatabase(); err != nil {
		return err
	}
	return c.createTables()
}

// getCreateLogsTableSQL 获取创建logs表的SQL，智能选择ID生成策略
func (c *ClickHouseLogStorage) getCreateLogsTableSQL() string {
	// 使用缓存的generateSerialID支持状态
	if c.supportsGenerateSerialID {
		logger.SysLog("Using generateSerialID for auto-increment IDs + program-generated UUID for relations")
		return `
		CREATE TABLE IF NOT EXISTS logs (
			id UInt64 DEFAULT generateSerialID('logs_id_seq'),
			uuid String,
			request_id String,
			user_id UInt32,
			created_at DateTime,
			type UInt8,
			content String,
			username LowCardinality(String),
			token_name LowCardinality(String),
			token_group LowCardinality(String),
			model_name LowCardinality(String),
			channel_name LowCardinality(String),
			quota Int32,
			cost_quota Int32,
			prompt_tokens UInt32,
			completion_tokens UInt32,
			channel_id UInt32,
			token_key String,
			request_duration UInt64,
			response_first_byte_duration UInt64,
			total_duration UInt64,
			elapsed_time UInt64,
			is_stream UInt8,
			system_prompt_reset UInt8,
			ip String,
			remote_ip String,
			other String,
			error_code LowCardinality(String),
			INDEX idx_uuid uuid TYPE bloom_filter GRANULARITY 1
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(created_at)
		ORDER BY (created_at, user_id, type)
		SETTINGS index_granularity = 8192`
	}

	// 如果generateSerialID不可用，使用手动ID生成 + UUID
	logger.SysLog("generateSerialID not available, using manual ID generation + UUID")
	return `
	CREATE TABLE IF NOT EXISTS logs (
		id UInt64,
		uuid String,
		request_id String,
		user_id UInt32,
		created_at DateTime,
		type UInt8,
		content String,
		username LowCardinality(String),
		token_name LowCardinality(String),
		token_group LowCardinality(String),
		model_name LowCardinality(String),
		channel_name LowCardinality(String),
		quota Int32,
		cost_quota Int32,
		prompt_tokens UInt32,
		completion_tokens UInt32,
		channel_id UInt32,
		token_key String,
		request_duration UInt64,
		response_first_byte_duration UInt64,
		total_duration UInt64,
		elapsed_time UInt64,
		is_stream UInt8,
		system_prompt_reset UInt8,
		ip String,
		remote_ip String,
		other String,
		error_code LowCardinality(String),
		INDEX idx_uuid uuid TYPE bloom_filter GRANULARITY 1
	) ENGINE = MergeTree()
	PARTITION BY toYYYYMM(created_at)
	ORDER BY (created_at, user_id, type)
	SETTINGS index_granularity = 8192`
}

// getCreateLogExtendsTableSQL 获取创建log_extends表的SQL
func (c *ClickHouseLogStorage) getCreateLogExtendsTableSQL() string {
	// 使用缓存的generateSerialID支持状态
	if c.supportsGenerateSerialID {
		return `
		CREATE TABLE IF NOT EXISTS log_extends (
			id UInt64 DEFAULT generateSerialID('log_extends_id_seq'),
			log_id UInt64,
			log_uuid String,
			created_at DateTime,
			prompt String,
			completion String,
			completion_id String,
			upstream_response String,
			full_response String,
			request_path String,
			INDEX idx_log_uuid log_uuid TYPE bloom_filter GRANULARITY 1
		) ENGINE = MergeTree()
		PARTITION BY toYYYYMM(created_at)
		ORDER BY (created_at, log_uuid)
		SETTINGS index_granularity = 8192`
	}

	// 如果generateSerialID不可用，使用手动ID + UUID关联
	return `
	CREATE TABLE IF NOT EXISTS log_extends (
		id UInt64,
		log_id UInt64,
		log_uuid String,
		created_at DateTime,
		prompt String,
		completion String,
		completion_id String,
		upstream_response String,
		full_response String,
		request_path String,
		INDEX idx_log_uuid log_uuid TYPE bloom_filter GRANULARITY 1
	) ENGINE = MergeTree()
	PARTITION BY toYYYYMM(created_at)
	ORDER BY (created_at, log_uuid)
	SETTINGS index_granularity = 8192`
}

// detectGenerateSerialIDSupport 检测generateSerialID函数是否可用（启动时调用一次）
func (c *ClickHouseLogStorage) detectGenerateSerialIDSupport() bool {
	// 尝试执行一个简单的generateSerialID查询
	_, err := c.db.Query("SELECT generateSerialID('test_seq') LIMIT 1")
	if err != nil {
		logger.SysLog(fmt.Sprintf("generateSerialID not supported: %v", err))
		return false
	}
	return true
}

// testGenerateSerialIDSupport 获取缓存的generateSerialID支持状态
func (c *ClickHouseLogStorage) testGenerateSerialIDSupport() bool {
	return c.supportsGenerateSerialID
}

// batchWriter 批量写入协程
func (c *ClickHouseLogStorage) batchWriter() {
	defer c.wg.Done()
	ticker := time.NewTicker(time.Duration(config.LogStorageFlushInterval) * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			c.flushBatch()
		case <-c.stopCh:
			c.flushBatch() // 最后一次刷新
			return
		}
	}
}

// flushBatch 刷新批次
func (c *ClickHouseLogStorage) flushBatch() {
	c.batchMu.Lock()
	if len(c.batchLogs) == 0 {
		c.batchMu.Unlock()
		return
	}

	logs := make([]*Log, len(c.batchLogs))
	copy(logs, c.batchLogs)
	c.batchLogs = c.batchLogs[:0] // 清空但保留容量
	c.batchMu.Unlock()

	ctx := context.Background()
	if err := c.RecordLogBatch(ctx, logs); err != nil {
		logger.Error(ctx, "failed to flush batch logs to ClickHouse: "+err.Error())
	} else {
		logger.Infof(ctx, "flushed %d logs to ClickHouse", len(logs))
	}
}

// RecordLog 记录单条日志
func (c *ClickHouseLogStorage) RecordLog(ctx context.Context, log *Log) error {
	if config.LogStorageAsyncWrite {
		// 异步写入
		c.batchMu.Lock()
		c.batchLogs = append(c.batchLogs, log)
		shouldFlush := len(c.batchLogs) >= config.LogStorageBatchSize
		c.batchMu.Unlock()

		if shouldFlush {
			c.flushBatch()
		}
		return nil
	} else {
		// 同步写入
		return c.insertLog(ctx, log)
	}
}

// insertLog 插入单条日志
func (c *ClickHouseLogStorage) insertLog(ctx context.Context, log *Log) error {
	if log.RequestId == "" {
		log.RequestId = helper.GetRequestID(ctx)
	}

	// 为数据库操作添加超时保护
	bgCtx := context.Background()
	timeoutCtx, cancel := context.WithTimeout(bgCtx, 30*time.Second)
	defer cancel()

	// 根据ClickHouse版本选择不同的插入策略
	if c.supportsGenerateSerialID {
		// 新版本：使用自增ID，不需要UUID
		return c.insertLogWithAutoIncrementID(timeoutCtx, log)
	} else {
		// 旧版本：使用UUID作为主键
		return c.insertLogWithUUID(timeoutCtx, log)
	}
}

// insertLogWithAutoIncrementID 新版本ClickHouse：数据库自动生成ID + 程序生成UUID
func (c *ClickHouseLogStorage) insertLogWithAutoIncrementID(ctx context.Context, log *Log) error {
	// 程序生成UUID，用于关联表
	if log.Uuid == "" {
		log.Uuid = helper.GenerateUUID()
	}

	// 让数据库自动生成ID，不需要提前获取
	// 插入数据，ID字段使用DEFAULT值（数据库自动生成）
	query := fmt.Sprintf(`INSERT INTO %s.logs (uuid, request_id, user_id, created_at, type, content, username, token_name, token_group, model_name, channel_name, quota, cost_quota, prompt_tokens, completion_tokens, channel_id, token_key, request_duration, response_first_byte_duration, total_duration, elapsed_time, is_stream, system_prompt_reset, ip, remote_ip, other, error_code) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, config.ClickHouseDatabase)

	_, err := c.db.ExecContext(ctx, query,
		log.Uuid,
		log.RequestId,
		log.UserId,
		time.Unix(log.CreatedAt, 0),
		log.Type,
		log.Content,
		log.Username,
		log.TokenName,
		log.TokenGroup,
		log.ModelName,
		log.ChannelName,
		log.Quota,
		log.CostQuota,
		log.PromptTokens,
		log.CompletionTokens,
		log.ChannelId,
		log.TokenKey,
		log.RequestDuration,
		log.ResponseFirstByteDuration,
		log.TotalDuration,
		log.ElapsedTime,
		boolToUint8(log.IsStream),
		boolToUint8(log.SystemPromptReset),
		log.Ip,
		log.RemoteIp,
		log.Other,
		log.ErrorCode,
	)

	if err != nil {
		return fmt.Errorf("failed to insert log with UUID %s: %v", log.Uuid, err)
	}

	// 插入成功，ID由数据库自动生成，我们不需要知道具体值
	// UUID已经足够用于关联表了
	log.Id = 0 // 标记为未获取（如果需要可以后续查询）
	return nil
}

// insertLogWithUUID 旧版本ClickHouse：手动生成ID + 程序生成UUID
func (c *ClickHouseLogStorage) insertLogWithUUID(ctx context.Context, log *Log) error {
	// 程序生成UUID
	if log.Uuid == "" {
		log.Uuid = helper.GenerateUUID()
	}

	// 手动生成ID（使用时间戳基础的简单递增）
	log.Id = int(time.Now().UnixNano())

	// 插入数据，包含手动生成的ID和程序生成的UUID
	query := fmt.Sprintf(`INSERT INTO %s.logs (id, uuid, request_id, user_id, created_at, type, content, username, token_name, token_group, model_name, channel_name, quota, cost_quota, prompt_tokens, completion_tokens, channel_id, token_key, request_duration, response_first_byte_duration, total_duration, elapsed_time, is_stream, system_prompt_reset, ip, remote_ip, other, error_code) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, config.ClickHouseDatabase)

	_, err := c.db.ExecContext(ctx, query,
		log.Id,
		log.Uuid,
		log.RequestId,
		log.UserId,
		time.Unix(log.CreatedAt, 0),
		log.Type,
		log.Content,
		log.Username,
		log.TokenName,
		log.TokenGroup,
		log.ModelName,
		log.ChannelName,
		log.Quota,
		log.CostQuota,
		log.PromptTokens,
		log.CompletionTokens,
		log.ChannelId,
		log.TokenKey,
		log.RequestDuration,
		log.ResponseFirstByteDuration,
		log.TotalDuration,
		log.ElapsedTime,
		boolToUint8(log.IsStream),
		boolToUint8(log.SystemPromptReset),
		log.Ip,
		log.RemoteIp,
		log.Other,
		log.ErrorCode,
	)

	if err != nil {
		return fmt.Errorf("failed to insert log with ID %d and UUID %s: %v", log.Id, log.Uuid, err)
	}

	return nil
}

// retrieveLastInsertedId 获取最近插入记录的ID（优化版本）
func (c *ClickHouseLogStorage) retrieveLastInsertedId(ctx context.Context, log *Log) error {
	// 使用request_id的唯一性来查找刚插入的记录
	// request_id在同一个请求中是唯一的，可以精确定位
	query := fmt.Sprintf(`SELECT id FROM %s.logs WHERE request_id = ? AND user_id = ? ORDER BY id DESC LIMIT 1`, config.ClickHouseDatabase)

	var id int64
	err := c.db.QueryRowContext(ctx, query, log.RequestId, log.UserId).Scan(&id)
	if err != nil {
		// 如果获取ID失败，记录日志但不返回错误，因为数据已经成功插入
		logger.SysLog(fmt.Sprintf("Failed to retrieve ID for log with request_id %s: %v", log.RequestId, err))
		return nil
	}

	// 设置ID到log对象中
	log.Id = int(id)
	return nil
}

// GetLogIdByRequestId 根据request_id获取log的ID（按需调用）
func (c *ClickHouseLogStorage) GetLogIdByRequestId(ctx context.Context, requestId string, userId int) (int, error) {
	query := fmt.Sprintf(`SELECT id FROM %s.logs WHERE request_id = ? AND user_id = ? ORDER BY id DESC LIMIT 1`, config.ClickHouseDatabase)

	var id int64
	err := c.db.QueryRowContext(ctx, query, requestId, userId).Scan(&id)
	if err != nil {
		if err == sql.ErrNoRows {
			return 0, fmt.Errorf("no log found for request_id %s and user_id %d", requestId, userId)
		}
		return 0, fmt.Errorf("failed to get log id: %v", err)
	}

	return int(id), nil
}

// retrieveInsertedLogUuid 查询刚插入记录的UUID
func (c *ClickHouseLogStorage) retrieveInsertedLogUuid(ctx context.Context, log *Log) error {
	// 使用request_id和created_at的组合来查找刚插入的记录
	query := fmt.Sprintf(`SELECT uuid FROM %s.logs WHERE request_id = ? AND user_id = ? AND created_at = ? ORDER BY id DESC LIMIT 1`, config.ClickHouseDatabase)

	var uuid string
	err := c.db.QueryRowContext(ctx, query, log.RequestId, log.UserId, time.Unix(log.CreatedAt, 0)).Scan(&uuid)
	if err != nil {
		logger.SysLog(fmt.Sprintf("Failed to retrieve UUID for log with request_id %s: %v", log.RequestId, err))
		// 即使获取UUID失败，也不返回错误，因为数据已经成功插入
		return nil
	}

	// 设置UUID到log对象中
	log.Uuid = uuid
	return nil
}

// insertLogWithGenerateSerialID 使用generateSerialID插入日志并获取ID
func (c *ClickHouseLogStorage) insertLogWithGenerateSerialID(ctx context.Context, log *Log) error {
	// 先生成ID
	var generatedId uint64
	err := c.db.QueryRowContext(ctx, "SELECT generateSerialID('logs_id_seq')").Scan(&generatedId)
	if err != nil {
		return fmt.Errorf("failed to generate ID: %v", err)
	}

	// 设置生成的ID到log对象中
	log.Id = int(generatedId)

	// 使用生成的ID插入数据，让ClickHouse自动生成UUID
	query := fmt.Sprintf(`INSERT INTO %s.logs (id, request_id, user_id, created_at, type, content, username, token_name, token_group, model_name, channel_name, quota, cost_quota, prompt_tokens, completion_tokens, channel_id, token_key, request_duration, response_first_byte_duration, total_duration, elapsed_time, is_stream, system_prompt_reset, ip, remote_ip, other, error_code) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, config.ClickHouseDatabase)

	_, err = c.db.ExecContext(ctx, query,
		log.Id,
		log.RequestId,
		log.UserId,
		time.Unix(log.CreatedAt, 0),
		log.Type,
		log.Content,
		log.Username,
		log.TokenName,
		log.TokenGroup,
		log.ModelName,
		log.ChannelName,
		log.Quota,
		log.CostQuota,
		log.PromptTokens,
		log.CompletionTokens,
		log.ChannelId,
		log.TokenKey,
		log.RequestDuration,
		log.ResponseFirstByteDuration,
		log.TotalDuration,
		log.ElapsedTime,
		boolToUint8(log.IsStream),
		boolToUint8(log.SystemPromptReset),
		log.Ip,
		log.RemoteIp,
		log.Other,
		log.ErrorCode,
	)

	if err != nil {
		return fmt.Errorf("failed to insert log with generated ID %d: %v", log.Id, err)
	}

	// 查询刚插入记录的UUID
	return c.retrieveInsertedLogUuid(ctx, log)
}

// boolToUint8 将bool转换为uint8
func boolToUint8(b bool) uint8 {
	if b {
		return 1
	}
	return 0
}

// uint8ToBool 将uint8转换为bool
func uint8ToBool(u uint8) bool {
	return u != 0
}

// safeUint64ToInt64 安全地将uint64转换为int64，避免溢出
func safeUint64ToInt64(u uint64) int64 {
	const maxInt64 = 9223372036854775807 // math.MaxInt64

	// 检查是否是一个明显异常的大值（接近uint64最大值）
	// 这通常表示是一个负数被转换为uint64的结果
	const suspiciousThreshold = 18446744073709551615 - 1000000 // uint64最大值减去一个合理的缓冲区

	if u > suspiciousThreshold {
		// 这很可能是一个负数被错误转换的结果，返回0作为安全值
		logger.SysLog(fmt.Sprintf("Detected suspicious large uint64 value: %d, converting to 0", u))
		return 0
	}

	if u > maxInt64 {
		// 如果值过大但不是异常值，返回最大的int64值
		return maxInt64
	}
	return int64(u)
}

// RecordLogBatch 批量记录日志
// 注意：批量插入后，logs中的Id字段不会被设置为生成的ID值
// 如果需要获取生成的ID，请使用RecordLog进行单条插入
func (c *ClickHouseLogStorage) RecordLogBatch(ctx context.Context, logs []*Log) error {
	if len(logs) == 0 {
		return nil
	}

	// 开始事务
	tx, err := c.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	// 准备批量插入语句，让ClickHouse自动生成ID和UUID
	query := `INSERT INTO logs (request_id, user_id, created_at, type, content, username, token_name, token_group, model_name, channel_name, quota, cost_quota, prompt_tokens, completion_tokens, channel_id, token_key, request_duration, response_first_byte_duration, total_duration, elapsed_time, is_stream, system_prompt_reset, ip, remote_ip, other, error_code) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
	stmt, err := tx.PrepareContext(ctx, query)
	if err != nil {
		return fmt.Errorf("failed to prepare statement: %v", err)
	}
	defer stmt.Close()

	// 批量插入
	for _, log := range logs {
		if log.RequestId == "" {
			log.RequestId = helper.GetRequestID(ctx)
		}

		_, err = stmt.ExecContext(ctx,
			log.RequestId,
			log.UserId,
			time.Unix(log.CreatedAt, 0),
			log.Type,
			log.Content,
			log.Username,
			log.TokenName,
			log.TokenGroup,
			log.ModelName,
			log.ChannelName,
			log.Quota,
			log.CostQuota,
			log.PromptTokens,
			log.CompletionTokens,
			log.ChannelId,
			log.TokenKey,
			log.RequestDuration,
			log.ResponseFirstByteDuration,
			log.TotalDuration,
			log.ElapsedTime,
			boolToUint8(log.IsStream),
			boolToUint8(log.SystemPromptReset),
			log.Ip,
			log.RemoteIp,
			log.Other,
			log.ErrorCode,
		)
		if err != nil {
			return fmt.Errorf("failed to execute batch insert: %v", err)
		}
	}

	// 提交事务
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	return nil
}

// RecordConsumeLogByDetailIfZeroQuota 记录消费日志（如果配额为零）
func (c *ClickHouseLogStorage) RecordConsumeLogByDetailIfZeroQuota(ctx context.Context, requestId string, ip string, remoteIp string, userId int, channelId int,
	promptTokens int, completionTokens int, modelName string, tokenName string, tokenKey string, tokenGroup string,
	channelName string, quota int, costQuota int, requestDuration int64, responseFirstByteDuration int64, totalDuration int64,
	isStream bool, content string, other string) (*Log, error) {

	if ctx == nil {
		ctx = context.Background()
	}

	// 获取用户信息
	user, _ := GetUserById(userId, false)
	var username string
	if user != nil {
		username = user.Username
	}

	log := &Log{
		UserId:                    userId,
		Username:                  username,
		CreatedAt:                 helper.GetTimestamp(),
		Type:                      LogTypeConsume,
		Content:                   content,
		TokenName:                 tokenName,
		TokenKey:                  tokenKey,
		TokenGroup:                tokenGroup,
		ModelName:                 modelName,
		Quota:                     quota,
		CostQuota:                 costQuota,
		PromptTokens:              promptTokens,
		CompletionTokens:          completionTokens,
		ChannelId:                 channelId,
		ChannelName:               channelName,
		RequestDuration:           requestDuration,
		ResponseFirstByteDuration: responseFirstByteDuration,
		TotalDuration:             totalDuration,
		IsStream:                  isStream,
		RequestId:                 requestId,
		Ip:                        ip,
		RemoteIp:                  remoteIp,
		Other:                     other,
	}

	// 使用现有的 RecordLog 方法
	err := c.RecordLog(ctx, log)
	return log, err
}

// buildWhereClause 构建WHERE子句
func (c *ClickHouseLogStorage) buildWhereClause(userId int, logType []int, startTimestamp int64, endTimestamp int64,
	modelName string, username string, tokenName string, tokenKey string, tokenGroup string,
	channelName string, channel int, isStream string, requestId string, ip string,
	promptTokensMin *int, promptTokensMax *int, completionTokensMin *int, completionTokensMax *int,
	totalDurationMin *float64, totalDurationMax *float64, requestDurationMin *float64, requestDurationMax *float64,
	responseFirstByteDurationMin *float64, responseFirstByteDurationMax *float64, excludeModels []string,
	errorCode string, excludeErrorCodes []string, quotaMin *int, quotaMax *int) (string, []interface{}) {

	var conditions []string
	var args []interface{}

	// 日志类型过滤
	if len(logType) > 0 && !(len(logType) == 1 && logType[0] == 0) {
		placeholders := make([]string, len(logType))
		for i, t := range logType {
			placeholders[i] = "?"
			args = append(args, t)
		}
		conditions = append(conditions, fmt.Sprintf("type IN (%s)", strings.Join(placeholders, ",")))
	}

	// 时间范围过滤
	if startTimestamp != 0 {
		conditions = append(conditions, "created_at >= ?")
		args = append(args, time.Unix(startTimestamp, 0))
	}
	if endTimestamp != 0 {
		conditions = append(conditions, "created_at <= ?")
		args = append(args, time.Unix(endTimestamp, 0))
	}

	// 字符串字段过滤
	stringFields := map[string]string{
		"model_name":   modelName,
		"username":     username,
		"token_name":   tokenName,
		"token_key":    tokenKey,
		"token_group":  tokenGroup,
		"channel_name": channelName,
		"request_id":   requestId,
		"ip":           ip,
		"error_code":   errorCode,
	}

	for field, value := range stringFields {
		if value != "" {
			// 处理token_key的特殊情况
			if field == "token_key" && strings.HasPrefix(value, "sk-") && len(strings.Split(value, "-")[1]) > 1 {
				value = strings.Split(value, "-")[1]
			}
			conditions = append(conditions, field+" = ?")
			args = append(args, value)
		}
	}

	// 整数字段过滤
	if channel != 0 {
		conditions = append(conditions, "channel_id = ?")
		args = append(args, channel)
	}

	// 布尔字段过滤
	if isStream != "" {
		conditions = append(conditions, "is_stream = ?")
		if isStream == "true" {
			args = append(args, 1)
		} else {
			args = append(args, 0)
		}
	}

	// 数值范围过滤
	if promptTokensMin != nil {
		conditions = append(conditions, "prompt_tokens >= ?")
		args = append(args, *promptTokensMin)
	}
	if promptTokensMax != nil {
		conditions = append(conditions, "prompt_tokens <= ?")
		args = append(args, *promptTokensMax)
	}
	if completionTokensMin != nil {
		conditions = append(conditions, "completion_tokens >= ?")
		args = append(args, *completionTokensMin)
	}
	if completionTokensMax != nil {
		conditions = append(conditions, "completion_tokens <= ?")
		args = append(args, *completionTokensMax)
	}
	if quotaMin != nil {
		conditions = append(conditions, "quota >= ?")
		args = append(args, *quotaMin)
	}
	if quotaMax != nil {
		conditions = append(conditions, "quota <= ?")
		args = append(args, *quotaMax)
	}

	// 时长范围过滤
	if totalDurationMin != nil {
		conditions = append(conditions, "total_duration >= ?")
		args = append(args, int64(*totalDurationMin))
	}
	if totalDurationMax != nil {
		conditions = append(conditions, "total_duration <= ?")
		args = append(args, int64(*totalDurationMax))
	}
	if requestDurationMin != nil {
		conditions = append(conditions, "request_duration >= ?")
		args = append(args, int64(*requestDurationMin))
	}
	if requestDurationMax != nil {
		conditions = append(conditions, "request_duration <= ?")
		args = append(args, int64(*requestDurationMax))
	}
	if responseFirstByteDurationMin != nil {
		conditions = append(conditions, "response_first_byte_duration >= ?")
		args = append(args, int64(*responseFirstByteDurationMin))
	}
	if responseFirstByteDurationMax != nil {
		conditions = append(conditions, "response_first_byte_duration <= ?")
		args = append(args, int64(*responseFirstByteDurationMax))
	}

	// 排除模型
	if len(excludeModels) > 0 {
		placeholders := make([]string, len(excludeModels))
		for i, model := range excludeModels {
			placeholders[i] = "?"
			args = append(args, model)
		}
		conditions = append(conditions, fmt.Sprintf("model_name NOT IN (%s)", strings.Join(placeholders, ",")))
	}

	// 排除错误代码
	if len(excludeErrorCodes) > 0 {
		placeholders := make([]string, len(excludeErrorCodes))
		for i, code := range excludeErrorCodes {
			placeholders[i] = "?"
			args = append(args, code)
		}
		conditions = append(conditions, fmt.Sprintf("error_code NOT IN (%s)", strings.Join(placeholders, ",")))
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	return whereClause, args
}

// GetAllLogs 获取所有日志
func (c *ClickHouseLogStorage) GetAllLogs(userId int, timezone string, logType []int, startTimestamp int64, endTimestamp int64,
	modelName string, username string, tokenName string, tokenKey string, tokenGroup string,
	channelName string, startIdx int, num int, channel int, isStream string, requestId string,
	ip string, promptTokensMin *int, promptTokensMax *int, completionTokensMin *int,
	completionTokensMax *int, totalDurationMin *float64, totalDurationMax *float64,
	requestDurationMin *float64, requestDurationMax *float64, responseFirstByteDurationMin *float64,
	responseFirstByteDurationMax *float64, excludeModels []string, errorCode string,
	excludeErrorCodes []string, quotaMin *int, quotaMax *int) ([]*Log, error) {

	whereClause, args := c.buildWhereClause(userId, logType, startTimestamp, endTimestamp,
		modelName, username, tokenName, tokenKey, tokenGroup, channelName, channel,
		isStream, requestId, ip, promptTokensMin, promptTokensMax, completionTokensMin,
		completionTokensMax, totalDurationMin, totalDurationMax, requestDurationMin,
		requestDurationMax, responseFirstByteDurationMin, responseFirstByteDurationMax,
		excludeModels, errorCode, excludeErrorCodes, quotaMin, quotaMax)

	// 构建查询语句
	query := "SELECT * FROM logs " + whereClause + " ORDER BY created_at DESC LIMIT ? OFFSET ?"
	args = append(args, num, startIdx)

	rows, err := c.db.Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query logs: %v", err)
	}
	defer rows.Close()

	return c.scanLogs(rows)
}

// scanLogs 扫描查询结果
func (c *ClickHouseLogStorage) scanLogs(rows *sql.Rows) ([]*Log, error) {
	var logs []*Log

	for rows.Next() {
		log := &Log{}
		var createdAt time.Time
		var isStream, systemPromptReset uint8
		// 使用uint64临时变量来接收ClickHouse的UInt64字段，避免溢出错误
		var requestDuration, responseFirstByteDuration, totalDuration, elapsedTime uint64

		err := rows.Scan(
			&log.Id,
			&log.Uuid,
			&log.RequestId,
			&log.UserId,
			&createdAt,
			&log.Type,
			&log.Content,
			&log.Username,
			&log.TokenName,
			&log.TokenGroup,
			&log.ModelName,
			&log.ChannelName,
			&log.Quota,
			&log.CostQuota,
			&log.PromptTokens,
			&log.CompletionTokens,
			&log.ChannelId,
			&log.TokenKey,
			&requestDuration,
			&responseFirstByteDuration,
			&totalDuration,
			&elapsedTime,
			&isStream,
			&systemPromptReset,
			&log.Ip,
			&log.RemoteIp,
			&log.Other,
			&log.ErrorCode,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan log row: %v", err)
		}

		// 安全地转换uint64到int64，如果值过大则设置为最大值
		log.RequestDuration = safeUint64ToInt64(requestDuration)
		log.ResponseFirstByteDuration = safeUint64ToInt64(responseFirstByteDuration)
		log.TotalDuration = safeUint64ToInt64(totalDuration)
		log.ElapsedTime = safeUint64ToInt64(elapsedTime)

		log.CreatedAt = createdAt.Unix()
		log.IsStream = uint8ToBool(isStream)
		log.SystemPromptReset = uint8ToBool(systemPromptReset)

		logs = append(logs, log)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("rows iteration error: %v", err)
	}

	return logs, nil
}

// CountAllLogs 统计所有日志数量
func (c *ClickHouseLogStorage) CountAllLogs(userId int, logType []int, startTimestamp int64, endTimestamp int64,
	modelName string, username string, tokenName string, tokenKey string, tokenGroup string,
	channelName string, channel int, isStream string, requestId string, ip string,
	promptTokensMin *int, promptTokensMax *int, completionTokensMin *int, completionTokensMax *int,
	totalDurationMin *float64, totalDurationMax *float64, requestDurationMin *float64,
	requestDurationMax *float64, responseFirstByteDurationMin *float64, responseFirstByteDurationMax *float64,
	excludeModels []string, errorCode string, excludeErrorCodes []string, quotaMin *int, quotaMax *int) (int64, error) {

	whereClause, args := c.buildWhereClause(userId, logType, startTimestamp, endTimestamp,
		modelName, username, tokenName, tokenKey, tokenGroup, channelName, channel,
		isStream, requestId, ip, promptTokensMin, promptTokensMax, completionTokensMin,
		completionTokensMax, totalDurationMin, totalDurationMax, requestDurationMin,
		requestDurationMax, responseFirstByteDurationMin, responseFirstByteDurationMax,
		excludeModels, errorCode, excludeErrorCodes, quotaMin, quotaMax)

	query := "SELECT count(*) FROM logs " + whereClause

	var count int64
	err := c.db.QueryRow(query, args...).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count logs: %v", err)
	}

	return count, nil
}

// GetUserLogs 获取用户日志
func (c *ClickHouseLogStorage) GetUserLogs(userId int, logType []int, startTimestamp int64, endTimestamp int64,
	modelName string, tokenName string, tokenKey string, startIdx int, num int,
	isStream string, requestId string) ([]*Log, error) {

	// 处理token key格式
	if tokenKey != "" {
		if strings.HasPrefix(tokenKey, "sk-") && len(strings.Split(tokenKey, "-")[1]) > 1 {
			tokenKey = strings.Split(tokenKey, "-")[1]
		}
	}

	whereClause, args := c.buildWhereClause(userId, logType, startTimestamp, endTimestamp,
		modelName, "", tokenName, tokenKey, "", "", 0, isStream, requestId, "",
		nil, nil, nil, nil, nil, nil, nil, nil, nil, nil,
		[]string{}, "", []string{}, nil, nil)

	// 添加用户ID过滤
	if whereClause == "" {
		whereClause = "WHERE user_id = ?"
		args = []interface{}{userId}
	} else {
		whereClause += " AND user_id = ?"
		args = append(args, userId)
	}

	// 只有admin才显示渠道和成本信息以及系统日志和下游错误日志
	if !IsAdmin(userId) {
		// 添加日志类型过滤，排除系统日志和下游错误日志
		excludeTypes := []int{6, 7, 8, 15, 16} // LogTypeSystemInfo, LogTypeSystemWarn, LogTypeSystemErr, LogTypeDownstreamError, LogTypeTest
		if len(excludeTypes) > 0 {
			whereClause += " AND type NOT IN ("
			for i, t := range excludeTypes {
				if i > 0 {
					whereClause += ","
				}
				whereClause += "?"
				args = append(args, t)
			}
			whereClause += ")"
		}
	}

	// 构建查询语句
	query := "SELECT * FROM logs " + whereClause + " ORDER BY created_at DESC LIMIT ? OFFSET ?"
	args = append(args, num, startIdx)

	rows, err := c.db.Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query user logs: %v", err)
	}
	defer rows.Close()

	logs, err := c.scanLogs(rows)
	if err != nil {
		return nil, err
	}

	// 对于非管理员用户，清除敏感字段
	if !IsAdmin(userId) {
		for _, log := range logs {
			log.Id = 0
			log.ChannelName = ""
			log.ChannelId = 0
			log.CostQuota = 0
			log.ErrorCode = ""
		}
	}

	return logs, nil
}

// CountUserLogs 统计用户日志数量
func (c *ClickHouseLogStorage) CountUserLogs(userId int, logType []int, startTimestamp int64, endTimestamp int64,
	modelName string, tokenName string, tokenKey string, isStream string, requestId string) (int64, error) {

	// 处理token key格式
	if tokenKey != "" {
		if strings.HasPrefix(tokenKey, "sk-") && len(strings.Split(tokenKey, "-")[1]) > 1 {
			tokenKey = strings.Split(tokenKey, "-")[1]
		}
	}

	whereClause, args := c.buildWhereClause(userId, logType, startTimestamp, endTimestamp,
		modelName, "", tokenName, tokenKey, "", "", 0, isStream, requestId, "",
		nil, nil, nil, nil, nil, nil, nil, nil, nil, nil,
		[]string{}, "", []string{}, nil, nil)

	// 添加用户ID过滤
	if whereClause == "" {
		whereClause = "WHERE user_id = ?"
		args = []interface{}{userId}
	} else {
		whereClause += " AND user_id = ?"
		args = append(args, userId)
	}

	// 只有admin才显示渠道和成本信息以及系统日志和下游错误日志
	if !IsAdmin(userId) {
		// 添加日志类型过滤，排除系统日志和下游错误日志
		excludeTypes := []int{6, 7, 8, 15, 16} // LogTypeSystemInfo, LogTypeSystemWarn, LogTypeSystemErr, LogTypeDownstreamError, LogTypeTest
		if len(excludeTypes) > 0 {
			whereClause += " AND type NOT IN ("
			for i, t := range excludeTypes {
				if i > 0 {
					whereClause += ","
				}
				whereClause += "?"
				args = append(args, t)
			}
			whereClause += ")"
		}
	}

	query := "SELECT count(*) FROM logs " + whereClause

	var count int64
	err := c.db.QueryRow(query, args...).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count user logs: %v", err)
	}

	return count, nil
}

// SearchAllLogs 搜索所有日志
func (c *ClickHouseLogStorage) SearchAllLogs(keyword string) ([]*Log, error) {
	// ClickHouse的全文搜索功能相对有限，这里使用LIKE进行简单搜索
	query := `
		SELECT * FROM logs
		WHERE content LIKE ? OR username LIKE ? OR model_name LIKE ? OR token_name LIKE ? OR error_code LIKE ?
		ORDER BY created_at DESC
		LIMIT ?`

	likeKeyword := "%" + keyword + "%"
	rows, err := c.db.Query(query, likeKeyword, likeKeyword, likeKeyword, likeKeyword, likeKeyword, config.MaxRecentItems)
	if err != nil {
		return nil, fmt.Errorf("failed to search logs: %v", err)
	}
	defer rows.Close()

	return c.scanLogs(rows)
}

// SearchUserLogs 搜索用户日志
func (c *ClickHouseLogStorage) SearchUserLogs(userId int, keyword string) ([]*Log, error) {
	whereClause := "WHERE user_id = ?"
	args := []interface{}{userId}

	// 添加关键词搜索条件
	if keyword != "" {
		whereClause += " AND (content LIKE ? OR model_name LIKE ? OR token_name LIKE ? OR error_code LIKE ?)"
		likeKeyword := "%" + keyword + "%"
		args = append(args, likeKeyword, likeKeyword, likeKeyword, likeKeyword)
	}

	// 只有admin才显示渠道和成本信息以及系统日志和下游错误日志
	if !IsAdmin(userId) {
		// 添加日志类型过滤，排除系统日志和下游错误日志
		excludeTypes := []int{6, 7, 8, 15, 16} // LogTypeSystemInfo, LogTypeSystemWarn, LogTypeSystemErr, LogTypeDownstreamError, LogTypeTest
		if len(excludeTypes) > 0 {
			whereClause += " AND type NOT IN ("
			for i, t := range excludeTypes {
				if i > 0 {
					whereClause += ","
				}
				whereClause += "?"
				args = append(args, t)
			}
			whereClause += ")"
		}
	}

	query := "SELECT * FROM logs " + whereClause + " ORDER BY created_at DESC LIMIT ?"
	args = append(args, config.MaxRecentItems)

	rows, err := c.db.Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to search user logs: %v", err)
	}
	defer rows.Close()

	logs, err := c.scanLogs(rows)
	if err != nil {
		return nil, err
	}

	// 对于非管理员用户，清除敏感字段
	if !IsAdmin(userId) {
		for _, log := range logs {
			log.Id = 0
			log.ChannelName = ""
			log.ChannelId = 0
			log.CostQuota = 0
			log.ErrorCode = ""
		}
	}

	return logs, nil
}

// SearchUserLogsByKey 根据key搜索用户日志
func (c *ClickHouseLogStorage) SearchUserLogsByKey(key string, startIdx int, num int) ([]*Log, error) {
	query := `
		SELECT * FROM logs
		WHERE token_key = ? AND type = 2
		ORDER BY created_at DESC
		LIMIT ? OFFSET ?`

	rows, err := c.db.Query(query, key, num, startIdx)
	if err != nil {
		return nil, fmt.Errorf("failed to search logs by key: %v", err)
	}
	defer rows.Close()

	return c.scanLogs(rows)
}

// CountUserLogsByKey 根据key统计用户日志数量
func (c *ClickHouseLogStorage) CountUserLogsByKey(key string) (int64, error) {
	query := "SELECT count(*) FROM logs WHERE token_key = ?"

	var count int64
	err := c.db.QueryRow(query, key).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count logs by key: %v", err)
	}

	return count, nil
}

// SumUsedQuota 统计使用的配额
func (c *ClickHouseLogStorage) SumUsedQuota(logType int, startTimestamp int64, endTimestamp int64, modelName string,
	username string, tokenName string, tokenKey string, tokenGroup string, channel int, useRedis bool) (Stat, error) {

	// 构建查询条件
	var conditions []string
	var args []interface{}

	// 日志类型过滤（消费和退款）
	logConsumedTypes := []int{LogTypeConsume, LogTypeRefund}
	placeholders := make([]string, len(logConsumedTypes))
	for i, t := range logConsumedTypes {
		placeholders[i] = "?"
		args = append(args, t)
	}
	conditions = append(conditions, fmt.Sprintf("type IN (%s)", strings.Join(placeholders, ",")))

	// 时间范围过滤
	if startTimestamp != 0 {
		conditions = append(conditions, "created_at >= ?")
		args = append(args, time.Unix(startTimestamp, 0))
	}
	if endTimestamp != 0 {
		conditions = append(conditions, "created_at <= ?")
		args = append(args, time.Unix(endTimestamp, 0))
	}

	// 其他过滤条件
	stringFilters := map[string]string{
		"username":    username,
		"token_name":  tokenName,
		"token_key":   tokenKey,
		"token_group": tokenGroup,
		"model_name":  modelName,
	}

	for field, value := range stringFilters {
		if value != "" {
			if field == "token_key" && strings.HasPrefix(value, "sk-") && len(strings.Split(value, "-")[1]) > 1 {
				value = strings.Split(value, "-")[1]
			}
			conditions = append(conditions, field+" = ?")
			args = append(args, value)
		}
	}

	if channel != 0 {
		conditions = append(conditions, "channel_id = ?")
		args = append(args, channel)
	}

	whereClause := "WHERE " + strings.Join(conditions, " AND ")

	// 查询配额总和
	quotaQuery := "SELECT sum(quota) FROM logs " + whereClause
	var totalQuota sql.NullInt64
	err := c.db.QueryRow(quotaQuery, args...).Scan(&totalQuota)
	if err != nil {
		return Stat{}, fmt.Errorf("failed to sum quota: %v", err)
	}

	stat := Stat{}
	if totalQuota.Valid {
		stat.Quota = int(totalQuota.Int64)
	}

	// 计算最近一分钟的RPM和TPM
	now := time.Now()
	oneMinuteAgo := now.Add(-1 * time.Minute)

	rpmConditions := append(conditions, "created_at >= ?", "created_at <= ?")
	rpmArgs := append(args, oneMinuteAgo, now)
	rpmWhereClause := "WHERE " + strings.Join(rpmConditions, " AND ")

	// RPM查询
	rpmQuery := "SELECT count(*) FROM logs " + rpmWhereClause
	var rpm int64
	err = c.db.QueryRow(rpmQuery, rpmArgs...).Scan(&rpm)
	if err != nil {
		return Stat{}, fmt.Errorf("failed to calculate RPM: %v", err)
	}
	stat.Rpm = int(rpm)

	// TPM查询
	tpmQuery := "SELECT sum(prompt_tokens + completion_tokens) FROM logs " + rpmWhereClause
	var tpm sql.NullInt64
	err = c.db.QueryRow(tpmQuery, rpmArgs...).Scan(&tpm)
	if err != nil {
		return Stat{}, fmt.Errorf("failed to calculate TPM: %v", err)
	}
	if tpm.Valid {
		stat.Tpm = int(tpm.Int64)
	}

	// MPM查询（配额/500000）
	mpmQuery := "SELECT sum(quota) / 500000 FROM logs " + rpmWhereClause
	var mpm sql.NullFloat64
	err = c.db.QueryRow(mpmQuery, rpmArgs...).Scan(&mpm)
	if err != nil {
		return Stat{}, fmt.Errorf("failed to calculate MPM: %v", err)
	}
	if mpm.Valid {
		stat.Mpm = mpm.Float64
	}

	return stat, nil
}

// SumUsedQuotaByKey 根据key统计使用的配额
func (c *ClickHouseLogStorage) SumUsedQuotaByKey(key string, startTimestamp int64, endTimestamp int64) (Stat, error) {
	return c.SumUsedQuota(0, startTimestamp, endTimestamp, "", "", "", key, "", 0, false)
}

// SumAllDailyUsageStatsByDimension 按维度统计每日使用情况
func (c *ClickHouseLogStorage) SumAllDailyUsageStatsByDimension(userId int, timezone string, tokenName string, username string,
	channel int, channelName string, modelName string, startTimestamp int64, endTimestamp int64,
	dimension string, granularity string) ([]*DailyModelUsageStats, error) {

	// 这是一个复杂的聚合查询，ClickHouse非常适合这种场景
	// 构建基础查询条件
	var conditions []string
	var args []interface{}

	// 用户过滤
	if userId != 0 {
		conditions = append(conditions, "user_id = ?")
		args = append(args, userId)
	}

	// 时间范围过滤
	if startTimestamp != 0 {
		conditions = append(conditions, "created_at >= ?")
		args = append(args, time.Unix(startTimestamp, 0))
	}
	if endTimestamp != 0 {
		conditions = append(conditions, "created_at <= ?")
		args = append(args, time.Unix(endTimestamp, 0))
	}

	// 其他过滤条件
	stringFilters := map[string]string{
		"token_name":   tokenName,
		"username":     username,
		"channel_name": channelName,
		"model_name":   modelName,
	}

	for field, value := range stringFilters {
		if value != "" {
			conditions = append(conditions, field+" = ?")
			args = append(args, value)
		}
	}

	if channel != 0 {
		conditions = append(conditions, "channel_id = ?")
		args = append(args, channel)
	}

	// 构建WHERE子句
	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// 根据granularity确定时间分组函数
	var timeGroupFunc string
	switch granularity {
	case "hour":
		timeGroupFunc = "toStartOfHour(created_at)"
	case "day":
		timeGroupFunc = "toDate(created_at)"
	case "month":
		timeGroupFunc = "toStartOfMonth(created_at)"
	default:
		timeGroupFunc = "toDate(created_at)" // 默认按天
	}

	// 根据dimension确定分组字段
	var groupByFields []string
	var selectFields []string

	selectFields = append(selectFields, timeGroupFunc+" as date")
	groupByFields = append(groupByFields, timeGroupFunc)

	switch dimension {
	case "model":
		selectFields = append(selectFields, "model_name")
		groupByFields = append(groupByFields, "model_name")
	case "user":
		selectFields = append(selectFields, "user_id", "username")
		groupByFields = append(groupByFields, "user_id", "username")
	case "channel":
		selectFields = append(selectFields, "channel_id", "channel_name")
		groupByFields = append(groupByFields, "channel_id", "channel_name")
	case "token":
		selectFields = append(selectFields, "token_name")
		groupByFields = append(groupByFields, "token_name")
	}

	// 添加聚合字段
	selectFields = append(selectFields,
		"count(*) as request_count",
		"sum(quota) as total_quota",
		"sum(prompt_tokens) as total_prompt_tokens",
		"sum(completion_tokens) as total_completion_tokens",
		"avg(total_duration) as avg_duration",
	)

	// 构建完整查询
	query := fmt.Sprintf("SELECT %s FROM logs %s GROUP BY %s ORDER BY date DESC",
		strings.Join(selectFields, ", "),
		whereClause,
		strings.Join(groupByFields, ", "))

	rows, err := c.db.Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query daily usage stats: %v", err)
	}
	defer rows.Close()

	var stats []*DailyModelUsageStats
	for rows.Next() {
		stat := &DailyModelUsageStats{}
		var date time.Time
		var requestCount, totalQuota, totalPromptTokens, totalCompletionTokens int64
		var avgDuration float64

		// 根据dimension扫描不同的字段
		switch dimension {
		case "model":
			var modelName string
			err = rows.Scan(&date, &modelName, &requestCount, &totalQuota, &totalPromptTokens, &totalCompletionTokens, &avgDuration)
			stat.ModelName = modelName
		case "user":
			var userId int
			var username string
			err = rows.Scan(&date, &userId, &username, &requestCount, &totalQuota, &totalPromptTokens, &totalCompletionTokens, &avgDuration)
			stat.UserId = strconv.Itoa(userId) // 转换为string
			stat.Username = username
		default:
			err = rows.Scan(&date, &requestCount, &totalQuota, &totalPromptTokens, &totalCompletionTokens, &avgDuration)
		}

		if err != nil {
			return nil, fmt.Errorf("failed to scan daily usage stats: %v", err)
		}

		stat.Date = date.Format("2006-01-02")
		stat.SumQuota = int(totalQuota) // 使用正确的字段名

		stats = append(stats, stat)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("rows iteration error: %v", err)
	}

	return stats, nil
}

// HealthCheck 健康检查
func (c *ClickHouseLogStorage) HealthCheck() error {
	return c.db.Ping()
}

// Close 关闭连接
func (c *ClickHouseLogStorage) Close() error {
	close(c.stopCh)
	c.wg.Wait()
	return c.db.Close()
}

// RecordLogExtend 记录LogExtend
func (c *ClickHouseLogStorage) RecordLogExtend(ctx context.Context, logExtend *LogExtend) error {
	// 为数据库操作添加超时保护
	bgCtx := context.Background()
	timeoutCtx, cancel := context.WithTimeout(bgCtx, 30*time.Second)
	defer cancel()

	// 根据ClickHouse版本选择不同的插入策略
	if c.supportsGenerateSerialID {
		// 新版本：使用自增ID和log_id关联
		return c.insertLogExtendWithAutoIncrementID(timeoutCtx, logExtend)
	} else {
		// 旧版本：使用UUID关联
		return c.insertLogExtendWithUUID(timeoutCtx, logExtend)
	}
}

// insertLogExtendWithAutoIncrementID 新版本ClickHouse：数据库自动生成ID + UUID关联插入LogExtend
func (c *ClickHouseLogStorage) insertLogExtendWithAutoIncrementID(ctx context.Context, logExtend *LogExtend) error {
	// 检查LogUuid是否存在（用于关联）
	if logExtend.LogUuid == "" {
		return fmt.Errorf("log_uuid is required for log_extend insertion")
	}

	// 让数据库自动生成ID，不需要提前获取
	// 插入数据，ID字段使用DEFAULT值（数据库自动生成）
	query := `INSERT INTO log_extends (log_id, log_uuid, created_at, prompt, completion, completion_id, upstream_response, full_response, request_path) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`

	_, err := c.db.ExecContext(ctx, query,
		logExtend.LogId,   // 保留log_id字段用于兼容
		logExtend.LogUuid, // 主要使用UUID关联
		time.Unix(logExtend.CreatedAt, 0),
		logExtend.Prompt,
		logExtend.Completion,
		logExtend.CompletionId,
		logExtend.UpstreamResponse,
		logExtend.FullResponse,
		logExtend.RequestPath,
	)

	if err != nil {
		return fmt.Errorf("failed to insert log_extend with UUID %s: %v", logExtend.LogUuid, err)
	}

	// 插入成功，ID由数据库自动生成，我们不需要知道具体值
	logExtend.Id = 0 // 标记为未获取
	return nil
}

// insertLogExtendWithUUID 旧版本ClickHouse：使用UUID关联插入LogExtend
func (c *ClickHouseLogStorage) insertLogExtendWithUUID(ctx context.Context, logExtend *LogExtend) error {
	// 生成UUID作为主键
	if logExtend.LogUuid == "" {
		return fmt.Errorf("log_uuid is required for UUID-based log_extend insertion")
	}

	// 生成自己的UUID
	extendUuid := helper.GenerateUUID()

	// 使用UUID插入数据，通过log_uuid关联
	query := `INSERT INTO log_extends (uuid, log_uuid, created_at, prompt, completion, completion_id, upstream_response, full_response, request_path) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`

	_, err := c.db.ExecContext(ctx, query,
		extendUuid,
		logExtend.LogUuid,
		time.Unix(logExtend.CreatedAt, 0),
		logExtend.Prompt,
		logExtend.Completion,
		logExtend.CompletionId,
		logExtend.UpstreamResponse,
		logExtend.FullResponse,
		logExtend.RequestPath,
	)

	if err != nil {
		return fmt.Errorf("failed to insert log_extend with UUID %s: %v", extendUuid, err)
	}

	// 旧版本不设置数字ID，保持为0
	logExtend.Id = 0
	return nil
}

// insertLogExtendWithGenerateSerialID 使用generateSerialID插入LogExtend并获取ID
func (c *ClickHouseLogStorage) insertLogExtendWithGenerateSerialID(ctx context.Context, logExtend *LogExtend) error {
	// 先生成ID
	var generatedId uint64
	err := c.db.QueryRowContext(ctx, "SELECT generateSerialID('log_extends_id_seq')").Scan(&generatedId)
	if err != nil {
		return fmt.Errorf("failed to generate ID for log_extend: %v", err)
	}

	// 设置生成的ID到logExtend对象中
	logExtend.Id = int(generatedId)

	// 使用生成的ID插入数据
	query := `INSERT INTO log_extends (id, log_id, log_uuid, created_at, prompt, completion, completion_id, upstream_response, full_response, request_path) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	_, err = c.db.ExecContext(ctx, query,
		logExtend.Id,
		logExtend.LogId,
		logExtend.LogUuid,
		time.Unix(logExtend.CreatedAt, 0),
		logExtend.Prompt,
		logExtend.Completion,
		logExtend.CompletionId,
		logExtend.UpstreamResponse,
		logExtend.FullResponse,
		logExtend.RequestPath,
	)

	if err != nil {
		return fmt.Errorf("failed to insert log_extend with generated ID %d: %v", logExtend.Id, err)
	}

	return nil
}

// RecordLogExtendBatch 批量记录LogExtend
func (c *ClickHouseLogStorage) RecordLogExtendBatch(ctx context.Context, logExtends []*LogExtend) error {
	if len(logExtends) == 0 {
		return nil
	}

	// 开始事务
	tx, err := c.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	// 准备批量插入语句，让ClickHouse自动生成ID
	query := `INSERT INTO log_extends (log_id, created_at, prompt, completion, completion_id, upstream_response, full_response, request_path) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`
	stmt, err := tx.PrepareContext(ctx, query)
	if err != nil {
		return fmt.Errorf("failed to prepare statement: %v", err)
	}
	defer stmt.Close()

	// 批量插入
	for _, logExtend := range logExtends {
		_, err = stmt.ExecContext(ctx,
			logExtend.LogId,
			time.Unix(logExtend.CreatedAt, 0),
			logExtend.Prompt,
			logExtend.Completion,
			logExtend.CompletionId,
			logExtend.UpstreamResponse,
			logExtend.FullResponse,
			logExtend.RequestPath,
		)
		if err != nil {
			return fmt.Errorf("failed to execute batch insert: %v", err)
		}
	}

	// 提交事务
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	return nil
}

// GetLogExtendByLogId 根据LogId获取LogExtend
func (c *ClickHouseLogStorage) GetLogExtendByLogId(logId int) (*LogExtend, error) {
	// 根据ClickHouse版本选择不同的查询策略
	if c.supportsGenerateSerialID {
		// 新版本：直接使用log_id查询
		return c.getLogExtendByLogIdDirect(logId)
	} else {
		// 旧版本：需要先查询UUID再关联
		return c.getLogExtendByLogIdViaUuid(logId)
	}
}

// getLogExtendByLogIdDirect 新版本：通过log_id查询UUID，再通过UUID查询LogExtend
func (c *ClickHouseLogStorage) getLogExtendByLogIdDirect(logId int) (*LogExtend, error) {
	// 先根据log_id查询对应的UUID
	var logUuid string
	uuidQuery := fmt.Sprintf("SELECT uuid FROM %s.logs WHERE id = ? LIMIT 1", config.ClickHouseDatabase)
	err := c.db.QueryRow(uuidQuery, logId).Scan(&logUuid)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("no log found for log_id %d", logId)
		}
		return nil, fmt.Errorf("failed to get uuid for log_id %d: %v", logId, err)
	}

	// 使用UUID查询LogExtend
	return c.getLogExtendByUuid(logUuid)
}

// getLogExtendByLogIdViaUuid 旧版本：通过UUID查询LogExtend
func (c *ClickHouseLogStorage) getLogExtendByLogIdViaUuid(logId int) (*LogExtend, error) {
	// 这种情况下，前端传入的是数字ID，但旧版本ClickHouse使用UUID
	// 我们需要通过其他方式找到对应的LogExtend，比如通过时间和用户信息
	// 但这种情况比较复杂，建议提示用户升级ClickHouse或使用UUID查询
	return nil, fmt.Errorf("log_id %d query not supported in UUID-based ClickHouse mode, please use UUID-based queries", logId)
}

// getLogExtendByLogIdFallback 传统的根据LogId查询方式（兼容旧数据）
func (c *ClickHouseLogStorage) getLogExtendByLogIdFallback(logId int) (*LogExtend, error) {
	query := "SELECT * FROM log_extends WHERE log_id = ? ORDER BY created_at DESC LIMIT 1"

	row := c.db.QueryRow(query, logId)

	logExtend := &LogExtend{}
	var createdAt time.Time

	err := row.Scan(
		&logExtend.Id,
		&logExtend.LogId,
		&logExtend.LogUuid,
		&createdAt,
		&logExtend.Prompt,
		&logExtend.Completion,
		&logExtend.CompletionId,
		&logExtend.UpstreamResponse,
		&logExtend.FullResponse,
		&logExtend.RequestPath,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("no log extend found for log_id %d", logId)
		}
		return nil, fmt.Errorf("failed to scan log extend: %v", err)
	}

	logExtend.CreatedAt = createdAt.Unix()
	return logExtend, nil
}

// getLogExtendByUuid 根据UUID查询LogExtend
func (c *ClickHouseLogStorage) getLogExtendByUuid(logUuid string) (*LogExtend, error) {
	query := "SELECT * FROM log_extends WHERE log_uuid = ? ORDER BY created_at DESC LIMIT 1"

	row := c.db.QueryRow(query, logUuid)

	logExtend := &LogExtend{}
	var createdAt time.Time

	err := row.Scan(
		&logExtend.Id,
		&logExtend.LogId,
		&logExtend.LogUuid,
		&createdAt,
		&logExtend.Prompt,
		&logExtend.Completion,
		&logExtend.CompletionId,
		&logExtend.UpstreamResponse,
		&logExtend.FullResponse,
		&logExtend.RequestPath,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("no log extend found for log_uuid %s", logUuid)
		}
		return nil, fmt.Errorf("failed to scan log extend: %v", err)
	}

	logExtend.CreatedAt = createdAt.Unix()
	return logExtend, nil
}

// DeleteLogExtendByLogId 根据LogId删除LogExtend
func (c *ClickHouseLogStorage) DeleteLogExtendByLogId(logId int) error {
	query := "ALTER TABLE log_extends DELETE WHERE log_id = ?"
	_, err := c.db.Exec(query, logId)
	return err
}

// DeleteLogExtendByTimestamp 根据时间戳删除LogExtend
func (c *ClickHouseLogStorage) DeleteLogExtendByTimestamp(targetTimestamp int64) (int64, error) {
	// 先统计要删除的记录数
	countQuery := "SELECT count(*) FROM log_extends WHERE created_at < ?"
	var count int64
	err := c.db.QueryRow(countQuery, time.Unix(targetTimestamp, 0)).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count records: %v", err)
	}

	// 执行删除
	deleteQuery := "ALTER TABLE log_extends DELETE WHERE created_at < ?"
	_, err = c.db.Exec(deleteQuery, time.Unix(targetTimestamp, 0))
	if err != nil {
		return 0, fmt.Errorf("failed to delete records: %v", err)
	}

	return count, nil
}

// DeleteInvalidLogExtend 删除无效的LogExtend记录
func (c *ClickHouseLogStorage) DeleteInvalidLogExtend() error {
	// ClickHouse中删除不存在关联的记录
	query := `
		ALTER TABLE log_extends DELETE
		WHERE log_id NOT IN (
			SELECT id FROM logs
		)`

	_, err := c.db.Exec(query)
	return err
}

// GetStorageType 获取存储类型
func (c *ClickHouseLogStorage) GetStorageType() string {
	return "clickhouse"
}

// RecordRefundLogByDetailIfZeroQuota 记录退款日志（如果配额为零）
func (c *ClickHouseLogStorage) RecordRefundLogByDetailIfZeroQuota(ctx context.Context, requestId string, ip string, remoteIp string, userId int, channelId int,
	promptTokens int, completionTokens int, modelName string, tokenName string, tokenKey string, channelName string,
	quota int, requestDuration int64, responseFirstByteDuration int64, totalDuration int64, isStream bool, content string) (*Log, error) {

	if ctx == nil {
		ctx = context.Background()
	}

	if requestId != "" {
		ctx = context.WithValue(ctx, helper.RequestIdKey, requestId)
	}

	logger.Info(ctx, fmt.Sprintf("record Refund log: userId=%d, channelId=%d, promptTokens=%d, completionTokens=%d, modelName=%s, tokenName=%s, quota=%d,totalDuration=%d, content=%s", userId, channelId, promptTokens, completionTokens, modelName, tokenName, quota, totalDuration, content))

	if !config.LogConsumeEnabled {
		return nil, nil
	}

	requestId, ok := ctx.Value(helper.RequestIdKey).(string)
	if !ok {
		requestId = ""
	}

	log := &Log{
		UserId:                    userId,
		Username:                  CacheGetUsernameById(userId),
		CreatedAt:                 helper.GetTimestamp(),
		Type:                      LogTypeRefund,
		Content:                   content,
		PromptTokens:              promptTokens,
		CompletionTokens:          completionTokens,
		TokenName:                 tokenName,
		TokenKey:                  tokenKey,
		ChannelName:               channelName,
		ModelName:                 modelName,
		Quota:                     quota,
		ChannelId:                 channelId,
		TotalDuration:             totalDuration,
		RequestDuration:           requestDuration,
		ResponseFirstByteDuration: responseFirstByteDuration,
		IsStream:                  isStream,
		RequestId:                 requestId,
		Ip:                        ip,
		RemoteIp:                  remoteIp,
	}

	// 使用ClickHouse的RecordLog方法
	err := c.RecordLog(ctx, log)
	if err != nil {
		logger.Error(ctx, "<RecordRefundLogByDetailIfZeroQuota> failed to record log: "+err.Error())
		return log, err
	}

	return log, nil
}

// RecordSysLogToDBAndFile 记录系统日志到数据库和文件
func (c *ClickHouseLogStorage) RecordSysLogToDBAndFile(ctx context.Context, requestId string, logType int, userId int, channelId int, modelName string, tokenName string, channelName string, content string, prompt string) error {
	// 如果是错误日志类型
	if logType == LogTypeDownstreamError || logType == LogTypeSystemErr {
		// 检查是否启用错误日志
		if !config.LogErrorEnabled {
			return nil
		}

		// 获取错误码
		errorCode := getErrorCodeFromContext(ctx)

		// 检查是否需要忽略该错误码
		if shouldIgnoreErrorCode(errorCode) {
			return nil
		}

		// 检查是否需要采样
		if !shouldSampleError(errorCode) {
			return nil
		}

		// 截断content和prompt
		content = TruncateOptimized(content, config.MaxPromptLogLength, modelName)
		if prompt != "" {
			prompt = TruncateOptimized(prompt, config.MaxPromptLogLength, modelName)
		}
	}

	// 如果是系统信息日志类型且日志开关已关闭，则直接返回
	if logType == LogTypeSystemInfo && !config.LogSysInfoEnabled {
		return nil
	}

	// 根据日志类型判断记录什么类型的日志文件
	switch logType {
	case LogTypeSystemInfo:
		logger.SysLog(fmt.Sprintf("系统信息: %s", content))
	case LogTypeSystemErr:
		logger.SysError(fmt.Sprintf("系统错误: %s", content))
	default:
		logger.SysLog(fmt.Sprintf("系统信息: %s", content))
	}

	if !config.LogConsumeEnabled {
		return nil
	}

	// 判断prompt中是否包含base64,如果包含,需要截断
	if strings.Contains(prompt, "base64") {
		prompt = "base64"
	}

	if requestId == "" {
		requestId1, ok := ctx.Value(helper.RequestIdKey).(string)
		if ok {
			requestId = requestId1
		}
	}

	log := &Log{
		RequestId:   requestId,
		UserId:      userId,
		Username:    CacheGetUsernameById(userId),
		CreatedAt:   helper.GetTimestamp(),
		Type:        logType,
		Content:     content,
		TokenName:   tokenName,
		ChannelName: channelName,
		ModelName:   modelName,
		ChannelId:   channelId,
		ErrorCode:   getErrorCodeFromContext(ctx), // 从context中获取错误码
	}

	// 使用ClickHouse的RecordLog方法
	err := c.RecordLog(ctx, log)
	if err != nil {
		logger.Error(ctx, "<RecordSysLogToDBAndFile> failed to record log: "+err.Error())
		return err
	}

	if prompt == "" {
		// 提示词为空不存extend
		return nil
	}

	// 判断最大Prompt长度
	prompt = TruncateOptimized(prompt, config.MaxPromptLogLength, modelName)
	// 保存log-ex
	logExtend := &LogExtend{
		LogId:     log.Id,
		Prompt:    prompt,
		CreatedAt: helper.GetTimestamp(),
	}
	err = c.RecordLogExtend(ctx, logExtend)
	if err != nil {
		logger.Error(ctx, "<RecordSysLogToDBAndFile> failed to record logExtend: "+err.Error())
		return err
	}

	return nil
}

// RecordSysLogToDBAndFileByGinContext 记录系统日志到数据库和文件（通过Gin Context）
func (c *ClickHouseLogStorage) RecordSysLogToDBAndFileByGinContext(ginCtx interface{}, logType int, content string, prompt string) error {
	// 类型断言获取gin.Context
	ctx, ok := ginCtx.(*gin.Context)
	if !ok {
		return fmt.Errorf("invalid context type, expected *gin.Context")
	}

	// 如果是错误日志类型
	if logType == LogTypeDownstreamError || logType == LogTypeSystemErr {
		// 检查是否启用错误日志
		if !config.LogErrorEnabled {
			return nil
		}

		// 获取错误码
		errorCode := ctx.GetString(ctxkey.ErrorCode)

		// 检查是否需要忽略该错误码
		if shouldIgnoreErrorCode(errorCode) {
			return nil
		}

		// 检查是否需要采样
		if !shouldSampleError(errorCode) {
			return nil
		}

		// 获取用户ID
		userId := ctx.GetInt(ctxkey.Id)

		// 获取用户最大Prompt长度配置
		userMaxPromptLogLength, err := CacheGetUserMaxPromptLogLength(userId)
		if err != nil {
			userMaxPromptLogLength = 0 // 出错时使用系统默认配置
		}

		var maxPromptLogLength int64
		if userMaxPromptLogLength == 0 {
			maxPromptLogLength = config.MaxPromptLogLength // 使用系统默认配置
		} else {
			maxPromptLogLength = userMaxPromptLogLength // 使用用户配置
		}

		// 截断content和prompt
		content = TruncateOptimized(content, maxPromptLogLength, ctx.GetString(ctxkey.RequestModel))
		if prompt != "" {
			prompt = TruncateOptimized(prompt, maxPromptLogLength, ctx.GetString(ctxkey.RequestModel))
		}
	}

	// 如果是系统信息日志类型且日志开关已关闭，则直接返回
	if logType == LogTypeSystemInfo && !config.LogSysInfoEnabled {
		return nil
	}

	// 根据日志类型判断记录什么类型的日志文件
	switch logType {
	case LogTypeSystemInfo:
		logger.SysLog(fmt.Sprintf("系统信息: %s", content))
	case LogTypeSystemErr:
		logger.SysError(fmt.Sprintf("系统错误: %s", content))
	default:
		logger.SysLog(fmt.Sprintf("系统信息: %s", content))
	}

	if !config.LogConsumeEnabled {
		return nil
	}

	// 判断prompt中是否包含base64,如果包含,需要截断
	if strings.Contains(prompt, "base64") {
		prompt = "base64"
	}

	failedDuration := ctx.GetInt64(ctxkey.FailedDuration)
	var requestDuration int64
	var responseFirstByteDuration int64
	var totalDuration int64
	if failedDuration > 0 {
		requestDuration = failedDuration
		responseFirstByteDuration = failedDuration
		totalDuration = failedDuration
	}

	log := &Log{
		RequestId:                 ctx.GetString(helper.RequestIdKey),
		UserId:                    ctx.GetInt(ctxkey.Id),
		Username:                  CacheGetUsernameById(ctx.GetInt(ctxkey.Id)),
		CreatedAt:                 helper.GetTimestamp(),
		Type:                      logType,
		Content:                   content,
		TokenName:                 ctx.GetString(ctxkey.TokenName),
		TokenKey:                  ctx.GetString(ctxkey.TokenKey),
		TokenGroup:                ctx.GetString(ctxkey.TokenGroup),
		RequestDuration:           requestDuration,
		ResponseFirstByteDuration: responseFirstByteDuration,
		TotalDuration:             totalDuration,
		ChannelName:               ctx.GetString(ctxkey.ChannelName),
		ModelName:                 ctx.GetString(ctxkey.RequestModel),
		ChannelId:                 ctx.GetInt(ctxkey.ChannelId),
		Ip:                        helper.GetClientRealIp(ctx),
		RemoteIp:                  ctx.RemoteIP(),
		ErrorCode:                 ctx.GetString(ctxkey.ErrorCode),
	}

	// 使用ClickHouse的RecordLog方法
	err := c.RecordLog(context.Background(), log)
	if err != nil {
		logger.Error(context.TODO(), "<RecordSysLogToDBAndFileByGinContext> failed to record log: "+err.Error())
		return err
	}

	if prompt == "" {
		// 提示词为空不存extend
		return nil
	}

	// 获取用户ID
	userId := ctx.GetInt(ctxkey.Id)

	// 获取用户最大Prompt长度配置
	userMaxPromptLogLength, err := CacheGetUserMaxPromptLogLength(userId)
	if err != nil {
		userMaxPromptLogLength = 0 // 出错时使用系统默认配置
	}

	var maxPromptLogLength int64
	if userMaxPromptLogLength == 0 {
		maxPromptLogLength = config.MaxPromptLogLength // 使用系统默认配置
	} else {
		maxPromptLogLength = userMaxPromptLogLength // 使用用户配置
	}

	// 判断最大Prompt长度
	prompt = TruncateOptimized(prompt, maxPromptLogLength, ctx.GetString(ctxkey.RequestModel))
	// 保存log-ex
	logExtend := &LogExtend{
		LogId:     log.Id,
		Prompt:    prompt,
		CreatedAt: helper.GetTimestamp(),
	}
	err = c.RecordLogExtend(context.Background(), logExtend)
	if err != nil {
		logger.Error(context.TODO(), "<RecordSysLogToDBAndFileByGinContext> failed to record logExtend: "+err.Error())
		return err
	}

	return nil
}

// RecordLogToDBAndFileByMeta 记录日志到数据库和文件（通过Meta）
func (c *ClickHouseLogStorage) RecordLogToDBAndFileByMeta(ctx context.Context, logType int, toFile bool, meta Meta, content string, quota int, requestDuration int64, responseFirstByteDuration int64, totalDuration int64) error {
	// 如果是错误日志类型
	if logType == LogTypeDownstreamError || logType == LogTypeSystemErr {
		// 检查是否启用错误日志
		if !config.LogErrorEnabled {
			return nil
		}

		// 获取错误码
		errorCode := meta.ErrorCode

		// 检查是否需要忽略该错误码
		if shouldIgnoreErrorCode(errorCode) {
			return nil
		}

		// 获取用户最大Prompt长度配置
		userMaxPromptLogLength, err := CacheGetUserMaxPromptLogLength(meta.UserId)
		if err != nil {
			userMaxPromptLogLength = 0 // 出错时使用系统默认配置
		}

		var maxPromptLogLength int64
		if userMaxPromptLogLength == 0 {
			maxPromptLogLength = config.MaxPromptLogLength // 使用系统默认配置
		} else {
			maxPromptLogLength = userMaxPromptLogLength // 使用用户配置
		}

		// 截断content和prompt
		content = TruncateOptimized(content, maxPromptLogLength, lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(meta.ActualModelName))
		if meta.DetailPrompt != "" {
			meta.DetailPrompt = TruncateOptimized(meta.DetailPrompt, maxPromptLogLength, lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(meta.ActualModelName))
		}
	}

	// 如果是系统信息日志类型且日志开关已关闭，则直接返回
	if logType == LogTypeSystemInfo && !config.LogSysInfoEnabled {
		return nil
	}

	// 根据日志类型判断记录什么类型的日志文件
	if toFile {
		switch logType {
		case LogTypeSystemInfo:
			logger.SysLog(fmt.Sprintf("系统信息: %s", content))
		case LogTypeSystemErr:
			logger.SysError(fmt.Sprintf("系统错误: %s", content))
		default:
			logger.SysLog(fmt.Sprintf("系统信息: %s", content))
		}
	}

	if !config.LogConsumeEnabled {
		return nil
	}

	if ctx == nil {
		ctx = context.Background()
	}

	if meta.RequestId != "" {
		ctx = context.WithValue(ctx, helper.RequestIdKey, meta.RequestId)
	}

	log := &Log{
		UserId:                    meta.UserId,
		Username:                  CacheGetUsernameById(meta.UserId),
		CreatedAt:                 helper.GetTimestamp(),
		Type:                      logType,
		Content:                   content,
		PromptTokens:              meta.PromptTokens,
		CompletionTokens:          meta.CompletionTokens,
		TokenName:                 meta.TokenName,
		TokenKey:                  meta.TokenKey,
		TokenGroup:                meta.TokenGroup,
		ChannelName:               meta.ChannelName,
		ModelName:                 lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(meta.ActualModelName),
		Quota:                     quota,
		ChannelId:                 meta.ChannelId,
		TotalDuration:             totalDuration,
		RequestDuration:           requestDuration,
		ResponseFirstByteDuration: responseFirstByteDuration,
		IsStream:                  meta.IsStream,
		RequestId:                 meta.RequestId,
		Ip:                        meta.Ip,
		RemoteIp:                  meta.RemoteIp,
		ErrorCode:                 meta.ErrorCode,
	}

	// 使用ClickHouse的RecordLog方法
	err := c.RecordLog(ctx, log)
	if err != nil {
		logger.Error(ctx, "<RecordLogToDBAndFileByMeta> failed to record log: "+err.Error())
		return err
	}

	if meta.DetailPrompt == "" {
		// 提示词为空不存extend
		return nil
	}

	// 判断最大Prompt长度
	meta.DetailPrompt = TruncateOptimized(meta.DetailPrompt, config.MaxPromptLogLength, lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(meta.ActualModelName))
	// 保存log-ex
	logExtend := &LogExtend{
		LogId:     log.Id,
		Prompt:    meta.DetailPrompt,
		CreatedAt: helper.GetTimestamp(),
	}
	err = c.RecordLogExtend(ctx, logExtend)
	if err != nil {
		logger.Error(ctx, "<RecordLogToDBAndFileByMeta> failed to record logExtend: "+err.Error())
		return err
	}

	return nil
}

// TruncateLogExtendTable 清空LogExtend表
func (c *ClickHouseLogStorage) TruncateLogExtendTable(ctx context.Context) error {
	// ClickHouse使用TRUNCATE TABLE语句
	query := "TRUNCATE TABLE IF EXISTS log_extends"
	_, err := c.db.Exec(query)
	if err != nil {
		logger.Error(ctx, "<TruncateLogExtendTable> failed to truncate log_extends table: "+err.Error())
		return fmt.Errorf("failed to truncate log_extends table: %v", err)
	}

	logger.Info(ctx, "<TruncateLogExtendTable> successfully truncated log_extends table")
	return nil
}

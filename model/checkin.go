package model

import "github.com/songquanpeng/one-api/common/helper"

type Checkin struct {
	Id          int   `json:"id"`
	UserId      int   `json:"user_id" gorm:"index"`
	CheckinTime int64 `json:"checkin_time" gorm:"bigint"` // 签到时间
	Quota       int64 `json:"quota"`                      // 获得quota
	TotalDays   int   `json:"total_days" gorm:"-"`        // 累计签到天数
}

func (checkin *Checkin) Insert() error {
	var err error
	err = DB.Create(checkin).Error
	if err != nil {
		return err
	}
	return nil
}

func (checkin *Checkin) Update() error {
	var err error
	err = DB.Save(checkin).Error
	if err != nil {
		return err
	}
	return nil
}

func (checkin *Checkin) Delete() error {
	var err error
	err = DB.Delete(checkin).Error
	if err != nil {
		return err
	}
	return nil
}

func (checkin *Checkin) Get() error {
	var err error
	err = DB.First(checkin).Error
	if err != nil {
		return err
	}
	return nil
}

// GetCheckinByUserId 根据用户id获取签到记录
func GetCheckinByUserId(userId int) (*Checkin, error) {
	var checkin *Checkin
	var err error
	err = DB.Where("user_id = ?", userId).First(&checkin).Error
	if err != nil {
		return nil, err
	}
	return checkin, nil
}

// CheckUserTodayCheckin 判断当前用户当日没有签到记录
func CheckUserTodayCheckin(userId int) (bool, Checkin, error) {
	var checkin Checkin
	err := DB.First(&checkin, "user_id = ? AND checkin_time > ?", userId, helper.GetTodayZeroUnixTime()).Error
	if err != nil {
		return false, checkin, err
	}
	return true, checkin, nil
}

// 获取当前用户签到总次数
func GetCheckinTotalDays(userId int) (int64, error) {
	var count int64
	err := DB.Model(&Checkin{}).Where("user_id = ?", userId).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

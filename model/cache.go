package model

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/common/random"
	"gorm.io/gorm"
)

var (
	TokenCacheSeconds           = config.SyncFrequency
	GroupCacheSeconds           = config.SyncFrequency
	UserId2GroupCacheSeconds    = config.SyncFrequency
	UserId2QuotaCacheSeconds    = config.SyncFrequency
	UserId2StatusCacheSeconds   = config.SyncFrequency
	GroupModelsCacheSeconds     = config.SyncFrequency
	PackagePlanInstCacheSeconds = config.SyncFrequency
)

const (
	// 添加用户流式配置缓存过期时间（秒）
	UserStreamConfigCacheSeconds = 300 // 5分钟
	// 用户通知设置缓存过期时间（秒）- 设置较长的缓存时间，因为通知设置不经常变更
	UserNotificationSettingCacheSeconds = 1800 // 30分钟
	// 用户代理商服务器地址缓存过期时间（秒）- 设置较长的缓存时间，因为代理商信息不经常变更
	UserAgencyServerAddressCacheSeconds = 3600 // 60分钟
	// 用户信息缓存过期时间（秒）- 设置较短的缓存时间，因为用户信息比较敏感
	UserInfoCacheSeconds = 300 // 5分钟
)

// 修改为使用atomic.Pointer
var group2model2channelsPtr atomic.Pointer[map[string]map[string][]*Channel]
var channelSyncLock sync.RWMutex // 保留但主要用于其他目的，不再用于保护map读取

func init() {
	// 初始化空map
	initialMap := make(map[string]map[string][]*Channel)
	group2model2channelsPtr.Store(&initialMap)
}

// AsyncSetCacheWithLock 通用异步缓存写入方法
// 使用非阻塞锁机制，支持各种类型的缓存写入需求
func AsyncSetCacheWithLock(cacheKey string, value string, expiration time.Duration, lockTimeout time.Duration, description string) {
	if !common.RedisEnabled {
		return
	}

	helper.SafeGoroutine(func() {
		// 创建一个新的context，避免原始context被取消影响异步操作
		asyncCtx := context.Background()

		rdb := common.RDB
		lockKey := fmt.Sprintf("lock:cache_write:%s", cacheKey)

		// 直接使用SETNX尝试获取锁，不等待，立即返回结果
		acquired, err := rdb.SetNX(asyncCtx, lockKey, "1", lockTimeout).Result()
		if err != nil {
			logger.Error(asyncCtx, fmt.Sprintf("异步获取%s写锁失败: %s", description, err.Error()))
			return
		}

		if acquired {
			// 成功获取锁，写入Redis缓存
			defer func() {
				// 释放锁
				rdb.Del(asyncCtx, lockKey)
			}()

			err := common.RedisSet(cacheKey, value, expiration)
			if err != nil {
				logger.Error(asyncCtx, fmt.Sprintf("异步Redis写入%s缓存失败: %s", description, err.Error()))
			} else {
				logger.Infof(asyncCtx, "%s缓存异步写入成功", description)
			}
		} else {
			// 获取锁失败，直接跳过，不等待不重试
			logger.Infof(asyncCtx, "%s异步缓存写锁已被占用，跳过缓存写入", description)
		}
	})
}

// AsyncSetUserQuotaToRedis 异步写入用户余额到Redis缓存
// 使用Lua脚本确保只在key不存在时才写入，避免并发覆盖
func AsyncSetUserQuotaToRedis(userId int, quota int64) {
	if !common.RedisEnabled {
		return
	}

	helper.SafeGoroutine(func() {
		// 创建一个新的context，避免原始context被取消影响异步操作
		asyncCtx := context.Background()

		cacheKey := fmt.Sprintf("user_quota:%d", userId)
		description := fmt.Sprintf("用户 %d 余额: %d", userId, quota)
		expiration := time.Duration(UserId2QuotaCacheSeconds) * time.Second

		// 使用公共方法：只在key不存在时才写入
		written, err := common.RedisSetIfNotExists(cacheKey, fmt.Sprintf("%d", quota), expiration)

		if err != nil {
			logger.Error(asyncCtx, fmt.Sprintf("异步Redis写入%s缓存失败: %s", description, err.Error()))
		} else if written {
			logger.Infof(asyncCtx, "%s缓存异步写入成功", description)
		} else {
			logger.Infof(asyncCtx, "%s跳过写入，缓存已存在", description)
		}
	})
}

// AsyncSetUserGroupToRedis 异步写入用户组到Redis缓存
func AsyncSetUserGroupToRedis(userId int, group string) {
	cacheKey := fmt.Sprintf("user_group:%d", userId)
	value := group
	expiration := time.Duration(UserId2GroupCacheSeconds) * time.Second
	lockTimeout := 5 * time.Second
	description := fmt.Sprintf("用户 %d 分组: %s", userId, group)

	AsyncSetCacheWithLock(cacheKey, value, expiration, lockTimeout, description)
}

// AsyncSetUserEnabledToRedis 异步写入用户启用状态到Redis缓存
func AsyncSetUserEnabledToRedis(userId int, enabled bool) {
	cacheKey := fmt.Sprintf("user_enabled:%d", userId)
	value := "0"
	if enabled {
		value = "1"
	}
	expiration := time.Duration(UserId2StatusCacheSeconds) * time.Second
	lockTimeout := 5 * time.Second
	description := fmt.Sprintf("用户 %d 启用状态: %v", userId, enabled)

	AsyncSetCacheWithLock(cacheKey, value, expiration, lockTimeout, description)
}

// AsyncSetTokenToRedis 异步写入令牌到Redis缓存
func AsyncSetTokenToRedis(key string, tokenJSON string) {
	cacheKey := fmt.Sprintf("token:%s", key)
	value := tokenJSON
	expiration := time.Duration(TokenCacheSeconds) * time.Second
	lockTimeout := 5 * time.Second
	description := fmt.Sprintf("令牌: %s", key)

	AsyncSetCacheWithLock(cacheKey, value, expiration, lockTimeout, description)
}

// AsyncSetGroupModelsToRedis 异步写入分组模型到Redis缓存
func AsyncSetGroupModelsToRedis(group string, models []string) {
	cacheKey := fmt.Sprintf("group_models:%s", group)
	value := strings.Join(models, ",")
	expiration := time.Duration(GroupModelsCacheSeconds) * time.Second
	lockTimeout := 5 * time.Second
	description := fmt.Sprintf("分组 %s 模型列表", group)

	AsyncSetCacheWithLock(cacheKey, value, expiration, lockTimeout, description)
}

// AsyncSetUserModelRatioToRedis 异步写入用户模型比例到Redis缓存
func AsyncSetUserModelRatioToRedis(userId int, ratioJSON string) {
	cacheKey := fmt.Sprintf("user_model_ratio:%d", userId)
	value := ratioJSON
	expiration := time.Duration(UserId2QuotaCacheSeconds) * time.Second
	lockTimeout := 5 * time.Second
	description := fmt.Sprintf("用户 %d 模型比例", userId)

	AsyncSetCacheWithLock(cacheKey, value, expiration, lockTimeout, description)
}

// AsyncSetJSONToRedis 异步写入JSON数据到Redis缓存（通用JSON缓存）
func AsyncSetJSONToRedis(cacheKey string, data interface{}, expiration time.Duration, description string) {
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		logger.SysError(fmt.Sprintf("序列化%s数据失败: %s", description, err.Error()))
		return
	}

	lockTimeout := 5 * time.Second
	AsyncSetCacheWithLock(cacheKey, string(jsonBytes), expiration, lockTimeout, description)
}

func CacheGetTokenByKey(key string) (*Token, *TokenExtend, error) {
	keyCol := "`key`"
	if common.UsingPostgreSQL {
		keyCol = `"key"`
	}
	var token Token
	var tokenExtend TokenExtend
	if !common.RedisEnabled {
		err := DB.Where(keyCol+" = ?", key).First(&token).Error
		if err != nil {
			return nil, nil, err
		}
		_ = DB.Where("token_id = ?", token.Id).First(&tokenExtend).Error
		return &token, &tokenExtend, nil
	}
	tokenObjectString, tokenErr := common.RedisGet(fmt.Sprintf("token:%s", key))
	tokenExtendObjectString, _ := common.RedisGet(fmt.Sprintf("token_extend:%s", key))
	if tokenErr != nil {
		err := DB.Where(keyCol+" = ?", key).First(&token).Error
		if err != nil {
			return nil, nil, err
		}
		jsonBytes, err := json.Marshal(token)
		if err != nil {
			return nil, nil, err
		}
		_ = DB.Where("token_id = ?", token.Id).First(&tokenExtend).Error
		jsonBytes2, err := json.Marshal(tokenExtend)
		if err != nil {
			return nil, nil, err
		}
		err = common.RedisSet(fmt.Sprintf("token:%s", key), string(jsonBytes), time.Duration(TokenCacheSeconds)*time.Second)
		if err != nil {
			logger.SysError("Redis set token error: " + err.Error())
		}
		err = common.RedisSet(fmt.Sprintf("token_extend:%s", key), string(jsonBytes2), time.Duration(TokenCacheSeconds)*time.Second)
		if err != nil {
			logger.SysError("Redis set token_extend error: " + err.Error())
		}
		checkErr := checkAndActiveOnFirstUse(&token, &tokenExtend, key)
		if checkErr != nil {
			return nil, nil, checkErr
		}
		return &token, &tokenExtend, nil
	}
	err := json.Unmarshal([]byte(tokenObjectString), &token)
	_ = json.Unmarshal([]byte(tokenExtendObjectString), &tokenExtend)
	checkErr := checkAndActiveOnFirstUse(&token, &tokenExtend, key)
	if checkErr != nil {
		return nil, nil, checkErr
	}
	return &token, &tokenExtend, err
}

// CacheGetTokenById 通过Token ID获取Token信息（带缓存）
func CacheGetTokenById(id int) (*Token, error) {
	if !common.RedisEnabled {
		return GetTokenById(id)
	}

	cacheKey := fmt.Sprintf("token_by_id:%d", id)
	tokenJSON, err := common.RedisGet(cacheKey)
	if err == nil {
		// 缓存命中，反序列化Token信息
		var token Token
		err = json.Unmarshal([]byte(tokenJSON), &token)
		if err == nil {
			return &token, nil
		}
	}

	// 缓存未命中，从数据库获取
	token, err := GetTokenById(id)
	if err != nil {
		return nil, err
	}

	// 异步写入缓存
	AsyncSetTokenByIdToRedis(id, token)

	return token, nil
}

// AsyncSetTokenByIdToRedis 异步写入Token信息到Redis缓存（基于ID）
func AsyncSetTokenByIdToRedis(id int, token *Token) {
	cacheKey := fmt.Sprintf("token_by_id:%d", id)
	expiration := time.Duration(TokenCacheSeconds) * time.Second
	lockTimeout := 5 * time.Second
	description := fmt.Sprintf("Token ID %d 信息", id)

	helper.SafeGoroutine(func() {
		tokenJSON, err := json.Marshal(token)
		if err != nil {
			logger.SysError(fmt.Sprintf("序列化Token信息失败，ID: %d, 错误: %s", id, err.Error()))
			return
		}

		AsyncSetCacheWithLock(cacheKey, string(tokenJSON), expiration, lockTimeout, description)
	})
}

// DeleteTokenByIdCache 删除Token ID缓存
func DeleteTokenByIdCache(id int) {
	if !common.RedisEnabled {
		return
	}

	helper.SafeGoroutine(func() {
		cacheKey := fmt.Sprintf("token_by_id:%d", id)
		err := common.RedisDel(cacheKey)
		if err != nil {
			logger.SysError(fmt.Sprintf("删除Token ID缓存失败，ID: %d, 错误: %s", id, err.Error()))
		}
	})
}

func checkAndActiveOnFirstUse(token *Token, tokenExtend *TokenExtend, key string) error {
	// 判断tokenExtend当中是否配置了ActivateOnFirstUse并且DurationActivated不是已激活状态
	if tokenExtend.ActivateOnFirstUse && !tokenExtend.DurationActivated {
		// 修改激活状态
		tokenExtend.DurationActivated = true
		token.ExpiredTime = helper.GetTimestamp() + int64(tokenExtend.ValidDuration*86400)
		token.Status = 1
		err := DB.Save(&tokenExtend).Error
		if err != nil {
			return err
		}
		err = DB.Save(&token).Error
		if err != nil {
			return err
		}
		if common.RedisEnabled {
			// 写入redis
			jsonBytes, err := json.Marshal(token)
			if err != nil {
				return err
			}
			err = common.RedisSet(fmt.Sprintf("token:%s", key), string(jsonBytes), time.Duration(TokenCacheSeconds)*time.Second)
			if err != nil {
				logger.SysError("ActivateOnFirstUse Redis set token error: " + err.Error())
			}
			jsonBytes2, err := json.Marshal(tokenExtend)
			if err != nil {
				return err
			}
			err = common.RedisSet(fmt.Sprintf("token_extend:%s", key), string(jsonBytes2), time.Duration(TokenCacheSeconds)*time.Second)
			if err != nil {
				logger.SysError("ActivateOnFirstUse Redis set token_extend error: " + err.Error())
			}
		}
	}
	return nil
}

func CacheGetUserGroup(id int) (group string, err error) {
	if !common.RedisEnabled {
		return GetUserGroup(id)
	}
	group, err = common.RedisGet(fmt.Sprintf("user_group:%d", id))
	if err != nil {
		group, err = GetUserGroup(id)
		if err != nil {
			return "", err
		}
		// 使用异步写入，不阻塞响应
		AsyncSetUserGroupToRedis(id, group)
	}
	return group, err
}

func fetchAndUpdateUserQuota(ctx context.Context, id int) (quota int64, err error) {
	// 从数据库获取用户余额（不加锁，允许所有线程并发查询）
	quota, err = GetUserQuota(id)
	if err != nil {
		return 0, err
	}

	// 如果开启了批量更新且启用了考虑内存中余额的功能，需要从内存中获取未提交的增量
	if config.BatchUpdateEnabled && config.BatchUpdateConsiderMemoryQuotaEnabled {
		// 获取用户在批量更新中未提交的余额变动
		// 通过锁保护的方式获取用户在BatchUpdateTypeUserQuota中的未提交余额
		batchUpdateLocks[BatchUpdateTypeUserQuota].Lock()
		pendingQuota, exists := batchUpdateStores[BatchUpdateTypeUserQuota][id]
		batchUpdateLocks[BatchUpdateTypeUserQuota].Unlock()

		if exists {
			// 将未提交的变动加到数据库查询的余额中
			quota += pendingQuota
			logger.Infof(ctx, "用户 %d 存在未提交的余额变动: %d, 调整后余额: %d", id, pendingQuota, quota)
		}
	}

	// 异步写入Redis缓存，不阻塞用户响应
	if common.RedisEnabled {
		AsyncSetUserQuotaToRedis(id, quota)
	}

	// 立即返回数据库余额，不等待缓存写入完成
	return quota, nil
}

func CacheGetUserPackagePlanAllGroups(id int) (groups []string, err error) {
	if !common.RedisEnabled || !config.PackagePlanCacheEnabled {
		return GetUserPackagePlanGroup(id)
	}
	groupsStr, err := common.RedisGet(fmt.Sprintf("user_package_plan_all_groups:%d", id))
	if err != nil {
		groups, err = GetUserPackagePlanGroup(id)
		if err != nil {
			return nil, err
		}
		// 将planGroup转字符存入redis
		jsonBytes, err := json.Marshal(groups)
		if err != nil {
			return nil, err
		}
		err = common.RedisSet(fmt.Sprintf("user_package_plan_all_groups:%d", id), string(jsonBytes), time.Duration(PackagePlanInstCacheSeconds)*time.Second)
		if err != nil {
			logger.SysError("Redis set user_package_plan_all_groups error: " + err.Error())
		}

	}
	err = json.Unmarshal([]byte(groupsStr), &groups)
	return groups, err
}

// CacheGetUserActivePackagePlans 获取用户名下所有有效套餐
func CacheGetUserActivePackagePlans(userId int) (plans []PackagePlanInstance, err error) {
	if !common.RedisEnabled || !config.PackagePlanCacheEnabled {
		return GetUserActivePackagePlans(userId)
	}
	packagePlanInstanceList, err := common.RedisGetValuesWithPrefix[PackagePlanInstance](fmt.Sprintf("package_plan_inst:%d:", userId))
	if err != nil {
		packagePlanInstanceList, err = GetUserActivePackagePlans(userId)
		if err != nil {
			return nil, err
		}
		data := make(map[string]interface{})
		for _, plan := range packagePlanInstanceList {
			jsonBytes, err := json.Marshal(plan)
			if err != nil {
				return nil, err
			}
			data[fmt.Sprintf("package_plan_inst:%d:%d", userId, plan.Id)] = jsonBytes
		}
		if len(data) > 0 {
			err = common.RedisMSet(data)
			if err != nil {
				logger.SysError("Redis set package_plan_inst error: " + err.Error())
			}
		}
	}
	return packagePlanInstanceList, err
}

// CacheGetPackagePlanInstanceById 获取套餐实例
func CacheGetPackagePlanInstanceById(userId int, id int) (plan *PackagePlanInstance, err error) {
	if !common.RedisEnabled || !config.PackagePlanCacheEnabled {
		return GetPackagePlanInstanceById(id)
	}
	planStr, err := common.RedisGet(fmt.Sprintf("package_plan_inst:%d:%d", userId, id))
	if err != nil {
		plan, err = GetPackagePlanInstanceById(id)
		if err != nil {
			return nil, err
		}
		jsonBytes, err := json.Marshal(plan)
		if err != nil {
			return nil, err
		}
		err = common.RedisSet(fmt.Sprintf("package_plan_inst:%d:%d", userId, id), string(jsonBytes), time.Duration(PackagePlanInstCacheSeconds)*time.Second)
		if err != nil {
			logger.SysError("Redis set package_plan_inst error: " + err.Error())
		}
		return plan, nil
	}
	err = json.Unmarshal([]byte(planStr), &plan)
	return plan, err
}

func CacheGetFirstUserPackagePlanGroup(id int) (groups string, err error) {
	if !common.RedisEnabled || !config.PackagePlanCacheEnabled {
		return GetFirstUserPackagePlanGroup(id)
	}
	groups, err = common.RedisGet(fmt.Sprintf("user_package_plan_group:%d", id))
	if err != nil {
		groups, err = GetFirstUserPackagePlanGroup(id)
		if err != nil {
			return "", err
		}
		err = common.RedisSet(fmt.Sprintf("user_package_plan_group:%d", id), groups, time.Duration(PackagePlanInstCacheSeconds)*time.Second)
		if err != nil {
			logger.SysError("Redis set user_package_plan_group error: " + err.Error())
		}
	}
	return groups, err
}

func CacheGetUserQuota(ctx context.Context, id int) (quota int64, err error) {
	if !common.RedisEnabled {
		return GetUserQuota(id)
	}
	quotaString, err := common.RedisGetAndExtendExpiryUsingLua(fmt.Sprintf("user_quota:%d", id), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if err != nil {
		return fetchAndUpdateUserQuota(ctx, id)
	}
	quota, err = strconv.ParseInt(quotaString, 10, 64)
	if err != nil {
		return 0, nil
	}
	if quota <= config.PreConsumedQuota { // when user's quota is less than pre-consumed quota, we need to fetch from db
		logger.Infof(ctx, "user %d's cached quota is too low: %d, refreshing from db", quota, id)
		return fetchAndUpdateUserQuota(ctx, id)
	}
	quota, err = strconv.ParseInt(quotaString, 10, 64)
	if config.QuotaExpireEnabled {
		// 开启了用户余额过期时间
		quotaExpireTimeString, err := common.RedisGetAndExtendExpiryUsingLua(fmt.Sprintf("quota_expire_time:%d", id), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
		if err != nil {
			_, quotaExpireTime, err := GetUserQuotaAndExpireTime(id)
			if err != nil {
				return 0, err
			}
			err = CacheUpdateUserQuotaExpireTime(id, quotaExpireTime)
			if err != nil {
				return 0, err
			}
			quotaExpireTimeString = fmt.Sprintf("%d", quotaExpireTime)
		}
		quotaExpireTime, err := strconv.Atoi(quotaExpireTimeString)
		if err != nil {
			return 0, err
		}
		if quotaExpireTime != 0 && int64(quotaExpireTime) < helper.GetTimestamp() {
			return 0, nil
		}
	}
	return quota, err
}

func CacheGetUserQuotaAndExpireTime(ctx context.Context, id int) (quota int64, expireTime int64, err error) {
	if !common.RedisEnabled {
		return GetUserQuotaAndExpireTime(id)
	}
	quotaString, err := common.RedisGetAndExtendExpiryUsingLua(fmt.Sprintf("user_quota:%d", id), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	quotaExpireTimeString, expireTimeErr := common.RedisGetAndExtendExpiryUsingLua(fmt.Sprintf("quota_expire_time:%d", id), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if err == nil && expireTimeErr == nil {
		quota, err = strconv.ParseInt(quotaString, 10, 64)
		expireTime, err = strconv.ParseInt(quotaExpireTimeString, 10, 64)
		return quota, expireTime, err
	}
	quota, quotaExpireTime, getUserQuotaAndExpireTimeErr := GetUserQuotaAndExpireTime(id)
	if getUserQuotaAndExpireTimeErr != nil {
		return 0, 0, getUserQuotaAndExpireTimeErr
	}
	if err != nil {
		AsyncSetUserQuotaToRedis(id, quota)
	}
	if expireTimeErr != nil {
		err = common.RedisSet(fmt.Sprintf("quota_expire_time:%d", id), fmt.Sprintf("%d", quotaExpireTime), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
		if err != nil {
			logger.SysError("Redis set quota_expire_time error: " + err.Error())
		}
	}
	return quota, quotaExpireTime, nil
}

func CacheUpdateUserQuota(ctx context.Context, id int) error {
	if !common.RedisEnabled {
		return nil
	}
	quota, err := CacheGetUserQuota(ctx, id)
	if err != nil {
		return err
	}
	// 使用异步写入，不阻塞调用方
	AsyncSetUserQuotaToRedis(id, quota)
	return nil
}

func CacheUpdateUserQuotaExpireTime(id int, quotaExpireTime int64) error {
	if !common.RedisEnabled {
		return nil
	}
	err := common.RedisSet(fmt.Sprintf("quota_expire_time:%d", id), fmt.Sprintf("%d", quotaExpireTime), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	return err
}

func CacheDecreaseUserQuota(id int, quota int64) error {
	if !common.RedisEnabled {
		return nil
	}
	// done 这里有问题,在开启redis的时候如果恰好运行到这一行,缓存刚好过期,用户余额扣减就会生成一个负数的key,需要确保一致性
	err := common.RedisDecreaseWithLuaCheckExists(fmt.Sprintf("user_quota:%d", id), quota)
	return err
}

func CacheIsUserEnabled(userId int) (bool, error) {
	if !common.RedisEnabled {
		return IsUserEnabled(userId)
	}
	enabled, err := common.RedisGet(fmt.Sprintf("user_enabled:%d", userId))
	if err == nil {
		return enabled == "1", nil
	}

	userEnabled, err := IsUserEnabled(userId)
	if err != nil {
		return false, err
	}
	// 使用异步写入，不阻塞响应
	AsyncSetUserEnabledToRedis(userId, userEnabled)
	return userEnabled, nil
}

func CacheGetGroupModels(ctx context.Context, group string) ([]string, error) {
	if !common.RedisEnabled {
		return GetGroupModels(ctx, group)
	}
	modelsStr, err := common.RedisGet(fmt.Sprintf("group_models:%s", group))
	if err == nil {
		return strings.Split(modelsStr, ","), nil
	}
	models, err := GetGroupModels(ctx, group)
	if err != nil {
		return nil, err
	}
	// 使用异步写入，不阻塞响应
	AsyncSetGroupModelsToRedis(group, models)
	return models, nil
}

func CacheGetUserModelRatio(id int, modelName string) (ratio float64, ok bool, ratioJsonStr string, err error) {
	if !common.RedisEnabled {
		return GetModelRatio(id, modelName)
	}
	ratioString, err := common.RedisGetAndExtendExpiryUsingLua(fmt.Sprintf("user_model_ratio:%d", id), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if err != nil {
		ratio, ok, ratioJsonStr, err = GetModelRatio(id, modelName)
		// 使用异步写入，不阻塞响应
		AsyncSetUserModelRatioToRedis(id, ratioJsonStr)
		return ratio, ok, ratioString, err
	}
	var modelRatio map[string]float64
	err = json.Unmarshal([]byte(ratioString), &modelRatio)
	if err != nil {
		return 30, false, ratioString, err
	}
	if ratio, ok = modelRatio[modelName]; ok {
		return ratio, true, ratioString, nil
	}
	return 30, false, ratioJsonStr, err
}

func CacheGetUserModelFixedPrice(id int, modelName string) (price float64, ok bool, priceJsonStr string, err error) {
	if !common.RedisEnabled {
		return GetModelFixedPrice(id, modelName)
	}
	priceString, err := common.RedisGetAndExtendExpiryUsingLua(fmt.Sprintf("user_model_fixed_price:%d", id), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if err != nil {
		price, ok, priceJsonStr, err = GetModelFixedPrice(id, modelName)
		// 添加缓存
		redisErr := common.RedisSet(fmt.Sprintf("user_model_fixed_price:%d", id), priceJsonStr, time.Duration(UserId2QuotaCacheSeconds)*time.Second)
		if redisErr != nil {
			logger.SysError("Redis set user model fixed price error: " + redisErr.Error())
		}
		return price, ok, priceString, nil
	}
	var modelFixedPrice map[string]float64
	err = json.Unmarshal([]byte(priceString), &modelFixedPrice)
	if err != nil {
		return 1, false, priceString, err
	}
	if price, ok = modelFixedPrice[modelName]; ok {
		return price, true, priceString, nil
	}
	return 1, false, priceString, err
}

func CacheGetUserCompletionRatio(id int, modelName string) (ratio float64, ok bool, ratioJsonStr string, err error) {
	if !common.RedisEnabled {
		return GetCompletionRatio(id, modelName)
	}
	completionRatioStr, err := common.RedisGetAndExtendExpiryUsingLua(fmt.Sprintf("user_completion_ratio:%d", id), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if err != nil {
		ratio, ok, ratioJsonStr, err = GetCompletionRatio(id, modelName)
		redisErr := common.RedisSet(fmt.Sprintf("user_completion_ratio:%d", id), ratioJsonStr, time.Duration(UserId2QuotaCacheSeconds)*time.Second)
		if redisErr != nil {
			logger.SysError("Redis set user completion ratio error: " + redisErr.Error())
		}
		return ratio, ok, completionRatioStr, nil
	}
	var completionRatio map[string]float64
	err = json.Unmarshal([]byte(completionRatioStr), &completionRatio)
	if err == nil {
		if ratio, ok = completionRatio[modelName]; ok {
			return ratio, true, completionRatioStr, nil
		}
	}
	return 1, false, completionRatioStr, err
}

func CacheGetUserNewTikTokenBilling(id int) (newTikTokenBilling int, err error) {
	if !common.RedisEnabled {
		return GetUserNewTikTokenBillingByUserId(id)
	}
	newTikTokenBillingStr, err := common.RedisGetAndExtendExpiryUsingLua(fmt.Sprintf("user_new_tik_token_billing:%d", id), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if err != nil {
		newTikTokenBilling, err = GetUserNewTikTokenBillingByUserId(id)
		if err != nil {
			return
		}
		redisErr := common.RedisSet(fmt.Sprintf("user_new_tik_token_billing:%d", id), strconv.Itoa(newTikTokenBilling), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
		if redisErr != nil {
			logger.SysError("Redis set user_new_tik_token_billing error: " + redisErr.Error())
		}
		return newTikTokenBilling, nil
	}
	newTikTokenBilling, err = strconv.Atoi(newTikTokenBillingStr)
	return newTikTokenBilling, err
}

// GetNewTikTokenBilling 获取是否为新版TikToken计费
func GetNewTikTokenBilling(userId int) bool {
	userNewTikTokenBilling, err := CacheGetUserNewTikTokenBilling(userId)
	if err != nil {
		logger.SysDebug("error get user new tik token billing: " + err.Error())
		return config.NewTiktokenEnabled
	}
	if userNewTikTokenBilling == 1 {
		return false
	} else if userNewTikTokenBilling == 2 {
		return true
	} else {
		return config.NewTiktokenEnabled
	}
}

func GetCacheUser(userId int) (*User, error) {
	if !common.RedisEnabled {
		return GetUserById(userId, false)
	}
	userInfo, err := common.RedisGet(fmt.Sprintf("user_info:%d", userId))
	if err == nil {
		var user User
		err = json.Unmarshal([]byte(userInfo), &user)
		if err != nil {
			return nil, err
		}
		return &user, nil
	}
	user, err := GetUserById(userId, false)
	if err != nil {
		return nil, err
	}
	// 使用异步JSON写入，不阻塞响应
	cacheKey := fmt.Sprintf("user_info:%d", userId)
	expiration := time.Duration(UserId2StatusCacheSeconds) * time.Second
	description := fmt.Sprintf("用户 %d 信息", userId)
	AsyncSetJSONToRedis(cacheKey, user, expiration, description)
	return user, nil
}

func InitChannelCache() {
	newChannelId2channel := make(map[int]*Channel)
	var channels []*Channel

	// Use GetDB() to support database switching
	db := GetDB()
	if db != nil {
		// NoSQL database - use the database interface
		dbImpl, ok := db.(DatabaseInterface)
		if ok {
			// Get channels with enabled status only
			allChannels, err := dbImpl.GetAllChannels(0, 0, "", true, 0, "", "", "", "", 0, ChannelStatusEnabled, 0, "", "", "", "")
			if err != nil {
				logger.SysError("Failed to get channels from NoSQL database: " + err.Error())
				return
			}
			channels = allChannels
		} else {
			logger.SysError("Database interface type assertion failed in InitChannelCache")
			return
		}
	} else {
		// SQL database - use GORM
		DB.Where("status = ?", ChannelStatusEnabled).Find(&channels)
	}

	for _, channel := range channels {
		newChannelId2channel[channel.Id] = channel
	}

	var abilities []*Ability
	if db != nil {
		// NoSQL database - use the database interface
		dbImpl, ok := db.(DatabaseInterface)
		if ok {
			allAbilities, err := dbImpl.GetAllAbilities(0, 0, "", "", "", 0, false) // Get all abilities, not just enabled ones
			if err != nil {
				logger.SysError("Failed to get abilities from NoSQL database: " + err.Error())
				return
			}
			abilities = allAbilities
		} else {
			logger.SysError("Database interface type assertion failed for abilities in InitChannelCache")
			return
		}
	} else {
		// SQL database - use GORM
		DB.Find(&abilities)
	}

	groups := make(map[string]bool)
	for _, ability := range abilities {
		groups[ability.Group] = true
	}
	newGroup2model2channels := make(map[string]map[string][]*Channel)
	for group := range groups {
		newGroup2model2channels[group] = make(map[string][]*Channel)
	}
	for _, channel := range channels {
		groups := strings.Split(channel.Group, ",")
		for _, group := range groups {
			models := strings.Split(channel.Models, ",")
			for _, model := range models {
				if _, ok := newGroup2model2channels[group][model]; !ok {
					newGroup2model2channels[group][model] = make([]*Channel, 0)
				}
				newGroup2model2channels[group][model] = append(newGroup2model2channels[group][model], channel)
			}
		}
	}

	if !config.GlobalIgnorePriorityEnabled {
		// sort by priority
		for group, model2channels := range newGroup2model2channels {
			for model, channels := range model2channels {
				sort.Slice(channels, func(i, j int) bool {
					return channels[i].GetSort() > channels[j].GetSort()
				})
				newGroup2model2channels[group][model] = channels
			}
		}
	}

	// 原子替换整个map，无需清空操作
	group2model2channelsPtr.Store(&newGroup2model2channels)

	logger.SysLog("channels synced from database")
}

func InitChannelCache2() {
	startTime := time.Now()
	logger.SysLog("Starting channel cache synchronization...")

	newChannelId2channel := make(map[int]*Channel)
	var channels []*Channel

	// Use GetDB() to support database switching
	db := GetNoSQLDB()
	channelSyncStart := time.Now()
	if db != nil {
		// NoSQL database - use the database interface
		dbImpl, ok := db.(DatabaseInterface)
		if ok {
			// Get channels with enabled or partially disabled status
			allChannels, err := dbImpl.GetAllChannels(0, 0, "", true, 0, "", "", "", "", 0, 0, 0, "", "", "", "")
			if err != nil {
				logger.SysError("Failed to get channels from NoSQL database: " + err.Error())
				return
			}

			// Filter channels by status
			for _, channel := range allChannels {
				if channel.Status == common.ChannelStatusEnabled || channel.Status == common.ChannelStatusPartiallyAutoDisabled {
					channels = append(channels, channel)
				}
			}
		} else {
			logger.SysError("Database interface type assertion failed in InitChannelCache2")
			return
		}
	} else {
		// SQL database - use GORM
		DB.Where("status in (?)", []int{common.ChannelStatusEnabled, common.ChannelStatusPartiallyAutoDisabled}).Find(&channels)
	}
	channelSyncDuration := time.Since(channelSyncStart)
	logger.SysLog(fmt.Sprintf("Channel data sync completed in %v, loaded %d channels", channelSyncDuration, len(channels)))

	for _, channel := range channels {
		newChannelId2channel[channel.Id] = channel
	}
	// 构造一个newGroup2model2channelsAbilityMap的Ability的map
	newGroup2model2channelsAbilityMap := make(map[int]map[string]map[string]bool)

	var abilities []*Ability
	abilitySyncStart := time.Now()
	// 查找enabled为true的ability
	if db != nil {
		// NoSQL database - use the database interface
		dbImpl, ok := db.(DatabaseInterface)
		if ok {
			allAbilities, err := dbImpl.GetAllAbilities(0, 0, "", "", "", 0, true)
			if err != nil {
				logger.SysError("Failed to get abilities from NoSQL database: " + err.Error())
				return
			}
			abilities = allAbilities
		} else {
			logger.SysError("Database interface type assertion failed for abilities in InitChannelCache2")
			return
		}
	} else {
		// SQL database - use GORM
		DB.Where("enabled = ?", true).Find(&abilities)
	}
	abilitySyncDuration := time.Since(abilitySyncStart)
	logger.SysLog(fmt.Sprintf("Ability data sync completed in %v, loaded %d abilities", abilitySyncDuration, len(abilities)))

	cacheProcessStart := time.Now()
	groups := make(map[string]bool)
	for _, ability := range abilities {
		groups[ability.Group] = true
		if _, ok := newGroup2model2channelsAbilityMap[ability.ChannelId]; !ok {
			newGroup2model2channelsAbilityMap[ability.ChannelId] = make(map[string]map[string]bool)
		}
		if _, ok := newGroup2model2channelsAbilityMap[ability.ChannelId][ability.Group]; !ok {
			newGroup2model2channelsAbilityMap[ability.ChannelId][ability.Group] = make(map[string]bool)
		}
		newGroup2model2channelsAbilityMap[ability.ChannelId][ability.Group][ability.Model] = true
	}
	newGroup2model2channels := make(map[string]map[string][]*Channel)
	for group := range groups {
		newGroup2model2channels[group] = make(map[string][]*Channel)
	}
	for _, channel := range channels {
		groups := strings.Split(channel.Group, ",")
		for _, group := range groups {
			if _, ok := newGroup2model2channels[group]; !ok {
				newGroup2model2channels[group] = make(map[string][]*Channel)
			}
			models := strings.Split(channel.Models, ",")
			for _, model := range models {
				if _, ok := newGroup2model2channels[group][model]; !ok {
					newGroup2model2channels[group][model] = make([]*Channel, 0)
				}
				if newGroup2model2channelsAbilityMap[channel.Id][group][model] {
					// 这里增加Ability的校验 如果有Ability则加入 没有则不加入
					newGroup2model2channels[group][model] = append(newGroup2model2channels[group][model], channel)
				}
			}
		}
	}

	sortStart := time.Now()
	// 只有在不忽略优先级的情况下才进行排序
	if !config.GlobalIgnorePriorityEnabled {
		// sort by Sort and Weight
		for group, model2channels := range newGroup2model2channels {
			for model, channels := range model2channels {
				sort.Slice(channels, func(i, j int) bool {
					if channels[i].GetSort() == channels[j].GetSort() {
						// 如果忽略权重计算，则可以不比较权重
						if !config.GlobalIgnoreWeightCalculationEnabled {
							return channels[i].GetWeight() > channels[j].GetWeight()
						} else {
							// 排序相同且忽略权重计算时，返回任意结果即可
							return true
						}
					} else {
						return channels[i].GetSort() > channels[j].GetSort()
					}
				})
				newGroup2model2channels[group][model] = channels
			}
		}
	}
	sortDuration := time.Since(sortStart)
	cacheProcessDuration := time.Since(cacheProcessStart)

	// 原子替换整个map，一次操作，非常高效
	group2model2channelsPtr.Store(&newGroup2model2channels)

	totalDuration := time.Since(startTime)
	logger.SysLog(fmt.Sprintf("Channel cache synchronization completed - Total: %v (Channel sync: %v, Ability sync: %v, Cache processing: %v, Sorting: %v)",
		totalDuration, channelSyncDuration, abilitySyncDuration, cacheProcessDuration, sortDuration))
}

// 修改函数使用atomic.Pointer
func GetChannelsByGroupAndModelFromGroup2model2channels(group string, model string) []*Channel {
	// 原子加载当前map
	currentMap := group2model2channelsPtr.Load()

	// 查找精确匹配
	if modelMap, ok := (*currentMap)[group]; ok {
		if channels, ok := modelMap[model]; ok {
			return channels
		}

		// 没找到，尝试通配符匹配
		for key, channels := range modelMap {
			// 将通配符 '*' 转换为正则表达式的 '.*'
			pattern := "^" + strings.Replace(regexp.QuoteMeta(key), "\\*", ".*", -1) + "$"
			matched, err := regexp.MatchString(pattern, model)
			if err != nil {
				logger.SysError("regexp error: " + err.Error())
				return nil
			}
			if matched {
				return channels
			}
		}
	}

	// 没有匹配项，返回默认值
	logger.SysError("channels not found: " + group + " " + model)
	return nil
}

// 修改函数使用原子指针
func RemoveChannelsByGroupAndModelFromGroup2model2channels(group string, model string, channelId int) map[string]map[string][]*Channel {
	// 创建当前map的拷贝
	currentMap := group2model2channelsPtr.Load()
	newMap := make(map[string]map[string][]*Channel)

	// 深拷贝当前map
	for g, modelMap := range *currentMap {
		newMap[g] = make(map[string][]*Channel)
		for m, channels := range modelMap {
			// 只有当我们需要修改的组和模型时才进行深拷贝
			if g == group && (m == model || isModelMatchPattern(m, model)) {
				// 针对这种情况，我们只需为可能修改的切片分配空间
				targetChannels := make([]*Channel, 0, len(channels))
				for _, ch := range channels {
					if ch.Id != channelId {
						targetChannels = append(targetChannels, ch)
					}
				}
				newMap[g][m] = targetChannels
			} else {
				// 对于不需要修改的部分，直接引用原始切片，避免不必要的内存分配
				newMap[g][m] = channels
			}
		}
	}

	// 原子替换
	group2model2channelsPtr.Store(&newMap)
	return newMap
}

// 辅助函数，检查模型名是否匹配模式（包括通配符）
func isModelMatchPattern(pattern string, model string) bool {
	regexPattern := "^" + strings.Replace(regexp.QuoteMeta(pattern), "\\*", ".*", -1) + "$"
	matched, err := regexp.MatchString(regexPattern, model)
	return err == nil && matched
}

// CacheGetChannelExByChannelId
func CacheGetChannelExByChannelId(channelId int) (channelEx *ChannelExtend, err error) {
	if !common.RedisEnabled {
		return GetChannelExtendByChannelId(channelId)
	}
	channelExStr := ""
	channelExStr, err = common.RedisGet(fmt.Sprintf("channel_ex:%d", channelId))
	if err != nil {
		channelEx, err = GetChannelExtendByChannelId(channelId)
		if err != nil {
			return nil, err
		}
		jsonBytes, err := json.Marshal(channelEx)
		if err != nil {
			return nil, err
		}
		channelExStr = string(jsonBytes)
		err = common.RedisSet(fmt.Sprintf("channel_ex:%d", channelId), channelExStr, time.Duration(UserId2GroupCacheSeconds)*time.Second)
		if err != nil {
			logger.SysError("Redis set channel_ex error: " + err.Error())
		}
	}
	if channelExStr == "" {
		return nil, errors.New("channel_ex not found")
	}
	err = json.Unmarshal([]byte(channelExStr), &channelEx)
	if err != nil {
		return nil, err
	}
	return channelEx, nil
}

func CacheGetUserMJSensitiveWordsRefund(userId int) (int, error) {
	if !common.RedisEnabled {
		return GetUserMJSensitiveWordsRefundByUserId(userId)
	}

	refundStr, err := common.RedisGetAndExtendExpiryUsingLua(fmt.Sprintf("user_mj_sensitive_words_refund:%d", userId), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if err == nil {
		refund, _ := strconv.Atoi(refundStr)
		return refund, nil
	}

	refund, err := GetUserMJSensitiveWordsRefundByUserId(userId)
	if err != nil {
		return 0, err
	}

	redisErr := common.RedisSet(fmt.Sprintf("user_mj_sensitive_words_refund:%d", userId), strconv.Itoa(refund), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if redisErr != nil {
		logger.SysError("Redis set user_mj_sensitive_words_refund error: " + redisErr.Error())
	}

	return refund, nil
}

func GetUserMJSensitiveWordsRefundByUserId(userId int) (int, error) {
	var userExtend UserExtend
	err := DB.Select("mj_sensitive_words_refund").Where("user_id = ?", userId).First(&userExtend).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, nil // 用户没有自定义配置，返回0表示使用系统默认值
		}
		return 0, err
	}
	return userExtend.GetMJSensitiveWordsRefund(), nil
}

func ShouldRefundMJSensitiveWordsError(userId int) bool {
	refund, err := CacheGetUserMJSensitiveWordsRefund(userId)
	if err != nil {
		logger.SysError("error get user mj sensitive words refund: " + err.Error())
		return config.MJSensitiveWordsRefundEnabled // 如果出错，使用系统默认配置
	}
	switch refund {
	case 0:
		return config.MJSensitiveWordsRefundEnabled // 使用系统默认配置
	case 1:
		return true // 返还
	case 2:
		return false // 不返还
	default:
		return config.MJSensitiveWordsRefundEnabled // 未知值，使用系统默认配置
	}
}

func CacheGetGroupByName(name string) (*Group, error) {
	if !common.RedisEnabled {
		return GetGroupByName(name)
	}

	groupStr, err := common.RedisGetAndExtendExpiryUsingLua(fmt.Sprintf("group:%s", name), time.Duration(GroupCacheSeconds)*time.Second)
	if err == nil {
		var group Group
		err = json.Unmarshal([]byte(groupStr), &group)
		if err == nil {
			return &group, nil
		}
	}

	group, err := GetGroupByName(name)
	if err != nil {
		return nil, err
	}

	groupJSON, err := json.Marshal(group)
	if err != nil {
		return nil, err
	}

	redisErr := common.RedisSet(fmt.Sprintf("group:%s", name), string(groupJSON), time.Duration(GroupCacheSeconds)*time.Second)
	if redisErr != nil {
		logger.SysError("Redis set group error: " + redisErr.Error())
	}

	return group, nil
}

func CacheGetUserGroupDiscounts(userId int) (map[string]float64, error) {
	if !common.RedisEnabled {
		userExtend, err := GetUserExByUserId(userId)
		if err != nil {
			return nil, err
		}
		return userExtend.GetGroupDiscounts()
	}

	discountsStr, err := common.RedisGetAndExtendExpiryUsingLua(fmt.Sprintf("user_group_discounts:%d", userId), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if err == nil {
		var discounts map[string]float64
		err = json.Unmarshal([]byte(discountsStr), &discounts)
		if err == nil {
			return discounts, nil
		}
	}

	userExtend, err := GetUserExByUserId(userId)
	if err != nil {
		return nil, err
	}
	discounts, err := userExtend.GetGroupDiscounts()
	if err != nil {
		return nil, err
	}

	discountsJSON, err := json.Marshal(discounts)
	if err != nil {
		return nil, err
	}

	redisErr := common.RedisSet(fmt.Sprintf("user_group_discounts:%d", userId), string(discountsJSON), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if redisErr != nil {
		logger.SysError("Redis set user group discounts error: " + redisErr.Error())
	}

	return discounts, nil
}

func CacheUpdateUserGroupDiscounts(userId int, discounts map[string]float64) error {
	if !common.RedisEnabled {
		return nil
	}

	discountsJSON, err := json.Marshal(discounts)
	if err != nil {
		return err
	}

	return common.RedisSet(fmt.Sprintf("user_group_discounts:%d", userId), string(discountsJSON), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
}

func UpdateUserGroupDiscounts(userId int, discounts map[string]float64) error {
	userExtend, err := GetUserExByUserId(userId)
	if err != nil {
		return err
	}

	err = userExtend.SetGroupDiscounts(discounts)
	if err != nil {
		return err
	}

	err = userExtend.Update()
	if err != nil {
		return err
	}

	return CacheUpdateUserGroupDiscounts(userId, discounts)
}

func CacheGetUserLogDetailEnabled(userId int) (int, error) {
	if !common.RedisEnabled {
		return GetUserLogDetailEnabledByUserId(userId)
	}

	logDetailEnabledStr, err := common.RedisGetAndExtendExpiryUsingLua(fmt.Sprintf("user_log_detail_enabled:%d", userId), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if err == nil {
		logDetailEnabled, _ := strconv.Atoi(logDetailEnabledStr)
		return logDetailEnabled, nil
	}

	logDetailEnabled, err := GetUserLogDetailEnabledByUserId(userId)
	if err != nil {
		return 0, err
	}

	redisErr := common.RedisSet(fmt.Sprintf("user_log_detail_enabled:%d", userId), strconv.Itoa(logDetailEnabled), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if redisErr != nil {
		logger.SysError("Redis set user_log_detail_enabled error: " + redisErr.Error())
	}

	return logDetailEnabled, nil
}

// CacheGetUserLogDownstreamErrorEnabled 从缓存获取用户下游错误记录配置
func CacheGetUserLogDownstreamErrorEnabled(userId int) (int, error) {
	if !common.RedisEnabled {
		return GetUserLogDownstreamErrorEnabledByUserId(userId)
	}

	logDownstreamErrorEnabledStr, err := common.RedisGetAndExtendExpiryUsingLua(fmt.Sprintf("user_log_downstream_error_enabled:%d", userId), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if err == nil {
		logDownstreamErrorEnabled, _ := strconv.Atoi(logDownstreamErrorEnabledStr)
		return logDownstreamErrorEnabled, nil
	}

	logDownstreamErrorEnabled, err := GetUserLogDownstreamErrorEnabledByUserId(userId)
	if err != nil {
		return 0, err
	}

	redisErr := common.RedisSet(fmt.Sprintf("user_log_downstream_error_enabled:%d", userId), strconv.Itoa(logDownstreamErrorEnabled), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if redisErr != nil {
		logger.SysError("Redis set user_log_downstream_error_enabled error: " + redisErr.Error())
	}

	return logDownstreamErrorEnabled, nil
}

// CacheGetUserLogUpstreamResponseEnabled 从缓存获取用户上游响应记录配置
func CacheGetUserLogUpstreamResponseEnabled(userId int) (int, error) {
	if !common.RedisEnabled {
		return GetUserLogUpstreamResponseEnabledByUserId(userId)
	}

	logUpstreamResponseEnabledStr, err := common.RedisGetAndExtendExpiryUsingLua(fmt.Sprintf("user_log_upstream_response_enabled:%d", userId), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if err == nil {
		logUpstreamResponseEnabled, _ := strconv.Atoi(logUpstreamResponseEnabledStr)
		return logUpstreamResponseEnabled, nil
	}

	logUpstreamResponseEnabled, err := GetUserLogUpstreamResponseEnabledByUserId(userId)
	if err != nil {
		return 0, err
	}

	redisErr := common.RedisSet(fmt.Sprintf("user_log_upstream_response_enabled:%d", userId), strconv.Itoa(logUpstreamResponseEnabled), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if redisErr != nil {
		logger.SysError("Redis set user_log_upstream_response_enabled error: " + redisErr.Error())
	}

	return logUpstreamResponseEnabled, nil
}

// CacheGetUserLogFullResponseEnabled 从缓存获取用户完整响应记录配置
func CacheGetUserLogFullResponseEnabled(userId int) (int, error) {
	if !common.RedisEnabled {
		return GetUserLogFullResponseEnabledByUserId(userId)
	}

	logFullResponseEnabledStr, err := common.RedisGetAndExtendExpiryUsingLua(fmt.Sprintf("user_log_full_response_enabled:%d", userId), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if err == nil {
		logFullResponseEnabled, _ := strconv.Atoi(logFullResponseEnabledStr)
		return logFullResponseEnabled, nil
	}

	logFullResponseEnabled, err := GetUserLogFullResponseEnabledByUserId(userId)
	if err != nil {
		return 0, err
	}

	redisErr := common.RedisSet(fmt.Sprintf("user_log_full_response_enabled:%d", userId), strconv.Itoa(logFullResponseEnabled), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if redisErr != nil {
		logger.SysError("Redis set user_log_full_response_enabled error: " + redisErr.Error())
	}

	return logFullResponseEnabled, nil
}

// CacheGetUserMaxPromptLogLength 从缓存获取用户最大prompt日志长度配置
func CacheGetUserMaxPromptLogLength(userId int) (int64, error) {
	if !common.RedisEnabled {
		return GetUserMaxPromptLogLengthByUserId(userId)
	}

	maxPromptLogLengthStr, err := common.RedisGetAndExtendExpiryUsingLua(fmt.Sprintf("user_max_prompt_log_length:%d", userId), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if err == nil {
		maxPromptLogLength, _ := strconv.ParseInt(maxPromptLogLengthStr, 10, 64)
		return maxPromptLogLength, nil
	}

	maxPromptLogLength, err := GetUserMaxPromptLogLengthByUserId(userId)
	if err != nil {
		return 0, err
	}

	redisErr := common.RedisSet(fmt.Sprintf("user_max_prompt_log_length:%d", userId), strconv.FormatInt(maxPromptLogLength, 10), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if redisErr != nil {
		logger.SysError("Redis set user_max_prompt_log_length error: " + redisErr.Error())
	}

	return maxPromptLogLength, nil
}

// GetCacheUserStreamConfig 从缓存获取用户流式配置
func GetCacheUserStreamConfig(userId int) (trustUpstream int, forceOption int, forceDownstream int, err error) {
	if !common.RedisEnabled {
		return GetUserStreamConfigByUserId(userId)
	}

	userStreamConfigKey := fmt.Sprintf("user_stream_config:%d", userId)
	userStreamConfigStr, err := common.RedisGet(userStreamConfigKey)
	if err == nil {
		// 缓存命中，解析配置
		var configs []int
		err = json.Unmarshal([]byte(userStreamConfigStr), &configs)
		if err != nil {
			return 0, 0, 0, err
		}
		if len(configs) == 3 {
			return configs[0], configs[1], configs[2], nil
		}
		return 0, 0, 0, errors.New("invalid cache format")
	}

	// 缓存未命中，从数据库获取
	trustUpstream, forceOption, forceDownstream, err = GetUserStreamConfigByUserId(userId)
	if err != nil {
		return 0, 0, 0, err
	}

	// 将配置存入缓存
	configs := []int{trustUpstream, forceOption, forceDownstream}
	jsonBytes, err := json.Marshal(configs)
	if err != nil {
		return trustUpstream, forceOption, forceDownstream, nil // 返回数据但不缓存
	}

	err = common.RedisSet(userStreamConfigKey, string(jsonBytes), time.Duration(UserStreamConfigCacheSeconds)*time.Second)
	if err != nil {
		logger.SysError("Redis set user stream config error: " + err.Error())
	}

	return trustUpstream, forceOption, forceDownstream, nil
}

// UpdateUserStreamConfigCache 更新用户流式配置缓存
func UpdateUserStreamConfigCache(userId int, trustUpstream int, forceOption int, forceDownstream int) error {
	if !common.RedisEnabled {
		return nil
	}

	userStreamConfigKey := fmt.Sprintf("user_stream_config:%d", userId)
	configs := []int{trustUpstream, forceOption, forceDownstream}
	jsonBytes, err := json.Marshal(configs)
	if err != nil {
		return err
	}

	return common.RedisSet(userStreamConfigKey, string(jsonBytes), time.Duration(UserStreamConfigCacheSeconds)*time.Second)
}

// DeleteUserStreamConfigCache 删除用户流式配置缓存
func DeleteUserStreamConfigCache(userId int) error {
	if !common.RedisEnabled {
		return nil
	}

	userStreamConfigKey := fmt.Sprintf("user_stream_config:%d", userId)
	return common.RedisDel(userStreamConfigKey)
}

// CacheGetUserChannelScoreRouting 从缓存获取用户渠道路由配置
func CacheGetUserChannelScoreRouting(userId int) (int, error) {
	if !common.RedisEnabled {
		return GetUserChannelScoreRoutingByUserId(userId)
	}

	routingStr, err := common.RedisGetAndExtendExpiryUsingLua(fmt.Sprintf("user_channel_score_routing:%d", userId), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if err == nil {
		routing, _ := strconv.Atoi(routingStr)
		return routing, nil
	}

	routing, err := GetUserChannelScoreRoutingByUserId(userId)
	if err != nil {
		return 0, err
	}

	redisErr := common.RedisSet(fmt.Sprintf("user_channel_score_routing:%d", userId), strconv.Itoa(routing), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if redisErr != nil {
		logger.SysError("Redis set user_channel_score_routing error: " + redisErr.Error())
	}

	return routing, nil
}

// UpdateUserChannelScoreRoutingCache 更新用户渠道路由配置缓存
func UpdateUserChannelScoreRoutingCache(userId int, routing int) error {
	if !common.RedisEnabled {
		return nil
	}

	return common.RedisSet(fmt.Sprintf("user_channel_score_routing:%d", userId), strconv.Itoa(routing), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
}

// DeleteUserChannelScoreRoutingCache 删除用户渠道路由配置缓存
func DeleteUserChannelScoreRoutingCache(userId int) error {
	if !common.RedisEnabled {
		return nil
	}

	return common.RedisDel(fmt.Sprintf("user_channel_score_routing:%d", userId))
}

// CacheGetUserSay1DirectSuccessMode 从缓存获取用户是否启用吃掉拨测功能配置
func CacheGetUserSay1DirectSuccessMode(userId int) (int, error) {
	if !common.RedisEnabled {
		return GetUserSay1DirectSuccessModeByUserId(userId)
	}

	modeStr, err := common.RedisGetAndExtendExpiryUsingLua(fmt.Sprintf("user_say1_direct_success_mode:%d", userId), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if err == nil {
		mode, _ := strconv.Atoi(modeStr)
		return mode, nil
	}

	mode, err := GetUserSay1DirectSuccessModeByUserId(userId)
	if err != nil {
		return 0, err
	}

	redisErr := common.RedisSet(fmt.Sprintf("user_say1_direct_success_mode:%d", userId), strconv.Itoa(mode), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if redisErr != nil {
		logger.SysError("Redis set user_say1_direct_success_mode error: " + redisErr.Error())
	}

	return mode, nil
}

// UpdateUserSay1DirectSuccessModeCache 更新用户吃掉拨测配置缓存
func UpdateUserSay1DirectSuccessModeCache(userId int, mode int) error {
	if !common.RedisEnabled {
		return nil
	}

	return common.RedisSet(fmt.Sprintf("user_say1_direct_success_mode:%d", userId), strconv.Itoa(mode), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
}

// DeleteUserSay1DirectSuccessModeCache 删除用户吃掉拨测配置缓存
func DeleteUserSay1DirectSuccessModeCache(userId int) error {
	if !common.RedisEnabled {
		return nil
	}

	return common.RedisDel(fmt.Sprintf("user_say1_direct_success_mode:%d", userId))
}

// CacheGetUserClaudeMessageNormalizationEnabled 从缓存获取用户是否启用Claude消息整理功能配置
func CacheGetUserClaudeMessageNormalizationEnabled(userId int) (int, error) {
	if !common.RedisEnabled {
		return GetUserClaudeMessageNormalizationEnabledByUserId(userId)
	}

	enabledStr, err := common.RedisGetAndExtendExpiryUsingLua(fmt.Sprintf("user_claude_message_normalization:%d", userId), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if err == nil {
		enabled, _ := strconv.Atoi(enabledStr)
		return enabled, nil
	}

	enabled, err := GetUserClaudeMessageNormalizationEnabledByUserId(userId)
	if err != nil {
		return 0, err
	}

	redisErr := common.RedisSet(fmt.Sprintf("user_claude_message_normalization:%d", userId), strconv.Itoa(enabled), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if redisErr != nil {
		logger.SysError("Redis set user_claude_message_normalization error: " + redisErr.Error())
	}

	return enabled, nil
}

// UpdateUserClaudeMessageNormalizationCache 更新用户Claude消息整理配置缓存
func UpdateUserClaudeMessageNormalizationCache(userId int, enabled int) error {
	if !common.RedisEnabled {
		return nil
	}

	return common.RedisSet(fmt.Sprintf("user_claude_message_normalization:%d", userId), strconv.Itoa(enabled), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
}

// DeleteUserClaudeMessageNormalizationCache 删除用户Claude消息整理配置缓存
func DeleteUserClaudeMessageNormalizationCache(userId int) error {
	if !common.RedisEnabled {
		return nil
	}

	return common.RedisDel(fmt.Sprintf("user_claude_message_normalization:%d", userId))
}

// CacheGetUserMockOpenAICompleteFormat 从缓存获取用户是否启用模拟OpenAI官方响应格式配置
func CacheGetUserMockOpenAICompleteFormat(userId int) (int, error) {
	if !common.RedisEnabled {
		return GetUserMockOpenAICompleteFormatByUserId(userId)
	}

	modeStr, err := common.RedisGetAndExtendExpiryUsingLua(fmt.Sprintf("user_mock_openai_complete_format:%d", userId), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if err == nil {
		mode, _ := strconv.Atoi(modeStr)
		return mode, nil
	}

	mode, err := GetUserMockOpenAICompleteFormatByUserId(userId)
	if err != nil {
		return 0, err
	}

	redisErr := common.RedisSet(fmt.Sprintf("user_mock_openai_complete_format:%d", userId), strconv.Itoa(mode), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if redisErr != nil {
		logger.SysError("Redis set user_mock_openai_complete_format error: " + redisErr.Error())
	}

	return mode, nil
}

// UpdateUserMockOpenAICompleteFormatCache 更新用户模拟OpenAI官方响应格式配置缓存
func UpdateUserMockOpenAICompleteFormatCache(userId int, mode int) error {
	if !common.RedisEnabled {
		return nil
	}

	return common.RedisSet(fmt.Sprintf("user_mock_openai_complete_format:%d", userId), strconv.Itoa(mode), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
}

// DeleteUserMockOpenAICompleteFormatCache 删除用户模拟OpenAI官方响应格式配置缓存
func DeleteUserMockOpenAICompleteFormatCache(userId int) error {
	if !common.RedisEnabled {
		return nil
	}

	return common.RedisDel(fmt.Sprintf("user_mock_openai_complete_format:%d", userId))
}

// 获取用户额外可见分组设置
func CacheGetUserExtraVisibleGroups(userId int) (string, error) {
	if !common.RedisEnabled {
		// 如果Redis未启用，直接从数据库获取
		var userExtend *UserExtend
		var err error
		// 优化性能,只查询需要的字段
		err = DB.Select("extra_visible_groups,user_id").
			Where("user_id = ?", userId).First(&userExtend).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// 如果记录不存在，返回空字符串
				return "", nil
			}
			return "", err
		}
		if userExtend.ExtraVisibleGroups == nil {
			return "", nil
		}
		return *userExtend.ExtraVisibleGroups, nil
	}

	// 使用Redis缓存
	cacheKey := fmt.Sprintf("user_extra_visible_groups:%d", userId)
	val, err := common.RedisGet(cacheKey)
	if err == nil {
		return val, nil
	}

	// 缓存未命中，从数据库获取
	var userExtend *UserExtend
	err = DB.Select("extra_visible_groups,user_id").
		Where("user_id = ?", userId).First(&userExtend).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果记录不存在，缓存空字符串
			_ = common.RedisSet(cacheKey, "", time.Minute*30)
			return "", nil
		}
		return "", err
	}

	var extraGroups string
	if userExtend.ExtraVisibleGroups != nil {
		extraGroups = *userExtend.ExtraVisibleGroups
	} else {
		extraGroups = ""
	}

	// 设置缓存
	_ = common.RedisSet(cacheKey, extraGroups, time.Hour*24)
	return extraGroups, nil
}

// 更新用户额外可见分组缓存
func UpdateUserExtraVisibleGroupsCache(userId int, extraGroups string) error {
	if !common.RedisEnabled {
		return nil
	}
	cacheKey := fmt.Sprintf("user_extra_visible_groups:%d", userId)
	return common.RedisSet(cacheKey, extraGroups, time.Hour*24)
}

// 删除用户额外可见分组缓存
func DeleteUserExtraVisibleGroupsCache(userId int) error {
	if !common.RedisEnabled {
		return nil
	}
	cacheKey := fmt.Sprintf("user_extra_visible_groups:%d", userId)
	return common.RedisDel(cacheKey)
}

func SyncChannelCache(frequency int) {
	for {
		time.Sleep(time.Duration(frequency) * time.Second)
		logger.SysLog("syncing channels from database")
		InitChannelCache2()
	}
}

func CacheGetRandomSatisfiedChannel(group string, model string, ignoreFirstPriority bool, tokenBillingType int, inputHasFunctionCall bool, inputHasImage bool, excludeIds []int, isV1MessagesPath bool, userId int) (*Channel, error) {
	if !config.MemoryCacheEnabled {
		return GetRandomSatisfiedChannel(group, model, tokenBillingType, inputHasFunctionCall, inputHasImage, excludeIds, ignoreFirstPriority, isV1MessagesPath)
	}

	// 直接调用，不需要加锁
	originalChannels := GetChannelsByGroupAndModelFromGroup2model2channels(group, model)

	if len(originalChannels) == 0 {
		return nil, errors.New("channel not found")
	}

	// 拷贝一份,通过tokenBillingType进行过滤
	channels := make([]*Channel, 0)
	if tokenBillingType == common.BillingTypeMixed || config.GlobalIgnoreBillingTypeFilteringEnabled {
		// 混合模式或全局忽略计费方式筛选时不过滤
		channels = originalChannels
	} else {
		for _, channel := range originalChannels {
			if channel.BillingType == tokenBillingType {
				channels = append(channels, channel)
			}
		}
	}

	// 需要排除excludeIds
	if excludeIds != nil && len(excludeIds) > 0 {
		excludeIdsMap := make(map[int]bool)
		for _, excludeId := range excludeIds {
			excludeIdsMap[excludeId] = true
		}
		newChannels := make([]*Channel, 0)
		for _, channel := range channels {
			if _, ok := excludeIdsMap[channel.Id]; !ok {
				newChannels = append(newChannels, channel)
			}
		}
		channels = newChannels
	}

	// 如果入参中包含function call,则过滤掉没有function call的channel
	if inputHasFunctionCall && !config.GlobalIgnoreFunctionCallFilteringEnabled {
		newChannels := make([]*Channel, 0)
		for _, channel := range channels {
			if channel.GetFunctionCallEnabled() {
				newChannels = append(newChannels, channel)
			}
		}
		channels = newChannels
	}

	// 如果入参中包含image,则过滤掉没有image的channel
	if inputHasImage && !config.GlobalIgnoreImageSupportFilteringEnabled {
		newChannels := make([]*Channel, 0)
		for _, channel := range channels {
			if channel.GetImageSupported() {
				newChannels = append(newChannels, channel)
			}
		}
		channels = newChannels
	}

	// 如果是 /v1/messages 路径，过滤掉不支持的渠道
	if isV1MessagesPath {
		newChannels := make([]*Channel, 0)
		for _, channel := range channels {
			if channel.GetV1MessagesSupported() {
				newChannels = append(newChannels, channel)
			}
		}
		channels = newChannels
	}

	if len(channels) == 0 {
		return nil, errors.New("channel not found")
	}

	// 如果启用了全局忽略优先级，直接随机选择一个渠道
	if config.GlobalIgnorePriorityEnabled {
		// 当启用全局忽略优先级时，自动也忽略权重计算
		randIdx := rand.Intn(len(channels))
		return channels[randIdx], nil
	}

	// 从缓存获取用户的渠道路由配置
	channelScoreRouting, err := CacheGetUserChannelScoreRouting(userId)
	if err != nil {
		// 如果出错，使用系统默认配置
		logger.SysError("获取用户渠道路由配置失败: " + err.Error())
		channelScoreRouting = 0
	}

	// 检查用户是否有特定的渠道路由设置
	useScoreRouting := config.ChannelScoreRoutingEnabled // 默认使用系统配置
	if channelScoreRouting == 1 {
		useScoreRouting = true // 用户设置为启用
	} else if channelScoreRouting == 2 {
		useScoreRouting = false // 用户设置为禁用
	}

	// 如果启用基于渠道得分的路由，使用渠道选择器
	if useScoreRouting {
		// 提取渠道ID和对应的渠道对象
		channelIDs := make([]int, len(channels))
		channelMap := make(map[int]*Channel)
		for i, channel := range channels {
			channelIDs[i] = channel.Id
			channelMap[channel.Id] = channel
		}

		// 构建渠道评分映射
		scoreMap := make(map[int]float64)
		for _, channelID := range channelIDs {
			// 只有在启用渠道指标统计时才获取评分数据
			if config.ChannelMetricsEnabled {
				summary, err := GetChannelMetricsSummary(channelID, model, "hour")
				if err == nil && summary.TotalRequests > 0 {
					scoreMap[channelID] = summary.Score
				} else {
					// 如果没有最近数据，尝试使用更长时间范围的数据
					summary, err = GetChannelMetricsSummary(channelID, model, "day")
					if err == nil && summary.TotalRequests > 0 {
						scoreMap[channelID] = summary.Score
					} else {
						// 默认中等评分
						scoreMap[channelID] = 0.5
					}
				}
			} else {
				// 如果未启用指标统计，所有渠道使用相同的默认评分
				scoreMap[channelID] = 0.5
			}
		}

		// 按评分、权重和优先级选择渠道
		selectedChannelID := selectChannelByScoreAndWeight(channels, scoreMap)
		if selectedChannelID > 0 {
			logger.SysLog(fmt.Sprintf("智能路由选择渠道 ID: %d, 模型: %s", selectedChannelID, model))
			return channelMap[selectedChannelID], nil
		}
	}

	// 原有的选择逻辑
	endIdx := len(channels)
	// choose by sort
	firstChannel := channels[0]
	if firstChannel.GetSort() > 0 {
		for i := range channels {
			if channels[i].GetSort() != firstChannel.GetSort() {
				endIdx = i
				break
			}
		}
	}
	// 只选择相同优先级(sort)的渠道
	weightChannels := make([]*Channel, endIdx)
	copy(weightChannels, channels[:endIdx])

	// 如果启用了全局忽略权重计算，直接随机选择一个渠道
	if config.GlobalIgnoreWeightCalculationEnabled {
		randIdx := rand.Intn(len(weightChannels))
		return weightChannels[randIdx], nil
	}

	// 平滑系数，避免权重为0的渠道完全没有机会被选中
	smoothingFactor := 10
	// 计算总权重
	totalWeight := 0
	for _, channel := range weightChannels {
		totalWeight += int(channel.GetWeight()) + smoothingFactor
	}

	// 生成随机值
	randomWeight := rand.Intn(totalWeight)

	// 根据权重选择渠道
	for _, channel := range weightChannels {
		randomWeight -= int(channel.GetWeight()) + smoothingFactor
		if randomWeight < 0 {
			return channel, nil
		}
	}

	// 兜底：如果没有选中任何渠道，随机选择一个
	idx := rand.Intn(endIdx)
	if ignoreFirstPriority {
		if endIdx < len(channels) { // which means there are more than one priority
			idx = random.RandRange(endIdx, len(channels))
		}
	}
	return channels[idx], nil
}

// 根据评分、权重和优先级选择渠道
func selectChannelByScoreAndWeight(channels []*Channel, scoreMap map[int]float64) int {
	// 首先按Sort分组
	sortGroups := make(map[int][]*Channel)
	var sorts []int

	for _, channel := range channels {
		sort := channel.GetSort()

		if _, exists := sortGroups[sort]; !exists {
			sorts = append(sorts, sort)
		}
		sortGroups[sort] = append(sortGroups[sort], channel)
	}

	// 按Sort降序排序
	sort.Slice(sorts, func(i, j int) bool {
		return sorts[i] > sorts[j]
	})

	// 选择最高Sort组
	highestSortGroup := sortGroups[sorts[0]]

	// 如果启用了全局忽略权重计算，直接随机选择一个渠道
	if config.GlobalIgnoreWeightCalculationEnabled && len(highestSortGroup) > 0 {
		randIdx := rand.Intn(len(highestSortGroup))
		return highestSortGroup[randIdx].Id
	}

	// 计算评分加权总和
	type WeightedChannel struct {
		channelID int
		weight    float64 // 综合权重 = 原始权重 * 评分
	}

	weightedChannels := make([]WeightedChannel, 0, len(highestSortGroup))
	totalWeight := float64(0)

	for _, channel := range highestSortGroup {
		// 获取原始权重
		originalWeight := float64(channel.GetWeightForCalculation())

		// 获取评分，如果没有评分则使用默认值0.5
		score := float64(0.5)
		if s, exists := scoreMap[channel.Id]; exists {
			score = s
		}

		// 计算综合权重 = 原始权重 * (0.5 + 评分)
		// 这样即使评分为0的渠道也有机会被选中，但概率较低
		combinedWeight := originalWeight * (0.5 + score)

		weightedChannels = append(weightedChannels, WeightedChannel{
			channelID: channel.Id,
			weight:    combinedWeight,
		})

		totalWeight += combinedWeight
	}

	// 如果总权重为0，随机选择
	if totalWeight <= 0 {
		randomIdx := rand.Intn(len(highestSortGroup))
		return highestSortGroup[randomIdx].Id
	}

	// 按综合权重随机选择
	randomValue := rand.Float64() * totalWeight
	currentWeight := float64(0)

	for _, wc := range weightedChannels {
		currentWeight += wc.weight
		if randomValue < currentWeight {
			return wc.channelID
		}
	}

	// 兜底，返回第一个
	return highestSortGroup[0].Id
}

// CacheGetUserEmail 获取用户邮箱（带缓存）
func CacheGetUserEmail(userId int) (email string, err error) {
	if !common.RedisEnabled {
		return GetUserEmail(userId)
	}
	email, err = common.RedisGet(fmt.Sprintf("user_email:%d", userId))
	if err != nil {
		email, err = GetUserEmail(userId)
		if err != nil {
			return "", err
		}
		err = common.RedisSet(fmt.Sprintf("user_email:%d", userId), email, time.Duration(UserId2QuotaCacheSeconds)*time.Second)
		if err != nil {
			logger.SysError("Redis set user email error: " + err.Error())
		}
	}
	return email, nil
}

// CacheGetUsernameById 获取用户名（带缓存）
func CacheGetUsernameById(userId int) (username string) {
	if !common.RedisEnabled {
		return GetUsernameById(userId)
	}
	username, err := common.RedisGet(fmt.Sprintf("user_username:%d", userId))
	if err != nil {
		username = GetUsernameById(userId)
		err = common.RedisSet(fmt.Sprintf("user_username:%d", userId), username, time.Duration(UserId2QuotaCacheSeconds)*time.Second)
		if err != nil {
			logger.SysError("Redis set user username error: " + err.Error())
		}
	}
	return username
}

// CacheGetRootUserEmail 获取Root用户邮箱（带缓存）
func CacheGetRootUserEmail() (email string) {
	if !common.RedisEnabled {
		return GetRootUserEmail()
	}
	email, err := common.RedisGet("root_user_email")
	if err != nil {
		email = GetRootUserEmail()
		err = common.RedisSet("root_user_email", email, time.Duration(UserInfoCacheSeconds)*time.Second)
		if err != nil {
			logger.SysError("Redis set root user email error: " + err.Error())
		}
	}
	return email
}

// CacheGetRootUserTelegramId 获取Root用户TelegramId（带缓存）
func CacheGetRootUserTelegramId() (telegramId string) {
	if !common.RedisEnabled {
		return GetRootUserTelegramId()
	}
	telegramId, err := common.RedisGet("root_user_telegram_id")
	if err != nil {
		telegramId = GetRootUserTelegramId()
		err = common.RedisSet("root_user_telegram_id", telegramId, time.Duration(UserInfoCacheSeconds)*time.Second)
		if err != nil {
			logger.SysError("Redis set root user telegram_id error: " + err.Error())
		}
	}
	return telegramId
}

// CacheGetRootUserAccessToken 获取Root用户AccessToken（带缓存）
func CacheGetRootUserAccessToken() (accessToken string) {
	if !common.RedisEnabled {
		return GetRootUserAccessToken()
	}
	accessToken, err := common.RedisGet("root_user_access_token")
	if err != nil {
		accessToken = GetRootUserAccessToken()
		err = common.RedisSet("root_user_access_token", accessToken, time.Duration(TokenCacheSeconds)*time.Second)
		if err != nil {
			logger.SysError("Redis set root user access_token error: " + err.Error())
		}
	}
	return accessToken
}

// SetUserQuotaToRedis 设置用户在Redis中的余额
// 注意：此函数现在使用异步写入，不阻塞调用方，总是返回nil
func SetUserQuotaToRedis(userId int, quota int64) error {
	if !common.RedisEnabled {
		return errors.New("Redis未启用")
	}

	// 使用异步写入方法
	AsyncSetUserQuotaToRedis(userId, quota)

	// 异步更新过期时间缓存
	helper.SafeGoroutine(func() {
		_, quotaExpireTime, err := GetUserQuotaAndExpireTime(userId)
		if err == nil {
			// 过期时间缓存可以同步写入，因为不涉及锁竞争
			err = common.RedisSet(fmt.Sprintf("quota_expire_time:%d", userId), fmt.Sprintf("%d", quotaExpireTime), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
			if err != nil {
				logger.SysError("Redis set quota expire time error: " + err.Error())
			}
		}
	})

	return nil
}

// CacheGetUserNotificationSetting 获取用户通知设置（带缓存）
func CacheGetUserNotificationSetting(userId int) (*UserNotificationSetting, error) {
	if !common.RedisEnabled {
		return GetUserNotificationSetting(userId)
	}

	cacheKey := fmt.Sprintf("user_notification_setting:%d", userId)
	settingJSON, err := common.RedisGet(cacheKey)
	if err != nil {
		// 缓存未命中，从数据库获取
		setting, err := GetUserNotificationSetting(userId)
		if err != nil {
			return nil, err
		}

		// 异步写入缓存
		AsyncSetUserNotificationSettingToRedis(userId, setting)

		return setting, nil
	}

	// 反序列化缓存数据
	var setting UserNotificationSetting
	err = json.Unmarshal([]byte(settingJSON), &setting)
	if err != nil {
		logger.SysError(fmt.Sprintf("Redis反序列化用户通知设置失败，用户ID: %d, 错误: %s", userId, err.Error()))
		// 缓存数据损坏，从数据库重新获取
		return GetUserNotificationSetting(userId)
	}

	return &setting, nil
}

// AsyncSetUserNotificationSettingToRedis 异步写入用户通知设置到Redis缓存
func AsyncSetUserNotificationSettingToRedis(userId int, setting *UserNotificationSetting) {
	if !common.RedisEnabled || setting == nil {
		return
	}

	helper.SafeGoroutine(func() {
		// 序列化设置数据
		settingJSON, err := json.Marshal(setting)
		if err != nil {
			logger.SysError(fmt.Sprintf("序列化用户通知设置失败，用户ID: %d, 错误: %s", userId, err.Error()))
			return
		}

		cacheKey := fmt.Sprintf("user_notification_setting:%d", userId)
		value := string(settingJSON)
		expiration := time.Duration(UserNotificationSettingCacheSeconds) * time.Second
		lockTimeout := 5 * time.Second
		description := fmt.Sprintf("用户 %d 通知设置", userId)

		AsyncSetCacheWithLock(cacheKey, value, expiration, lockTimeout, description)
	})
}

// UpdateUserNotificationSettingCache 更新用户通知设置缓存
func UpdateUserNotificationSettingCache(userId int, setting *UserNotificationSetting) error {
	if !common.RedisEnabled || setting == nil {
		return nil
	}

	// 序列化设置数据
	settingJSON, err := json.Marshal(setting)
	if err != nil {
		return fmt.Errorf("序列化用户通知设置失败: %s", err.Error())
	}

	cacheKey := fmt.Sprintf("user_notification_setting:%d", userId)
	expiration := time.Duration(UserNotificationSettingCacheSeconds) * time.Second

	return common.RedisSet(cacheKey, string(settingJSON), expiration)
}

// DeleteUserNotificationSettingCache 删除用户通知设置缓存
func DeleteUserNotificationSettingCache(userId int) error {
	if !common.RedisEnabled {
		return nil
	}

	cacheKey := fmt.Sprintf("user_notification_setting:%d", userId)
	return common.RedisDel(cacheKey)
}

// CacheGetUserAgencyServerAddress 获取用户代理商服务器地址（带缓存）
func CacheGetUserAgencyServerAddress(userId int) (string, error) {
	if !common.RedisEnabled {
		return GetUserAgencyServerAddress(userId)
	}

	cacheKey := fmt.Sprintf("user_agency_server_address:%d", userId)
	serverAddress, err := common.RedisGet(cacheKey)
	if err == nil {
		// 缓存命中
		if serverAddress == "" {
			return "", nil // 用户不属于代理商
		}
		return serverAddress, nil
	}

	// 缓存未命中，从数据库获取
	serverAddress, err = GetUserAgencyServerAddress(userId)
	if err != nil {
		return "", err
	}

	// 异步写入缓存
	AsyncSetUserAgencyServerAddressToRedis(userId, serverAddress)

	return serverAddress, nil
}

// AsyncSetUserAgencyServerAddressToRedis 异步写入用户代理商服务器地址到Redis缓存
func AsyncSetUserAgencyServerAddressToRedis(userId int, serverAddress string) {
	cacheKey := fmt.Sprintf("user_agency_server_address:%d", userId)
	value := serverAddress
	expiration := time.Duration(UserAgencyServerAddressCacheSeconds) * time.Second
	lockTimeout := 5 * time.Second
	description := fmt.Sprintf("用户 %d 代理商服务器地址", userId)

	AsyncSetCacheWithLock(cacheKey, value, expiration, lockTimeout, description)
}

// UpdateUserAgencyServerAddressCache 更新用户代理商服务器地址缓存
func UpdateUserAgencyServerAddressCache(userId int, serverAddress string) error {
	if !common.RedisEnabled {
		return nil
	}

	cacheKey := fmt.Sprintf("user_agency_server_address:%d", userId)
	expiration := time.Duration(UserAgencyServerAddressCacheSeconds) * time.Second
	return common.RedisSet(cacheKey, serverAddress, expiration)
}

// DeleteUserAgencyServerAddressCache 删除用户代理商服务器地址缓存
func DeleteUserAgencyServerAddressCache(userId int) error {
	if !common.RedisEnabled {
		return nil
	}

	cacheKey := fmt.Sprintf("user_agency_server_address:%d", userId)
	return common.RedisDel(cacheKey)
}

// CacheGetUserById 获取用户信息（带缓存）
func CacheGetUserById(userId int, includeDeletedUsers bool) (*User, error) {
	if !common.RedisEnabled {
		return GetUserById(userId, includeDeletedUsers)
	}

	cacheKey := fmt.Sprintf("user_info:%d", userId)
	if includeDeletedUsers {
		cacheKey = fmt.Sprintf("user_info_with_deleted:%d", userId)
	}

	userJson, err := common.RedisGet(cacheKey)
	if err == nil {
		// 缓存命中，反序列化用户信息
		var user User
		err = json.Unmarshal([]byte(userJson), &user)
		if err == nil {
			return &user, nil
		}
	}

	// 缓存未命中，从数据库获取
	user, err := GetUserById(userId, includeDeletedUsers)
	if err != nil {
		return nil, err
	}

	// 异步写入缓存
	AsyncSetUserInfoToRedis(userId, user, includeDeletedUsers)

	return user, nil
}

// AsyncSetUserInfoToRedis 异步写入用户信息到Redis缓存
func AsyncSetUserInfoToRedis(userId int, user *User, includeDeletedUsers bool) {
	cacheKey := fmt.Sprintf("user_info:%d", userId)
	if includeDeletedUsers {
		cacheKey = fmt.Sprintf("user_info_with_deleted:%d", userId)
	}

	helper.SafeGoroutine(func() {
		userJson, err := json.Marshal(user)
		if err != nil {
			logger.SysError("序列化用户信息失败: " + err.Error())
			return
		}

		err = common.RedisSet(cacheKey, string(userJson), time.Duration(UserInfoCacheSeconds)*time.Second)
		if err != nil {
			logger.SysError("写入用户信息缓存失败: " + err.Error())
		}
	})
}

// DeleteUserInfoCache 删除用户信息缓存
func DeleteUserInfoCache(userId int) {
	if !common.RedisEnabled {
		return
	}

	helper.SafeGoroutine(func() {
		cacheKey1 := fmt.Sprintf("user_info:%d", userId)
		cacheKey2 := fmt.Sprintf("user_info_with_deleted:%d", userId)

		err := common.RedisDel(cacheKey1)
		if err != nil {
			logger.SysError("删除用户信息缓存失败: " + err.Error())
		}

		err = common.RedisDel(cacheKey2)
		if err != nil {
			logger.SysError("删除用户信息缓存失败: " + err.Error())
		}
	})
}

// CacheGetUserRouteDiscounts 获取用户动态路由折扣缓存
func CacheGetUserRouteDiscounts(userId int) (map[string]float64, error) {
	if !common.RedisEnabled {
		userExtend, err := GetUserExByUserId(userId)
		if err != nil {
			return nil, err
		}
		return userExtend.GetRouteDiscounts()
	}

	discountsStr, err := common.RedisGetAndExtendExpiryUsingLua(fmt.Sprintf("user_route_discounts:%d", userId), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if err == nil {
		var discounts map[string]float64
		err = json.Unmarshal([]byte(discountsStr), &discounts)
		if err == nil {
			return discounts, nil
		}
	}

	userExtend, err := GetUserExByUserId(userId)
	if err != nil {
		return nil, err
	}
	discounts, err := userExtend.GetRouteDiscounts()
	if err != nil {
		return nil, err
	}

	discountsJSON, err := json.Marshal(discounts)
	if err != nil {
		return nil, err
	}

	redisErr := common.RedisSet(fmt.Sprintf("user_route_discounts:%d", userId), string(discountsJSON), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
	if redisErr != nil {
		logger.SysError("Redis set user route discounts error: " + redisErr.Error())
	}

	return discounts, nil
}

// CacheUpdateUserRouteDiscounts 更新用户动态路由折扣缓存
func CacheUpdateUserRouteDiscounts(userId int, discounts map[string]float64) error {
	if !common.RedisEnabled {
		return nil
	}

	discountsJSON, err := json.Marshal(discounts)
	if err != nil {
		return err
	}

	return common.RedisSet(fmt.Sprintf("user_route_discounts:%d", userId), string(discountsJSON), time.Duration(UserId2QuotaCacheSeconds)*time.Second)
}

// UpdateUserRouteDiscounts 更新用户动态路由折扣
func UpdateUserRouteDiscounts(userId int, discounts map[string]float64) error {
	userExtend, err := GetUserExByUserId(userId)
	if err != nil {
		return err
	}

	err = userExtend.SetRouteDiscounts(discounts)
	if err != nil {
		return err
	}

	err = userExtend.Update()
	if err != nil {
		return err
	}

	return CacheUpdateUserRouteDiscounts(userId, discounts)
}

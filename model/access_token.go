package model

import (
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/random"
	"gorm.io/gorm"
)

// 权限常量定义
const (
	// 基础操作权限
	PermRead   int64 = 1 << 0 // 读取权限
	PermWrite  int64 = 1 << 1 // 写入权限
	PermDelete int64 = 1 << 2 // 删除权限

	// 资源特定权限
	PermToken      int64 = 1 << 10 // 令牌管理权限
	PermChannel    int64 = 1 << 11 // 渠道管理权限
	PermLog        int64 = 1 << 12 // 日志查看权限
	PermStatistics int64 = 1 << 13 // 统计信息查看权限
	PermUserAdmin  int64 = 1 << 14 // 用户管理权限
	PermQuota      int64 = 1 << 15 // 额度管理权限

	// 预设权限组合
	PermReadOnly int64 = PermRead
	PermFull     int64 = PermRead | PermWrite | PermDelete | PermToken | PermChannel | PermLog | PermStatistics | PermUserAdmin | PermQuota
)

// AccessToken 新的访问令牌模型
type AccessToken struct {
	Id          int    `json:"id" gorm:"primaryKey"`
	UserId      int    `json:"user_id" gorm:"index"`
	Name        string `json:"name" gorm:"type:varchar(255);not null"`
	Key         string `json:"key" gorm:"type:varchar(64);uniqueIndex;not null"`
	Permissions int64  `json:"permissions" gorm:"bigint;default:0"`
	CreatedTime int64  `json:"created_time" gorm:"bigint"`
	ExpiredTime int64  `json:"expired_time" gorm:"bigint;default:-1"` // -1 表示永不过期
	Status      int    `json:"status" gorm:"type:int;default:1"`      // 1:enabled, 2:disabled
	Remark      string `json:"remark" gorm:"type:varchar(500)"`
}

// 创建新的访问令牌
func CreateAccessToken(userId int, name string, permissions int64, expiredTime int64, remark string) (*AccessToken, error) {
	token := AccessToken{
		UserId:      userId,
		Name:        name,
		Key:         random.GetUUID(),
		Permissions: permissions,
		CreatedTime: helper.GetTimestamp(),
		ExpiredTime: expiredTime,
		Status:      1, // 默认启用
		Remark:      remark,
	}

	err := DB.Create(&token).Error
	if err != nil {
		return nil, err
	}
	return &token, nil
}

// 获取用户的所有访问令牌
func GetUserAccessTokens(userId int) ([]AccessToken, error) {
	var tokens []AccessToken
	err := DB.Where("user_id = ?", userId).Find(&tokens).Error
	return tokens, err
}

// 根据Key获取访问令牌
func GetAccessTokenByKey(key string) (*AccessToken, error) {
	var token AccessToken
	keyCol := "`key`"
	if common.UsingPostgreSQL {
		keyCol = `"key"`
	}
	result := DB.Where(keyCol+" = ?", key).First(&token)
	if result.Error != nil {
		return nil, result.Error
	}
	return &token, nil
}

// 验证访问令牌并返回用户和令牌权限
func ValidateAccessTokenPermissions(key string) (*User, int64, error) {
	// 先尝试使用新的访问令牌系统
	token, err := GetAccessTokenByKey(key)
	if err == nil && token != nil {
		// 检查令牌是否已过期
		if token.ExpiredTime > 0 && token.ExpiredTime < helper.GetTimestamp() {
			return nil, 0, gorm.ErrRecordNotFound
		}
		// 检查令牌状态
		if token.Status != 1 {
			return nil, 0, gorm.ErrRecordNotFound
		}

		// 获取用户信息
		user, err := GetUserById(token.UserId, false)
		if err != nil {
			return nil, 0, err
		}

		return user, token.Permissions, nil
	}

	// 回退到旧的访问令牌系统
	user := ValidateAccessToken(key)
	if user != nil {
		return user, PermFull, nil // 旧的访问令牌具有全部权限
	}

	return nil, 0, gorm.ErrRecordNotFound
}

// 验证访问令牌对特定权限的访问
func HasTokenPermission(permissions int64, requiredPermission int64) bool {
	return permissions&requiredPermission == requiredPermission
}

// 更新访问令牌状态
func UpdateAccessTokenStatus(id int, status int) error {
	return DB.Model(&AccessToken{}).Where("id = ?", id).Update("status", status).Error
}

// 删除访问令牌
func DeleteAccessToken(id int) error {
	return DB.Delete(&AccessToken{}, id).Error
}

// 更新访问令牌信息
func UpdateAccessToken(token *AccessToken) error {
	return DB.Save(token).Error
}

package model

type Meta struct {
	StartTime_                      int64
	RequestEndTime_                 int64
	ResponseFirstByteTime_          int64 // 响应首个字节的时间
	RequestId                       string
	Ip                              string
	RemoteIp                        string
	Mode                            int
	IsPackagePlan                   bool
	CustomFullURLEnabled            bool
	ArrangeMessages                 bool
	OriginalModelPricing            bool
	PackagePlanInstance             PackagePlanInstance
	BillingType                     int
	ChannelType                     int
	ChannelId                       int
	ChannelName                     string
	TokenId                         int
	TokenName                       string
	TokenKey                        string
	UserId                          int
	UserName                        string
	Group                           string
	TokenGroup                      string
	RequestModel                    string
	ModelFixedPrice                 float64
	ModelMapping                    map[string]string
	Base64ImagePrefixMapping        map[string]string
	ExtraHeaders                    map[string]string
	ModelMappingArr                 []map[string]string
	BaseURL                         string
	APIVersion                      string
	APIKey                          string
	APIType                         int
	Config                          ChannelConfig
	DetailPrompt                    string
	DetailCompletion                string
	UpstreamResponse                string // 上游返回的原始响应
	FullResponse                    string // 处理后返回给客户的最终响应
	CompletionId                    string
	IsStream                        bool
	OriginModelName                 string
	ActualModelName                 string
	RequestURLPath                  string
	PromptTokens                    int // only for DoResponse
	CompletionTokens                int
	ExcludeCustomPromptCostEnabled  bool
	UsageRecalculationEnabled       bool
	IsAdminPass                     bool
	ChannelTimeoutBreakerTime       int64
	EmptyResponseErrorEnabled       bool
	RemoveImageDownloadErrorEnabled bool
	ForceChatUrlEnabled             bool
	IgnoreFcTcEnabled               bool
	OriginalModelFakeRespEnabled    bool
	RequestTokenLimitEnabled        bool
	MinRequestTokenCount            int64
	MaxRequestTokenCount            int64
	RequestDuration                 int64
	ResponseFirstByteDuration       int64
	TotalDuration                   int64
	Quota                           int64
	ForceO1StreamEnabled            bool // 是否强制O1流式输出
	ErrorCode                       string
	IsV1MessagesPath                bool // 是否是 /v1/messages 路径
}

package model

type ServiceList struct {
	Id         int        `json:"id"`
	ServiceId  int        `json:"service_id" gorm:"index"` //工单号
	Status     string     `json:"status"`                  //工单状态，1:待处理，2:处理中，3:已完成
	UserId     int        `json:"user_id" gorm:"index"`    //用户id
	CreateTime int64      `json:"create_time"`             //创建时间，unix时间戳
	UpdateTime int64      `json:"update_time"`             //更新时间，unix时间戳
	FinishTime int64      `json:"finish_time"`             //结束时间，unix时间戳
	Priority   int        `json:"priority"`                //优先级，1:低，2:中，3:高
	Content    []struct { //工单内容
		Time int64  `json:"time"`
		Msg  string `json:"msg"`
		Role int    `json:"role"` //角色，1:用户，2:客服
	} `json:"content"`
}

func (serviceList *ServiceList) Insert() error {
	var err error
	err = DB.Create(serviceList).Error
	return err
}

func (serviceList *ServiceList) Update() error {
	var err error
	err = DB.Save(serviceList).Error
	return err
}

func GetServiceListById(id int) *ServiceList {
	var serviceList *ServiceList
	var err error
	err = DB.Where("id = ?", id).First(&serviceList).Error
	if err != nil {
		return nil
	}
	return serviceList
}

func GetServiceListByUserId(userId int) ([]*ServiceList, error) {
	var serviceList []*ServiceList
	var err error
	err = DB.Where("user_id = ?", userId).Find(&serviceList).Error
	return serviceList, err
}

func GetAllServiceLists(startIdx int, num int, id int, serviceId int, status string, userId int, createTime int64, updateTime int64, finishTime int64, priority int) ([]*ServiceList, error) {
	var serviceList []*ServiceList
	var err error
	tx := DB
	if id != 0 {
		tx = tx.Where("id = ?", id)
	}
	if serviceId != 0 {
		tx = tx.Where("service_id = ?", serviceId)
	}
	if status != "" {
		tx = tx.Where("status = ?", status)
	}
	if userId != 0 {
		tx = tx.Where("user_id = ?", userId)
	}
	if createTime != 0 {
		tx = tx.Where("create_time = ?", createTime)
	}
	if updateTime != 0 {
		tx = tx.Where("update_time = ?", updateTime)
	}
	if finishTime != 0 {
		tx = tx.Where("finish_time = ?", finishTime)
	}
	if priority != 0 {
		tx = tx.Where("priority = ?", priority)
	}
	err = tx.Order("id desc").Limit(num).Offset(startIdx).Find(&serviceList).Error
	return serviceList, err
}

func CountAllServiceLists(id int, serviceId int, status string, userId int, createTime int64, updateTime int64, finishTime int64, priority int) ([]*ServiceList, error) {
	var serviceLists []*ServiceList
	var err error
	tx := DB
	if id != 0 {
		tx = tx.Where("id = ?", id)
	}
	if serviceId != 0 {
		tx = tx.Where("service_id = ?", serviceId)
	}
	if status != "" {
		tx = tx.Where("status = ?", status)
	}
	if userId != 0 {
		tx = tx.Where("user_id = ?", userId)
	}
	if createTime != 0 {
		tx = tx.Where("create_time = ?", createTime)
	}
	if updateTime != 0 {
		tx = tx.Where("update_time = ?", updateTime)
	}
	if finishTime != 0 {
		tx = tx.Where("finish_time = ?", finishTime)
	}
	if priority != 0 {
		tx = tx.Where("priority = ?", priority)
	}
	err = tx.Find(&serviceLists).Error
	return serviceLists, err
}

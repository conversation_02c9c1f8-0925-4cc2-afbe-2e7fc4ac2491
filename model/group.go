package model

import (
	"encoding/json"
	"errors"
	"fmt"

	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/relay/billing/ratio"
	"gorm.io/gorm"
)

type Group struct {
	Id                 int     `json:"id"`
	Name               string  `json:"name" gorm:"type:varchar(32);uniqueIndex"`
	DisplayName        string  `json:"display_name" gorm:"type:varchar(255)"`
	Description        string  `json:"description" gorm:"type:text"`
	SortOrder          int     `json:"sort_order" gorm:"type:int;default:0;index"`
	IsSelectable       *bool   `json:"is_selectable" gorm:"default:1;index"`
	GroupRatio         float64 `json:"group_ratio" gorm:"type:float;default:1"`
	ColorMapping       string  `json:"color_mapping" gorm:"type:varchar(20)"`
	TopupGroupMinLimit float64 `json:"topup_group_min_limit" gorm:"type:float;default:0"`
	InviteBonusRatio   float64 `json:"invite_bonus_ratio" gorm:"type:float;default:0"`
	TopupGroupRatio    float64 `json:"topup_group_ratio" gorm:"type:float;default:1"`
	ConsumptionRatio   float64 `json:"consumption_ratio" gorm:"type:float;default:1"`
	ModelRatio         string  `json:"model_ratio" gorm:"type:text"`
	ModelFixedPrice    string  `json:"model_fixed_price" gorm:"type:text"`
	IsVisible          *bool   `json:"is_visible" gorm:"default:1;index"`
}

// 添加新的返回结构体
type GroupWithRatio struct {
	*Group
	ConvertRatio      float64 `json:"convert_ratio"`
	CurrentGroupRatio float64 `json:"current_group_ratio"`
	CurrentTopupRatio float64 `json:"current_topup_ratio"`
}

// 扩展返回结构体,包含当前用户组信息
type GroupsWithRatioResponse struct {
	Groups            []*GroupWithRatio `json:"groups"`              // 可选分组列表
	CurrentGroupRatio float64           `json:"current_group_ratio"` // 当前用户组倍率
	CurrentTopupRatio float64           `json:"current_topup_ratio"` // 当前用户组充值倍率
}

// GetSelectableGroups 获取可选的用户组
func GetSelectableGroups(startIdx int, num int, name string, displayName string) ([]*Group, error) {
	var groups []*Group
	tx := DB.Where("is_selectable = ? AND is_visible = ?", true, true)
	if name != "" {
		tx = tx.Where("name LIKE ?", "%"+name+"%")
	}
	if displayName != "" {
		tx = tx.Where("display_name LIKE ?", "%"+displayName+"%")
	}
	err := tx.Order("sort_order asc, id desc").Limit(num).Offset(startIdx).Find(&groups).Error
	return groups, err
}

// GetSelectableGroupsWithRatio 获取可选的用户组,并计算转换率
func GetSelectableGroupsWithRatio(startIdx int, num int, name string, displayName string, userGroup string, isAdmin bool) ([]*GroupWithRatio, error) {
	var groups []*Group
	tx := DB.Where("is_selectable = ?", true)

	// 如果不是管理员,需要检查可见性
	if !isAdmin {
		tx = tx.Where("is_visible = ?", true)
	}

	if name != "" {
		tx = tx.Where("name LIKE ?", "%"+name+"%")
	}
	if displayName != "" {
		tx = tx.Where("display_name LIKE ?", "%"+displayName+"%")
	}
	err := tx.Order("sort_order asc, id desc").Limit(num).Offset(startIdx).Find(&groups).Error
	if err != nil {
		return nil, err
	}

	// 获取用户当前分组信息
	currentGroup, err := GetGroupByName(userGroup)
	if err != nil {
		return nil, err
	}
	if currentGroup == nil {
		return nil, fmt.Errorf("user group not found: %s", userGroup)
	}

	// 计算每个分组的转换率
	result := make([]*GroupWithRatio, len(groups))
	for i, group := range groups {
		// 计算转换率
		topupConvertRatio := group.TopupGroupRatio / currentGroup.TopupGroupRatio
		groupConvertRatio := group.GroupRatio / currentGroup.GroupRatio
		convertRatio := topupConvertRatio * groupConvertRatio

		result[i] = &GroupWithRatio{
			Group:             group,
			ConvertRatio:      convertRatio,
			CurrentGroupRatio: currentGroup.GroupRatio,
			CurrentTopupRatio: currentGroup.TopupGroupRatio,
		}
	}

	return result, nil
}

func GetAllGroups(startIdx int, num int, name string, displayName string) ([]*Group, error) {
	var groups []*Group
	tx := DB
	if name != "" {
		tx = tx.Where("name LIKE ?", "%"+name+"%")
	}
	if displayName != "" {
		tx = tx.Where("display_name LIKE ?", "%"+displayName+"%")
	}
	err := tx.Order("sort_order asc, id desc").Limit(num).Offset(startIdx).Find(&groups).Error
	return groups, err
}

func CountAllGroups(name string, displayName string) (int64, error) {
	var count int64
	tx := DB.Model(&Group{})
	if name != "" {
		tx = tx.Where("name LIKE ?", "%"+name+"%")
	}
	if displayName != "" {
		tx = tx.Where("display_name LIKE ?", "%"+displayName+"%")
	}
	err := tx.Count(&count).Error
	return count, err
}

func SearchGroups(keyword string) (groups []*Group, err error) {
	err = DB.Where("id = ? or name LIKE ? or display_name LIKE ?", keyword, "%"+keyword+"%", "%"+keyword+"%").Find(&groups).Error
	return groups, err
}

func GetGroupById(id int) (*Group, error) {
	if id == 0 {
		return nil, errors.New("id 为空！")
	}
	group := Group{Id: id}
	err := DB.First(&group, "id = ?", id).Error
	return &group, err
}

func (group *Group) GetIsSelectable() bool {
	if group.IsSelectable == nil {
		return false
	}
	return *group.IsSelectable
}

func (group *Group) Insert() error {
	err := DB.Create(group).Error
	if err != nil {
		logger.SysError("插入用户组失败: " + err.Error())
		return err
	}
	logger.SysLog("成功插入用户组: " + group.Name)
	if err := SyncGroupConfigurationsToJSON(); err != nil {
		logger.SysError("同步用户组配置到 JSON 失败: " + err.Error())
		return err
	}
	return nil
}

func (group *Group) Update() error {
	// 创建一个 map 来存储所有需要更新的字段
	updates := map[string]interface{}{
		"name":                  group.Name,
		"display_name":          group.DisplayName,
		"description":           group.Description,
		"is_selectable":         group.IsSelectable,
		"group_ratio":           group.GroupRatio,
		"color_mapping":         group.ColorMapping,
		"topup_group_min_limit": group.TopupGroupMinLimit,
		"invite_bonus_ratio":    group.InviteBonusRatio,
		"topup_group_ratio":     group.TopupGroupRatio,
		"consumption_ratio":     group.ConsumptionRatio,
		"model_ratio":           group.ModelRatio,
		"model_fixed_price":     group.ModelFixedPrice,
		"is_visible":            group.IsVisible,
		"sort_order":            group.SortOrder,
	}

	err := DB.Model(group).Updates(updates).Error
	if err != nil {
		logger.SysError("更新用户组失败: " + err.Error())
		return err
	}
	logger.SysLog("成功更新用户组: " + group.Name)

	// 删除 Redis 缓存
	if err := DeleteGroupCache(group.Name); err != nil {
		logger.SysError("删除用户组 Redis 缓存失败: " + err.Error())
	}

	if err := SyncGroupConfigurationsToJSON(); err != nil {
		logger.SysError("同步用户组配置到 JSON 失败: " + err.Error())
		return err
	}
	return nil
}

func (group *Group) Delete() error {
	err := DB.Delete(group).Error
	if err != nil {
		logger.SysError("删除用户组失败: " + err.Error())
		return err
	}
	logger.SysLog("成功删除用户组: " + group.Name)

	// 删除 Redis 缓存
	if err := DeleteGroupCache(group.Name); err != nil {
		logger.SysError("删除用户组 Redis 缓存失败: " + err.Error())
	}

	return SyncGroupConfigurationsToJSON()
}

func DeleteGroupById(id int) error {
	if id == 0 {
		return errors.New("id 为空！")
	}

	var group Group
	err := DB.Where("id = ?", id).First(&group).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("未找到 ID 为 %d 的用户组", id)
		}
		return err
	}

	err = DB.Delete(&group).Error
	if err != nil {
		logger.SysError(fmt.Sprintf("删除用户组失败 (ID: %d): %s", id, err.Error()))
		return err
	}

	logger.SysLog(fmt.Sprintf("成功删除用户组 (ID: %d, Name: %s)", id, group.Name))

	// 删除 Redis 缓存
	if err := DeleteGroupCache(group.Name); err != nil {
		logger.SysError(fmt.Sprintf("删除用户组 Redis 缓存失败 (ID: %d, Name: %s): %s", id, group.Name, err.Error()))
	}

	if err := SyncGroupConfigurationsToJSON(); err != nil {
		logger.SysError(fmt.Sprintf("同步用户组配置到 JSON 失败 (after deleting ID: %d): %s", id, err.Error()))
		return err
	}

	return nil
}

func DeleteGroupByIds(ids []int) (int64, error) {
	if len(ids) == 0 {
		return 0, errors.New("ids 为空！")
	}

	var groups []Group
	if err := DB.Where("id in (?)", ids).Find(&groups).Error; err != nil {
		return 0, err
	}

	result := DB.Where("id in (?)", ids).Delete(&Group{})
	if result.Error != nil {
		return 0, result.Error
	}

	// 删除 Redis 缓存
	for _, group := range groups {
		if err := DeleteGroupCache(group.Name); err != nil {
			logger.SysError("删除用户组 Redis 缓存失败: " + err.Error())
		}
	}

	if err := SyncGroupConfigurationsToJSON(); err != nil {
		logger.SysError("同步用户组配置到 JSON 失败: " + err.Error())
		return result.RowsAffected, err
	}
	return result.RowsAffected, nil
}

func MergeGroupConfigurations() error {
	// 获取所有的组配置
	groupRatios := ratio.GetGroupRatioMap()
	inviteBonusRatios := ratio.GetInviteBonusRatioMap()
	groupColorMappings := ratio.GetGroupColorMappingMap()
	groupTopupGroupMinLimit := ratio.GetTopupGroupMinLimitMap()
	groupTopupGroupRatio := ratio.GetTopupGroupRatioMap()
	isSelectable := true
	// 遍历所有的组配置
	for groupName := range groupRatios {
		group := Group{
			Name:               groupName,
			DisplayName:        groupName,
			IsSelectable:       &isSelectable,
			GroupRatio:         groupRatios[groupName],
			InviteBonusRatio:   inviteBonusRatios[groupName],
			ColorMapping:       groupColorMappings[groupName],
			TopupGroupMinLimit: float64(groupTopupGroupMinLimit[groupName]),
			TopupGroupRatio:    groupTopupGroupRatio[groupName],
		}

		var existingGroup Group
		if err := DB.Where("name = ?", groupName).First(&existingGroup).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// 如果不存在，则创建新记录
				if err := DB.Create(&group).Error; err != nil {
					logger.SysError("创建用户组失败: " + err.Error())
					return err
				}
				logger.SysLog("成功创建用户组: " + groupName)
			} else {
				logger.SysError("查询用户组失败: " + err.Error())
				return err
			}
		} else {
			// 如果存在，则更新记录
			updates := map[string]interface{}{
				"group_ratio":           group.GroupRatio,
				"invite_bonus_ratio":    group.InviteBonusRatio,
				"topup_group_min_limit": group.TopupGroupMinLimit,
				"topup_group_ratio":     group.TopupGroupRatio,
			}
			if err := DB.Model(&existingGroup).Updates(updates).Error; err != nil {
				logger.SysError("更新用户组失败: " + err.Error())
				return err
			}
			logger.SysLog("成功更新用户组: " + groupName)
		}
	}
	// 在所有操作完成后同步配置
	if err := SyncGroupConfigurationsToJSON(); err != nil {
		logger.SysError("同步用户组配置到 JSON 失败: " + err.Error())
		return err
	}

	return nil
}

func SyncGroupConfigurationsToJSON() error {
	var groups []Group
	if err := DB.Find(&groups).Error; err != nil {
		logger.SysError("从数据库获取用户组失败: " + err.Error())
		return err
	}

	groupRatios := make(map[string]float64)
	inviteBonusRatios := make(map[string]float64)
	groupColorMappings := make(map[string]string)
	groupTopupGroupMinLimit := make(map[string]int)
	groupTopupGroupRatio := make(map[string]float64)

	for _, group := range groups {
		groupRatios[group.Name] = group.GroupRatio
		inviteBonusRatios[group.Name] = group.InviteBonusRatio
		groupColorMappings[group.Name] = group.ColorMapping
		groupTopupGroupMinLimit[group.Name] = int(group.TopupGroupMinLimit)
		groupTopupGroupRatio[group.Name] = group.TopupGroupRatio
	}

	// 将 map 转换为 JSON 字符串
	groupRatiosJSON, err := json.Marshal(groupRatios)
	if err != nil {
		logger.SysError("序列化 GroupRatio 失败: " + err.Error())
		return err
	}

	inviteBonusRatiosJSON, err := json.Marshal(inviteBonusRatios)
	if err != nil {
		logger.SysError("序列化 InviteBonusRatio 失败: " + err.Error())
		return err
	}

	groupColorMappingsJSON, err := json.Marshal(groupColorMappings)
	if err != nil {
		logger.SysError("序列化 GroupColorMapping 失败: " + err.Error())
		return err
	}

	groupTopupGroupMinLimitJSON, err := json.Marshal(groupTopupGroupMinLimit)
	if err != nil {
		logger.SysError("序列化 TopupGroupMinLimit 失败: " + err.Error())
		return err
	}

	groupTopupGroupRatioJSON, err := json.Marshal(groupTopupGroupRatio)
	if err != nil {
		logger.SysError("序列化 TopupGroupRatio 失败: " + err.Error())
		return err
	}

	// 使用 UpdateOption 函数更新配置
	if err := UpdateOption("GroupRatio", string(groupRatiosJSON)); err != nil {
		logger.SysError("更新 GroupRatio 失败: " + err.Error())
		return err
	}
	if err := UpdateOption("InviteBonusRatio", string(inviteBonusRatiosJSON)); err != nil {
		logger.SysError("更新 InviteBonusRatio 失败: " + err.Error())
		return err
	}
	if err := UpdateOption("GroupColorMapping", string(groupColorMappingsJSON)); err != nil {
		logger.SysError("更新 GroupColorMapping 失败: " + err.Error())
		return err
	}
	if err := UpdateOption("TopupGroupMinLimit", string(groupTopupGroupMinLimitJSON)); err != nil {
		logger.SysError("更新 TopupGroupMinLimit 失败: " + err.Error())
		return err
	}
	if err := UpdateOption("TopupGroupRatio", string(groupTopupGroupRatioJSON)); err != nil {
		logger.SysError("更新 TopupGroupRatio 失败: " + err.Error())
		return err
	}

	logger.SysLog("用户组配置已成功同步到 JSON")
	return nil
}

func InitGroupConfigurations() error {
	if err := MergeGroupConfigurations(); err != nil {
		return err
	}
	return SyncGroupConfigurationsToJSON()
}

func (group *Group) UpdateIsSelectable() error {
	err := DB.Model(group).Update("is_selectable", group.IsSelectable).Error
	if err != nil {
		return err
	}

	// 删除 Redis 缓存
	if err := DeleteGroupCache(group.Name); err != nil {
		logger.SysError("删除用户组 Redis 缓存失败: " + err.Error())
	}

	return SyncGroupConfigurationsToJSON()
}

// UpdateSortOrder
func (group *Group) UpdateSortOrder() error {
	err := DB.Model(group).Update("sort_order", group.SortOrder).Error
	if err != nil {
		return err
	}

	// 删除 Redis 缓存
	if err := DeleteGroupCache(group.Name); err != nil {
		logger.SysError("删除用户组 Redis 缓存失败: " + err.Error())
	}

	return SyncGroupConfigurationsToJSON()
}

func GetGroupByName(name string) (*Group, error) {
	var group Group
	err := DB.Where("name = ?", name).First(&group).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &group, nil
}
func DeleteGroupCache(name string) error {
	if !common.RedisEnabled {
		return nil
	}
	return common.RedisDel(fmt.Sprintf("group:%s", name))
}

func (group *Group) GetIsVisible() bool {
	if group.IsVisible == nil {
		return false
	}
	return *group.IsVisible
}

func (group *Group) UpdateIsVisible() error {
	err := DB.Model(group).Update("is_visible", group.IsVisible).Error
	if err != nil {
		return err
	}

	// 删除 Redis 缓存
	if err := DeleteGroupCache(group.Name); err != nil {
		logger.SysError("删除用户组 Redis 缓存失败: " + err.Error())
	}

	return SyncGroupConfigurationsToJSON()
}

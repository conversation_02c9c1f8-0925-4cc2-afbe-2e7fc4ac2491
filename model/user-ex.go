package model

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	billingratio "github.com/songquanpeng/one-api/relay/billing/ratio"

	"github.com/songquanpeng/one-api/common"
	"gorm.io/gorm"
)

type UserExtend struct {
	Id                         int      `json:"id"`
	UserId                     int      `json:"user_id" gorm:"index"`                                                    // 用户id
	WarningQuota               int      `json:"warning_quota"`                                                           // 自定义预警额度配置
	AffQuota                   int64    `json:"aff_quota" gorm:"type:bigint;default:0;column:aff_quota"`                 // 邀请剩余额度
	AffHistoryQuota            int64    `json:"aff_history_quota" gorm:"type:bigint;default:0;column:aff_history"`       // 邀请历史额度
	ChatRecordJsonData         string   `json:"chat_record_json_data"`                                                   // 聊天记录
	SettingJsonData            string   `json:"setting_json_data"`                                                       // 个性设置缓存
	EducationCertification     bool     `json:"education_certification" gorm:"default:0;column:education_certification"` // 是否教育认证用户
	ModelRatio                 *string  `json:"model_ratio"`
	ModelFixedPrice            *string  `json:"model_fixed_price"`
	CompletionRatio            *string  `json:"completion_ratio"`
	TopupRatio                 *float64 `json:"topup_ratio" gorm:"type:float;default:0"`                                                           // 用于覆盖分组充值倍率 给单个用户设定
	NewTikTokenBilling         *int8    `json:"new_tik_token_billing" gorm:"type:smallint;default:0;column:new_tik_token_billing"`                 // 是否开启新版tiktoken计费,默认0,为0则不替换系统默认配置,旧版为1,新版为2
	MJSensitiveWordsRefund     *int8    `json:"mj_sensitive_words_refund" gorm:"type:smallint;default:0;column:mj_sensitive_words_refund"`         // MJ敏感词退款,默认0,为0则不替换系统默认配置,1为返还,2为不返还
	GroupDiscounts             string   `json:"group_discounts"`                                                                                   // 分组折扣信息,JSON格式
	RouteDiscounts             string   `json:"route_discounts"`                                                                                   // 动态路由折扣信息,JSON格式
	LogDetailEnabled           *int8    `json:"log_detail_enabled" gorm:"type:smallint;default:0;column:log_detail_enabled"`                       // 是否记录详细日志,默认0表示使用系统默认配置,1为记录,2为不记录
	LogDownstreamErrorEnabled  *int8    `json:"log_downstream_error_enabled" gorm:"type:smallint;default:0;column:log_downstream_error_enabled"`   // 是否记录下游错误,默认0表示使用系统默认配置,1为记录,2为不记录
	LogUpstreamResponseEnabled *int8    `json:"log_upstream_response_enabled" gorm:"type:smallint;default:0;column:log_upstream_response_enabled"` // 是否记录上游响应,默认0表示使用系统默认配置,1为记录,2为不记录
	LogFullResponseEnabled     *int8    `json:"log_full_response_enabled" gorm:"type:smallint;default:0;column:log_full_response_enabled"`         // 是否记录完整响应,默认0表示使用系统默认配置,1为记录,2为不记录
	MaxPromptLogLength         *int64   `json:"max_prompt_log_length" gorm:"type:bigint;default:0;column:max_prompt_log_length"`                   // 个性化日志prompt长度限制,默认0表示使用系统默认配置
	TrustUpstreamStreamUsage   *int8    `json:"trust_upstream_stream_usage" gorm:"type:smallint;default:0;column:trust_upstream_stream_usage"`     // 是否信任上游流式用量统计,默认0表示使用系统默认配置,1为信任,2为不信任
	ForceStreamOption          *int8    `json:"force_stream_option" gorm:"type:smallint;default:0;column:force_stream_option"`                     // 是否强制要求上游返回用量,默认0表示使用系统默认配置,1为强制,2为不强制
	ForceDownstreamStreamUsage *int8    `json:"force_downstream_stream_usage" gorm:"type:smallint;default:0;column:force_downstream_stream_usage"` // 是否强制返回下游流式用量,默认0表示使用系统默认配置,1为强制,2为不强制
	ChannelScoreRouting        *int8    `json:"channel_score_routing" gorm:"type:smallint;default:0;column:channel_score_routing"`                 // 是否启用基于渠道得分的路由,默认0表示使用系统默认配置,1为启用,2为禁用
	Say1DirectSuccessMode      *int8    `json:"say1_direct_success_mode" gorm:"type:smallint;default:0;column:say1_direct_success_mode"`           // 是否开启吃掉拨测功能,默认0表示使用系统默认配置,1为开启,2为关闭
	MockOpenAICompleteFormat   *int8    `json:"mock_openai_complete_format" gorm:"type:smallint;default:0;column:mock_openai_complete_format"`     // 是否完全模拟OpenAI官方响应格式,默认0表示使用系统默用配置,1为启用,2为禁用
	ClaudeMessageNormalization *int8    `json:"claude_message_normalization" gorm:"type:smallint;default:0;column:claude_message_normalization"`   // 是否启用Claude消息整理功能,默认0表示使用系统默认配置,1为启用,2为禁用
	ExtraVisibleGroups         *string  `json:"extra_visible_groups" gorm:"type:text"`                                                             // 用户额外可见的分组，追加到系统查询分组的接口中额外返回的分组，多个分组用英文逗号分隔
}

func (userExtend *UserExtend) Insert() error {
	var err error
	err = DB.Create(userExtend).Error
	if err != nil {
		return err
	}
	return nil
}

func (userExtend *UserExtend) Update() error {
	var err error
	err = DB.Save(userExtend).Error
	if err != nil {
		return err
	}
	return nil
}

func (userExtend *UserExtend) UpdateWithoutChatRecordJsonData() error {
	var err error
	if userExtend.Id == 0 {
		err = DB.Omit("ChatRecordJsonData").Save(userExtend).Error
	} else {
		err = DB.Model(userExtend).Omit("ChatRecordJsonData").Updates(userExtend).Error
	}
	if err != nil {
		return err
	}
	return nil
}

func (userExtend *UserExtend) Delete() error {
	var err error
	err = DB.Delete(userExtend).Error
	if err != nil {
		return err
	}
	return nil
}

func (userExtend *UserExtend) Get() error {
	var err error
	err = DB.First(userExtend).Error
	if err != nil {
		return err
	}
	return nil
}

func GetUserExByUserId(userId int) (*UserExtend, error) {
	var userExtend *UserExtend
	var err error
	err = DB.Where("user_id = ?", userId).First(&userExtend).Error
	if err != nil {
		return nil, err
	}
	return userExtend, nil
}

func GetChatRecordByUserId(userId int) (*UserExtend, error) {
	var userExtend *UserExtend
	var err error
	err = DB.Where("user_id = ?", userId).First(&userExtend).Error
	if err != nil {
		return nil, err
	}
	return userExtend, nil
}

func (userExtend *UserExtend) SaveChatRecordByUserId() error {
	var err error
	// 先判断是否存在userExtend记录
	err = userExtend.Get()
	if err != nil {
		// 不存在,则插入
		err = userExtend.Insert()
		if err != nil {
			return err
		}
		return nil
	}
	// 只修改ChatRecordJsonData这一个字段,根据user_id修改
	result := DB.Model(userExtend).Select("chat_record_json_data").Where("user_id = ?", userExtend.UserId).Updates(userExtend)
	if result.Error != nil {
		return result.Error
	}
	return err
}

// DeleteChatRecordByUserId 删除聊天记录
func (userExtend *UserExtend) DeleteChatRecordByUserId() error {
	var err error
	// 只修改ChatRecordJsonData这一个字段
	result := DB.Model(userExtend).Select("chat_record_json_data").Updates(userExtend)
	if result.Error != nil {
		return result.Error
	}
	return err
}

// VerifyUserEducationCertification 验证用户是否教育认证
func VerifyUserEducationCertification(userId int) (bool, error) {
	// TODO：先判断是否存在userExtend记录
	var userExtend *UserExtend
	var err error
	err = DB.Where("user_id = ?", userId).First(&userExtend).Error
	if err != nil {
		return false, err
	}
	return userExtend.EducationCertification, nil
}

// GetAllUserExtendExceptChatRecord 获取所有用户扩展信息，除了聊天记录
func GetAllUserExtendExceptChatRecord() ([]*UserExtend, error) {
	var userExtend []*UserExtend
	var err error
	err = DB.Select("id,user_id,warning_quota,setting_json_data,education_certification").Find(&userExtend).Error
	if err != nil {
		return nil, err
	}
	return userExtend, nil
}

// GetUserExExceptChatRecordById 获取自己的用户扩展信息/管理员根据 id 获取，除了聊天记录
func GetUserExExceptChatRecordById(userId int) (*UserExtend, error) {
	var userExtend *UserExtend
	var err error
	err = DB.Select("id,user_id,warning_quota,setting_json_data,education_certification").Where("user_id = ?", userId).First(&userExtend).Error
	if err != nil {
		return nil, err
	}
	return userExtend, nil
}

func GetModelRatio(userId int, modelName string) (float64, bool, string, error) {
	var userExtend *UserExtend
	var err error
	err = DB.Where("user_id = ?", userId).First(&userExtend).Error
	if err != nil {
		return 30, false, "", err
	}

	// 获取系统默认的ModelRatio
	systemModelRatio := billingratio.GetModelRatioMap()

	// 用户自定义的ModelRatio
	var userModelRatio map[string]float64
	if userExtend.GetModelRatio() != "" {
		err = json.Unmarshal([]byte(userExtend.GetModelRatio()), &userModelRatio)
		if err != nil {
			return 30, false, "", err
		}
	} else {
		return 30, false, "", nil
	}
	// 创建一个新的map来存储合并后的结果
	mergedModelRatio := make(map[string]float64)
	for k, v := range systemModelRatio {
		mergedModelRatio[k] = v
	}
	for k, v := range userModelRatio {
		mergedModelRatio[k] = v
	}

	// 将合并后的结果转换为JSON字符串
	mergedModelRatioJSON, err := json.Marshal(mergedModelRatio)
	if err != nil {
		return 30, false, "", err
	}

	// 返回合并后的结果
	if ratio, ok := mergedModelRatio[modelName]; ok {
		return ratio, true, string(mergedModelRatioJSON), nil
	}
	return 30, false, string(mergedModelRatioJSON), nil
}

func GetModelFixedPrice(userId int, modelName string) (float64, bool, string, error) {
	var userExtend *UserExtend
	var err error
	err = DB.Where("user_id = ?", userId).First(&userExtend).Error
	if err != nil {
		return 30, false, "", err
	}

	// 获取系统默认的ModelFixedPrice
	systemModelFixedPrice := billingratio.GetModelFixedPriceMap()

	// 用户自定义的ModelFixedPrice
	var userModelFixedPrice map[string]float64
	if userExtend.GetModelFixedPrice() != "" {
		err = json.Unmarshal([]byte(userExtend.GetModelFixedPrice()), &userModelFixedPrice)
		if err != nil {
			return 30, false, "", err
		}
	} else {
		return 30, false, "", nil
	}

	// 创建一个新的map来存储合并后的结果
	mergedModelFixedPrice := make(map[string]float64)
	for k, v := range systemModelFixedPrice {
		mergedModelFixedPrice[k] = v
	}
	for k, v := range userModelFixedPrice {
		mergedModelFixedPrice[k] = v
	}

	// 将合并后的结果转换为JSON字符串
	mergedModelFixedPriceJSON, err := json.Marshal(mergedModelFixedPrice)
	if err != nil {
		return 30, false, "", err
	}

	// 返回合并后的结果
	if price, ok := mergedModelFixedPrice[modelName]; ok {
		return price, true, string(mergedModelFixedPriceJSON), nil
	}
	return 30, false, string(mergedModelFixedPriceJSON), nil
}

func GetCompletionRatio(userId int, modelName string) (float64, bool, string, error) {
	var userExtend *UserExtend
	var err error
	err = DB.Where("user_id = ?", userId).First(&userExtend).Error
	if err != nil {
		return 5, false, "", err
	}

	// 获取系统默认的CompletionRatio
	systemCompletionRatio := billingratio.GetCompletionRatioMap()

	// 用户自定义的CompletionRatio
	var userCompletionRatio map[string]float64
	if userExtend.GetCompletionRatio() != "" {
		err = json.Unmarshal([]byte(userExtend.GetCompletionRatio()), &userCompletionRatio)
		if err != nil {
			return 5, false, "", err
		}
	} else {
		return 5, false, "", nil
	}

	// 创建一个新的map来存储合并后的结果
	mergedCompletionRatio := make(map[string]float64)
	for k, v := range systemCompletionRatio {
		mergedCompletionRatio[k] = v
	}
	for k, v := range userCompletionRatio {
		mergedCompletionRatio[k] = v
	}

	// 将合并后的结果转换为JSON字符串
	mergedCompletionRatioJSON, err := json.Marshal(mergedCompletionRatio)
	if err != nil {
		return 5, false, "", err
	}

	// 返回合并后的结果
	if ratio, ok := mergedCompletionRatio[modelName]; ok {
		return ratio, true, string(mergedCompletionRatioJSON), nil
	}
	return 5, false, string(mergedCompletionRatioJSON), nil
}

func (userExtend *UserExtend) GetTopupRatio() float64 {
	if userExtend.TopupRatio == nil {
		return 0
	}
	return *userExtend.TopupRatio
}

// NewTikTokenBilling
func (userExtend *UserExtend) GetNewTikTokenBilling() int {
	if userExtend.NewTikTokenBilling == nil {
		return 0
	}
	return int(*userExtend.NewTikTokenBilling)
}

// GetUserNewTikTokenBillingByUserId 获取用户新版tiktoken计费配置
func GetUserNewTikTokenBillingByUserId(userId int) (int, error) {
	var userExtend *UserExtend
	var err error
	// 优化性能,只查询NewTikTokenBilling和UserId
	err = DB.Select("new_tik_token_billing,user_id").Where("user_id = ?", userId).First(&userExtend).Error
	if err != nil {
		return 0, err
	}
	if userExtend.NewTikTokenBilling == nil {
		return 0, nil
	}
	return userExtend.GetNewTikTokenBilling(), nil

}

func (userExtend *UserExtend) TransferAffQuotaToQuota(ctx context.Context, quota int64) error {
	var err error
	user := &User{}
	user.Id = userExtend.UserId
	tx := DB.Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()
	// 先校验用户aff_quota余额是否充足
	if userExtend.AffQuota < quota {
		return errors.New("邀请奖励金余额不足")
	}
	if err = tx.Model(user).Update("quota", gorm.Expr("quota + ?", quota)).Error; err != nil {
		return err
	}
	if err = tx.Model(userExtend).Update("aff_quota", gorm.Expr("aff_quota - ?", quota)).Error; err != nil {
		return err
	}
	RecordLog(ctx, userExtend.UserId, LogTypeInviteBonusTransfer, fmt.Sprintf("用户 [%d] 邀请奖励金转成余额成功, 转出 %s 邀请奖励金", userExtend.UserId, common.LogQuota(quota)))
	return err
}

// 检查扩展信息是否存在并且如果不存在就自动创建,带事务
func CheckUserExtendAndCreateIfNotExist(userId int, tx *gorm.DB) (*UserExtend, error) {
	userExtend := &UserExtend{}
	err := tx.Where("user_id = ?", userId).First(userExtend).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			userExtend.UserId = userId
			err = tx.Create(userExtend).Error
			if err != nil {
				return nil, err
			}
		} else {
			return nil, err
		}
	}
	return userExtend, nil
}

func (userExtend *UserExtend) GetMJSensitiveWordsRefund() int {
	if userExtend.MJSensitiveWordsRefund == nil {
		return 0 // 返回0表示使用系统默认配置
	}
	return int(*userExtend.MJSensitiveWordsRefund)
}

// 新增方法: 获取用户的分组折扣
func (userExtend *UserExtend) GetGroupDiscounts() (map[string]float64, error) {
	var groupDiscounts map[string]float64
	if userExtend.GroupDiscounts == "" {
		return make(map[string]float64), nil
	}
	err := json.Unmarshal([]byte(userExtend.GroupDiscounts), &groupDiscounts)
	if err != nil {
		return nil, err
	}
	return groupDiscounts, nil
}

// 新增方法: 设置用户的分组折扣
func (userExtend *UserExtend) SetGroupDiscounts(groupDiscounts map[string]float64) error {
	jsonData, err := json.Marshal(groupDiscounts)
	if err != nil {
		return err
	}
	userExtend.GroupDiscounts = string(jsonData)
	return nil
}

// 新增方法: 获取特定分组的折扣
func (userExtend *UserExtend) GetGroupDiscount(group string) (float64, error) {
	groupDiscounts, err := userExtend.GetGroupDiscounts()
	if err != nil {
		return 1.0, err
	}
	if discount, ok := groupDiscounts[group]; ok {
		return discount, nil
	}
	return 1.0, nil // 如果没有找到对应的折扣,返回默认值 1.0
}

// 新增方法: 更新特定分组的折扣
func (userExtend *UserExtend) UpdateGroupDiscount(group string, discount float64) error {
	groupDiscounts, err := userExtend.GetGroupDiscounts()
	if err != nil {
		return err
	}
	groupDiscounts[group] = discount
	return userExtend.SetGroupDiscounts(groupDiscounts)
}

// GetLogDetailEnabled 获取用户是否记录详细日志配置
func (userExtend *UserExtend) GetLogDetailEnabled() int {
	if userExtend.LogDetailEnabled == nil {
		return 0 // 返回0表示使用系统默认配置
	}
	return int(*userExtend.LogDetailEnabled)
}

// GetLogUpstreamResponseEnabled 获取用户是否记录上游响应配置
func (userExtend *UserExtend) GetLogUpstreamResponseEnabled() int {
	if userExtend.LogUpstreamResponseEnabled == nil {
		return 0 // 返回0表示使用系统默认配置
	}
	return int(*userExtend.LogUpstreamResponseEnabled)
}

// GetLogFullResponseEnabled 获取用户是否记录完整响应配置
func (userExtend *UserExtend) GetLogFullResponseEnabled() int {
	if userExtend.LogFullResponseEnabled == nil {
		return 0 // 返回0表示使用系统默认配置
	}
	return int(*userExtend.LogFullResponseEnabled)
}

// GetUserLogDetailEnabledByUserId 获取用户是否记录详细日志配置
func GetUserLogDetailEnabledByUserId(userId int) (int, error) {
	var userExtend *UserExtend
	var err error
	// 优化性能,只查询LogDetailEnabled和UserId
	err = DB.Select("log_detail_enabled,user_id").Where("user_id = ?", userId).First(&userExtend).Error
	if err != nil {
		return 0, err
	}
	return userExtend.GetLogDetailEnabled(), nil
}

// GetUserLogUpstreamResponseEnabledByUserId 获取用户是否记录上游响应配置
func GetUserLogUpstreamResponseEnabledByUserId(userId int) (int, error) {
	var userExtend *UserExtend
	var err error
	// 优化性能,只查询LogUpstreamResponseEnabled和UserId
	err = DB.Select("log_upstream_response_enabled,user_id").Where("user_id = ?", userId).First(&userExtend).Error
	if err != nil {
		return 0, err
	}
	return userExtend.GetLogUpstreamResponseEnabled(), nil
}

// GetUserLogFullResponseEnabledByUserId 获取用户是否记录完整响应配置
func GetUserLogFullResponseEnabledByUserId(userId int) (int, error) {
	var userExtend *UserExtend
	var err error
	// 优化性能,只查询LogFullResponseEnabled和UserId
	err = DB.Select("log_full_response_enabled,user_id").Where("user_id = ?", userId).First(&userExtend).Error
	if err != nil {
		return 0, err
	}
	return userExtend.GetLogFullResponseEnabled(), nil
}
func (userExtend *UserExtend) GetModelRatio() string {
	if userExtend.ModelRatio == nil {
		return ""
	}
	return *userExtend.ModelRatio
}

func (userExtend *UserExtend) GetModelFixedPrice() string {
	if userExtend.ModelFixedPrice == nil {
		return ""
	}
	return *userExtend.ModelFixedPrice
}

func (userExtend *UserExtend) GetCompletionRatio() string {
	if userExtend.CompletionRatio == nil {
		return ""
	}
	return *userExtend.CompletionRatio
}

// 获取用户是否信任上游流式用量统计配置
func (userExtend *UserExtend) GetTrustUpstreamStreamUsageEnabled() int {
	if userExtend.TrustUpstreamStreamUsage == nil {
		return 0 // 返回0表示使用系统默认配置
	}
	return int(*userExtend.TrustUpstreamStreamUsage)
}

// 获取用户是否强制要求上游返回用量配置
func (userExtend *UserExtend) GetForceStreamOptionEnabled() int {
	if userExtend.ForceStreamOption == nil {
		return 0 // 返回0表示使用系统默认配置
	}
	return int(*userExtend.ForceStreamOption)
}

// 获取用户是否强制返回下游流式用量配置
func (userExtend *UserExtend) GetForceDownstreamStreamUsageEnabled() int {
	if userExtend.ForceDownstreamStreamUsage == nil {
		return 0 // 返回0表示使用系统默认配置
	}
	return int(*userExtend.ForceDownstreamStreamUsage)
}

// 根据用户ID获取用户的流式用量配置
func GetUserStreamConfigByUserId(userId int) (trustUpstream int, forceOption int, forceDownstream int, err error) {
	var userExtend *UserExtend
	// 优化性能,只查询需要的字段
	err = DB.Select("trust_upstream_stream_usage,force_stream_option,force_downstream_stream_usage,user_id").
		Where("user_id = ?", userId).First(&userExtend).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果记录不存在，返回默认值0（使用系统配置）
			return 0, 0, 0, nil
		}
		return 0, 0, 0, err
	}
	return userExtend.GetTrustUpstreamStreamUsageEnabled(),
		userExtend.GetForceStreamOptionEnabled(),
		userExtend.GetForceDownstreamStreamUsageEnabled(),
		nil
}

// 添加获取方法
func (userExtend *UserExtend) GetChannelScoreRoutingEnabled() int {
	if userExtend.ChannelScoreRouting == nil {
		return 0 // 返回0表示使用系统默认配置
	}
	return int(*userExtend.ChannelScoreRouting)
}

// 根据用户ID获取用户的渠道路由配置
func GetUserChannelScoreRoutingByUserId(userId int) (int, error) {
	var userExtend *UserExtend
	var err error
	// 优化性能,只查询需要的字段
	err = DB.Select("channel_score_routing,user_id").
		Where("user_id = ?", userId).First(&userExtend).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果记录不存在，返回默认值0（使用系统配置）
			return 0, nil
		}
		return 0, err
	}
	return userExtend.GetChannelScoreRoutingEnabled(), nil
}

// GetSpecialSettingsUserIds 获取所有设置了特殊金额相关配置的用户ID列表
func GetSpecialSettingsUserIds() ([]int, error) {
	var userIds []int
	var err error

	// 查询所有在UserExtend表中设置了特殊金额相关字段的用户
	// 这些字段包括：ModelRatio, ModelFixedPrice, CompletionRatio, TopupRatio,
	// NewTikTokenBilling, MJSensitiveWordsRefund, GroupDiscounts
	err = DB.Model(&UserExtend{}).
		Where("model_ratio IS NOT NULL AND model_ratio != ''").
		Or("model_fixed_price IS NOT NULL AND model_fixed_price != ''").
		Or("completion_ratio IS NOT NULL AND completion_ratio != ''").
		Or("topup_ratio IS NOT NULL AND topup_ratio != 0").
		Or("new_tik_token_billing IS NOT NULL AND new_tik_token_billing != 0").
		Or("mj_sensitive_words_refund IS NOT NULL AND mj_sensitive_words_refund != 0").
		Or("group_discounts IS NOT NULL AND group_discounts != ''").
		Pluck("user_id", &userIds).Error

	return userIds, err
}

// FilterUsersWithSpecialSettings 在已有的用户查询基础上增加特殊设置过滤条件
func FilterUsersWithSpecialSettings(tx *gorm.DB, hasSpecialSettings bool) *gorm.DB {
	// 获取所有有特殊设置的用户ID
	specialUserIds, err := GetSpecialSettingsUserIds()
	if err != nil || len(specialUserIds) == 0 {
		if hasSpecialSettings {
			// 如果要求有特殊设置但没找到任何用户，返回不可能匹配的条件
			return tx.Where("1 = 0")
		}
		// 如果出错或没有特殊设置的用户，且不要求特殊设置，则不添加额外条件
		return tx
	}

	if hasSpecialSettings {
		// 查找有特殊设置的用户
		return tx.Where("id IN ?", specialUserIds)
	} else {
		// 查找没有特殊设置的用户
		return tx.Where("id NOT IN ?", specialUserIds)
	}
}

// 获取用户是否开启吃掉拨测功能配置
func (userExtend *UserExtend) GetSay1DirectSuccessModeEnabled() int {
	if userExtend.Say1DirectSuccessMode == nil {
		return 0 // 返回0表示使用系统默认配置
	}
	return int(*userExtend.Say1DirectSuccessMode)
}

// 根据用户ID获取用户是否开启吃掉拨测功能配置
func GetUserSay1DirectSuccessModeByUserId(userId int) (int, error) {
	var userExtend *UserExtend
	var err error
	// 优化性能,只查询需要的字段
	err = DB.Select("say1_direct_success_mode,user_id").
		Where("user_id = ?", userId).First(&userExtend).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果记录不存在，返回默认值0（使用系统配置）
			return 0, nil
		}
		return 0, err
	}
	return userExtend.GetSay1DirectSuccessModeEnabled(), nil
}

// 获取用户是否启用模拟OpenAI官方响应格式配置
func (userExtend *UserExtend) GetMockOpenAICompleteFormatEnabled() int {
	if userExtend.MockOpenAICompleteFormat == nil {
		return 0 // 返回0表示使用系统默认配置
	}
	return int(*userExtend.MockOpenAICompleteFormat)
}

// 根据用户ID获取用户是否启用模拟OpenAI官方响应格式配置
func GetUserMockOpenAICompleteFormatByUserId(userId int) (int, error) {
	var userExtend *UserExtend
	var err error
	// 优化性能,只查询需要的字段
	err = DB.Select("mock_openai_complete_format,user_id").
		Where("user_id = ?", userId).First(&userExtend).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果记录不存在，返回默认值0（使用系统配置）
			return 0, nil
		}
		return 0, err
	}
	return userExtend.GetMockOpenAICompleteFormatEnabled(), nil
}

// 更新用户是否启用模拟OpenAI官方响应格式配置
func UpdateUserMockOpenAICompleteFormat(userId int, mode int) error {
	var userExtend *UserExtend
	err := DB.Where("user_id = ?", userId).First(&userExtend).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 如果记录不存在，则创建一个新记录
		modeVal := int8(mode) // 将mode赋值给一个变量，然后取其地址
		userExtend = &UserExtend{
			UserId:                   userId,
			MockOpenAICompleteFormat: &modeVal,
		}
		err = userExtend.Insert()
		if err != nil {
			return err
		}
		return nil
	}

	// 如果记录存在，则更新
	modeVal := int8(mode)
	userExtend.MockOpenAICompleteFormat = &modeVal
	err = userExtend.Update()
	if err != nil {
		return err
	}
	return nil
}

// 获取用户额外可见分组
func (userExtend *UserExtend) GetExtraVisibleGroups() string {
	if userExtend.ExtraVisibleGroups == nil {
		return ""
	}
	return *userExtend.ExtraVisibleGroups
}

// 根据用户ID获取用户额外可见分组
func GetUserExtraVisibleGroupsByUserId(userId int) (string, error) {
	var userExtend *UserExtend
	var err error
	// 优化性能,只查询需要的字段
	err = DB.Select("extra_visible_groups,user_id").
		Where("user_id = ?", userId).First(&userExtend).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果记录不存在，返回空字符串
			return "", nil
		}
		return "", err
	}
	return userExtend.GetExtraVisibleGroups(), nil
}

// AddUserExtraVisibleGroupsToGroupList 将用户额外可见分组添加到分组列表中（用于字符串列表）
func AddUserExtraVisibleGroupsToGroupList(userId int, groupNames []string) []string {
	if userId <= 0 {
		return groupNames
	}

	extraGroups, err := CacheGetUserExtraVisibleGroups(userId)
	if err != nil || extraGroups == "" {
		return groupNames
	}

	extraGroupsList := strings.Split(extraGroups, ",")
	for _, group := range extraGroupsList {
		group = strings.TrimSpace(group)
		if group != "" {
			// 检查是否已存在，避免重复添加
			exists := false
			for _, existingGroup := range groupNames {
				if existingGroup == group {
					exists = true
					break
				}
			}
			if !exists {
				groupNames = append(groupNames, group)
			}
		}
	}

	return groupNames
}

// AddUserExtraVisibleGroupsToGroupWithRatioList 将用户额外可见分组添加到GroupWithRatio列表中
func AddUserExtraVisibleGroupsToGroupWithRatioList(userId int, userGroup string, groups []*GroupWithRatio) ([]*GroupWithRatio, error) {
	if userId <= 0 {
		return groups, nil
	}

	extraGroups, err := CacheGetUserExtraVisibleGroups(userId)
	if err != nil || extraGroups == "" {
		return groups, nil
	}

	// 获取用户当前分组信息，用于计算转换率
	currentGroup, err := CacheGetGroupByName(userGroup)
	if err != nil {
		return groups, err
	}

	extraGroupsList := strings.Split(extraGroups, ",")
	for _, groupName := range extraGroupsList {
		groupName = strings.TrimSpace(groupName)
		if groupName != "" {
			// 检查该分组是否已经在结果中
			exists := false
			for _, group := range groups {
				if group.Name == groupName {
					exists = true
					break
				}
			}
			// 如果不存在，尝试获取该分组并添加
			if !exists {
				extraGroup, err := CacheGetGroupByName(groupName)
				if err == nil && extraGroup != nil {
					// 计算转换率
					convertRatio := 1.0
					if currentGroup.GroupRatio > 0 && extraGroup.GroupRatio > 0 {
						convertRatio = extraGroup.GroupRatio / currentGroup.GroupRatio
					}
					groups = append(groups, &GroupWithRatio{
						Group:             extraGroup,
						ConvertRatio:      convertRatio,
						CurrentGroupRatio: currentGroup.GroupRatio,
						CurrentTopupRatio: currentGroup.TopupGroupRatio,
					})
				}
			}
		}
	}

	return groups, nil
}

// GetLogDownstreamErrorEnabled 获取用户是否记录下游错误配置
func (userExtend *UserExtend) GetLogDownstreamErrorEnabled() int {
	if userExtend.LogDownstreamErrorEnabled == nil {
		return 0 // 返回0表示使用系统默认配置
	}
	return int(*userExtend.LogDownstreamErrorEnabled)
}

// GetClaudeMessageNormalizationEnabled 获取用户是否启用Claude消息整理功能配置
func (userExtend *UserExtend) GetClaudeMessageNormalizationEnabled() int {
	if userExtend.ClaudeMessageNormalization == nil {
		return 0 // 返回0表示使用系统默认配置
	}
	return int(*userExtend.ClaudeMessageNormalization)
}

// GetUserLogDownstreamErrorEnabledByUserId 获取用户是否记录下游错误配置
func GetUserLogDownstreamErrorEnabledByUserId(userId int) (int, error) {
	var userExtend *UserExtend
	var err error
	// 优化性能,只查询LogDownstreamErrorEnabled和UserId
	err = DB.Select("log_downstream_error_enabled,user_id").Where("user_id = ?", userId).First(&userExtend).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果记录不存在，返回默认值0（使用系统配置）
			return 0, nil
		}
		return 0, err
	}
	return userExtend.GetLogDownstreamErrorEnabled(), nil
}

// GetMaxPromptLogLength 获取用户个性化日志prompt长度限制配置
func (userExtend *UserExtend) GetMaxPromptLogLength() int64 {
	if userExtend.MaxPromptLogLength == nil || *userExtend.MaxPromptLogLength == 0 {
		return 0 // 返回0表示使用系统默认配置
	}
	return *userExtend.MaxPromptLogLength
}

// GetUserMaxPromptLogLengthByUserId 获取用户个性化日志prompt长度限制配置
func GetUserMaxPromptLogLengthByUserId(userId int) (int64, error) {
	var userExtend *UserExtend
	var err error
	// 优化性能,只查询MaxPromptLogLength和UserId
	err = DB.Select("max_prompt_log_length,user_id").Where("user_id = ?", userId).First(&userExtend).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果记录不存在，返回默认值0（使用系统配置）
			return 0, nil
		}
		return 0, err
	}
	return userExtend.GetMaxPromptLogLength(), nil
}

// GetUserClaudeMessageNormalizationEnabledByUserId 获取用户是否启用Claude消息整理功能配置
func GetUserClaudeMessageNormalizationEnabledByUserId(userId int) (int, error) {
	var userExtend *UserExtend
	var err error
	// 优化性能,只查询ClaudeMessageNormalization和UserId
	err = DB.Select("claude_message_normalization,user_id").Where("user_id = ?", userId).First(&userExtend).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果记录不存在，返回默认值0（使用系统配置）
			return 0, nil
		}
		return 0, err
	}
	return userExtend.GetClaudeMessageNormalizationEnabled(), nil
}

// GetRouteDiscounts 获取用户的动态路由折扣配置
func (userExtend *UserExtend) GetRouteDiscounts() (map[string]float64, error) {
	if userExtend.RouteDiscounts == "" {
		return make(map[string]float64), nil
	}
	var discounts map[string]float64
	err := json.Unmarshal([]byte(userExtend.RouteDiscounts), &discounts)
	if err != nil {
		return make(map[string]float64), err
	}
	return discounts, nil
}

// SetRouteDiscounts 设置用户的动态路由折扣配置
func (userExtend *UserExtend) SetRouteDiscounts(routeDiscounts map[string]float64) error {
	data, err := json.Marshal(routeDiscounts)
	if err != nil {
		return err
	}
	userExtend.RouteDiscounts = string(data)
	return nil
}

// GetRouteDiscount 获取指定路由的折扣率
func (userExtend *UserExtend) GetRouteDiscount(route string) (float64, error) {
	discounts, err := userExtend.GetRouteDiscounts()
	if err != nil {
		return 1.0, err
	}
	if discount, ok := discounts[route]; ok {
		return discount, nil
	}
	return 1.0, nil // 默认无折扣
}

// UpdateRouteDiscount 更新指定路由的折扣率
func (userExtend *UserExtend) UpdateRouteDiscount(route string, discount float64) error {
	discounts, err := userExtend.GetRouteDiscounts()
	if err != nil {
		return err
	}
	discounts[route] = discount
	return userExtend.SetRouteDiscounts(discounts)
}

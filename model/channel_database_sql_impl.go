package model

import (
	"context"
	"errors"

	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/logger"
	"gorm.io/gorm"
)

// SQLDatabaseImpl SQL数据库实现
type SQLDatabaseImpl struct{}

// Channel operations
func (s *SQLDatabaseImpl) GetChannelById(id int, selectAll bool) (*Channel, error) {
	channel := Channel{Id: id}
	var err error = nil
	if selectAll {
		err = DB.First(&channel, "id = ?", id).Error
	} else {
		err = DB.Omit("key").First(&channel, "id = ?", id).Error
	}
	return &channel, err
}

func (s *SQLDatabaseImpl) GetAllChannels(startIdx int, num int, orderByStr string, selectAll bool, id int, name string, key string,
	group string, models string, channelGroupId int, status int, billingType int, baseUrl string,
	overFrequencyAutoDisable string, scope string, disableReason string) ([]*Channel, error) {
	return GetAllChannels(startIdx, num, orderByStr, selectAll, id, name, key, group, models, channelGroupId, status, billingType, baseUrl, overFrequencyAutoDisable, scope, disableReason)
}

func (s *SQLDatabaseImpl) SearchChannels(startIdx int, num int, keyword string, channelGroupId int) ([]*Channel, error) {
	return SearchChannels(startIdx, num, keyword, channelGroupId)
}

func (s *SQLDatabaseImpl) CountChannels(keyword string, id int, name string, key string, group string, models string, channelGroupId int, status int, billingType int, baseUrl string, overFrequencyAutoDisable string, disableReason string) (int64, error) {
	return CountChannels(keyword, id, name, key, group, models, channelGroupId, status, billingType, baseUrl, overFrequencyAutoDisable, disableReason)
}

func (s *SQLDatabaseImpl) InsertChannel(channel *Channel) error {
	return channel.Insert()
}

func (s *SQLDatabaseImpl) UpdateChannel(channel *Channel) error {
	return channel.Update()
}

func (s *SQLDatabaseImpl) DeleteChannel(channel *Channel) error {
	return channel.Delete()
}

func (s *SQLDatabaseImpl) BatchInsertChannels(channels []Channel) error {
	return BatchInsertChannels(channels)
}

func (s *SQLDatabaseImpl) UpdateChannelStatusById(id int, status int) error {
	err := DB.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(&Ability{}).Where("channel_id = ?", id).Select("enabled").Update("enabled", status == common.ChannelStatusEnabled).Error
		if err != nil {
			logger.SysError("failed to update ability status: " + err.Error())
			return err
		}
		err = tx.Model(&Channel{}).Where("id = ?", id).Update("status", status).Error
		if err != nil {
			logger.SysError("failed to update channel status: " + err.Error())
		}
		return err
	})
	if err != nil {
		logger.SysError("tx failed to update channel status: " + err.Error())
	}
	return err
}

func (s *SQLDatabaseImpl) UpdateChannelAbilityStatusByIdReturnErr(id int, requestModel string, status int) error {
	// 先检查渠道状态
	var channel Channel
	err := DB.Where("id = ?", id).First(&channel).Error
	if err != nil {
		return err
	}
	// 如果是手动禁用状态，直接返回
	if channel.Status == ChannelStatusManuallyDisabled {
		return errors.New("channel is manually disabled")
	}

	err = DB.Transaction(func(tx *gorm.DB) error {
		// 更新指定的 Ability 状态
		err := tx.Model(&Ability{}).Where("channel_id = ? and model = ?", id, requestModel).Select("enabled").Update("enabled", status == common.ChannelStatusEnabled).Error
		if err != nil {
			logger.SysError("failed to update ability status: " + err.Error())
			return err
		}

		if status != common.ChannelStatusEnabled {
			// 检查是否所有 Ability 都被禁用
			var enabledAbilityCount int64
			err = tx.Model(&Ability{}).Where("channel_id = ? AND enabled = ?", id, true).Count(&enabledAbilityCount).Error
			if err != nil {
				logger.SysError("failed to count enabled abilities: " + err.Error())
				return err
			}

			if enabledAbilityCount == 0 {
				// 所有 Ability 都被禁用，将渠道状态更新为完全禁用
				err = tx.Model(&Channel{}).Where("id = ?", id).Update("status", common.ChannelStatusAutoDisabled).Error
			} else {
				// 还有启用的 Ability，将渠道状态更新为部分禁用
				err = tx.Model(&Channel{}).Where("id = ?", id).Update("status", common.ChannelStatusPartiallyAutoDisabled).Error
			}
		} else {
			// 查询除了当前更新的 Ability 之外的所有 Ability
			var disabledAbilityCount int64
			err = tx.Model(&Ability{}).Where("channel_id = ? AND model != ? AND enabled = ?", id, requestModel, false).Count(&disabledAbilityCount).Error
			if err != nil {
				logger.SysError("failed to count disabled abilities: " + err.Error())
				return err
			}

			// 如果没有禁用的 Ability，则将渠道状态更新为完全启用
			if disabledAbilityCount == 0 {
				err = tx.Model(&Channel{}).Where("id = ?", id).Update("status", common.ChannelStatusEnabled).Error
			} else {
				// 还有禁用的 Ability，保持部分禁用状态
				err = tx.Model(&Channel{}).Where("id = ?", id).Update("status", common.ChannelStatusPartiallyAutoDisabled).Error
			}
		}

		return err
	})

	return err
}

func (s *SQLDatabaseImpl) UpdateChannelDisableReasonById(id int, disableReason string) error {
	UpdateChannelDisableReasonById(id, disableReason)
	return nil
}

func (s *SQLDatabaseImpl) UpdateChannelUsedQuota(id int, quota int64) error {
	if config.BatchUpdateEnabled {
		addNewRecord(BatchUpdateTypeChannelUsedQuota, id, quota)
		return nil
	}
	return s.updateChannelUsedQuotaDirect(id, quota)
}

// updateChannelUsedQuotaDirect 直接更新渠道使用配额
func (s *SQLDatabaseImpl) updateChannelUsedQuotaDirect(id int, quota int64) error {
	err := DB.Model(&Channel{}).Where("id = ?", id).Update("used_quota", gorm.Expr("used_quota + ?", quota)).Error
	if err != nil {
		logger.SysError("failed to update channel used quota: " + err.Error())
	}
	return err
}

func (s *SQLDatabaseImpl) UpdateChannelBalance(id int, balance float64) error {
	channel := &Channel{Id: id}
	channel.UpdateBalance(balance)
	return nil
}

func (s *SQLDatabaseImpl) UpdateChannelResponseTime(id int, responseTime int64) error {
	channel := &Channel{Id: id}
	channel.UpdateResponseTime(responseTime)
	return nil
}

func (s *SQLDatabaseImpl) DeleteChannelByStatus(status int64) (int64, error) {
	return DeleteChannelByStatus(status)
}

func (s *SQLDatabaseImpl) DeleteChannelByType(deleteType int) (int64, error) {
	return DeleteChannelByType(deleteType)
}

func (s *SQLDatabaseImpl) DeleteChannelByDisableReason(disableReason string) (int64, error) {
	return DeleteChannelByDisableReason(disableReason)
}

func (s *SQLDatabaseImpl) DeleteChannelByIds(ids []int) (int64, error) {
	return DeleteChannelByIds(ids)
}

func (s *SQLDatabaseImpl) CountChannelByStatus() (map[int]int, error) {
	return CountChannelByStatus()
}

func (s *SQLDatabaseImpl) GetChannelModelMappingsBySudoGroup(group string) ([]Channel, error) {
	return GetChannelModelMappingsBySudoGroup(group)
}

func (s *SQLDatabaseImpl) GetAllModels() ([]string, error) {
	return GetAllModels()
}

// Ability operations
func (s *SQLDatabaseImpl) GetAllAbilities(startIdx int, num int, orderBy string, group string, model string, channelId int, enabled bool) ([]*Ability, error) {
	return GetAllAbilities(startIdx, num, orderBy, group, model, channelId, enabled)
}

func (s *SQLDatabaseImpl) CountAbilities(group string, model string, channelId int, enabled bool) (int64, error) {
	return CountAbilities(group, model, channelId, enabled)
}

func (s *SQLDatabaseImpl) InsertAbility(ability *Ability) error {
	return ability.Insert()
}

func (s *SQLDatabaseImpl) UpdateAbility(ability *Ability) error {
	return ability.Update()
}

func (s *SQLDatabaseImpl) DeleteAbility(ability *Ability) error {
	return ability.Delete()
}

func (s *SQLDatabaseImpl) DeleteAbilitiesByIds(ids []int) (int64, error) {
	return DeleteAbilitiesByIds(ids)
}

func (s *SQLDatabaseImpl) GetAbilityByGroupModelChannel(group string, model string, channelId int) (*Ability, error) {
	return GetAbilityByGroupModelChannel(group, model, channelId)
}

func (s *SQLDatabaseImpl) UpdateAbilityEnabledStatus(group string, model string, channelId int, enabled bool) error {
	return UpdateAbilityEnabledStatus(group, model, channelId, enabled)
}

func (s *SQLDatabaseImpl) GetAbilitiesByChannelId(channelId int) ([]*Ability, error) {
	return GetAbilitiesByChannelId(channelId)
}

func (s *SQLDatabaseImpl) GetRandomSatisfiedChannel(group string, model string, tokenBillingType int, inputHasFunctionCall bool, inputHasImage bool, excludeIds []int, ignoreFirstPriority bool, isV1MessagesPath bool) (*Channel, error) {
	return GetRandomSatisfiedChannel(group, model, tokenBillingType, inputHasFunctionCall, inputHasImage, excludeIds, ignoreFirstPriority, isV1MessagesPath)
}

func (s *SQLDatabaseImpl) UpdateAbilityStatus(channelId int, status bool) error {
	return UpdateAbilityStatus(channelId, status)
}

func (s *SQLDatabaseImpl) GetGroupModels(ctx context.Context, group string) ([]string, error) {
	return GetGroupModels(ctx, group)
}

func (s *SQLDatabaseImpl) GetAvailableModelByGroup(group string) []string {
	return GetAvailableModelByGroup(group)
}

func (s *SQLDatabaseImpl) HasSearchSerperModel() bool {
	return HasSearchSerperModel()
}

func (s *SQLDatabaseImpl) DeleteAbilitiesNotExistsInChannel() (int64, error) {
	return DeleteAbilitiesNotExistsInChannel()
}

func (s *SQLDatabaseImpl) GetFirstAbilityByChannelIdAndModel(channelId int, model string) (*Ability, error) {
	return GetFirstAbilityByChannelIdAndModel(channelId, model)
}

func (s *SQLDatabaseImpl) GetAllDistinctModels() ([]string, error) {
	return GetAllDistinctModels()
}

func (s *SQLDatabaseImpl) GetAllEnabledDistinctModels() ([]string, error) {
	return GetAllEnabledDistinctModels()
}

// ChannelExtend operations
func (s *SQLDatabaseImpl) GetChannelExtendByChannelId(channelId int) (*ChannelExtend, error) {
	return GetChannelExtendByChannelId(channelId)
}

func (s *SQLDatabaseImpl) InsertChannelExtend(channelExtend *ChannelExtend) error {
	return channelExtend.Insert(DB)
}

func (s *SQLDatabaseImpl) UpdateChannelExtend(channelExtend *ChannelExtend) error {
	return channelExtend.Update(DB)
}

func (s *SQLDatabaseImpl) DeleteChannelExtend(channelExtend *ChannelExtend) error {
	return channelExtend.Delete(DB)
}

func (s *SQLDatabaseImpl) DeleteChannelExtendByChannelId(channelId int) error {
	return DeleteChannelExtendByChannelIdByTx(DB, channelId)
}

func (s *SQLDatabaseImpl) BatchInsertChannelExtends(channelExtends []ChannelExtend, sqlOnly bool) error {
	return BatchInsertChannelExtendsByTx(DB, channelExtends, sqlOnly)
}

// DeleteChannelExtendsNotExistsInChannel 删除不存在对应 channel 的 channel_extends 记录
func (s *SQLDatabaseImpl) DeleteChannelExtendsNotExistsInChannel() (int64, error) {
	result := DB.Where("not exists (SELECT 1 from channels where id = channel_id)").Delete(&ChannelExtend{})
	return result.RowsAffected, result.Error
}

// Batch operations
func (s *SQLDatabaseImpl) BatchUpdateChannelsToChannelGroup(channelIds []int, channelGroupId int) error {
	return BatchUpdateChannelsToChannelGroup(channelIds, channelGroupId)
}

// ChannelGroup operations
func (s *SQLDatabaseImpl) GetChannelGroupById(id int, selectAll bool) (*ChannelGroup, error) {
	return GetChannelGroupById(id, selectAll)
}

func (s *SQLDatabaseImpl) GetAllChannelGroups(startIdx int, num int, selectAll bool, id int, name string, status int, group string) ([]*ChannelGroup, error) {
	return GetAllChannelGroups(startIdx, num, selectAll, id, name, status, group)
}

func (s *SQLDatabaseImpl) SearchChannelGroups(startIdx int, num int, keyword string) ([]*ChannelGroup, error) {
	return SearchChannelGroups(startIdx, num, keyword)
}

func (s *SQLDatabaseImpl) CountChannelGroups(id int, name string, group string) (int64, error) {
	return CountChannelGroups(id, name, group)
}

func (s *SQLDatabaseImpl) InsertChannelGroup(channelGroup *ChannelGroup) error {
	return channelGroup.Insert()
}

func (s *SQLDatabaseImpl) UpdateChannelGroup(channelGroup *ChannelGroup) error {
	return channelGroup.Update()
}

func (s *SQLDatabaseImpl) DeleteChannelGroup(channelGroup *ChannelGroup) error {
	return channelGroup.Delete()
}

func (s *SQLDatabaseImpl) BatchInsertChannelGroups(channelGroups []ChannelGroup) error {
	return BatchInsertChannelGroups(channelGroups)
}

func (s *SQLDatabaseImpl) UpdateChannelGroupStatusById(id int, status int) error {
	UpdateChannelGroupStatusById(id, status)
	return nil
}

func (s *SQLDatabaseImpl) DeleteChannelGroupByStatus(status int64) (int64, error) {
	return DeleteChannelGroupByStatus(status)
}

func (s *SQLDatabaseImpl) DeleteDisabledChannelGroup() (int64, error) {
	return DeleteDisabledChannelGroup()
}

func (s *SQLDatabaseImpl) GetChannelBanStatistics(disableReason string) ([]*BanStatistics, error) {
	return GetChannelBanStatistics(disableReason)
}

func (s *SQLDatabaseImpl) GetChannelStatistics(statusFilter int, disableReason string, groupBy string) ([]*ChannelStatistics, error) {
	return getChannelStatisticsSQL(statusFilter, disableReason, groupBy)
}

func (s *SQLDatabaseImpl) GetChannelCreationSpeedStatistics(statusFilter int, disableReason string, groupBy string, timeGranularity string) (map[string][]*ChannelCreationSpeedItem, error) {
	return getChannelCreationSpeedStatisticsSQL(statusFilter, disableReason, groupBy, timeGranularity)
}

// InitializeCounters SQL实现中的空操作，因为SQL使用AUTO_INCREMENT，不需要手动初始化counter
func (s *SQLDatabaseImpl) InitializeCounters() error {
	// SQL数据库使用AUTO_INCREMENT，不需要手动初始化counter
	return nil
}

package model

import (
	"errors"
	"fmt"

	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/logger"
)

// DatabaseAdapter 数据库适配器，统一对外接口
type DatabaseAdapter struct {
	dbManager *DatabaseManager
}

var DBAdapter *DatabaseAdapter

// InitDatabaseAdapter 初始化数据库适配器
func InitDatabaseAdapter() error {
	if err := InitDatabaseManager(); err != nil {
		return err
	}

	DBAdapter = &DatabaseAdapter{
		dbManager: DBManager,
	}

	// 初始化迁移服务
	InitMigrationService()

	// 如果启用了NoSQL，执行一次性迁移检查
	if DBManager.IsUsingNoSQL() {
		migrationService := GetMigrationService()
		if err := migrationService.CheckAndMigrateOnce(); err != nil {
			logger.SysError("Failed to perform migration: " + err.Error())
			// 迁移失败时回退到SQL
			DBManager.SwitchToSQL()
			logger.SysLog("Switched back to SQL due to migration failure")
		} else {
			logger.SysLog("Database adapter initialized with NoSQL support")
		}

		// 每次程序启动时都初始化计数器，确保计数器值正确
		nosqlDB := DBManager.GetNoSQLDB()
		if nosqlDB != nil {
			logger.SysLog("Initializing counters on startup...")
			if err := nosqlDB.InitializeCounters(); err != nil {
				logger.SysError("Failed to initialize counters on startup: " + err.Error())
				// 不致命错误，继续运行，但记录警告
			} else {
				logger.SysLog("Counters initialized successfully on startup")
			}
		}
	} else {
		logger.SysLog("Database adapter initialized with SQL only")
	}

	return nil
}

// GetDB 获取当前数据库实现
func GetDB() DatabaseInterface {
	if DBAdapter == nil || DBAdapter.dbManager == nil {
		logger.SysError("Database adapter not initialized")
		return nil
	}
	return DBAdapter.dbManager.GetDB()
}

// GetSQLDB 强制获取SQL数据库（用于备份、同步等）
func GetSQLDB() DatabaseInterface {
	if DBAdapter == nil || DBAdapter.dbManager == nil {
		logger.SysError("Database adapter not initialized")
		return nil
	}
	return DBAdapter.dbManager.GetSQLDB()
}

// GetNoSQLDB 强制获取NoSQL数据库（用于特殊操作）
func GetNoSQLDB() DatabaseInterface {
	if DBAdapter == nil || DBAdapter.dbManager == nil {
		logger.SysError("Database adapter not initialized")
		return nil
	}
	return DBAdapter.dbManager.GetNoSQLDB()
}

// IsUsingNoSQL 检查是否正在使用NoSQL
func IsUsingNoSQL() bool {
	if DBAdapter == nil || DBAdapter.dbManager == nil {
		return false
	}
	return DBAdapter.dbManager.IsUsingNoSQL()
}

// SwitchToNoSQL 切换到NoSQL数据库
func SwitchToNoSQL() error {
	if DBAdapter == nil {
		return errors.New("database adapter not initialized")
	}

	if DBAdapter.dbManager == nil {
		return errors.New("database manager not initialized")
	}

	return DBAdapter.dbManager.SwitchToNoSQL()
}

// SwitchToSQL 切换到SQL数据库
func SwitchToSQL() {
	if DBAdapter != nil && DBAdapter.dbManager != nil {
		DBAdapter.dbManager.SwitchToSQL()
	}
}

// PerformDataMigration 执行数据迁移
func PerformDataMigration() error {
	if DBAdapter == nil {
		return errors.New("database adapter not initialized")
	}

	if DBAdapter.dbManager == nil {
		return errors.New("database manager not initialized")
	}

	return DBAdapter.dbManager.MigrateData()
}

// GetDatabaseStats 获取数据库状态统计
func GetDatabaseStats() map[string]interface{} {
	stats := make(map[string]interface{})

	if DBAdapter == nil {
		stats["error"] = "database adapter not initialized"
		return stats
	}

	stats["current_database"] = "SQL"
	if DBAdapter.dbManager != nil && DBAdapter.dbManager.IsUsingNoSQL() {
		stats["current_database"] = "NoSQL"
	}

	stats["channel_nosql_enabled"] = config.ChannelNoSQLEnabled
	stats["channel_nosql_type"] = config.ChannelNoSQLType
	stats["channel_migration_enabled"] = config.ChannelMigrationEnabled

	// 添加更多统计信息
	if DB != nil {
		var channelCount int64
		DB.Model(&Channel{}).Count(&channelCount)
		stats["channel_count"] = channelCount

		var abilityCount int64
		DB.Model(&Ability{}).Count(&abilityCount)
		stats["ability_count"] = abilityCount

		var channelExtendCount int64
		DB.Model(&ChannelExtend{}).Count(&channelExtendCount)
		stats["channel_extend_count"] = channelExtendCount

		var channelGroupCount int64
		DB.Model(&ChannelGroup{}).Count(&channelGroupCount)
		stats["channel_group_count"] = channelGroupCount
	}

	return stats
}

// SafeUpdateChannelStatus 安全的渠道状态更新（带重试机制）
func SafeUpdateChannelStatus(id int, status int) error {
	db := GetDB()
	if db == nil {
		return fmt.Errorf("database not available")
	}

	err := db.UpdateChannelStatusById(id, status)

	// 如果NoSQL操作失败，回退到SQL
	if err != nil && IsUsingNoSQL() {
		logger.SysError(fmt.Sprintf("NoSQL update failed for channel %d, falling back to SQL: %s", id, err.Error()))

		sqlDB := GetSQLDB()
		if sqlDB != nil {
			return sqlDB.UpdateChannelStatusById(id, status)
		}
	}

	return err
}

// SafeUpdateChannelAbilityStatus 安全的渠道能力状态更新
func SafeUpdateChannelAbilityStatus(id int, requestModel string, status int) error {
	db := GetDB()
	if db == nil {
		return fmt.Errorf("database not available")
	}

	err := db.UpdateChannelAbilityStatusByIdReturnErr(id, requestModel, status)

	// 如果NoSQL操作失败，回退到SQL
	if err != nil && IsUsingNoSQL() {
		logger.SysError(fmt.Sprintf("NoSQL ability update failed for channel %d model %s, falling back to SQL: %s", id, requestModel, err.Error()))

		sqlDB := GetSQLDB()
		if sqlDB != nil {
			return sqlDB.UpdateChannelAbilityStatusByIdReturnErr(id, requestModel, status)
		}
	}

	return err
}

// Cleanup 清理资源
func Cleanup() {
	// 迁移服务不需要特殊清理，因为它只是一次性操作
	logger.SysLog("Database adapter cleaned up")
}

package model

import (
	"fmt"
	"strings"

	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"gorm.io/gorm"
)

type ChannelGroup struct {
	Id                       int     `json:"id" bson:"id" gorm:"primaryKey"`
	Type                     int     `json:"type" bson:"type" gorm:"default:0"`
	Status                   int     `json:"status" bson:"status" gorm:"default:1"`
	Name                     string  `json:"name" bson:"name" gorm:"index"`
	Weight                   *uint   `json:"weight" bson:"weight" gorm:"type:smallint;default:0"`
	CreatedTime              int64   `json:"created_time" bson:"created_time" gorm:"bigint"`
	BaseURL                  *string `json:"base_url" bson:"base_url" gorm:"column:base_url;default:''"`
	Models                   string  `json:"models" bson:"models"`
	Group                    string  `json:"group" bson:"group" gorm:"type:varchar(255);default:'default'"`
	Sort                     *int    `json:"sort" bson:"sort" gorm:"default:0"`
	OverFrequencyAutoDisable *bool   `json:"overFrequencyAutoDisable" bson:"overFrequencyAutoDisable" gorm:"default:0"`
	RetryInterval            *int    `json:"retryInterval" bson:"retryInterval" gorm:"default:300"`
	RequestTokenLimitEnabled *bool   `json:"request_token_limit_enabled" bson:"request_token_limit_enabled" gorm:"default:0"`
	MinRequestTokenCount     *int64  `json:"min_request_token_count" bson:"min_request_token_count" gorm:"default:0"`
	MaxRequestTokenCount     *int64  `json:"max_request_token_count" bson:"max_request_token_count" gorm:"default:0"`
	Config                   string  `json:"config" bson:"config"`
	// 新增：渠道统计信息字段（不存储到数据库，仅用于API响应）
	ChannelStats *ChannelGroupStats `json:"channelStats,omitempty" gorm:"-" bson:"-"`
}

// ChannelGroupStats 渠道组统计信息
type ChannelGroupStats struct {
	Total              int64 `json:"total"`
	Enabled            int64 `json:"enabled"`            // 状态1：正常
	Disabled           int64 `json:"disabled"`           // 状态2：手动禁用
	AutoDisabled       int64 `json:"autoDisabled"`       // 状态3：自动禁用（重试中）
	MaxRetriesExceeded int64 `json:"maxRetriesExceeded"` // 状态4：停用（达到最大重试次数）
	PartiallyDisabled  int64 `json:"partiallyDisabled"`  // 状态5：部分禁用
	Other              int64 `json:"other"`              // 其他未知状态
}

func GetAllChannelGroups(startIdx int, num int, selectAll bool, id int, name string, status int, group string) ([]*ChannelGroup, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.GetAllChannelGroups(startIdx, num, selectAll, id, name, status, group)
	}

	// 回退到原来的SQL实现
	var channelGroups []*ChannelGroup
	var err error
	groupCol := "`group`"
	if common.UsingPostgreSQL {
		groupCol = `"group"`
	}
	tx := DB
	if id != 0 {
		tx = tx.Where("id = ?", id)
	}
	if name != "" {
		tx = tx.Where("name LIKE ?", "%"+name+"%")
	}
	if status != 0 {
		tx = tx.Where("status = ?", status)
	}
	if group != "" {
		// 模糊
		tx = tx.Where(groupCol+" LIKE ?", "%"+group+"%")
	}
	if selectAll {
		err = tx.Order("sort desc").Order("id desc").Find(&channelGroups).Error
	} else {
		err = tx.Omit("key").Order("sort desc").Order("id desc").Limit(num).Offset(startIdx).Find(&channelGroups).Error
	}

	if err != nil {
		return channelGroups, err
	}

	// 如果查询成功且有数据，为每个渠道组获取统计信息
	if len(channelGroups) > 0 {
		// 收集所有渠道组ID
		groupIds := make([]int, len(channelGroups))
		for i, group := range channelGroups {
			groupIds[i] = group.Id
		}

		// 一次性查询所有渠道组的统计信息
		statsMap, err := getChannelGroupsStats(groupIds)
		if err != nil {
			// 如果统计查询失败，记录错误但不影响主查询
			logger.SysError("failed to get channel group stats: " + err.Error())
		} else {
			// 为每个渠道组分配对应的统计信息
			for _, group := range channelGroups {
				if stats, exists := statsMap[group.Id]; exists {
					group.ChannelStats = stats
				} else {
					// 如果没有找到统计信息，设置默认值
					group.ChannelStats = &ChannelGroupStats{
						Total: 0, Enabled: 0, Disabled: 0, AutoDisabled: 0, MaxRetriesExceeded: 0, PartiallyDisabled: 0, Other: 0,
					}
				}
			}
		}
	}

	return channelGroups, err
}

// getChannelGroupsStats 批量获取多个渠道组的统计信息
func getChannelGroupsStats(groupIds []int) (map[int]*ChannelGroupStats, error) {
	if len(groupIds) == 0 {
		return make(map[int]*ChannelGroupStats), nil
	}

	statsMap := make(map[int]*ChannelGroupStats)

	// 使用一次SQL查询获取所有渠道组的统计信息
	var results []struct {
		ChannelGroupId int   `gorm:"column:channel_group_id"`
		Status         int   `gorm:"column:status"`
		Count          int64 `gorm:"column:count"`
	}

	// 构建SQL查询，按渠道组ID和状态分组统计
	query := `
		SELECT 
			channel_group_id,
			status,
			COUNT(*) as count
		FROM channels 
		WHERE channel_group_id IN (?)
		GROUP BY channel_group_id, status
	`

	err := DB.Raw(query, groupIds).Scan(&results).Error
	if err != nil {
		return nil, err
	}

	// 初始化所有渠道组的统计信息
	for _, groupId := range groupIds {
		statsMap[groupId] = &ChannelGroupStats{
			Total: 0, Enabled: 0, Disabled: 0, AutoDisabled: 0, MaxRetriesExceeded: 0, PartiallyDisabled: 0, Other: 0,
		}
	}

	// 根据查询结果填充统计信息
	for _, result := range results {
		stats := statsMap[result.ChannelGroupId]
		stats.Total += result.Count

		switch result.Status {
		case 1:
			stats.Enabled = result.Count
		case 2:
			stats.Disabled = result.Count
		case 3:
			stats.AutoDisabled = result.Count
		case 4:
			stats.MaxRetriesExceeded = result.Count
		case 5:
			stats.PartiallyDisabled = result.Count
		default:
			stats.Other += result.Count
		}
	}

	return statsMap, nil
}

func CountChannelGroups(id int, name string, group string) (int64, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.CountChannelGroups(id, name, group)
	}

	// 回退到原来的SQL实现
	var count int64
	groupCol := "`group`"
	if common.UsingPostgreSQL {
		groupCol = `"group"`
	}
	tx := DB.Model(&ChannelGroup{})
	if id != 0 {
		tx = tx.Where("id = ?", id)
	}
	if name != "" {
		tx = tx.Where("name LIKE ?", "%"+name+"%")
	}
	if group != "" {
		// 模糊
		tx = tx.Where(groupCol+" LIKE ?", "%"+group+"%")
	}
	err := tx.Count(&count).Error
	return count, err
}

func SearchChannelGroups(startIdx int, num int, keyword string) (channels []*ChannelGroup, err error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.SearchChannelGroups(startIdx, num, keyword)
	}

	// 回退到原来的SQL实现
	keyCol := "`key`"
	groupCol := "`group`"
	if common.UsingPostgreSQL {
		keyCol = `"key"`
		groupCol = `"group"`
	}
	err = DB.Omit("key").Where("id = ? or name LIKE ? or "+keyCol+" = ? or "+groupCol+" like ? ", helper.String2Int(keyword), "%"+keyword+"%", keyword, "%"+keyword+"%").Order("sort desc").Limit(num).Offset(startIdx).Find(&channels).Error
	return channels, err
}

func GetChannelGroupById(id int, selectAll bool) (*ChannelGroup, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.GetChannelGroupById(id, selectAll)
	}

	// 回退到原来的SQL实现
	channelGroup := ChannelGroup{Id: id}
	var err error = nil
	if selectAll {
		err = DB.First(&channelGroup, "id = ?", id).Error
	} else {
		err = DB.Omit("key").First(&channelGroup, "id = ?", id).Error
	}
	return &channelGroup, err
}

func BatchInsertChannelGroups(channels []ChannelGroup) error {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.BatchInsertChannelGroups(channels)
	}

	// 回退到原来的SQL实现
	var err error
	err = DB.Create(&channels).Error
	if err != nil {
		return err
	}
	//for _, channel_ := range channels {
	//	err = channel_.AddAbilities()
	//	if err != nil {
	//		return err
	//	}
	//}
	return nil
}

func (channelGroup *ChannelGroup) Insert() error {
	return channelGroup.InsertWithSync(false)
}

func (channelGroup *ChannelGroup) InsertWithSync(syncBothDB bool) error {
	var nosqlErr error

	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		nosqlErr = db.InsertChannelGroup(channelGroup)
		if nosqlErr != nil {
			logger.SysError("failed to insert channel group in NoSQL: " + nosqlErr.Error())
		}

		// 如果不需要同步两个数据库，直接返回NoSQL的结果
		if !syncBothDB {
			return nosqlErr
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作 - 回退到原来的SQL实现
	var err error
	err = DB.Create(channelGroup).Error
	if err != nil {
		// 处理双写的错误情况
		if syncBothDB && nosqlErr != nil {
			logger.SysError("Both NoSQL and SQL channel group insert failed")
			return err
		}
		if syncBothDB && nosqlErr == nil {
			logger.SysError("NoSQL channel group insert succeeded but SQL failed, data inconsistency may occur")
		}
		return err
	}

	// 处理双写的错误情况
	if syncBothDB && err == nil && nosqlErr != nil {
		logger.SysError("NoSQL channel group insert failed but SQL succeeded, data inconsistency may occur")
		return fmt.Errorf("NoSQL channel group insert failed: %v", nosqlErr)
	}

	//err = channel.AddAbilities()
	return err
}

func (channelGroup *ChannelGroup) Update() error {
	return channelGroup.UpdateWithSync(false)
}

func (channelGroup *ChannelGroup) UpdateWithSync(syncBothDB bool) error {
	var nosqlErr error

	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		nosqlErr = db.UpdateChannelGroup(channelGroup)
		if nosqlErr != nil {
			logger.SysError("failed to update channel group in NoSQL: " + nosqlErr.Error())
		}

		// 如果不需要同步两个数据库，直接返回NoSQL的结果
		if !syncBothDB {
			return nosqlErr
		}
		// 如果需要同步，继续执行SQL操作
	}

	// 回退到原来的SQL实现
	// 更新渠道组基本信息
	tx := DB.Begin()
	err := tx.Model(channelGroup).Updates(channelGroup).Error
	if err != nil {
		tx.Rollback()
		// 处理双写的错误情况
		if syncBothDB && nosqlErr != nil {
			logger.SysError("Both NoSQL and SQL channel group update failed")
			return fmt.Errorf("NoSQL error: %v, SQL error: %v", nosqlErr, err)
		}
		return err
	}

	var channelGroupByQuery ChannelGroup
	err = tx.Model(ChannelGroup{}).First(&channelGroupByQuery, "id = ?", channelGroup.Id).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	// 查找对应channelGroup下的channel
	var channels []*Channel
	err = tx.Model(&Channel{}).Where("channel_group_id = ?", channelGroup.Id).Find(&channels).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	// 将channels中的id提取转换成新的切片
	channelIds := make([]int, 0, len(channels))
	for _, channel := range channels {
		channelIds = append(channelIds, channel.Id)
	}

	if len(channelIds) == 0 {
		// 下面没挂渠道则直接提交事务并返回
		return tx.Commit().Error
	}

	// 提交第一个事务，完成渠道组的更新
	err = tx.Commit().Error
	if err != nil {
		return err
	}

	// 打开新事务处理Ability的删除
	tx = DB.Begin()

	// 分批删除ability记录，避免一次性删除过多
	const deleteBatchSize = 1000
	for i := 0; i < len(channelIds); i += deleteBatchSize {
		end := i + deleteBatchSize
		if end > len(channelIds) {
			end = len(channelIds)
		}

		batchChannelIds := channelIds[i:end]
		err = tx.Where("channel_id in ?", batchChannelIds).Delete(&Ability{}).Error
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	// 分批更新channel，避免产生过多占位符
	const updateBatchSize = 500
	for i := 0; i < len(channelIds); i += updateBatchSize {
		end := i + updateBatchSize
		if end > len(channelIds) {
			end = len(channelIds)
		}

		batchChannelIds := channelIds[i:end]
		err = tx.Model(&Channel{}).Where("id in ?", batchChannelIds).Updates(Channel{
			Status:                   channelGroup.Status,
			BaseURL:                  channelGroup.BaseURL,
			Models:                   channelGroup.Models,
			Group:                    channelGroup.Group,
			Sort:                     channelGroup.Sort,
			Weight:                   channelGroup.Weight,
			OverFrequencyAutoDisable: channelGroup.OverFrequencyAutoDisable,
			RetryInterval:            channelGroup.RetryInterval,
			Config:                   channelGroup.Config,
		}).Error
		if err != nil {
			tx.Rollback()
			return err
		}

		// 更新渠道扩展表中的token限制相关字段
		const extendBatchSize = 500 // 每批处理的数量
		batchCounter := 0           // 用于跟踪处理了多少批次

		// 分批处理channelIds以减少预处理语句数量
		for i := 0; i < len(channelIds); i += extendBatchSize {
			batchCounter++
			end := i + extendBatchSize
			if end > len(channelIds) {
				end = len(channelIds)
			}
			currentBatchIds := channelIds[i:end]

			// 先查询当前批次中存在的渠道扩展记录
			var existingExtends []ChannelExtend
			err = tx.Model(&ChannelExtend{}).Where("channel_id IN (?)", currentBatchIds).Find(&existingExtends).Error
			if err != nil {
				tx.Rollback()
				return err
			}

			// 创建一个映射，标记哪些渠道ID已经有扩展记录
			existingChannelIds := make(map[int]bool)
			for _, extend := range existingExtends {
				existingChannelIds[extend.ChannelId] = true
			}

			// 批量更新已有的记录
			if len(existingExtends) > 0 {
				err = tx.Model(&ChannelExtend{}).Where("channel_id IN (?)", currentBatchIds).Updates(map[string]interface{}{
					"request_token_limit_enabled": channelGroup.RequestTokenLimitEnabled,
					"min_request_token_count":     channelGroup.MinRequestTokenCount,
					"max_request_token_count":     channelGroup.MaxRequestTokenCount,
				}).Error
				if err != nil {
					tx.Rollback()
					return err
				}
			}

			// 收集需要创建的新记录
			var newExtends []ChannelExtend
			for _, channelId := range currentBatchIds {
				if !existingChannelIds[channelId] {
					newExtend := ChannelExtend{
						ChannelId:                channelId,
						RequestTokenLimitEnabled: *channelGroup.RequestTokenLimitEnabled,
						MinRequestTokenCount:     *channelGroup.MinRequestTokenCount,
						MaxRequestTokenCount:     *channelGroup.MaxRequestTokenCount,
					}
					newExtends = append(newExtends, newExtend)
				}
			}

			// 批量创建新记录
			if len(newExtends) > 0 {
				// 进一步分批创建，避免一次性创建太多记录
				const createBatchSize = 100
				for j := 0; j < len(newExtends); j += createBatchSize {
					createEnd := j + createBatchSize
					if createEnd > len(newExtends) {
						createEnd = len(newExtends)
					}
					currentCreateBatch := newExtends[j:createEnd]

					err = tx.Create(&currentCreateBatch).Error
					if err != nil {
						tx.Rollback()
						return err
					}
				}
			}

			// 每处理3批，提交一次事务并开始新事务，以释放预处理语句资源
			if batchCounter%3 == 0 {
				err = tx.Commit().Error
				if err != nil {
					return err
				}
				tx = DB.Begin()
				if tx.Error != nil {
					return tx.Error
				}
			}
		}
	}

	// 提交第二个事务，完成Ability删除和Channel更新
	err = tx.Commit().Error
	if err != nil {
		return err
	}

	// 获取更新后的channels并创建新的abilities
	// 此处开始新事务
	tx = DB.Begin()

	// 这里不一次性查询所有channel，而是在循环中分批查询和处理
	// 这样可以避免一次性加载太多数据和产生过多占位符
	const queryBatchSize = 200

	// 分批查询和处理channel
	channelCounter := 0
	currentBatchStart := 0

	for currentBatchStart < len(channelIds) {
		// 确定当前批次的结束位置
		currentBatchEnd := currentBatchStart + queryBatchSize
		if currentBatchEnd > len(channelIds) {
			currentBatchEnd = len(channelIds)
		}

		// 获取当前批次的channel IDs
		currentBatchChannelIds := channelIds[currentBatchStart:currentBatchEnd]

		// 查询当前批次的channels
		var currentBatchChannels []*Channel
		err = tx.Model(&Channel{}).Where("id IN (?)", currentBatchChannelIds).Find(&currentBatchChannels).Error
		if err != nil {
			tx.Rollback()
			return err
		}

		// 处理当前批次的channel
		for _, channelAfterUpdate := range currentBatchChannels {
			channelCounter++

			models_ := strings.Split(channelAfterUpdate.Models, ",")
			groups_ := strings.Split(channelAfterUpdate.Group, ",")

			// 为当前channel创建abilities
			channelAbilities := make([]Ability, 0, len(models_)*len(groups_))
			for _, model := range models_ {
				for _, group := range groups_ {
					// 清理字符串，避免空白字符导致的问题
					model = strings.TrimSpace(model)
					group = strings.TrimSpace(group)
					if model == "" || group == "" {
						continue
					}

					ability := Ability{
						Group:               group,
						Model:               model,
						ChannelId:           channelAfterUpdate.Id,
						Enabled:             &[]bool{channelAfterUpdate.Status == common.ChannelStatusEnabled}[0],
						Sort:                channelAfterUpdate.Sort,
						Weight:              channelAfterUpdate.Weight,
						BillingType:         channelAfterUpdate.BillingType,
						FunctionCallEnabled: channelAfterUpdate.FunctionCallEnabled,
						ImageSupported:      channelAfterUpdate.ImageSupported,
					}
					channelAbilities = append(channelAbilities, ability)
				}
			}

			// 小批量插入当前channel的abilities
			const abilityBatchSize = 500
			for i := 0; i < len(channelAbilities); i += abilityBatchSize {
				end := i + abilityBatchSize
				if end > len(channelAbilities) {
					end = len(channelAbilities)
				}

				chunk := channelAbilities[i:end]
				err = tx.Create(&chunk).Error
				if err != nil {
					tx.Rollback()
					return err
				}
			}

			// 每处理5个channel，提交一次事务并开启新事务
			if channelCounter%5 == 0 {
				err = tx.Commit().Error
				if err != nil {
					return err
				}
				tx = DB.Begin()
			}
		}

		// 移动到下一批
		currentBatchStart = currentBatchEnd
	}

	// 提交最后的事务
	return tx.Commit().Error
}

func (channelGroup *ChannelGroup) Delete() error {
	return channelGroup.DeleteWithSync(false)
}

func (channelGroup *ChannelGroup) DeleteWithSync(syncBothDB bool) error {
	var nosqlErr error

	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		nosqlErr = db.DeleteChannelGroup(channelGroup)
		if nosqlErr != nil {
			logger.SysError("failed to delete channel group in NoSQL: " + nosqlErr.Error())
		}

		// 如果不需要同步两个数据库，直接返回NoSQL的结果
		if !syncBothDB {
			return nosqlErr
		}
		// 如果需要同步，继续执行SQL操作
	}

	// SQL数据库操作 - 回退到原来的SQL实现
	err := DB.Transaction(func(tx *gorm.DB) error {
		err2 := tx.Delete(channelGroup).Error
		if err2 != nil {
			return err2
		}
		// 释放渠道组中的渠道(更新渠道表中的channel_group_id为0)
		err2 = tx.Model(&Channel{}).Where("channel_group_id = ?", channelGroup.Id).Update("channel_group_id", 0).Error
		return err2
	})

	// 处理双写的错误情况
	if syncBothDB && err == nil && nosqlErr != nil {
		logger.SysError("NoSQL channel group delete failed but SQL succeeded, data inconsistency may occur")
		return fmt.Errorf("NoSQL channel group delete failed: %v", nosqlErr)
	}

	return err
}

func UpdateChannelGroupStatusById(id int, status int) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		err := db.UpdateChannelGroupStatusById(id, status)
		if err != nil {
			logger.SysError("failed to update channel group status: " + err.Error())
		}
		return
	}

	// 回退到原来的SQL实现
	err := UpdateAbilityStatus(id, status == common.ChannelStatusEnabled)
	if err != nil {
		logger.SysError("failed to update ability status: " + err.Error())
	}
	err = DB.Model(&ChannelGroup{}).Where("id = ?", id).Update("status", status).Error
	if err != nil {
		logger.SysError("failed to update channel status: " + err.Error())
	}
}

func DeleteChannelGroupByStatus(status int64) (int64, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.DeleteChannelGroupByStatus(status)
	}

	// 回退到原来的SQL实现
	result := DB.Where("status = ?", status).Delete(&ChannelGroup{})
	return result.RowsAffected, result.Error
}

func DeleteDisabledChannelGroup() (int64, error) {
	// 使用渠道数据库接口
	db := getChannelDB()
	if db != nil {
		return db.DeleteDisabledChannelGroup()
	}

	// 回退到原来的SQL实现
	result := DB.Where("status = ? or status = ?", common.ChannelStatusAutoDisabled, common.ChannelStatusManuallyDisabled).Delete(&ChannelGroup{})
	return result.RowsAffected, result.Error
}

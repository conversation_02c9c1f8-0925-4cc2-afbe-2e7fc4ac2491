package model

import (
	"context"
)

// LogStorage 日志存储接口，定义了所有日志存储实现必须支持的操作
type LogStorage interface {
	// 基础日志操作
	RecordLog(ctx context.Context, log *Log) error
	RecordLogBatch(ctx context.Context, logs []*Log) error

	// 特殊日志记录方法
	RecordConsumeLogByDetailIfZeroQuota(ctx context.Context, requestId string, ip string, remoteIp string, userId int, channelId int,
		promptTokens int, completionTokens int, modelName string, tokenName string, tokenKey string, tokenGroup string,
		channelName string, quota int, costQuota int, requestDuration int64, responseFirstByteDuration int64, totalDuration int64,
		isStream bool, content string, other string) (*Log, error)
	RecordRefundLogByDetailIfZeroQuota(ctx context.Context, requestId string, ip string, remoteIp string, userId int, channelId int,
		promptTokens int, completionTokens int, modelName string, tokenName string, tokenKey string, channelName string,
		quota int, requestDuration int64, responseFirstByteDuration int64, totalDuration int64, isStream bool, content string) (*Log, error)

	// 系统日志记录方法
	RecordSysLogToDBAndFileByGinContext(c interface{}, logType int, content string, prompt string) error
	RecordLogToDBAndFileByMeta(ctx context.Context, logType int, toFile bool, meta Meta, content string, quota int, requestDuration int64, responseFirstByteDuration int64, totalDuration int64) error
	RecordSysLogToDBAndFile(ctx context.Context, requestId string, logType int, userId int, channelId int, modelName string, tokenName string, channelName string, content string, prompt string) error

	// LogExtend操作
	RecordLogExtend(ctx context.Context, logExtend *LogExtend) error
	RecordLogExtendBatch(ctx context.Context, logExtends []*LogExtend) error
	GetLogExtendByLogId(logId int) (*LogExtend, error)
	DeleteLogExtendByLogId(logId int) error
	DeleteLogExtendByTimestamp(targetTimestamp int64) (int64, error)
	DeleteInvalidLogExtend() error
	TruncateLogExtendTable(ctx context.Context) error

	// 日志查询
	GetAllLogs(userId int, timezone string, logType []int, startTimestamp int64, endTimestamp int64,
		modelName string, username string, tokenName string, tokenKey string, tokenGroup string,
		channelName string, startIdx int, num int, channel int, isStream string, requestId string,
		ip string, promptTokensMin *int, promptTokensMax *int, completionTokensMin *int,
		completionTokensMax *int, totalDurationMin *float64, totalDurationMax *float64,
		requestDurationMin *float64, requestDurationMax *float64, responseFirstByteDurationMin *float64,
		responseFirstByteDurationMax *float64, excludeModels []string, errorCode string,
		excludeErrorCodes []string, quotaMin *int, quotaMax *int) ([]*Log, error)

	CountAllLogs(userId int, logType []int, startTimestamp int64, endTimestamp int64,
		modelName string, username string, tokenName string, tokenKey string, tokenGroup string,
		channelName string, channel int, isStream string, requestId string, ip string,
		promptTokensMin *int, promptTokensMax *int, completionTokensMin *int, completionTokensMax *int,
		totalDurationMin *float64, totalDurationMax *float64, requestDurationMin *float64,
		requestDurationMax *float64, responseFirstByteDurationMin *float64, responseFirstByteDurationMax *float64,
		excludeModels []string, errorCode string, excludeErrorCodes []string, quotaMin *int, quotaMax *int) (int64, error)

	GetUserLogs(userId int, logType []int, startTimestamp int64, endTimestamp int64,
		modelName string, tokenName string, tokenKey string, startIdx int, num int,
		isStream string, requestId string) ([]*Log, error)

	CountUserLogs(userId int, logType []int, startTimestamp int64, endTimestamp int64,
		modelName string, tokenName string, tokenKey string, isStream string, requestId string) (int64, error)

	SearchAllLogs(keyword string) ([]*Log, error)
	SearchUserLogs(userId int, keyword string) ([]*Log, error)
	SearchUserLogsByKey(key string, startIdx int, num int) ([]*Log, error)
	CountUserLogsByKey(key string) (int64, error)

	// 统计功能
	SumUsedQuota(logType int, startTimestamp int64, endTimestamp int64, modelName string,
		username string, tokenName string, tokenKey string, tokenGroup string, channel int, useRedis bool) (Stat, error)

	SumUsedQuotaByKey(key string, startTimestamp int64, endTimestamp int64) (Stat, error)

	SumAllDailyUsageStatsByDimension(userId int, timezone string, tokenName string, username string,
		channel int, channelName string, modelName string, startTimestamp int64, endTimestamp int64,
		dimension string, granularity string) ([]*DailyModelUsageStats, error)

	// 健康检查和连接管理
	HealthCheck() error
	Close() error

	// 存储类型标识
	GetStorageType() string
}

// LogStorageConfig 日志存储配置
type LogStorageConfig struct {
	Type             string            `json:"type"`              // mysql, clickhouse, elasticsearch
	ConnectionString string            `json:"connection_string"` // 连接字符串
	Database         string            `json:"database"`          // 数据库名
	Table            string            `json:"table"`             // 表名
	BatchSize        int               `json:"batch_size"`        // 批量写入大小
	FlushInterval    int               `json:"flush_interval"`    // 刷新间隔(秒)
	MaxRetries       int               `json:"max_retries"`       // 最大重试次数
	Extra            map[string]string `json:"extra"`             // 额外配置
}

// LogStorageFactory 日志存储工厂接口
type LogStorageFactory interface {
	CreateLogStorage(config *LogStorageConfig) (LogStorage, error)
	GetSupportedTypes() []string
}

package model

type PromptObject struct {
	Role    string `json:"role"`
	Message string `json:"message"`
}

type CustomPrompts struct {
	ID          int    `json:"id"`
	Status      int    `json:"status"`      // 1: enable, 2: disable，3: will be deleted
	Group       string `json:"group"`       // the group of the custom prompts
	Lang        string `json:"lang"`        // the language of the custom prompts
	Title       string `json:"title"`       // the title of the custom prompts
	Description string `json:"description"` // the description of the custom prompts
	/*
	   Prompt is an array of prompt objects, where each object is a map[string]string.
	   The keys in the map are:
	   - "role": the role of the prompt, which can be "system", "assistant", or "user".
	   - "message": the content of the prompt, which is a string.

	   Example prompt objects:
	   [
	       {
	           "role": "system",
	           "message": "You are a helpful writer, please help the user to write the article"
	       },
	       {
	           "role": "assistant",
	           "message": "Please give me the topic of the article"
	       },
	       {
	           "role": "user",
	           "message": "The topic is 'The benefits of regular exercise'"
	       },
	       {
	           "role": "assistant",
	           "message": "In order to help you better, please tell me the purpose of the article, such as to promote a product, to share knowledge, to express opinions, etc."
	       }
	   ]
	*/
	Prompt string `json:"prompt" gorm:"type:text" `
}

// Path: model/custom-prompts.go

func (CustomPrompts *CustomPrompts) Insert() error {
	var err error
	err = DB.Create(CustomPrompts).Error
	return err
}

func BatchInsertCustomPrompts(CustomPrompts []*CustomPrompts) (int64, error) {
	result := DB.CreateInBatches(CustomPrompts, len(CustomPrompts))
	return result.RowsAffected, result.Error
}

func (CustomPrompts *CustomPrompts) Update() (int64, error) {
	result := DB.Model(CustomPrompts).Updates(CustomPrompts)
	return result.RowsAffected, result.Error
}

func (CustomPrompts *CustomPrompts) Delete() (int64, error) {
	result := DB.Delete(CustomPrompts)
	return result.RowsAffected, result.Error
}

func DeleteCustomPromptsById(id int) (int64, error) {
	result := DB.Where("id = ?", id).Delete(&CustomPrompts{})
	return result.RowsAffected, result.Error
}

func DeleteCustomPromptsByIds(ids []int) (int64, error) {
	result := DB.Where("id in (?)", ids).Delete(&CustomPrompts{})
	return result.RowsAffected, result.Error
}

func GetAllCustomPrompts(startIdx int, num int, id int, status int, group string, lang string) ([]*CustomPrompts, error) {
	var CustomPrompts []*CustomPrompts
	var err error
	var tx = DB
	if id != 0 {
		tx = tx.Where("id = ?", id)
	}
	if status != 0 {
		tx = tx.Where("status = ?", status)
	}
	if group != "" {
		tx = tx.Where("group = ?", group)
	}
	if lang != "" {
		tx = tx.Where("lang = ?", lang)
	}
	err = tx.Offset(startIdx).Limit(num).Find(&CustomPrompts).Error
	if err != nil {
		return nil, err
	}
	return CustomPrompts, nil
}

func CountCustomPrompts(id int, status int, group string, lang string) (count int64, err error) {
	var tx = DB
	if id != 0 {
		tx = tx.Where("id = ?", id)
	}
	if status != 0 {
		tx = tx.Where("status = ?", status)
	}
	if group != "" {
		tx = tx.Where("group = ?", group)
	}
	if lang != "" {
		tx = tx.Where("lang = ?", lang)
	}
	err = tx.Model(&CustomPrompts{}).Count(&count).Error
	return
}

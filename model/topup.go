package model

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type TopUp struct {
	Id             int     `json:"id"`
	UserId         int     `json:"user_id" gorm:"index"`
	Username       string  `json:"username" gorm:"-"`
	AgencyId       int     `json:"agency_id" gorm:"index"` // 代理商id
	Amount         float64 `json:"amount"`                 //充值金额（美金）
	Money          float64 `json:"money"`                  //支付金额（人民币）
	MoneyOrigin    float64 `json:"money_origin"`           //支付金额（人民币,没打折之前应该支付的金额）
	MoneyUsdt      float64 `json:"money_usdt"`             //支付金额（USDT）
	MoneyPayPalUsd float64 `json:"money_paypal_usd"`       //支付金额（PayPal）
	MoneyEth       float64 `json:"money_eth"`              //支付金额（USDT）
	TradeNo        string  `json:"trade_no" gorm:"column:trade_no;type:varchar(191);uniqueIndex:idx_top_ups_trade_no"`
	PaymentMethod  string  `json:"payment_method" gorm:"index"` //支付方式，可选值：alipay，wxpay
	CreateTime     int64   `json:"create_time"`
	Status         string  `json:"status" gorm:"type:varchar(50)"`
}

func (topUp *TopUp) Insert() error {
	var err error
	err = DB.Create(topUp).Error
	return err
}

func (topUp *TopUp) Update() error {
	var err error
	err = DB.Save(topUp).Error
	return err
}

func (topUp *TopUp) UpdateByTx(tx *gorm.DB) error {
	var err error
	err = tx.Save(topUp).Error
	return err
}

func GetTopUpById(id int) *TopUp {
	var topUp *TopUp
	var err error
	err = DB.Where("id = ?", id).First(&topUp).Error
	if err != nil {
		return nil
	}
	return topUp
}

func GetTopUpByTradeNo(tradeNo string) *TopUp {
	var topUp *TopUp
	var err error
	err = DB.Where("trade_no = ?", tradeNo).First(&topUp).Error
	if err != nil {
		return nil
	}
	return topUp
}

func GetTopUpByTradeNoByTx(tx *gorm.DB, tradeNo string) (*TopUp, error) {
	topUp := &TopUp{}
	// 尝试锁定特定ID的记录
	err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).Where("trade_no = ?", tradeNo).First(&topUp).Error
	if err != nil {
		return nil, err
	}
	return topUp, nil
}

// GetTopUpList 获取充值记录列表
func GetTopUpList(page, pageSize int, conditions map[string]string) ([]TopUp, int64, error) {
	query := DB.Model(&TopUp{})

	// 如果有用户名搜索,需要关联users表
	if username, ok := conditions["username"]; ok && username != "" {
		query = query.Joins("JOIN users ON top_ups.user_id = users.id").
			Where("users.username LIKE ?", "%"+username+"%")
	}

	if amount, ok := conditions["amount"]; ok && amount != "" {
		query = query.Where("top_ups.amount = ?", amount)
	}

	if paymentMethod, ok := conditions["payment_method"]; ok && paymentMethod != "" {
		query = query.Where("top_ups.payment_method = ?", paymentMethod)
	}

	if tradeNo, ok := conditions["trade_no"]; ok && tradeNo != "" {
		query = query.Where("top_ups.trade_no LIKE ?", "%"+tradeNo+"%")
	}

	if status, ok := conditions["status"]; ok && status != "" {
		query = query.Where("top_ups.status = ?", status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取充值记录列表
	var topUps []TopUp
	if err := query.Order("top_ups.id DESC").
		Limit(pageSize).
		Offset((page - 1) * pageSize).
		Find(&topUps).Error; err != nil {
		return nil, 0, err
	}

	// 填充用户名
	for i := range topUps {
		user, err := GetUserById(topUps[i].UserId, false)
		if err == nil {
			topUps[i].Username = user.Username
		}
	}

	return topUps, total, nil
}

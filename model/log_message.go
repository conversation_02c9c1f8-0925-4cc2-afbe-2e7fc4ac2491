package model

type ErrorLogMessage struct {
	Description     string      `json:"description"` // 错误描述
	DownstreamError *ErrorInfo  `json:"downstream_error"`
	OriginalError   *ErrorInfo  `json:"original_error"`
	RequestParams   interface{} `json:"request_params"`
}

type ErrorInfo struct {
	StatusCode int             `json:"status_code"`
	Error      ErrorDetailInfo `json:"error"`
}

type ErrorDetailInfo struct {
	Message string      `json:"message"`
	Type    string      `json:"type"`
	Code    interface{} `json:"code"`
}

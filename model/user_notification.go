package model

import (
	"encoding/json"
	"strings"

	"github.com/songquanpeng/one-api/common/helper"

	"gorm.io/gorm"
)

// UserNotificationSetting 用户通知设置
type UserNotificationSetting struct {
	ID                  int     `json:"id" gorm:"primaryKey"`
	UserId              int     `json:"user_id" gorm:"index;not null"`
	SubscriptionEvents  string  `json:"subscription_events" gorm:"type:text"`  // JSON格式存储订阅事件
	NotificationMethods string  `json:"notification_methods" gorm:"type:text"` // JSON格式存储通知方式
	WebhookUrl          string  `json:"webhook_url"`
	WebhookToken        string  `json:"webhook_token"`
	WebhookSecret       string  `json:"webhook_secret"`                 // 新增：与 new-api 兼容的 webhook 密钥字段
	CustomEmail         string  `json:"custom_email"`                   // 自定义邮箱地址，如果为空则使用用户预留邮箱（兼容历史数据）
	CustomEmails        string  `json:"custom_emails" gorm:"type:text"` // JSON格式存储多个自定义邮箱地址
	EmailEnabled        bool    `json:"email_enabled" gorm:"default:true"`
	TelegramEnabled     bool    `json:"telegram_enabled" gorm:"default:false"`
	TelegramBotToken    string  `json:"telegram_bot_token"` // Telegram Bot Token
	TelegramChatId      string  `json:"telegram_chat_id"`   // Telegram Chat ID
	WebhookEnabled      bool    `json:"webhook_enabled" gorm:"default:false"`
	WxPusherEnabled     bool    `json:"wxpusher_enabled" gorm:"default:false"`
	QyWxBotEnabled      bool    `json:"qywxbot_enabled" gorm:"default:false"`
	DingtalkEnabled     bool    `json:"dingtalk_enabled" gorm:"default:false"`
	FeishuEnabled       bool    `json:"feishu_enabled" gorm:"default:false"`
	QyWxWebhookUrl      string  `json:"qywx_webhook_url"`                    // 企业微信机器人Webhook URL
	DingtalkWebhookUrl  string  `json:"dingtalk_webhook_url"`                // 钉钉机器人Webhook URL
	FeishuWebhookUrl    string  `json:"feishu_webhook_url"`                  // 飞书机器人Webhook URL
	WxPusherAppToken    string  `json:"wxpusher_app_token"`                  // WxPusher APP Token
	WxPusherUid         string  `json:"wxpusher_uid"`                        // WxPusher 用户UID
	BalanceThreshold    float64 `json:"balance_threshold" gorm:"default:30"` // 余额预警阈值
	CreatedAt           int64   `json:"created_at"`
	UpdatedAt           int64   `json:"updated_at"`
}

// SubscriptionEvent 订阅事件类型
type SubscriptionEvent struct {
	Key         string `json:"key"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Enabled     bool   `json:"enabled"`
}

// NotificationMethod 通知方式
type NotificationMethod struct {
	Type    string `json:"type"`
	Enabled bool   `json:"enabled"`
	Config  string `json:"config,omitempty"` // 额外配置，如频率限制等
}

func (setting *UserNotificationSetting) TableName() string {
	return "user_notification_settings"
}

// GetSubscriptionEvents 获取订阅事件列表
func (setting *UserNotificationSetting) GetSubscriptionEvents() ([]SubscriptionEvent, error) {
	if setting.SubscriptionEvents == "" {
		return []SubscriptionEvent{}, nil
	}

	var events []SubscriptionEvent
	err := json.Unmarshal([]byte(setting.SubscriptionEvents), &events)
	return events, err
}

// SetSubscriptionEvents 设置订阅事件列表
func (setting *UserNotificationSetting) SetSubscriptionEvents(events []SubscriptionEvent) error {
	data, err := json.Marshal(events)
	if err != nil {
		return err
	}
	setting.SubscriptionEvents = string(data)
	return nil
}

// GetNotificationMethods 获取通知方式列表
func (setting *UserNotificationSetting) GetNotificationMethods() ([]NotificationMethod, error) {
	if setting.NotificationMethods == "" {
		return []NotificationMethod{}, nil
	}

	var methods []NotificationMethod
	err := json.Unmarshal([]byte(setting.NotificationMethods), &methods)
	return methods, err
}

// SetNotificationMethods 设置通知方式列表
func (setting *UserNotificationSetting) SetNotificationMethods(methods []NotificationMethod) error {
	data, err := json.Marshal(methods)
	if err != nil {
		return err
	}
	setting.NotificationMethods = string(data)
	return nil
}

// GetEffectiveEmails 获取有效的邮箱地址列表（优先使用自定义邮箱，如果没有则使用用户预留邮箱）
func (setting *UserNotificationSetting) GetEffectiveEmails() ([]string, error) {
	var emails []string

	// 优先使用新的多邮箱字段
	if setting.CustomEmails != "" {
		if err := json.Unmarshal([]byte(setting.CustomEmails), &emails); err != nil {
			return nil, err
		}
		// 过滤掉空邮箱
		var validEmails []string
		for _, email := range emails {
			if strings.TrimSpace(email) != "" {
				validEmails = append(validEmails, strings.TrimSpace(email))
			}
		}
		if len(validEmails) > 0 {
			return validEmails, nil
		}
	}

	// 兼容历史数据：如果新字段为空，检查旧的单邮箱字段
	if setting.CustomEmail != "" {
		return []string{strings.TrimSpace(setting.CustomEmail)}, nil
	}

	// 最后使用用户预留的邮箱
	userEmail, err := CacheGetUserEmail(setting.UserId)
	if err != nil {
		return nil, err
	}
	if userEmail != "" {
		return []string{userEmail}, nil
	}

	return []string{}, nil
}

// GetEffectiveEmail 获取有效的邮箱地址（兼容性方法，返回第一个邮箱）
func (setting *UserNotificationSetting) GetEffectiveEmail() (string, error) {
	emails, err := setting.GetEffectiveEmails()
	if err != nil {
		return "", err
	}
	if len(emails) > 0 {
		return emails[0], nil
	}
	return "", nil
}

// SetCustomEmails 设置自定义邮箱列表
func (setting *UserNotificationSetting) SetCustomEmails(emails []string) error {

	// 过滤掉空邮箱
	var validEmails []string
	for _, email := range emails {
		if strings.TrimSpace(email) != "" {
			validEmails = append(validEmails, strings.TrimSpace(email))
		}
	}

	// 设置新的多邮箱字段
	data, err := json.Marshal(validEmails)
	if err != nil {
		return err
	}
	setting.CustomEmails = string(data)

	// 兼容性：同时更新旧的单邮箱字段（使用第一个邮箱）
	if len(validEmails) > 0 {
		setting.CustomEmail = validEmails[0]
	} else {
		setting.CustomEmail = ""
	}

	return nil
}

// GetCustomEmails 获取自定义邮箱列表
func (setting *UserNotificationSetting) GetCustomEmails() ([]string, error) {
	if setting.CustomEmails == "" {
		return []string{}, nil
	}

	var emails []string
	if err := json.Unmarshal([]byte(setting.CustomEmails), &emails); err != nil {
		return nil, err
	}
	return emails, nil
}

// GetUserNotificationSetting 获取用户通知设置
func GetUserNotificationSetting(userId int) (*UserNotificationSetting, error) {
	var setting UserNotificationSetting
	err := DB.Where("user_id = ?", userId).First(&setting).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果没有设置，创建默认设置
			return CreateDefaultUserNotificationSetting(userId)
		}
		return nil, err
	}
	return &setting, nil
}

// CreateDefaultUserNotificationSetting 创建默认用户通知设置
func CreateDefaultUserNotificationSetting(userId int) (*UserNotificationSetting, error) {
	// 默认订阅事件
	defaultEvents := []SubscriptionEvent{
		{Key: "account_balance_low", Name: "余额不足预警", Description: "当账户余额低于设定阈值时通知", Enabled: true},
		{Key: "account_quota_expiry", Name: "额度即将过期", Description: "当账户额度即将过期时通知", Enabled: true},
		{Key: "security_alert", Name: "安全警报", Description: "账户安全相关警报", Enabled: true},
		{Key: "system_announcement", Name: "系统公告", Description: "系统重要公告和更新通知", Enabled: false},
		{Key: "promotional_activity", Name: "促销活动通知", Description: "优惠活动和促销信息", Enabled: false},
		{Key: "model_pricing_update", Name: "模型价格更新", Description: "AI模型价格变动通知", Enabled: false},
		{Key: "anti_loss_contact", Name: "防失联-定期通知", Description: "定期发送通知确保联系方式有效", Enabled: false},
	}

	// 默认通知方式
	defaultMethods := []NotificationMethod{
		{Type: "email", Enabled: true},
		{Type: "telegram", Enabled: false},
		{Type: "webhook", Enabled: false},
		{Type: "wxpusher", Enabled: false},
		{Type: "qywxbot", Enabled: false},
		{Type: "dingtalk", Enabled: false},
		{Type: "feishu", Enabled: false},
	}

	setting := &UserNotificationSetting{
		UserId:           userId,
		EmailEnabled:     true,
		TelegramEnabled:  false,
		WebhookEnabled:   false,
		WxPusherEnabled:  false,
		QyWxBotEnabled:   false,
		DingtalkEnabled:  false,
		FeishuEnabled:    false,
		BalanceThreshold: 30.0, // 默认余额预警阈值为30美元
		CreatedAt:        helper.GetTimestamp(),
		UpdatedAt:        helper.GetTimestamp(),
	}

	if err := setting.SetSubscriptionEvents(defaultEvents); err != nil {
		return nil, err
	}

	if err := setting.SetNotificationMethods(defaultMethods); err != nil {
		return nil, err
	}

	err := DB.Create(setting).Error
	return setting, err
}

// UpdateUserNotificationSetting 更新用户通知设置
func UpdateUserNotificationSetting(setting *UserNotificationSetting) error {
	setting.UpdatedAt = helper.GetTimestamp()
	err := DB.Save(setting).Error
	if err != nil {
		return err
	}

	// 更新成功后，清理缓存并更新新的缓存
	DeleteUserNotificationSettingCache(setting.UserId)
	UpdateUserNotificationSettingCache(setting.UserId, setting)

	return nil
}

// GetAvailableEvents 获取所有可用的订阅事件
func GetAvailableEvents() []SubscriptionEvent {
	return []SubscriptionEvent{
		{Key: "account_balance_low", Name: "余额不足预警", Description: "当账户余额低于设定阈值时通知"},
		{Key: "account_quota_expiry", Name: "额度即将过期", Description: "当账户额度即将过期时通知"},
		{Key: "security_alert", Name: "安全警报", Description: "账户安全相关警报"},
		{Key: "system_announcement", Name: "系统公告", Description: "系统重要公告和更新通知"},
		{Key: "promotional_activity", Name: "促销活动通知", Description: "优惠活动和促销信息"},
		{Key: "model_pricing_update", Name: "模型价格更新", Description: "AI模型价格变动通知"},
		{Key: "anti_loss_contact", Name: "防失联-定期通知", Description: "定期发送通知确保联系方式有效"},
	}
}

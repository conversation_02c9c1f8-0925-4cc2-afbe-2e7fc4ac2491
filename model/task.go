package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"

	"github.com/songquanpeng/one-api/constant"
	commonRelay "github.com/songquanpeng/one-api/relay/common"
	"gorm.io/gorm"
)

type TaskStatus string

const (
	TaskStatusNotStart   TaskStatus = "NOT_START"
	TaskStatusSubmitted             = "SUBMITTED"
	TaskStatusQueued                = "QUEUED"
	TaskStatusInProgress            = "IN_PROGRESS"
	TaskStatusFailure               = "FAILURE"
	TaskStatusSuccess               = "SUCCESS"
	TaskStatusUnknown               = "UNKNOWN"
)

type Task struct {
	ID         int64                 `json:"id" gorm:"primary_key;AUTO_INCREMENT"`
	CreatedAt  int64                 `json:"created_at" gorm:"index"`
	UpdatedAt  int64                 `json:"updated_at"`
	TaskID     string                `json:"task_id" gorm:"type:varchar(50);index"`  // 第三方id，不一定有/ song id\ Task id
	Platform   constant.TaskPlatform `json:"platform" gorm:"type:varchar(30);index"` // 平台
	UserId     int                   `json:"user_id" gorm:"index"`
	ChannelId  int                   `json:"channel_id" gorm:"index"`
	Quota      int                   `json:"quota"`
	Action     string                `json:"action" gorm:"type:varchar(40);index"` // 任务类型, song, lyrics, description-mode
	Status     TaskStatus            `json:"status" gorm:"type:varchar(20);index"` // 任务状态
	FailReason string                `json:"fail_reason"`
	ResultUrl  string                `json:"result_url" gorm:"type:varchar(500)"` // 任务结果URL（视频、图片等）
	SubmitTime int64                 `json:"submit_time" gorm:"index"`
	StartTime  int64                 `json:"start_time" gorm:"index"`
	FinishTime int64                 `json:"finish_time" gorm:"index"`
	Progress   string                `json:"progress" gorm:"type:varchar(20);index"`
	Properties Properties            `json:"properties" gorm:"type:json"`

	Data json.RawMessage `json:"data" gorm:"type:json"`
}

func (t *Task) SetData(data any) {
	b, _ := json.Marshal(data)
	t.Data = json.RawMessage(b)
}

func (t *Task) GetData(v any) error {
	err := json.Unmarshal(t.Data, &v)
	return err
}

type Properties struct {
	Input string `json:"input"`
}

func (m *Properties) Scan(val interface{}) error {
	bytesValue, _ := val.([]byte)
	return json.Unmarshal(bytesValue, m)
}

func (m Properties) Value() (driver.Value, error) {
	return json.Marshal(m)
}

// SyncTaskQueryParams 用于包含所有搜索条件的结构体，可以根据需求添加更多字段
type SyncTaskQueryParams struct {
	Platform       constant.TaskPlatform
	ChannelID      string
	TaskID         string
	UserID         string
	Action         string
	Status         string
	StartTimestamp int64
	EndTimestamp   int64
	UserIDs        []int
}

func InitTask(platform constant.TaskPlatform, relayInfo *commonRelay.TaskRelayInfo) *Task {
	t := &Task{
		UserId:     relayInfo.UserId,
		SubmitTime: time.Now().Unix(),
		Status:     TaskStatusNotStart,
		Progress:   "0%",
		ChannelId:  relayInfo.ChannelId,
		Platform:   platform,
	}
	return t
}

func GetByTaskId(userId int, taskId string) (*Task, bool, error) {
	if taskId == "" {
		return nil, false, nil
	}
	var task *Task
	var err error
	err = DB.Where("user_id = ? and task_id = ?", userId, taskId).
		First(&task).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, false, nil
		}
		return nil, false, err
	}
	return task, true, nil
}

func GetByTaskIds(userId int, taskIds []any) ([]*Task, error) {
	if len(taskIds) == 0 {
		return nil, nil
	}
	var task []*Task
	var err error
	err = DB.Where("user_id = ? and task_id in (?)", userId, taskIds).
		Find(&task).Error
	if err != nil {
		return nil, err
	}
	return task, nil
}

func (Task *Task) Insert() error {
	var err error
	err = DB.Create(Task).Error
	return err
}

func (Task *Task) Update() error {
	var err error
	err = DB.Save(Task).Error
	return err
}

func TaskBulkUpdate(TaskIds []string, params map[string]any) error {
	if len(TaskIds) == 0 {
		return nil
	}
	return DB.Model(&Task{}).
		Where("task_id in (?)", TaskIds).
		Updates(params).Error
}

func TaskBulkUpdateByTaskIds(taskIDs []int64, params map[string]any) error {
	if len(taskIDs) == 0 {
		return nil
	}
	return DB.Model(&Task{}).
		Where("id in (?)", taskIDs).
		Updates(params).Error
}

func TaskBulkUpdateByID(ids []int64, params map[string]any) error {
	if len(ids) == 0 {
		return nil
	}
	return DB.Model(&Task{}).
		Where("id in (?)", ids).
		Updates(params).Error
}

type TaskQuotaUsage struct {
	Mode  string  `json:"mode"`
	Count float64 `json:"count"`
}

func SumUsedTaskQuota(queryParams SyncTaskQueryParams) (stat []TaskQuotaUsage, err error) {
	query := DB.Model(Task{})
	// 添加过滤条件
	if queryParams.ChannelID != "" {
		query = query.Where("channel_id = ?", queryParams.ChannelID)
	}
	if queryParams.UserID != "" {
		query = query.Where("user_id = ?", queryParams.UserID)
	}
	if len(queryParams.UserIDs) != 0 {
		query = query.Where("user_id in (?)", queryParams.UserIDs)
	}
	if queryParams.TaskID != "" {
		query = query.Where("task_id = ?", queryParams.TaskID)
	}
	if queryParams.Action != "" {
		query = query.Where("action = ?", queryParams.Action)
	}
	if queryParams.Status != "" {
		query = query.Where("status = ?", queryParams.Status)
	}
	if queryParams.StartTimestamp != 0 {
		query = query.Where("submit_time >= ?", queryParams.StartTimestamp)
	}
	if queryParams.EndTimestamp != 0 {
		query = query.Where("submit_time <= ?", queryParams.EndTimestamp)
	}
	err = query.Select("mode, sum(quota) as count").Group("mode").Find(&stat).Error
	return stat, err
}

// TaskCountAllTasks returns total tasks that match the given query params (admin usage)
func TaskCountAllTasks(queryParams SyncTaskQueryParams) int64 {
	var total int64
	query := DB.Model(&Task{})
	if queryParams.ChannelID != "" {
		query = query.Where("channel_id = ?", queryParams.ChannelID)
	}
	if queryParams.Platform != "" {
		query = query.Where("platform = ?", queryParams.Platform)
	}
	if queryParams.UserID != "" {
		query = query.Where("user_id = ?", queryParams.UserID)
	}
	if len(queryParams.UserIDs) != 0 {
		query = query.Where("user_id in (?)", queryParams.UserIDs)
	}
	if queryParams.TaskID != "" {
		query = query.Where("task_id = ?", queryParams.TaskID)
	}
	if queryParams.Action != "" {
		query = query.Where("action = ?", queryParams.Action)
	}
	if queryParams.Status != "" {
		query = query.Where("status = ?", queryParams.Status)
	}
	if queryParams.StartTimestamp != 0 {
		query = query.Where("submit_time >= ?", queryParams.StartTimestamp)
	}
	if queryParams.EndTimestamp != 0 {
		query = query.Where("submit_time <= ?", queryParams.EndTimestamp)
	}
	_ = query.Count(&total).Error
	return total
}

// TaskCountAllUserTask returns total tasks for given user
func TaskCountAllUserTask(userId int, queryParams SyncTaskQueryParams) int64 {
	var total int64
	query := DB.Model(&Task{}).Where("user_id = ?", userId)
	if queryParams.TaskID != "" {
		query = query.Where("task_id = ?", queryParams.TaskID)
	}
	if queryParams.Action != "" {
		query = query.Where("action = ?", queryParams.Action)
	}
	if queryParams.Status != "" {
		query = query.Where("status = ?", queryParams.Status)
	}
	if queryParams.Platform != "" {
		query = query.Where("platform = ?", queryParams.Platform)
	}
	if queryParams.StartTimestamp != 0 {
		query = query.Where("submit_time >= ?", queryParams.StartTimestamp)
	}
	if queryParams.EndTimestamp != 0 {
		query = query.Where("submit_time <= ?", queryParams.EndTimestamp)
	}
	_ = query.Count(&total).Error
	return total
}

// GetUserTasksWithPagination 获取用户任务列表（分页）
func GetUserTasksWithPagination(userId int, offset int, limit int, taskType string, status string) ([]*Task, int64, error) {
	var tasks []*Task
	var total int64

	query := DB.Where("user_id = ?", userId)

	// 添加过滤条件
	if taskType != "" {
		query = query.Where("action = ?", taskType)
	}
	if status != "" {
		// 转换状态格式
		dbStatus := convertStatusToDBFormat(status)
		if dbStatus != "" {
			query = query.Where("status = ?", dbStatus)
		}
	}

	// 获取总数
	err := query.Model(&Task{}).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err = query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&tasks).Error
	if err != nil {
		return nil, 0, err
	}

	return tasks, total, nil
}

// GetUserTaskStatistics 获取用户任务统计信息
func GetUserTaskStatistics(userId int) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 按状态统计
	var statusStats []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}

	err := DB.Model(&Task{}).
		Select("status, count(*) as count").
		Where("user_id = ?", userId).
		Group("status").
		Scan(&statusStats).Error

	if err != nil {
		return nil, err
	}

	// 转换状态格式
	statusMap := make(map[string]int64)
	for _, stat := range statusStats {
		convertedStatus := convertTaskStatusToAPI(stat.Status)
		statusMap[convertedStatus] = stat.Count
	}

	stats["by_status"] = statusMap

	// 总任务数
	var totalCount int64
	err = DB.Model(&Task{}).Where("user_id = ?", userId).Count(&totalCount).Error
	if err != nil {
		return nil, err
	}
	stats["total"] = totalCount

	// 按任务类型统计
	var typeStats []struct {
		Action string `json:"action"`
		Count  int64  `json:"count"`
	}

	err = DB.Model(&Task{}).
		Select("action, count(*) as count").
		Where("user_id = ?", userId).
		Group("action").
		Scan(&typeStats).Error

	if err != nil {
		return nil, err
	}

	typeMap := make(map[string]int64)
	for _, stat := range typeStats {
		typeMap[stat.Action] = stat.Count
	}
	stats["by_type"] = typeMap

	return stats, nil
}

// convertStatusToDBFormat 将API状态格式转换为数据库格式
func convertStatusToDBFormat(status string) string {
	switch status {
	case "pending":
		return string(TaskStatusNotStart)
	case "submitted":
		return string(TaskStatusSubmitted)
	case "queued":
		return string(TaskStatusQueued)
	case "in_progress":
		return string(TaskStatusInProgress)
	case "completed":
		return string(TaskStatusSuccess)
	case "failed":
		return string(TaskStatusFailure)
	default:
		return ""
	}
}

// convertTaskStatusToAPI 将数据库状态格式转换为API格式
func convertTaskStatusToAPI(status string) string {
	switch status {
	case string(TaskStatusNotStart):
		return "pending"
	case string(TaskStatusSubmitted):
		return "submitted"
	case string(TaskStatusQueued):
		return "queued"
	case string(TaskStatusInProgress):
		return "in_progress"
	case string(TaskStatusSuccess):
		return "completed"
	case string(TaskStatusFailure):
		return "failed"
	default:
		return "unknown"
	}
}

// GetAllUnFinishSyncTasks 获取所有未完成的同步任务
func GetAllUnFinishSyncTasks(limit int) []*Task {
	var tasks []*Task
	var err error
	// 获取所有进度不是100%且状态不是成功或失败的任务
	err = DB.Where("progress != ? AND status NOT IN (?, ?)", "100%", TaskStatusSuccess, TaskStatusFailure).
		Limit(limit).
		Find(&tasks).Error
	if err != nil {
		return nil
	}
	return tasks
}

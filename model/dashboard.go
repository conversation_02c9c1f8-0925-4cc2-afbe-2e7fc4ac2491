package model

import (
	"strconv"

	"gorm.io/gorm"
)

type DashboardSummary struct {
	Availability           float64  `json:"availability"`      // 可用性
	SiteStatus             float64  `json:"siteStatus"`        // 站点状态
	NetworkStatus          float64  `json:"networkStatus"`     // 网络状态
	TotalVisits            int      `json:"totalVisits"`       // 总访问人次
	RegisteredUsers        int      `json:"registeredUsers"`   // 注册用户数
	SiteUptimeDays         *float64 `json:"siteUptimeDays"`    // 网站运行时间 (单位:天)
	TotalAPICalls          int      `json:"totalAPICalls"`     // 总调用次数
	Gpt35calls             int      `json:"gpt35Calls"`        // GPT-3.5调用次数
	GPT4Calls              int      `json:"gpt4Calls"`         // GPT-4调用次数
	CurrentRespTimeMs      int      `json:"currentRespTimeMs"` // 当前响应时间 (单位:ms)
	AverageRespTimeMs      int      `json:"averageRespTimeMs"` // 平均响应时间 (单位:ms)
	FastestRespTimeMs      int      `json:"fastestRespTimeMs"` // 最快响应时间 (单位:ms)
	RPM                    int      `json:"RPM"`               // RPM (每分钟请求次数)
	RPD                    int      `json:"RPD"`               // RPD (每天请求次数)
	MainAllConnections     int
	MainActiveConnections  int
	LogAllConnections      int
	LogActiveConnections   int
	LogExAllConnections    int
	LogExActiveConnections int
}

func GetDashboardSummary(userId int) (dashboardSummary *DashboardSummary, err error) {
	var tx *gorm.DB
	var txMain *gorm.DB
	var txLogEx *gorm.DB
	tx = LOG_DB
	txMain = DB
	txLogEx = LOG_EX_DB
	dashboardSummary = &DashboardSummary{}
	// 查询GPT-3.5调用次数
	err = tx.Raw("SELECT count(1) from logs where model_name like ? ", "%gpt-3.5%").Scan(&dashboardSummary.Gpt35calls).Error
	if err != nil {
		return nil, err
	}
	// 查询GPT-4调用次数
	err = tx.Raw("SELECT count(1) from logs where model_name like ? ", "%gpt-4%").Scan(&dashboardSummary.GPT4Calls).Error
	if err != nil {
		return nil, err
	}
	// 查询api调用总次数
	err = tx.Raw("SELECT count(1) from logs ").Scan(&dashboardSummary.TotalAPICalls).Error
	if err != nil {
		return nil, err
	}
	// 查询网站运行时间,根据最初的token生成时间算
	if IsDBSQLite() {
		err = txMain.Raw("SELECT julianday('now') - julianday(datetime(min(created_time), 'unixepoch')) AS days_difference from tokens").Scan(&dashboardSummary.SiteUptimeDays).Error
	} else {
		err = txMain.Raw("SELECT DATEDIFF(CURDATE(), FROM_UNIXTIME(min(created_time))) AS days_difference from tokens").Scan(&dashboardSummary.SiteUptimeDays).Error
	}
	if err != nil {
		return nil, err
	}
	// 查询注册用户数
	err = txMain.Raw("SELECT count(1) from users ").Scan(&dashboardSummary.RegisteredUsers).Error
	if err != nil {
		return nil, err
	}

	// 查询RPD
	if IsLogDBSQLite() {
		err = tx.Raw("SELECT count(1) FROM logs WHERE created_at > strftime('%s', datetime('now', '-1 day'))").Scan(&dashboardSummary.RPD).Error
	} else {
		err = tx.Raw("SELECT count(1) from logs where created_at > unix_timestamp(date_sub(now(),interval 1 day))").Scan(&dashboardSummary.RPD).Error
	}
	if err != nil {
		return nil, err
	}
	// 查询RPM
	if IsLogDBSQLite() {
		err = tx.Raw("SELECT count(1) FROM logs WHERE created_at > strftime('%s', datetime('now', '-1 minute'))").Scan(&dashboardSummary.RPM).Error
	} else {
		err = tx.Raw("SELECT count(1) from logs where created_at > unix_timestamp(date_sub(now(),interval 1 minute))").Scan(&dashboardSummary.RPM).Error
	}
	if err != nil {
		return nil, err
	}

	// 获取数据库连接数
	type Status struct {
		VariableName string
		Value        string
	}
	var statusMain Status
	txMain.Raw("SHOW STATUS LIKE 'Threads_connected'").Scan(&statusMain)
	var mainActiveConnections int
	txMain.Raw("SELECT COUNT(*) AS active_connections FROM information_schema.PROCESSLIST WHERE `COMMAND` != 'Sleep'").Scan(&mainActiveConnections)
	dashboardSummary.MainActiveConnections = mainActiveConnections
	dashboardSummary.MainAllConnections, _ = strconv.Atoi(statusMain.Value)
	var statusLog Status
	tx.Raw("SHOW STATUS LIKE 'Threads_connected'").Scan(&statusLog)
	var logActiveConnections int
	tx.Raw("SELECT COUNT(*) AS active_connections FROM information_schema.PROCESSLIST WHERE `COMMAND` != 'Sleep'").Scan(&logActiveConnections)
	dashboardSummary.LogActiveConnections = logActiveConnections
	dashboardSummary.LogAllConnections, _ = strconv.Atoi(statusLog.Value)
	var statusLogEx Status
	txLogEx.Raw("SHOW STATUS LIKE 'Threads_connected'").Scan(&statusLogEx)
	var logExActiveConnections int
	txLogEx.Raw("SELECT COUNT(*) AS active_connections FROM information_schema.PROCESSLIST WHERE `COMMAND` != 'Sleep'").Scan(&logExActiveConnections)
	dashboardSummary.LogExActiveConnections = logExActiveConnections
	dashboardSummary.LogExAllConnections, _ = strconv.Atoi(statusLogEx.Value)

	return dashboardSummary, err
}

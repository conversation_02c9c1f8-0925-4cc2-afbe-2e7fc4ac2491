package model

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/songquanpeng/one-api/common/helper"

	"github.com/robfig/cron/v3"
	"github.com/songquanpeng/one-api/common/client"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/logger"
)

// 初始化 cron
var cronJob = cron.New()
var jobEntryMap = make(map[uint]cron.EntryID)
var CronJobRWMutex sync.RWMutex

type Job struct {
	Id          uint   `json:"id" gorm:"primaryKey;autoIncrement"`
	Name        string `json:"name" gorm:"type:varchar(200);uniqueIndex;not null;collate:utf8mb4_bin"`
	Status      int    `json:"status" gorm:"default:1"`
	JobType     int    `json:"job_type" gorm:"type:int;index"`
	Cron        string `json:"cron" gorm:"type:varchar(64);index"`
	Method      string `json:"method" gorm:"type:varchar(20);index"`
	Url         string `json:"url" gorm:"type:text;"`
	Header      string `json:"header"`
	Body        string `json:"body"`
	Description string `json:"description" gorm:"type:text;"`
	CreatedAt   int64  `json:"created_at" gorm:"bigint;index"`
}

func (job *Job) Insert() error {
	var err error
	err = DB.Create(job).Error
	return err
}

func (job *Job) Update() (int64, error) {
	// 排除创建时间
	result := DB.Model(job).Omit("created_at").Updates(job)
	return result.RowsAffected, result.Error
}

func (job *Job) Delete() (int64, error) {
	result := DB.Delete(job)
	return result.RowsAffected, result.Error
}

func DeleteJobById(id int) (int64, error) {
	result := DB.Where("id = ?", id).Delete(&Job{})
	return result.RowsAffected, result.Error
}

func DeleteJobByIds(ids []int) (int64, error) {
	result := DB.Where("id in (?)", ids).Delete(&Job{})
	return result.RowsAffected, result.Error
}

func GetJobById(id int) *Job {
	var job *Job
	var err error
	err = DB.Where("id = ?", id).First(&job).Error
	if err != nil {
		return nil
	}
	return job
}

func GetJobByName(word string) *Job {
	var job *Job
	var err error
	err = DB.Where("name = ?", word).First(&job).Error
	if err != nil {
		return nil
	}
	return job
}

func GetAllJobs(startIdx int, num int, jobType int, name string, cron string) ([]*Job, error) {
	var jobs []*Job
	var err error
	var tx = DB
	if name != "" {
		tx = tx.Where("name = ?", name)
	}
	if cron != "" {
		tx = tx.Where("cron = ?", cron)
	}
	if jobType != 0 {
		tx = tx.Where("job_type = ?", jobType)
	}
	err = tx.Offset(startIdx).Limit(num).Find(&jobs).Error
	if err != nil {
		return nil, err
	}
	return jobs, nil
}

func CountJobs(jobType int, name string, corn string) (int64, error) {
	var count int64
	var err error
	var tx = DB
	if name != "" {
		tx = tx.Where("name = ?", name)
	}
	if corn != "" {
		tx = tx.Where("cron = ?", corn)
	}
	if jobType != 0 {
		tx = tx.Where("job_type = ?", jobType)
	}
	err = tx.Model(&Job{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

func InitJobs() {
	var db = DB
	// 从数据库中读取所有的定时任务
	var jobs []Job
	db.Find(&jobs)
	CronJobRWMutex.Lock()
	// 将读取到的定时任务添加到 cron 中
	for _, job := range jobs {
		if job.Status == 1 {
			addJobToCron(job)
		}
	}
	// 启动 cron 调度器
	cronJob.Start()
	CronJobRWMutex.Unlock()
}

func addJobToCron(job Job) {
	entryID, addJobErr := cronJob.AddFunc(job.Cron, func() {
		logger.Info(nil, fmt.Sprintf("Running job:%s", job.Name))
		// 替换占位符
		url := replacePlaceholders(job.Url)
		body := replacePlaceholders(job.Body)
		req, err := http.NewRequest(job.Method, url, bytes.NewBuffer([]byte(body)))
		if err != nil {
			logger.Error(nil, fmt.Sprintf("Running job error http.NewRequest: %v", err))
			return
		}
		// 处理 header
		if job.Header != "" {
			header := make(map[string]string)
			err = json.Unmarshal([]byte(job.Header), &header)
			if err != nil {
				logger.Error(nil, fmt.Sprintf("Running job error json.Unmarshal : %v", err))
				return
			}
			for k, v := range header {
				req.Header.Set(k, replacePlaceholders(v))
			}
		}
		resp, err := client.HTTPClient.Do(req)
		if err != nil {
			logger.Error(nil, fmt.Sprintf("Running job error client.HTTPClient.Do : %v", err))
			return
		}
		defer func(Body io.ReadCloser) {
			if Body == nil {
				return
			}
			err := Body.Close()
			if err != nil {
				logger.Error(nil, fmt.Sprintf("Running job error Body.Close(): %v", err))
			}
		}(resp.Body)
		if resp.StatusCode != 200 {
			// 解析resp.Body成字符串
			bodyBytes, _ := io.ReadAll(resp.Body)
			logger.Error(nil, fmt.Sprintf("Running job error not 200 , StatusCode is [%d]: %v", resp.StatusCode, string(bodyBytes)))
			return
		}
		logger.Info(nil, fmt.Sprintf("Job completed:%s", job.Name))
	})
	if addJobErr != nil {
		logger.Error(nil, fmt.Sprintf("Adding job[%s] to cron error: %v", job.Name, addJobErr))
		return
	}
	jobEntryMap[job.Id] = entryID
	logger.Info(nil, fmt.Sprintf("Added job to cron:[%s] The cron is [%s]", job.Name, job.Cron))

	// 每天凌晨2点执行清理mj base64文件
	helper.SafeGoroutine(func() {
		for {
			now := time.Now()
			next := now.Add(time.Hour * 24)
			next = time.Date(next.Year(), next.Month(), next.Day(), 2, 0, 0, 0, next.Location())
			t := time.NewTimer(next.Sub(now))
			<-t.C
			cleanupMJBase64Storage()
		}
	})
}

// 新增函数：替换占位符
func replacePlaceholders(input string) string {
	placeholders := map[string]string{
		"{{ACCESS_TOKEN}}":   CacheGetRootUserAccessToken(),
		"{{SERVER_ADDRESS}}": config.ServerAddress,
		// 可以根据需要添加更多占位符
	}

	for placeholder, value := range placeholders {
		input = strings.ReplaceAll(input, placeholder, value)
	}

	return input
}

func cleanupMJBase64Storage() {
	var baseDir string

	// 检查 storage/mj 是否存在
	if _, err := os.Stat("storage/mj"); os.IsNotExist(err) {
		// 如果不存在，则使用 /data/storage/mj
		baseDir = "/data/storage/mj"
	} else {
		baseDir = "storage/mj"
	}

	retentionDays := config.MidjourneyBase64StorageRetentionDays
	if retentionDays <= 0 {
		return // 0表示永久保存
	}

	cutoffTime := time.Now().AddDate(0, 0, -retentionDays)

	// 第一遍遍历删除过期文件
	err := filepath.Walk(baseDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过目录，只处理文件
		if info.IsDir() {
			return nil
		}

		// 检查文件修改时间
		if info.ModTime().Before(cutoffTime) {
			err := os.Remove(path)
			if err != nil {
				logger.SysError(fmt.Sprintf("Failed to remove old MJ base64 file %s: %v", path, err))
			} else {
				logger.SysLog(fmt.Sprintf("Removed old MJ base64 file: %s", path))
			}
		}
		return nil
	})

	if err != nil {
		logger.SysError(fmt.Sprintf("Error cleaning up MJ base64 files: %v", err))
	}

	// 第二遍遍历删除空目录
	var dirs []string
	err = filepath.Walk(baseDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() {
			dirs = append(dirs, path)
		}
		return nil
	})

	// 从最深层的目录开始删除
	for i := len(dirs) - 1; i >= 0; i-- {
		dir := dirs[i]
		// 读取目录内容
		files, err := os.ReadDir(dir)
		if err != nil {
			logger.SysError(fmt.Sprintf("Error reading directory %s: %v", dir, err))
			continue
		}

		// 如果目录为空且不是基础目录，则删除
		if len(files) == 0 && dir != baseDir {
			err = os.Remove(dir)
			if err != nil {
				logger.SysError(fmt.Sprintf("Failed to remove empty directory %s: %v", dir, err))
			} else {
				logger.SysLog(fmt.Sprintf("Removed empty directory: %s", dir))
			}
		}
	}
}

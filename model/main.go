package model

import (
	"database/sql"
	"fmt"

	"github.com/go-mysql-org/go-mysql/canal"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/env"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/common/random"
	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	gormlogger "gorm.io/gorm/logger" // 给gorm的logger添加别名

	"log"
	"os"
	"strings"
	"time"
)

var DB *gorm.DB
var LOG_DB *gorm.DB
var LOG_EX_DB *gorm.DB

var SLAVE_DB *gorm.DB
var SLAVE_LOG_DB *gorm.DB
var SLAVE_LOG_EX_DB *gorm.DB

// DwDb 配置数仓数据源,用于保存用户聊天数据
var DwDb *gorm.DB

// 数据库类型缓存，避免重复检测
var (
	DBType           string
	LogDBType        string
	LogExDBType      string
	SlaveDBType      string
	SlaveLogDBType   string
	SlaveLogExDBType string
)

// 数据库类型判断函数 - 使用缓存的类型信息
func IsDBSQLite() bool {
	return DBType == "sqlite"
}

func IsDBMySQL() bool {
	return DBType == "mysql"
}

func IsDBPostgreSQL() bool {
	return DBType == "postgresql"
}

func IsLogDBSQLite() bool {
	return LogDBType == "sqlite"
}

func IsLogDBMySQL() bool {
	return LogDBType == "mysql"
}

func IsLogDBPostgreSQL() bool {
	return LogDBType == "postgresql"
}

func IsLogExDBSQLite() bool {
	return LogExDBType == "sqlite"
}

func IsLogExDBMySQL() bool {
	return LogExDBType == "mysql"
}

func IsLogExDBPostgreSQL() bool {
	return LogExDBType == "postgresql"
}

func CreateRootAccountIfNeed() error {
	var user User
	//if user.Status != util.UserStatusEnabled {
	if err := DB.First(&user).Error; err != nil {
		logger.SysLog("no user exists, creating a root user for you: username is root, password is 123456")
		hashedPassword, err := common.Password2Hash("123456")
		if err != nil {
			return err
		}
		accessToken := random.GetUUID()
		if config.InitialRootAccessToken != "" {
			accessToken = config.InitialRootAccessToken
		}
		rootUser := User{
			Username:    "root",
			Password:    hashedPassword,
			Role:        RoleRootUser,
			Status:      UserStatusEnabled,
			DisplayName: "Root User",
			AccessToken: accessToken,
			Quota:       ***************,
		}
		DB.Create(&rootUser)
		if config.InitialRootToken != "" {
			logger.SysLog("creating initial root token as requested")
			token := Token{
				Id:             1,
				UserId:         rootUser.Id,
				Key:            config.InitialRootToken,
				Status:         TokenStatusEnabled,
				Name:           "Initial Root Token",
				CreatedTime:    helper.GetTimestamp(),
				AccessedTime:   helper.GetTimestamp(),
				ExpiredTime:    -1,
				RemainQuota:    ***************,
				UnlimitedQuota: true,
			}
			DB.Create(&token)
		}
	}
	return nil
}

func chooseDB(envName string) (*gorm.DB, error) {
	dsn := os.Getenv(envName)

	switch {
	case strings.HasPrefix(dsn, "postgres://"):
		// Use PostgreSQL
		return openPostgreSQL(dsn)
	case dsn != "":
		// Use MySQL
		return openMySQL(dsn)
	default:
		// Use SQLite
		return openSQLite()
	}
}

func openPostgreSQL(dsn string) (*gorm.DB, error) {
	logger.SysLog("using PostgreSQL as database")
	common.UsingPostgreSQL = true
	dbConf := &gorm.Config{
		PrepareStmt: true,
	}

	// 仅在非调试模式下配置 logger
	if !config.DebugSQLEnabled {
		dbConf.Logger = getGormLogger()
	}

	return gorm.Open(postgres.New(postgres.Config{
		DSN:                  dsn,
		PreferSimpleProtocol: true,
	}), dbConf)
}

func openMySQL(dsn string) (*gorm.DB, error) {
	logger.SysLog("using MySQL as database")
	common.UsingMySQL = true
	dbConf := &gorm.Config{
		PrepareStmt: true,
	}

	// 仅在非调试模式下配置 logger
	if !config.DebugSQLEnabled {
		dbConf.Logger = getGormLogger()
	}

	return gorm.Open(mysql.Open(dsn), dbConf)
}

func openSQLite() (*gorm.DB, error) {
	logger.SysLog("SQL_DSN not set, using SQLite as database")
	common.UsingSQLite = true
	dsn := fmt.Sprintf("%s?_busy_timeout=%d", common.SQLitePath, common.SQLiteBusyTimeout)
	dbConf := &gorm.Config{
		PrepareStmt: true,
	}

	// 仅在非调试模式下配置 logger
	if !config.DebugSQLEnabled {
		dbConf.Logger = getGormLogger()
	}

	return gorm.Open(sqlite.Open(dsn), dbConf) // 修正这里，使用 sqlite.Open
}

func InitDB(envName string) (db *gorm.DB, err error) {
	db, err = chooseDB(envName)
	if err == nil {
		// 设置数据库类型缓存
		DBType = GetDBType(db)

		if config.DebugSQLEnabled {
			db = db.Debug()
		}
		sqlDB, err := db.DB()
		if err != nil {
			return nil, err
		}
		sqlDB.SetMaxIdleConns(env.Int("SQL_MAX_IDLE_CONNS", 100))
		sqlDB.SetMaxOpenConns(env.Int("SQL_MAX_OPEN_CONNS", 1000))
		sqlDB.SetConnMaxLifetime(time.Second * time.Duration(env.Int("SQL_MAX_LIFETIME", 60)))

		if !config.IsMasterNode {
			return db, err
		}
		if common.UsingMySQL {
			_, _ = sqlDB.Exec("DROP INDEX idx_channels_key ON channels;") // TODO: delete this line when most users have upgraded
		}
		logger.SysLog("database migration started")
		// 数据迁移备份
		//Migrate(db)
		err = db.AutoMigrate(&Channel{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&ChannelExtend{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&ChannelGroup{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&Token{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&TokenExtend{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&Group{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&User{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&UserExtend{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&Option{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&Redemption{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&Ability{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&Log{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&LogExtend{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&Midjourney{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&TopUp{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&Job{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&SensitiveWord{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&Checkin{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&PackagePlan{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&CustomPrompts{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&PackagePlanInstance{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&CashTransaction{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&Agency{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&AgencyCommissionSettlement{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&SensitiveWordHit{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&FileMetadata{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&ChannelModelMetrics{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&QuotaData{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&DynamicRouterGroup{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&DynamicRouterUpstream{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&DynamicRouterEndpoint{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&AccessToken{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&UserTimeoutConfig{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&UserNotificationSetting{})
		if err != nil {
			return nil, err
		}
		err = db.AutoMigrate(&Task{})
		if err != nil {
			return nil, err
		}
		DB = db
		logger.SysLog("database migrated")
		// 初始化新的数据库适配器
		if err := InitDatabaseAdapter(); err != nil {
			logger.SysError("Failed to initialize database adapter: " + err.Error())
			// 不致命错误，继续使用原有的SQL方式
		}
		return db, err
	} else {
		logger.FatalLog(err.Error())
	}
	return db, err
}

func InitDBLogDsn(envName string) (db *gorm.DB, err error) {
	db, err = chooseDB(envName)
	if err == nil {
		// 设置LOG数据库类型缓存
		LogDBType = GetDBType(db)

		if config.DebugSQLEnabled {
			db = db.Debug()
		}
		sqlDB, err := db.DB()
		if err != nil {
			return nil, err
		}
		sqlDB.SetMaxIdleConns(env.Int("SQL_MAX_IDLE_CONNS", 100))
		sqlDB.SetMaxOpenConns(env.Int("SQL_MAX_OPEN_CONNS", 1000))
		sqlDB.SetConnMaxLifetime(time.Second * time.Duration(env.Int("SQL_MAX_LIFETIME", 60)))
		if !config.IsMasterNode {
			return db, err
		}
		logger.SysLog("database migration started")
		// 数据迁移备份
		//Migrate(db)
		err = db.AutoMigrate(&Log{})
		if err != nil {
			return nil, err
		}
		logger.SysLog("log dsn database migrated")
		return db, err
	} else {
		logger.FatalLog(err.Error())
	}
	return db, err
}

func InitDBLogExDsn(envName string) (db *gorm.DB, err error) {
	db, err = chooseDB(envName)
	if err == nil {
		// 设置LOG_EX数据库类型缓存
		LogExDBType = GetDBType(db)

		if config.DebugSQLEnabled {
			db = db.Debug()
		}
		sqlDB, err := db.DB()
		if err != nil {
			return nil, err
		}
		sqlDB.SetMaxIdleConns(env.Int("SQL_MAX_IDLE_CONNS", 100))
		sqlDB.SetMaxOpenConns(env.Int("SQL_MAX_OPEN_CONNS", 1000))
		sqlDB.SetConnMaxLifetime(time.Second * time.Duration(env.Int("SQL_MAX_LIFETIME", 60)))
		if !config.IsMasterNode {
			return db, err
		}
		logger.SysLog("database migration started")
		// 数据迁移备份
		//Migrate(db)
		err = db.AutoMigrate(&LogExtend{})
		if err != nil {
			return nil, err
		}
		logger.SysLog("log ex dsn database migrated")
		return db, err
	} else {
		logger.FatalLog(err.Error())
	}
	return db, err
}

func migrateLOGDB() error {
	var err error
	if err = LOG_DB.AutoMigrate(&Log{}); err != nil {
		return err
	}
	return nil
}

func setDBConns(db *gorm.DB) *sql.DB {
	if config.DebugSQLEnabled {
		db = db.Debug()
	}

	sqlDB, err := db.DB()
	if err != nil {
		logger.FatalLog("failed to connect database: " + err.Error())
		return nil
	}

	sqlDB.SetMaxIdleConns(env.Int("SQL_MAX_IDLE_CONNS", 100))
	sqlDB.SetMaxOpenConns(env.Int("SQL_MAX_OPEN_CONNS", 1000))
	sqlDB.SetConnMaxLifetime(time.Second * time.Duration(env.Int("SQL_MAX_LIFETIME", 60)))
	return sqlDB
}

func closeDB(db *gorm.DB) error {
	sqlDB, err := db.DB()
	if err != nil {
		return err
	}
	err = sqlDB.Close()
	return err
}

func CloseDB() error {
	if LOG_DB != DB {
		err := closeDB(LOG_DB)
		if err != nil {
			return err
		}
	}
	if LOG_EX_DB != DB && LOG_EX_DB != LOG_DB {
		err := closeDB(LOG_EX_DB)
		if err != nil {
			return err
		}
	}
	if SLAVE_DB != nil {
		err := closeDB(SLAVE_DB)
		if err != nil {
			return err
		}
	}
	if SLAVE_LOG_DB != nil {
		err := closeDB(SLAVE_LOG_DB)
		if err != nil {
			return err
		}
	}
	if SLAVE_LOG_EX_DB != nil {
		err := closeDB(SLAVE_LOG_EX_DB)
		if err != nil {
			return err
		}
	}
	return closeDB(DB)
}

// Migrate 数据迁移脚本
func Migrate(db *gorm.DB) {
	// 如果当前是sqlite则不执行迁移
	if common.UsingSQLite {
		return
	}
	//tableName := "your_models"         // Gorm默认使用结构体名的蛇形复数作为表名
	indexName := "idx_top_ups_trade_no" // 你想检查的索引名称
	// 检查表是否存在
	tableExists := db.Migrator().HasTable(&TopUp{})
	if !tableExists {
		return
	}
	// 检查索引是否存在
	indexExists := db.Migrator().HasIndex(&TopUp{}, indexName)
	if !indexExists {
		logger.SysLog("Index NOT exists. Proceeding with table deletion...")
		err := backupTable(db, "top_ups")
		if err != nil {
			logger.SysError(fmt.Sprintf("failed to backup table: %v", err))
			return
		}
		// 如果索引不存在，执行删除表逻辑
		if err := db.Migrator().DropTable(&TopUp{}); err != nil {
			logger.SysError(fmt.Sprintf("failed to drop table: %v", err))
			return
		}

	} else {
		logger.SysLog("Index does exist. Skipping table deletion.")
		// 索引不存在的逻辑处理
	}
}

func backupTable(db *gorm.DB, tableName string) error {
	backupTableName := tableName + "_backup" + time.Now().Format("20060102150405")
	// 备份表，复制结构和数据
	if err := db.Exec("CREATE TABLE " + backupTableName + " LIKE " + tableName).Error; err != nil {
		return err
	}
	if err := db.Exec("INSERT INTO " + backupTableName + " SELECT * FROM " + tableName).Error; err != nil {
		return err
	}
	return nil
}

func InitSlaveDB() {
	needStartBinlogMonitor := false
	var err error
	if os.Getenv("SLAVE_SQL_DSN") != "" {
		dsn := os.Getenv("SLAVE_SQL_DSN")
		if strings.HasPrefix(dsn, "postgres://") {
			SLAVE_DB, err = gorm.Open(postgres.New(postgres.Config{
				DSN:                  dsn,
				PreferSimpleProtocol: true, // disables implicit prepared statement usage
			}), &gorm.Config{
				PrepareStmt: true, // precompile SQL
			})
			if err == nil {
				needStartBinlogMonitor = true
			}
		} else {
			SLAVE_DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
				PrepareStmt: true, // precompile SQL
			})
			if err == nil {
				needStartBinlogMonitor = true
			}
		}

	}
	if os.Getenv("SLAVE_LOG_SQL_DSN") != "" {
		dsn := os.Getenv("SLAVE_LOG_SQL_DSN")
		if strings.HasPrefix(dsn, "postgres://") {
			SLAVE_LOG_DB, err = gorm.Open(postgres.New(postgres.Config{
				DSN:                  dsn,
				PreferSimpleProtocol: true, // disables implicit prepared statement usage
			}), &gorm.Config{
				PrepareStmt: true, // precompile SQL
			})
			if err == nil {
				needStartBinlogMonitor = true
			}
		} else {
			SLAVE_LOG_DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
				PrepareStmt: true, // precompile SQL
			})
			if err == nil {
				needStartBinlogMonitor = true
			}
		}
	}
	if os.Getenv("SLAVE_LOG_EX_SQL_DSN") != "" {
		dsn := os.Getenv("SLAVE_LOG_EX_SQL_DSN")
		if strings.HasPrefix(dsn, "postgres://") {
			SLAVE_LOG_EX_DB, err = gorm.Open(postgres.New(postgres.Config{
				DSN:                  dsn,
				PreferSimpleProtocol: true, // disables implicit prepared statement usage
			}), &gorm.Config{
				PrepareStmt: true, // precompile SQL
			})
			if err == nil {
				needStartBinlogMonitor = true
			}
		} else {
			SLAVE_LOG_EX_DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
				PrepareStmt: true, // precompile SQL
			})
			if err == nil {
				needStartBinlogMonitor = true
			}
		}
	}

	if needStartBinlogMonitor {
		helper.SafeGoroutine(startBinlogMonitor)
	}

}

var dsnCurrentSchema = ""

func startBinlogMonitor() {
	dsn := os.Getenv("SQL_DSN")
	if dsn != "" {
		// 解析数据库名
		dbName := strings.Split(dsn, "/")[1]
		dsnCurrentSchema = dbName
		// 解析用户名和密码
		userPwd := strings.Split(strings.Split(dsn, "@")[0], ":")
		user := userPwd[0]
		pwd := userPwd[1]
		// 解析主机和端口也就是小括号中间的部分,不包含小括号 也就是 root:12345678@tcp(127.0.0.1:3306)/shellapi 中的127.0.0.1:3306
		hostPort := strings.Split(strings.Split(dsn, "@tcp(")[1], ")/")[0]
		// 创建 canal 实例
		cfg := canal.NewDefaultConfig()
		cfg.Addr = fmt.Sprintf(hostPort)
		cfg.User = user
		cfg.Password = pwd
		cfg.Dump.TableDB = dbName
		cfg.Dump.Tables = []string{}

		// 打印cfg的所有属性
		logger.SysLog(fmt.Sprintf("canal config: %v", cfg))

		c, err := canal.NewCanal(cfg)
		if err != nil {
			logger.SysError(fmt.Sprintf("failed to create canal instance: %v", err))
			return
		}
		// 注册事件处理器
		c.SetEventHandler(&eventHandler{})
		// 获取当前的 binlog 位置
		pos, err := c.GetMasterPos()
		if err != nil {
			logger.SysError(fmt.Sprintf("failed to get master position: %v", err))
			return
		}
		// 启动 canal
		helper.SafeGoroutine(func() {
			logger.SysLog(fmt.Sprintf("Starting canal from binlog position: %s", pos))
			if err := c.RunFrom(pos); err != nil {
				logger.SysError(fmt.Sprintf("failed to start canal: %v", err))
			}
		})
	}
}

type eventHandler struct {
	canal.DummyEventHandler
}

func (h *eventHandler) OnRow(e *canal.RowsEvent) error {
	defer func() {
		if r := recover(); r != nil {
			logger.SysError(fmt.Sprintf("Recovered from panic in OnRow: %v", r))
		}
	}()
	if e.Table.Schema != dsnCurrentSchema {
		return nil
	}
	logger.SysLog(fmt.Sprintf("Binlog Event: %s.%s.%s", e.Table.Schema, e.Table.Name, e.Action))
	// 根据事件类型执行相应的操作
	switch e.Action {
	case canal.InsertAction:
		if len(e.Rows) > 0 {
			// 执行插入操作
			for _, row := range e.Rows {
				if len(row) > 0 {
					rowMap := make(map[string]interface{})
					for i, col := range e.Table.Columns {
						rowMap[col.Name] = row[i]
					}
					if err := SLAVE_DB.Table(e.Table.Name).Create(rowMap).Error; err != nil {
						logger.SysError(fmt.Sprintf("Error inserting row: %v", err))
					}
				} else {
					logger.SysError("Empty row for InsertAction")
				}
			}
		} else {
			logger.SysError("Empty rows for InsertAction")
		}
	case canal.UpdateAction:
		if len(e.Rows) > 0 {
			// 执行更新操作
			for _, row := range e.Rows {
				if len(row) > 0 {
					if id, ok := row[0].(int64); ok {
						rowMap := make(map[string]interface{})
						for i, col := range e.Table.Columns {
							rowMap[col.Name] = row[i]
						}
						if err := SLAVE_DB.Table(e.Table.Name).Where("id = ?", id).Updates(rowMap).Error; err != nil {
							logger.SysError(fmt.Sprintf("Error updating row: %v", err))
						}
					} else {
						logger.SysError(fmt.Sprintf("Invalid id value: %v", row[0]))
					}
				} else {
					logger.SysError("Empty row for UpdateAction")
				}
			}
		} else {
			logger.SysError("Empty rows for UpdateAction")
		}
	case canal.DeleteAction:
		if len(e.Rows) > 0 {
			// 执行删除操作
			for _, row := range e.Rows {
				if len(row) > 0 {
					if id, ok := row[0].(int64); ok {
						if err := SLAVE_DB.Table(e.Table.Name).Where("id = ?", id).Delete(nil).Error; err != nil {
							logger.SysError(fmt.Sprintf("Error deleting row: %v", err))
						}
					} else {
						logger.SysError(fmt.Sprintf("Invalid id value: %v", row[0]))
					}
				} else {
					logger.SysError("Empty row for DeleteAction")
				}
			}
		} else {
			logger.SysError("Empty rows for DeleteAction")
		}
	}

	return nil
}

// 创建自定义的 logger 配置函数
func getGormLogger() gormlogger.Interface {
	newLogger := gormlogger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		gormlogger.Config{
			SlowThreshold:             time.Second * time.Duration(env.Int("SQL_SLOW_THRESHOLD", 10)), // 从环境变量获取慢查询阈值，默认10秒
			LogLevel:                  gormlogger.Warn,
			IgnoreRecordNotFoundError: true,
			Colorful:                  false,
		},
	)
	return newLogger
}

// 删除原有的低效率通用检测函数，使用上面的缓存方案
// GetDBType 检测给定数据库连接的类型（保留，供初始化时使用）
func GetDBType(db *gorm.DB) string {
	if db == nil {
		return "unknown"
	}

	dialectName := db.Dialector.Name()
	switch dialectName {
	case "postgres":
		return "postgresql"
	case "mysql":
		return "mysql"
	case "sqlite":
		return "sqlite"
	default:
		return dialectName
	}
}

package model

import (
	"fmt"
	"time"

	"github.com/songquanpeng/one-api/common/logger"
)

type SensitiveWordHit struct {
	Id              int    `json:"id" gorm:"primaryKey;autoIncrement"`
	UserId          int    `json:"user_id" gorm:"index"`
	Username        string `json:"username"`
	TokenId         int    `json:"token_id" gorm:"index"`
	TokenName       string `json:"token_name"`
	SensitiveWordId int    `json:"sensitive_word_id" gorm:"index"`
	SensitiveWord   string `json:"sensitive_word"`
	Category        string `json:"category"`
	Severity        int    `json:"severity"`
	InputContent    string `json:"input_content"`
	OutputContent   string `json:"output_content"`
	HitCount        int    `json:"hit_count"`
	CreatedAt       int64  `json:"created_at" gorm:"index"`
}

// Insert 插入敏感词命中记录
func (hit *SensitiveWordHit) Insert() error {
	hit.CreatedAt = time.Now().Unix()
	return DB.Create(hit).Error
}

// GetSensitiveWordHits 获取敏感词命中记录列表
func GetSensitiveWordHits(startIdx int, num int, userId int, tokenId int, sensitiveWordId int, startTime time.Time, endTime time.Time) ([]*SensitiveWordHit, error) {
	var hits []*SensitiveWordHit
	tx := DB

	if userId != 0 {
		tx = tx.Where("user_id = ?", userId)
	}
	if tokenId != 0 {
		tx = tx.Where("token_id = ?", tokenId)
	}
	if sensitiveWordId != 0 {
		tx = tx.Where("sensitive_word_id = ?", sensitiveWordId)
	}
	if !startTime.IsZero() {
		tx = tx.Where("created_at >= ?", startTime.Unix())
	}
	if !endTime.IsZero() {
		tx = tx.Where("created_at <= ?", endTime.Unix())
	}

	err := tx.Order("created_at desc").Offset(startIdx).Limit(num).Find(&hits).Error
	return hits, err
}

// CountSensitiveWordHits 统计敏感词命中记录数量
func CountSensitiveWordHits(userId int, tokenId int, sensitiveWordId int, startTime time.Time, endTime time.Time) (int64, error) {
	var count int64
	tx := DB

	if userId != 0 {
		tx = tx.Where("user_id = ?", userId)
	}
	if tokenId != 0 {
		tx = tx.Where("token_id = ?", tokenId)
	}
	if sensitiveWordId != 0 {
		tx = tx.Where("sensitive_word_id = ?", sensitiveWordId)
	}
	if !startTime.IsZero() {
		tx = tx.Where("created_at >= ?", startTime)
	}
	if !endTime.IsZero() {
		tx = tx.Where("created_at <= ?", endTime)
	}

	err := tx.Model(&SensitiveWordHit{}).Count(&count).Error
	return count, err
}

// DeleteSensitiveWordHit 删除敏感词命中记录
func DeleteSensitiveWordHit(id int) error {
	return DB.Delete(&SensitiveWordHit{}, id).Error
}

// DeleteSensitiveWordHitsByTime 按时间删除敏感词命中记录
func DeleteSensitiveWordHitsByTime(beforeTime time.Time) error {
	return DB.Where("created_at < ?", beforeTime).Delete(&SensitiveWordHit{}).Error
}

// RecordSensitiveWordHit 记录敏感词命中信息
func RecordSensitiveWordHit(meta Meta, word string, inputContent string, outputContent string) error {
	// 获取敏感词信息
	sensitiveWord := GetSensitiveWordByWord(word)
	if sensitiveWord == nil {
		logger.SysError(fmt.Sprintf("敏感词不存在: %s", word))
		return nil
	}

	// 记录命中信息
	hit := &SensitiveWordHit{
		UserId:          meta.UserId,
		Username:        meta.UserName,
		TokenId:         meta.TokenId,
		TokenName:       meta.TokenName,
		SensitiveWordId: sensitiveWord.Id,
		SensitiveWord:   word,
		Category:        sensitiveWord.Category,
		Severity:        sensitiveWord.Severity,
		InputContent:    inputContent,  // 用户输入内容
		OutputContent:   outputContent, // AI的响应(提示语)
		HitCount:        1,
	}

	return hit.Insert()
}

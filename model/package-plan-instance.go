package model

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"gorm.io/gorm"
	"time"
)

type PackagePlanInstance struct { //套餐实例,用户购买套餐后生成,一个用户可以购买多个套餐生成多个实例
	Id                int     `json:"id"`                                   //ID
	PackagePlanId     int     `json:"package_plan_id"`                      // 套餐ID,对应套餐管理表中的id
	UserId            int     `json:"user_id"`                              //用户ID
	OrderId           int     `json:"order_id"`                             //订单ID
	CreatedAt         int64   `json:"created_at"`                           //创建时间，unix时间戳
	Name              string  `json:"name"`                                 //套餐名称
	Group             string  `json:"group"`                                //套餐分组
	Status            int     `json:"status" gorm:"default:1"`              //1:正常，2:禁用 3:过期 4:耗尽
	Description       string  `json:"description"`                          //套餐描述
	Type              int     `json:"type"`                                 //1:按量，2：按次，3：按时长
	Price             float64 `json:"price"`                                //价格，单位：美金
	ExpiredTime       int64   `json:"expired_time" gorm:"default:-1"`       // 过期时间，unix时间戳，-1为无限制
	ValidPeriod       int     `json:"valid_period" gorm:"default:-1"`       //有效期，单位：天，-1为无限制
	FirstBuyDiscount  float64 `json:"first_buy_discount" gorm:"default:1"`  //首次购买折扣，0-1
	OneTimeBuy        bool    `json:"one_time_buy" gorm:"default:false"`    //是否只允许购买一次
	RepeatBuy         bool    `json:"repeat_buy" gorm:"default:false"`      //是否允许套餐未消耗完时重复购买
	RateLimitNum      int     `json:"rate_limit_num" gorm:"default:0"`      // 限制次数
	RateLimitDuration int     `json:"rate_limit_duration" gorm:"default:0"` // 限制周期
	AutoRenew         bool    `json:"auto_renew" gorm:"default:false"`      //是否自动续费
	AvailableModels   string  `json:"available_models"`
	Times             int     `json:"times"`
	Quota             int64   `json:"quota"`
	UsedQuota         int64   `json:"used_quota" gorm:"default:0"` // 已使用的配额
	UsedTimes         int     `json:"used_times" gorm:"default:0"` // 已使用的次数
}

// GetUserPackagePlanGroup 获取用户套餐分组
func GetUserPackagePlanGroup(userId int) ([]string, error) {
	var packagePlanInstances []*PackagePlanInstance
	err := DB.Where("user_id = ?", userId).Find(&packagePlanInstances).Error
	groups := make([]string, 0)
	for _, packagePlanInstance := range packagePlanInstances {
		groups = append(groups, packagePlanInstance.Group)
	}
	return groups, err
}

// GetUserActivePackagePlans 查找当前用户名下所有激活状态的套餐,按照失效时间倒序,id正序,返回套餐实例列表
func GetUserActivePackagePlans(userId int) ([]PackagePlanInstance, error) {
	var packagePlanInstances []PackagePlanInstance
	err := DB.Where("user_id = ? and status = 1", userId).Order("expired_time desc, id").Find(&packagePlanInstances).Error
	return packagePlanInstances, err
}

// GetFirstUserPackagePlanGroup 获取用户第一个套餐分组
func GetFirstUserPackagePlanGroup(userId int) (string, error) {
	var packagePlanInstances PackagePlanInstance
	err := DB.Where("user_id = ?", userId).First(&packagePlanInstances).Error
	return packagePlanInstances.Group, err
}

func GetAllPackagePlanInstances(userId int, startIdx int, num int, id int, name string, status int) ([]*PackagePlanInstance, error) {
	var packagePlanInstances []*PackagePlanInstance
	var err error
	tx := DB
	if userId != 0 {
		tx = tx.Where("user_id = ?", userId)
	}
	if id != 0 {
		tx = tx.Where("id = ?", id)
	}
	if name != "" {
		tx = tx.Where("name LIKE ?", "%"+name+"%")
	}
	if status != 0 {
		tx = tx.Where("status = ?", status)
	}
	if num == 0 {
		err = tx.Find(&packagePlanInstances).Error
	} else {
		err = tx.Order("id desc").Limit(num).Offset(startIdx).Find(&packagePlanInstances).Error
	}
	return packagePlanInstances, err
}

func CountAllPackagePlanInstances(userId int, id int, name string, status int) ([]*PackagePlanInstance, error) {
	var packagePlanInstances []*PackagePlanInstance
	var err error
	tx := DB
	if userId != 0 {
		tx = tx.Where("user_id = ?", userId)
	}
	if id != 0 {
		tx = tx.Where("id = ?", id)
	}
	if name != "" {
		tx = tx.Where("name LIKE ?", "%"+name+"%")
	}
	if status != 0 {
		tx = tx.Where("status = ?", status)
	}
	err = tx.Find(&packagePlanInstances).Error
	return packagePlanInstances, err
}

func GetPackagePlanInstanceById(id int) (*PackagePlanInstance, error) {
	var packagePlanInstances PackagePlanInstance
	err := DB.Where("id = ?", id).First(&packagePlanInstances).Error
	return &packagePlanInstances, err
}

func CreatePackagePlanInstance(packagePlanInstances *PackagePlanInstance) error {
	return DB.Create(packagePlanInstances).Error
}

func UpdatePackagePlanInstance(packagePlanInstances *PackagePlanInstance) error {
	return DB.Save(packagePlanInstances).Error
}

func CacheUpdatePackagePlanInstance(packagePlanInstances *PackagePlanInstance) error {
	if common.RedisEnabled {
		return OnlyCacheUpdatePackagePlanInstance(packagePlanInstances)
	} else {
		return UpdatePackagePlanInstance(packagePlanInstances)
	}
}

func CacheCreatePackagePlanInstanceAndSyncToDbByTx(tx *gorm.DB, packagePlanInstances *PackagePlanInstance) error {
	if common.RedisEnabled {
		err := tx.Create(packagePlanInstances).Error
		if err != nil {
			return err
		}
		return OnlyCacheCreatePackagePlanInstance(packagePlanInstances)
	} else {
		return tx.Create(packagePlanInstances).Error
	}
}

func OnlyCacheCreatePackagePlanInstance(packagePlanInstances *PackagePlanInstance) error {
	val, err := json.Marshal(packagePlanInstances)
	if err != nil {
		return err
	}
	rdb := common.RDB
	// 更新缓存
	lock := helper.NewRedisLock(rdb, fmt.Sprintf("lock:package_plan_inst:%d", packagePlanInstances.Id))
	if !lock.Lock(time.Second * 10) {
		return fmt.Errorf("lock failed")
	}
	defer lock.Unlock()
	err = rdb.Set(context.Background(), fmt.Sprintf("package_plan_inst:%d:%d", packagePlanInstances.UserId, packagePlanInstances.Id), val, 0).Err()
	if err != nil {
		return err
	}
	return nil
}

func CacheUpdatePackagePlanInstanceAndSyncToDb(packagePlanInstance *PackagePlanInstance) error {
	if common.RedisEnabled {
		err := OnlyCacheUpdatePackagePlanInstance(packagePlanInstance)
		if err != nil {
			return err
		}
		return UpdatePackagePlanInstance(packagePlanInstance)
	} else {
		return UpdatePackagePlanInstance(packagePlanInstance)
	}
}

func OnlyCacheUpdatePackagePlanInstance(packagePlanInstances *PackagePlanInstance) error {
	val, err := json.Marshal(packagePlanInstances)
	if err != nil {
		return err
	}
	rdb := common.RDB
	// 更新缓存
	lock := helper.NewRedisLock(rdb, fmt.Sprintf("lock:package_plan_inst:%d", packagePlanInstances.Id))
	if !lock.Lock(time.Second * 10) {
		return fmt.Errorf("lock failed")
	}
	defer lock.Unlock()
	if packagePlanInstances.ExpiredTime != -1 && packagePlanInstances.ExpiredTime < time.Now().Unix() || packagePlanInstances.Status != 1 {
		err = rdb.Del(context.Background(), fmt.Sprintf("package_plan_inst:%d:%d", packagePlanInstances.UserId, packagePlanInstances.Id)).Err()
	} else {
		// 更新缓存
		err = rdb.Set(context.Background(), fmt.Sprintf("package_plan_inst:%d:%d", packagePlanInstances.UserId, packagePlanInstances.Id), val, 0).Err()
	}
	if err != nil {
		return err
	}
	return nil
}

func DeletePackagePlanInstance(id int) error {
	return DB.Delete(&PackagePlanInstance{}, id).Error
}

func GetPackagePlanInstanceByOrderId(orderId int) (*PackagePlanInstance, error) {
	var packagePlanInstances PackagePlanInstance
	err := DB.Where("order_id = ?", orderId).First(&packagePlanInstances).Error
	return &packagePlanInstances, err
}

func GetPackagePlanInstanceByUserId(userId int) ([]*PackagePlanInstance, error) {
	var packagePlanInstances []*PackagePlanInstance
	err := DB.Where("user_id = ?", userId).Find(&packagePlanInstances).Error
	return packagePlanInstances, err
}

func GetActivePackagePlanInstanceByUserId(userId int) ([]*PackagePlanInstance, error) {
	var packagePlanInstances []*PackagePlanInstance
	err := DB.Where("user_id = ? and status = 1", userId).Find(&packagePlanInstances).Error
	return packagePlanInstances, err
}

func GetPackagePlanInstanceByPackagePlanId(packagePlanId int) ([]*PackagePlanInstance, error) {
	var packagePlanInstances []*PackagePlanInstance
	err := DB.Where("package_plan_id = ?", packagePlanId).Find(&packagePlanInstances).Error
	return packagePlanInstances, err
}

func GetPackagePlanInstanceByUserIdAndPackagePlanId(userId int, packagePlanId int) (*PackagePlanInstance, error) {
	var packagePlanInstances PackagePlanInstance
	err := DB.Where("user_id = ? AND package_plan_id = ?", userId, packagePlanId).First(&packagePlanInstances).Error
	return &packagePlanInstances, err
}

func PostConsumePackagePlanInstanceQuota(packagePlanInstances PackagePlanInstance, userId int, quota int64) (err error) {
	// 根据套餐类型扣减对应的套餐实例的quota或者times
	if packagePlanInstances.Type == 1 {
		// 按量
		err = DB.Model(&PackagePlanInstance{}).Where("id = ?", packagePlanInstances.Id).Updates(
			map[string]interface{}{
				"used_quota": gorm.Expr("used_quota + ?", quota),
			},
		).Error
	} else if packagePlanInstances.Type == 2 {
		// 按次
		err = DB.Model(&PackagePlanInstance{}).Where("id = ?", packagePlanInstances.Id).Updates(
			map[string]interface{}{
				"used_times": gorm.Expr("used_times + ?", 1),
			},
		).Error
	}
	return err
}

func PreConsumePackagePlanInstanceQuota(packagePlanInstance PackagePlanInstance, userId int, quota int64) (err error) {
	if quota < 0 {
		return errors.New("quota 不能为负数！")
	}
	activePackagePlan, err := CacheGetPackagePlanInstanceById(packagePlanInstance.UserId, packagePlanInstance.Id)
	if err != nil {
		return err
	}
	if activePackagePlan.Status != 1 {
		return errors.New("套餐实例已停用")
	}

	if activePackagePlan.Type == 2 && packagePlanInstance.Times-activePackagePlan.UsedTimes <= 0 {
		// 更新套餐实例状态
		activePackagePlan.Status = 4
		err = CacheUpdatePackagePlanInstanceAndSyncToDb(activePackagePlan)
		if err != nil {
			return err
		}
		return errors.New("套餐实例次数已用尽")
	} else if activePackagePlan.Type == 1 && activePackagePlan.Quota-activePackagePlan.UsedQuota < quota {
		// 更新套餐实例状态
		activePackagePlan.Status = 4
		err = CacheUpdatePackagePlanInstanceAndSyncToDb(activePackagePlan)
		if err != nil {
			return err
		}
		return errors.New("套餐实例额度不足")
	} else if activePackagePlan.ExpiredTime != -1 && activePackagePlan.ExpiredTime < time.Now().Unix() {
		// 更新套餐实例状态
		activePackagePlan.Status = 3
		err = CacheUpdatePackagePlanInstanceAndSyncToDb(activePackagePlan)
		if err != nil {
			return err
		}
		return errors.New("套餐实例已过期")
	}

	err = CachePostConsumePackagePlanInstanceQuota(packagePlanInstance, userId, quota)
	return err
}

func CachePostConsumePackagePlanInstanceQuota(packagePlanInstances PackagePlanInstance, userId int, quota int64) (err error) {
	if common.RedisEnabled {
		// 获取真实的套餐实例
		realPackagePlanInstances, err := CacheGetPackagePlanInstanceById(packagePlanInstances.UserId, packagePlanInstances.Id)
		// 根据套餐类型扣减对应的套餐实例的quota或者times
		if realPackagePlanInstances.Type == 1 {
			// 按量
			realPackagePlanInstances.UsedQuota += quota
		} else if realPackagePlanInstances.Type == 2 {
			// 按次
			realPackagePlanInstances.UsedTimes += 1
		}
		err = CacheUpdatePackagePlanInstance(realPackagePlanInstances)
		return err
	} else {
		return PostConsumePackagePlanInstanceQuota(packagePlanInstances, userId, quota)
	}
}

// SyncRedisPackagePlanInstanceToDb 同步Redis中的套餐实例到结构化数据库
func SyncRedisPackagePlanInstanceToDb(frequency int) {
	for {
		time.Sleep(time.Duration(frequency) * time.Second)
		logger.SysLog("SyncRedisPackagePlanInstanceToDb...")
		packagePlanInstances, err := common.RedisGetValuesWithPrefix[PackagePlanInstance]("package_plan_inst:")
		if err != nil {
			logger.SysError(fmt.Sprintf("SyncRedisPackagePlanInstanceToDb failed: %s", err.Error()))
			continue
		}
		if len(packagePlanInstances) == 0 {
			logger.SysLog("SyncRedisPackagePlanInstanceToDb finished , but no data found")
			continue
		}
		for _, packagePlanInstance := range packagePlanInstances {
			err = UpdatePackagePlanInstance(&packagePlanInstance)
			if err != nil {
				logger.SysError(fmt.Sprintf("SyncRedisPackagePlanInstanceToDb UpdatePackagePlanInstance failed: %s", err.Error()))
			}
		}
		logger.SysLog("SyncRedisPackagePlanInstanceToDb finished ! !")
	}

}

// InitPackagePlanInstanceCache 初始化套餐实例缓存
func InitPackagePlanInstanceCache() {
	// 先清空原有的缓存
	err := common.RedisDelByPrefix("package_plan_inst:")
	packagePlanInstances, err := GetAllPackagePlanInstances(0, 0, 0, 0, "", 1)
	if err != nil {
		logger.SysError("InitPackagePlanInstanceCache failed: " + err.Error())
	}
	data := make(map[string]interface{})
	for _, packagePlanInstance := range packagePlanInstances {
		jsonString, err := json.Marshal(packagePlanInstance)
		if err != nil {
			logger.SysError("InitPackagePlanInstanceCache failed: " + err.Error())
		}
		data[fmt.Sprintf("package_plan_inst:%d:%d", packagePlanInstance.UserId, packagePlanInstance.Id)] = jsonString
	}
	if len(data) == 0 {
		logger.SysLog("InitPackagePlanInstanceCache finished , but no data found")
		return
	}
	err = common.RedisMSet(data)
	if err != nil {
		logger.SysError("InitPackagePlanInstanceCache failed: " + err.Error())
	} else {
		logger.SysLog("InitPackagePlanInstanceCache finished ! ! loaded " + fmt.Sprintf("%d", len(packagePlanInstances)) + " records")
	}
}

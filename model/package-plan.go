package model

import (
	"errors"
	"strings"

	"github.com/songquanpeng/one-api/common"

	"gorm.io/gorm"
)

type PackagePlan struct { //套餐管理
	Id                int     `json:"id"`                                   //ID
	CreatedAt         int64   `json:"created_at"`                           //创建时间，unix时间戳
	Name              string  `json:"name"`                                 //套餐名称
	Group             string  `json:"group"`                                //套餐分组
	VisibleGroups     string  `json:"visible_groups"`                       //可以购买套餐的分组,只针对于某几个分组的用户可见,为空则所有用户可见,逗号分隔,例如：group1,group2
	Status            int     `json:"status" gorm:"default:1"`              //1:正常，2:下架，3:回收站
	Description       string  `json:"description"`                          //套餐描述
	Type              int     `json:"type"`                                 //1:按量，2：按次，3：按时长
	Price             float64 `json:"price"`                                //价格，单位：美金
	ExpiredTime       int64   `json:"expired_time" gorm:"default:-1"`       //失效时间，unix时间戳，-1为永不失效
	ValidPeriod       int     `json:"valid_period" gorm:"default:-1"`       //有效期，单位：天，-1为无限制
	FirstBuyDiscount  float64 `json:"first_buy_discount" gorm:"default:1"`  //首次购买折扣，0-1
	OneTimeBuy        bool    `json:"one_time_buy" gorm:"default:false"`    //是否只允许购买一次
	RepeatBuy         bool    `json:"repeat_buy" gorm:"default:false"`      //是否允许套餐未消耗完时重复购买
	RateLimitNum      int     `json:"rate_limit_num" gorm:"default:0"`      // 限制次数
	RateLimitDuration int     `json:"rate_limit_duration" gorm:"default:0"` // 限制周期
	AutoRenew         bool    `json:"auto_renew" gorm:"default:false"`      //是否自动续费
	//FIXME：之前写错了，应该是库存-1为不限制，0为无库存，请检查实现时是否有问题
	//FIXME：没有表不会自动创建
	Inventory       int    `json:"inventory" gorm:"default:0"` //库存，0为不限制
	AvailableModels string `json:"available_models"`
	Times           int    `json:"times"`
	Quota           int    `json:"quota"`
	IsXYHelper      bool   `json:"is_xy_helper" gorm:"default:false"`
}

func GetAllPackagePlans(startIdx int, num int, id int, name string, status int, visibleGroups string) ([]*PackagePlan, error) {
	var packagePlan []*PackagePlan
	var err error
	tx := DB
	if id != 0 {
		tx = tx.Where("id = ?", id)
	}
	if name != "" {
		tx = tx.Where("name LIKE ?", "%"+name+"%")
	}
	if status != 0 {
		tx = tx.Where("status = ?", status)
	}
	if visibleGroups != "" {
		// 按照逗号拆分
		visibleGroups_ := strings.Split(visibleGroups, ",")
		// FIND_IN_SET
		for _, visibleGroup := range visibleGroups_ {
			if common.UsingPostgreSQL {
				tx = tx.Where("position(? in visible_groups) > 0", visibleGroup)
			} else {
				tx = tx.Where("FIND_IN_SET(?, visible_groups)", visibleGroup)
			}
		}
	}
	err = tx.Order("id desc").Limit(num).Offset(startIdx).Find(&packagePlan).Error
	return packagePlan, err
}

func CountAllPackagePlans(id int, name string, status int, visibleGroups string) (int64, error) {
	var err error
	tx := DB
	if id != 0 {
		tx = tx.Where("id = ?", id)
	}
	if name != "" {
		tx = tx.Where("name LIKE ?", "%"+name+"%")
	}
	if status != 0 {
		tx = tx.Where("status = ?", status)
	}
	if visibleGroups != "" {
		// 按照逗号拆分
		visibleGroups_ := strings.Split(visibleGroups, ",")
		// FIND_IN_SET
		for _, visibleGroup := range visibleGroups_ {
			if common.UsingPostgreSQL {
				tx = tx.Where("position(? in visible_groups) > 0", visibleGroup)
			} else {
				tx = tx.Where("FIND_IN_SET(?, visible_groups)", visibleGroup)
			}
		}
	}
	var count int64
	err = tx.Model(&PackagePlan{}).Count(&count).Error
	return count, err
}

func GetPackagePlanById(id int64) (*PackagePlan, error) {
	var packagePlans PackagePlan
	err := DB.Where("id = ?", id).First(&packagePlans).Error
	return &packagePlans, err
}

func CreatePackagePlan(packagePlans *PackagePlan) error {
	err := DB.Create(packagePlans).Error
	return err
}

func UpdatePackagePlan(id int, packagePlans *PackagePlan) error {
	err := DB.Model(&PackagePlan{}).Where("id = ?", id).Updates(packagePlans).Error
	return err
}

func DeletePackagePlan(id int) error {
	err := DB.Where("id = ?", id).Delete(&PackagePlan{}).Error
	return err
}

func DeletePackagePlanByIds(ids []int) error {
	err := DB.Where("id in (?)", ids).Delete(&PackagePlan{}).Error
	return err
}

func DecreasePackagePlanInventoryByTx(tx *gorm.DB, id int, num int) error {
	err := tx.Model(&PackagePlan{}).Where("id = ?", id).Update("inventory", gorm.Expr("inventory - ?", num)).Error
	if err != nil {
		return err
	}
	// 校验库存是否足够
	var packagePlan PackagePlan
	err = tx.Where("id = ?", id).First(&packagePlan).Error
	if err != nil {
		return err
	}
	if packagePlan.Inventory < 0 {
		return errors.New("库存不足")
	}
	return err
}

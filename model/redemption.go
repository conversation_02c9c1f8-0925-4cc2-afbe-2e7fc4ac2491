package model

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strconv"

	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	billingratio "github.com/songquanpeng/one-api/relay/billing/ratio"
	"gorm.io/gorm"
)

const (
	RedemptionCodeStatusEnabled  = 1 // don't use 0, 0 is the default value!
	RedemptionCodeStatusDisabled = 2 // also don't use 0
	RedemptionCodeStatusUsed     = 3 // also don't use 0
	RedemptionCodeExpired        = 4 // also don't use 0
)

type Redemption struct {
	Id           int    `json:"id"`
	UserId       int    `json:"user_id"`
	Key          string `json:"key" gorm:"type:char(32);uniqueIndex"`
	Status       int    `json:"status" gorm:"default:1"`
	Name         string `json:"name" gorm:"index"`
	Quota        int64  `json:"quota" gorm:"bigint;default:100"`
	CreatedTime  int64  `json:"created_time" gorm:"bigint"`
	RedeemedTime int64  `json:"redeemed_time" gorm:"bigint"`
	Count        int    `json:"count" gorm:"-:all"`                    // only for api request
	ExpiredTime  int64  `json:"expired_time" gorm:"bigint;default:-1"` // -1 means never expired
	Username     string `json:"username" gorm:"-:migration;<-:false"`  // 兑换用户名，仅用于显示，迁移时忽略且禁止写入
}

func GetAllRedemptions(startIdx int, num int, name string, status int) ([]*Redemption, error) {
	var redemptions []*Redemption
	var err error
	tx := DB.Table("redemptions").
		Select("redemptions.*, users.username").
		Joins("LEFT JOIN users ON redemptions.user_id = users.id")
	if name != "" {
		tx = tx.Where("redemptions.name LIKE ?", "%"+name+"%")
	}
	if status != 0 {
		tx = tx.Where("redemptions.status = ?", status)
	}
	err = tx.Order("redemptions.id desc").Limit(num).Offset(startIdx).Find(&redemptions).Error
	return redemptions, err
}

func CountAllRedemptions(name string, status int) (count int64, err error) {
	tx := DB.Model(&Redemption{})
	if name != "" {
		tx = tx.Where("name LIKE ?", "%"+name+"%")
	}
	if status != 0 {
		tx = tx.Where("status = ?", status)
	}
	err = tx.Count(&count).Error
	return count, err
}

func SearchRedemptions(keyword string) (redemptions []*Redemption, err error) {
	err = DB.Table("redemptions").
		Select("redemptions.*, users.username").
		Joins("LEFT JOIN users ON redemptions.user_id = users.id").
		Where("redemptions.id = ? or redemptions.name LIKE ?", keyword, "%"+keyword+"%").
		Find(&redemptions).Error
	return redemptions, err
}

func GetRedemptionById(id int) (*Redemption, error) {
	if id == 0 {
		return nil, errors.New("id 为空！")
	}
	redemption := Redemption{Id: id}
	var err error = nil
	err = DB.First(&redemption, "id = ?", id).Error
	return &redemption, err
}

func Redeem(ctx context.Context, key string, userId int) (quota int64, err error) {
	if key == "" {
		return 0, errors.New("未提供兑换码")
	}
	if userId == 0 {
		return 0, errors.New("无效的 user id")
	}
	redemption := &Redemption{}

	keyCol := "`key`"
	if common.UsingPostgreSQL {
		keyCol = `"key"`
	}

	err = DB.Transaction(func(tx *gorm.DB) error {
		err := tx.Where(keyCol+" = ?", key).First(redemption).Error
		if err != nil {
			return errors.New("无效的兑换码")
		}

		if redemption.Status != RedemptionCodeStatusEnabled {
			if redemption.Status == RedemptionCodeStatusDisabled {
				return errors.New("兑换码已禁用！")
			} else if redemption.Status == RedemptionCodeStatusUsed {
				return errors.New("兑换码已使用！")
			} else if redemption.Status == RedemptionCodeExpired {
				return errors.New("兑换码已过期！")
			}
		}

		if redemption.ExpiredTime != -1 {
			if redemption.ExpiredTime < helper.GetTimestamp() {
				if !common.RedisEnabled {
					//FIXME：failed to update redemption status, reason:database is locked 这里必现
					redemption.Status = common.RedemptionCodeExpired
					err := redemption.SelectUpdate()
					if err != nil {
						logger.SysError("failed to update redemption status, reason:" + err.Error())
					}
				}
				return errors.New("该兑换码已过期")
			}
		}

		err = tx.Model(&User{}).Where("id = ?", userId).Update("quota", gorm.Expr("quota + ?", redemption.Quota)).Error
		if err != nil {
			return err
		}
		redemption.RedeemedTime = helper.GetTimestamp()
		redemption.Status = RedemptionCodeStatusUsed
		redemption.UserId = userId // 更新兑换码的用户id，覆盖之前创建兑换码的用户id
		err = tx.Save(redemption).Error
		if err != nil {
			return err
		}
		err2 := AddInviteBonus(ctx, tx, userId, int(redemption.Quota))
		if err2 != nil {
			return err2
		}
		return err
	})
	if err != nil {
		return 0, errors.New("兑换失败，" + err.Error())
	}
	RecordLog(ctx, userId, LogTypeTopup, fmt.Sprintf("通过兑换码充值 %s", common.LogQuota(redemption.Quota)))
	if config.QuotaExpireEnabled && config.QuotaExpireDays > 0 {
		err = AddUserQuotaExpire(userId, config.QuotaExpireDays)
		if err != nil {
			return 0, errors.New("兑换失败，" + err.Error())
		}
	}
	// 确保缓存双写一致
	if common.RedisEnabled {
		redisErr := common.RedisIncreaseWithLuaCheckExists(fmt.Sprintf("user_quota:%d", userId), redemption.Quota)
		if redisErr != nil {
			logger.SysError("IncreaseUserQuotaAndRedis 缓存双写一致 error: " + redisErr.Error())
		}
	}
	return redemption.Quota, nil
}

func AddUserQuotaExpire(userId int, days int) error {
	// 查找当前用户
	user, err := GetUserById(userId, false)
	if err != nil {
		return err
	}
	// 给用户延长余额days天的有效期,也就是赋值为当前时间戳common.GetTimestamp()加上天数
	quotaExpireTime := helper.GetTimestamp() + int64(days)*86400
	err = DB.Model(&User{}).Where("id = ?", user.Id).Update("quota_expire_time", quotaExpireTime).Error
	if err != nil {
		return err
	}
	// 更新缓存
	err = CacheUpdateUserQuotaExpireTime(userId, quotaExpireTime)
	if err != nil {
		return err
	}
	return nil
}

func AddUserQuotaExpireByTx(tx *gorm.DB, userId int, days int) error {
	// 查找当前用户
	user, err := GetUserById(userId, false)
	if err != nil {
		return err
	}
	// 给用户延长余额days天的有效期,也就是赋值为当前时间戳common.GetTimestamp()加上天数
	quotaExpireTime := helper.GetTimestamp() + int64(days)*86400
	err = tx.Model(&User{}).Where("id = ?", user.Id).Update("quota_expire_time", quotaExpireTime).Error
	if err != nil {
		return err
	}
	// 更新缓存
	err = CacheUpdateUserQuotaExpireTime(userId, quotaExpireTime)
	if err != nil {
		return err
	}
	return nil
}

func AddInviteBonus(ctx context.Context, tx *gorm.DB, userId int, quota int) error {
	// 查找当前用户的邀请人
	user, err := GetUserById(userId, false)
	if err != nil {
		return err
	}
	if user.InviterId != 0 {
		inviter, err := GetUserById(user.InviterId, false)
		if err != nil {
			return err
		}
		// 给邀请人增加额度
		// 获取当前邀请人的组群下的倍率
		inviteBonusRatio := billingratio.GetInviteBonusRatio(inviter.Group)
		// 检查扩展信息是否存在并且如果不存在就自动创建,带事务
		_, err = CheckUserExtendAndCreateIfNotExist(inviter.Id, tx)
		if err != nil {
			return err
		}
		err = tx.Model(&UserExtend{}).Where("user_id = ?", inviter.Id).Update("aff_history", gorm.Expr("aff_history + ?", int(float64(quota)*inviteBonusRatio))).Error
		if err != nil {
			return err
		}
		err = tx.Model(&UserExtend{}).Where("user_id = ?", inviter.Id).Update("aff_quota", gorm.Expr("aff_quota + ?", int(float64(quota)*inviteBonusRatio))).Error
		if err != nil {
			return err
		}
		// 记录邀请人的日志
		RecordLog(ctx, inviter.Id, LogTypeInviteBonus, fmt.Sprintf("邀请 %s 充值获得 %s", user.Username, common.LogQuota(int64(float64(quota)*inviteBonusRatio))))
	}
	return nil
}

func AddInviteBonusByAgency(ctx context.Context, tx *gorm.DB, topup *TopUp, quota int) error {
	// 查找当前用户的邀请人
	user, err := GetUserById(topup.UserId, false)
	if err != nil {
		return err
	}
	if user.InviterId != 0 {
		inviter, err := GetUserById(user.InviterId, false)
		if err != nil {
			return err
		}
		// 给邀请人增加额度
		// 获取当前邀请人的组群下的倍率
		inviteBonusRatio := billingratio.GetInviteBonusRatio(inviter.Group)
		// 检查扩展信息是否存在并且如果不存在就自动创建,带事务
		_, err = CheckUserExtendAndCreateIfNotExist(inviter.Id, tx)
		if err != nil {
			return err
		}
		err = tx.Model(&UserExtend{}).Where("user_id = ?", inviter.Id).Update("aff_history", gorm.Expr("aff_history + ?", int(float64(quota)*inviteBonusRatio))).Error
		if err != nil {
			return err
		}
		err = tx.Model(&UserExtend{}).Where("user_id = ?", inviter.Id).Update("aff_quota", gorm.Expr("aff_quota + ?", int(float64(quota)*inviteBonusRatio))).Error
		if err != nil {
			return err
		}
		// 记录邀请人的日志
		RecordLog(ctx, inviter.Id, LogTypeInviteBonus, fmt.Sprintf("邀请 %s 充值获得 %s", user.Username, common.LogQuota(int64(float64(quota)*inviteBonusRatio))))
	}

	// 增加代理商返利
	if user.AgencyId != 0 {
		agency, err := GetAgencyByUserId(user.AgencyId)
		if err != nil {
			// 获取代理商失败不应该影响正常充值
			logger.SysError("获取代理商倍率失败, err: " + err.Error())
			return nil
		}
		// 原始的钱
		originMoney := topup.MoneyOrigin
		// 现在的钱
		money := topup.Money
		// 代理商返利的倍率
		agencyBonusRatio := agency.CommissionRate
		// 代理商返利金额=(现在的钱-原始的钱) * 代理商返利的倍率
		bonusMoney := (money - originMoney) * agencyBonusRatio
		// 需要判断代理商返利金额是否大于0
		if bonusMoney <= 0 {
			bonusMoney = 0
		}
		err = tx.Model(&Agency{}).Where("user_id = ?", user.AgencyId).Update("history_commission_money", gorm.Expr("history_commission_money + ?", bonusMoney)).Error
		if err != nil {
			return err
		}
		err = tx.Model(&Agency{}).Where("user_id = ?", user.AgencyId).Update("commission_money", gorm.Expr("commission_money + ?", bonusMoney)).Error
		if err != nil {
			return err
		}
		// 记录销售额
		err = tx.Model(&Agency{}).Where("user_id = ?", user.AgencyId).Update("sales_volume", gorm.Expr("sales_volume + ?", money)).Error
		if err != nil {
			return err
		}
		// 获取更新后的代理商佣金余额
		var updatedCommissionMoney float64
		err = tx.Model(&Agency{}).Where("user_id = ?", user.AgencyId).
			Select("commission_money").
			Row().
			Scan(&updatedCommissionMoney)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				// 如果没有找到记录，可能是新代理商，我们可以假设余额为0或者刚刚添加的佣金
				updatedCommissionMoney = bonusMoney
				logger.SysError(fmt.Sprintf("代理商 (ID: %d) 记录不存在，假设初始佣金为刚添加的金额", user.AgencyId))
			} else {
				// 其他错误
				logger.SysError("获取代理商佣金余额失败, err: " + err.Error())
				// 在这里，我们可以选择使用一个默认值或者最近的已知余额
				updatedCommissionMoney = bonusMoney // 或者其他合适的默认值
			}
		}

		// 创建 CashTransaction 记录
		cashTransaction := &CashTransaction{
			UserId:        user.AgencyId,
			RelatedUserId: user.Id,
			Type:          TransactionTypeAgencyBonus,
			Amount:        bonusMoney,
			Balance:       updatedCommissionMoney,
			SourceAmount:  money,
			RelatedId:     strconv.Itoa(topup.Id),
			Status:        TransactionStatusSuccess,
			CreatedAt:     helper.GetTimestamp(),
			Description:   fmt.Sprintf("用户 %s 充值 %.2f 元，代理商获得返佣 %.2f 元", user.Username, money, bonusMoney),
		}
		if err := tx.Create(cashTransaction).Error; err != nil {
			logger.SysError("创建代理商返利记录失败, err: " + err.Error())
			// 考虑是否需要在这里回滚事务或采取其他措施
		} else {
			// 记录代理商的日志
			RecordLog(ctx, user.AgencyId, LogTypeAgencyBonus, fmt.Sprintf("代理商返利 [%.2f] 元", bonusMoney))
		}

	}
	return nil
}

func (redemption *Redemption) Insert() error {
	var err error
	err = DB.Create(redemption).Error
	return err
}

func (redemption *Redemption) SelectUpdate() error {
	// This can update zero values
	return DB.Model(redemption).Select("redeemed_time", "status").Updates(redemption).Error
}

// Update Make sure your token's fields is completed, because this will update non-zero values
func (redemption *Redemption) Update() error {
	if redemption.Status == common.RedemptionCodeStatusUsed {
		return errors.New("不能修改已被使用的兑换码！")
	}
	var err error
	err = DB.Model(redemption).Select("name", "status", "quota", "redeemed_time").Updates(redemption).Error
	return err
}

func (redemption *Redemption) Delete() error {
	var err error
	err = DB.Delete(redemption).Error
	return err
}

func DeleteRedemptionById(id int) (err error) {
	if id == 0 {
		return errors.New("id 为空！")
	}
	redemption := Redemption{Id: id}
	err = DB.Where(redemption).First(&redemption).Error
	if err != nil {
		return err
	}
	return redemption.Delete()
}

func DeleteRedemptionByIds(ids []int) (row int64, err error) {
	if len(ids) == 0 {
		return 0, errors.New("ids 为空！")
	}
	var redemption Redemption
	// 不能删除初始令牌,最后要返回删除的个数
	result := DB.Where("id in (?) ", ids).Delete(&redemption)
	return result.RowsAffected, result.Error
}

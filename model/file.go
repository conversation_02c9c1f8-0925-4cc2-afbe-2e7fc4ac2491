package model

import (
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/songquanpeng/one-api/common/config"
)

// FileMetadata 文件元数据表
type FileMetadata struct {
	ID             int64  `json:"id" gorm:"primaryKey;autoIncrement"`
	FileHash       string `json:"file_hash" gorm:"type:varchar(64);not null;uniqueIndex;comment:文件SHA256哈希值"`
	OriginalName   string `json:"original_name" gorm:"type:varchar(255);not null;comment:原始文件名"`
	FilePath       string `json:"file_path" gorm:"type:varchar(255);not null;comment:存储路径"`
	FileSize       int64  `json:"file_size" gorm:"not null;comment:文件大小(字节)"`
	MimeType       string `json:"mime_type" gorm:"type:varchar(127);not null;comment:文件MIME类型"`
	UploadTime     int64  `json:"upload_time" gorm:"not null;comment:上传时间"`
	ReferenceCount int    `json:"reference_count" gorm:"default:1;comment:引用计数"`
	UploaderID     int64  `json:"uploader_id" gorm:"not null;comment:上传者ID"`
	Status         int    `json:"status" gorm:"type:smallint;default:1;comment:状态:1-正常,0-已删除"`
	CreatedAt      int64  `json:"created_at" gorm:"autoCreateTime:unix"`
	UpdatedAt      int64  `json:"updated_at" gorm:"autoUpdateTime:unix"`
	URL            string `json:"url" gorm:"-"`
}

// TableName 指定表名
func (FileMetadata) TableName() string {
	return "file_metadata"
}

// GetFileByHash 通过文件hash获取文件元数据
func GetFileByHash(hash string) (*FileMetadata, error) {
	var file FileMetadata
	result := DB.Where("file_hash = ? AND status = 1", hash).First(&file)
	if result.Error != nil {
		return nil, result.Error
	}
	return &file, nil
}

// GetFileByID 通过ID获取文件元数据
func GetFileByID(id int64) (*FileMetadata, error) {
	var file FileMetadata
	result := DB.First(&file, id)
	if result.Error != nil {
		return nil, result.Error
	}
	return &file, nil
}

// GetFileByPath 通过文件路径获取文件元数据
func GetFileByPath(filePath string) (*FileMetadata, error) {
	var file FileMetadata
	result := DB.Where("file_path = ? AND status = 1", filePath).First(&file)
	if result.Error != nil {
		return nil, result.Error
	}
	return &file, nil
}

// Create 创建文件元数据记录
func (f *FileMetadata) Create() error {
	return DB.Create(f).Error
}

// IncrementReferenceCount 增加引用计数
func (f *FileMetadata) IncrementReferenceCount() error {
	return DB.Model(f).Update("reference_count", f.ReferenceCount+1).Error
}

// DecrementReferenceCount 减少引用计数
func (f *FileMetadata) DecrementReferenceCount() error {
	if f.ReferenceCount > 0 {
		return DB.Model(f).Update("reference_count", f.ReferenceCount-1).Error
	}
	return nil
}

// SoftDelete 软删除文件
func (f *FileMetadata) SoftDelete() error {
	return DB.Model(f).Update("status", 0).Error
}

// GetUnreferencedFiles 获取引用计数为0的文件
func GetUnreferencedFiles() ([]FileMetadata, error) {
	var files []FileMetadata
	result := DB.Where("reference_count = 0 AND status = 1").Find(&files)
	return files, result.Error
}

// GetExpiredFiles 获取超过指定时间的已删除文件
func GetExpiredFiles(retentionDays int) ([]FileMetadata, error) {
	var files []FileMetadata
	expireTime := time.Now().AddDate(0, 0, -retentionDays)
	result := DB.Where("status = 0 AND upload_time < ?", expireTime).Find(&files)
	return files, result.Error
}

// BatchDelete 批量物理删除文件记录
func BatchDelete(ids []int64) error {
	return DB.Delete(&FileMetadata{}, ids).Error
}

type FileListParams struct {
	Page      int
	PageSize  int
	Search    string
	MimeType  string
	SortField string
	SortOrder string
}

func GetFileList(params FileListParams, userId int64, isAdmin bool) ([]FileMetadata, int64, error) {
	var files []FileMetadata
	var total int64
	query := DB.Model(&FileMetadata{})

	// 如果不是管理员，只能看到自己的文件
	if !isAdmin {
		query = query.Where("uploader_id = ?", userId)
	}

	// 只查询未删除的文件
	query = query.Where("status = ?", 1)

	// 搜索条件
	if params.Search != "" {
		query = query.Where("original_name LIKE ?", "%"+params.Search+"%")
	}

	// MIME类型过滤
	if params.MimeType != "" {
		query = query.Where("mime_type = ?", params.MimeType)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 排序
	if params.SortField != "" {
		order := "DESC"
		if params.SortOrder == "ascend" {
			order = "ASC"
		}
		query = query.Order(fmt.Sprintf("%s %s", params.SortField, order))
	} else {
		// 默认按上传时间倒序
		query = query.Order("upload_time DESC")
	}

	// 分页
	offset := (params.Page - 1) * params.PageSize
	query = query.Offset(offset).Limit(params.PageSize)

	if err := query.Find(&files).Error; err != nil {
		return nil, 0, err
	}

	// 处理每个文件的URL
	for i := range files {
		// 从文件路径构建URL
		pathParts := strings.Split(files[i].FilePath, "/")
		if len(pathParts) >= 2 {
			datePath := pathParts[len(pathParts)-2]
			fileName := pathParts[len(pathParts)-1]
			files[i].URL = fmt.Sprintf("%s/fileSystem/download/%s/%s",
				config.FileSystemServerAddress,
				datePath,
				url.PathEscape(fileName))
		}
	}

	return files, total, nil
}

func DeleteFile(id int64) error {
	return DB.Model(&FileMetadata{}).Where("id = ?", id).Update("status", 0).Error
}

// Update 更新文件元数据
func (f *FileMetadata) Update() error {
	return DB.Save(f).Error
}

// GetFileByHashIncludeDeleted 通过文件哈希获取文件信息（包括已删除的文件）
func GetFileByHashIncludeDeleted(hash string) (*FileMetadata, error) {
	var file FileMetadata
	result := DB.Where("file_hash = ?", hash).First(&file)
	if result.Error != nil {
		return nil, result.Error
	}
	return &file, nil
}

// PermanentDeleteFile 从数据库中彻底删除文件记录
func PermanentDeleteFile(id int64) error {
	return DB.Unscoped().Delete(&FileMetadata{}, id).Error
}

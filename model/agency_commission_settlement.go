package model

import (
	"time"
)

const (
	// Settlement Status
	SettlementStatusUnknown = iota
	SettlementStatusPending
	SettlementStatusCompleted
	SettlementStatusFailed
	SettlementStatusCancelled
)

// AgencyCommissionSettlement 代理商佣金结算记录
type AgencyCommissionSettlement struct {
	Id                    int     `json:"id" gorm:"primaryKey"`
	AgencyId              int     `json:"agency_id" gorm:"index"`               // 代理商ID
	AgencyUserId          int     `json:"agency_user_id" gorm:"index"`          // 代理商用户ID
	AgencyName            string  `json:"agency_name"`                          // 代理商名称（冗余存储，便于查询）
	AdminUserId           int     `json:"admin_user_id" gorm:"index"`           // 执行结算的管理员用户ID
	AdminUsername         string  `json:"admin_username"`                       // 管理员用户名（冗余存储）
	SettleAmount          float64 `json:"settle_amount"`                        // 结算金额
	BeforeCommission      float64 `json:"before_commission"`                    // 结算前佣金余额
	AfterCommission       float64 `json:"after_commission"`                     // 结算后佣金余额
	Status                int     `json:"status" gorm:"index;default:2"`        // 结算状态，默认为已完成
	Note                  string  `json:"note"`                                 // 结算备注
	CreatedAt             int64   `json:"created_at" gorm:"bigint;index"`       // 创建时间
	UpdatedAt             int64   `json:"updated_at" gorm:"bigint"`             // 更新时间
	SettlementBatchNumber string  `json:"settlement_batch_number" gorm:"index"` // 结算批次号（可选，用于批量结算）
}

// CreateAgencyCommissionSettlement 创建佣金结算记录
func CreateAgencyCommissionSettlement(settlement *AgencyCommissionSettlement) error {
	settlement.CreatedAt = time.Now().Unix()
	settlement.UpdatedAt = settlement.CreatedAt
	if settlement.Status == 0 {
		settlement.Status = SettlementStatusCompleted
	}
	return DB.Create(settlement).Error
}

// GetAgencyCommissionSettlementById 根据ID获取佣金结算记录
func GetAgencyCommissionSettlementById(id int) (*AgencyCommissionSettlement, error) {
	var settlement AgencyCommissionSettlement
	err := DB.First(&settlement, id).Error
	if err != nil {
		return nil, err
	}
	return &settlement, nil
}

// GetAgencyCommissionSettlements 获取代理商的佣金结算记录列表
func GetAgencyCommissionSettlements(agencyId int, page, pageSize int) ([]*AgencyCommissionSettlement, int64, error) {
	var settlements []*AgencyCommissionSettlement
	var total int64

	offset := (page - 1) * pageSize

	// 计算总数
	err := DB.Model(&AgencyCommissionSettlement{}).Where("agency_id = ?", agencyId).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err = DB.Where("agency_id = ?", agencyId).
		Order("created_at desc").
		Offset(offset).
		Limit(pageSize).
		Find(&settlements).Error

	if err != nil {
		return nil, 0, err
	}

	return settlements, total, nil
}

// GetAllAgencyCommissionSettlements 获取所有代理商的佣金结算记录（管理员用）
func GetAllAgencyCommissionSettlements(page, pageSize int, agencyId int, adminUserId int, status int) ([]*AgencyCommissionSettlement, int64, error) {
	var settlements []*AgencyCommissionSettlement
	var total int64

	offset := (page - 1) * pageSize
	query := DB.Model(&AgencyCommissionSettlement{})

	// 添加筛选条件
	if agencyId > 0 {
		query = query.Where("agency_id = ?", agencyId)
	}
	if adminUserId > 0 {
		query = query.Where("admin_user_id = ?", adminUserId)
	}
	if status > 0 {
		query = query.Where("status = ?", status)
	}

	// 计算总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err = query.Order("created_at desc").
		Offset(offset).
		Limit(pageSize).
		Find(&settlements).Error

	if err != nil {
		return nil, 0, err
	}

	return settlements, total, nil
}

// UpdateAgencyCommissionSettlementStatus 更新佣金结算记录状态
func UpdateAgencyCommissionSettlementStatus(id int, status int) error {
	return DB.Model(&AgencyCommissionSettlement{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":     status,
			"updated_at": time.Now().Unix(),
		}).Error
}

// GetAgencyCommissionSettlementStats 获取代理商佣金结算统计
func GetAgencyCommissionSettlementStats(agencyId int, startTime, endTime int64) (map[string]interface{}, error) {
	var result struct {
		TotalAmount float64 `json:"total_amount"`
		Count       int64   `json:"count"`
	}

	query := DB.Model(&AgencyCommissionSettlement{}).
		Where("agency_id = ? AND status = ?", agencyId, SettlementStatusCompleted)

	if startTime > 0 {
		query = query.Where("created_at >= ?", startTime)
	}
	if endTime > 0 {
		query = query.Where("created_at <= ?", endTime)
	}

	err := query.Select("COALESCE(SUM(settle_amount), 0) as total_amount, COUNT(*) as count").
		Scan(&result).Error

	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"total_amount": result.TotalAmount,
		"count":        result.Count,
	}, nil
}

// GetAdminSettlementStats 获取管理员结算统计
func GetAdminSettlementStats(adminUserId int, startTime, endTime int64) (map[string]interface{}, error) {
	var result struct {
		TotalAmount float64 `json:"total_amount"`
		Count       int64   `json:"count"`
	}

	query := DB.Model(&AgencyCommissionSettlement{}).
		Where("admin_user_id = ? AND status = ?", adminUserId, SettlementStatusCompleted)

	if startTime > 0 {
		query = query.Where("created_at >= ?", startTime)
	}
	if endTime > 0 {
		query = query.Where("created_at <= ?", endTime)
	}

	err := query.Select("COALESCE(SUM(settle_amount), 0) as total_amount, COUNT(*) as count").
		Scan(&result).Error

	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"total_amount": result.TotalAmount,
		"count":        result.Count,
	}, nil
}

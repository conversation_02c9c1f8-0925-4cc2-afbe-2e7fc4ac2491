package model

import (
	"context"
	"fmt"
	"strings"

	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"gorm.io/gorm"
)

type LogExtend struct {
	Id               int    `json:"id"`
	LogId            int    `json:"log_id" gorm:"index"`
	LogUuid          string `json:"log_uuid" gorm:"-"` // ClickHouse专用UUID关联，MySQL不使用，不参与GORM操作
	CreatedAt        int64  `json:"created_at" gorm:"bigint;index"`
	Prompt           string `json:"prompt"`
	Completion       string `json:"completion"`
	CompletionId     string `json:"completion_id" gorm:"type:varchar(200);column:completion_id"`
	UpstreamResponse string `json:"upstream_response"`         // 修改为 longtext 类型
	FullResponse     string `json:"full_response"`             // 修改为 longtext 类型
	RequestPath      string `json:"request_path" gorm:"index"` // 存储请求的 URL 路径
}

var TotalShouldDeleteHistoryLogDetailsCount int64 = 0
var TotalAffectedHistoryLogDetailsCount int64 = 0

var TotalShouldDeleteUnbindLogExCount int64 = 0
var TotalAffectedUnbindLogExCount int64 = 0

func (logExtend *LogExtend) Insert() error {
	var err error
	err = LOG_EX_DB.Create(logExtend).Error
	if err != nil {
		return err
	}
	return nil
}

func (logExtend *LogExtend) InsertByTx(tx *gorm.DB) error {
	var err error
	err = tx.Create(logExtend).Error
	if err != nil {
		return err
	}
	return nil
}

func (logExtend *LogExtend) Update() error {
	var err error
	err = LOG_EX_DB.Save(logExtend).Error
	if err != nil {
		return err
	}
	return nil
}

func (logExtend *LogExtend) UpdateByTx(tx *gorm.DB) error {
	var err error
	err = tx.Save(logExtend).Error
	if err != nil {
		return err
	}
	return nil
}

func (logExtend *LogExtend) Delete() error {
	var err error
	err = LOG_EX_DB.Delete(logExtend).Error
	if err != nil {
		return err
	}
	return nil
}

func (logExtend *LogExtend) DeleteByTx(tx *gorm.DB) error {
	var err error
	err = tx.Delete(logExtend).Error
	if err != nil {
		return err
	}
	return nil
}

func GetLogExtendByLogId(logId int) (*LogExtend, error) {
	// 如果启用了新的日志存储系统，使用LogManager
	if config.LogStorageEnabled {
		logManager := GetLogManager()
		return logManager.GetLogExtendByLogId(logId)
	}

	// 使用原有的直接数据库查询方式
	var logExtend *LogExtend
	var err error
	err = LOG_EX_DB.Where("log_id = ?", logId).First(&logExtend).Error
	if err != nil {
		return nil, err
	}
	return logExtend, nil
}

func GetLogExtendByLogIdByTx(tx *gorm.DB, logId int) (*LogExtend, error) {
	var logExtend *LogExtend
	var err error
	err = tx.Where("log_id = ?", logId).First(&logExtend).Error
	if err != nil {
		return nil, err
	}
	return logExtend, nil
}

func (logExtend *LogExtend) Get() error {
	var err error
	err = LOG_EX_DB.First(logExtend).Error
	if err != nil {
		return err
	}
	return nil
}

func (logExtend *LogExtend) GetByTx(tx *gorm.DB) error {
	var err error
	err = tx.First(logExtend).Error
	if err != nil {
		return err
	}
	return nil
}

func RecordLogExtend(ctx context.Context, log *Log, prompt string, completion string, completionId string, upstreamResponse string, fullResponse string, requestPath string) {
	// 添加空值检查
	if log == nil {
		logger.Error(ctx, "<RecordLogExtend> log is nil")
		return
	}

	// 使用统一的判断函数来决定是否记录各种日志内容
	if !ShouldLogDetail(log.UserId) {
		// 不记录详细日志
		prompt = ""
		completion = ""
		upstreamResponse = ""
		fullResponse = ""
	} else {
		// 记录详细日志，但根据具体配置判断是否记录响应
		if !ShouldLogUpstreamResponse(log.UserId) {
			upstreamResponse = ""
		}

		if !ShouldLogFullResponse(log.UserId) {
			fullResponse = ""
		}
	}

	// 判断是否开启详细日志模型白名单
	if config.LogDetailsModelWhitelistEnabled {
		// 按照逗号切分
		modelWhitelist := strings.Split(config.LogDetailsModelWhitelist, ",")
		// 判断是否在白名单中,不在白名单中直接返回
		var isInWhitelist bool
		for _, v := range modelWhitelist {
			if v == log.ModelName {
				isInWhitelist = true
				break
			}
		}
		if !isInWhitelist {
			prompt = ""
			completion = ""
			upstreamResponse = ""
			fullResponse = ""
		}
	}

	// 获取用户最大Prompt长度配置
	userMaxPromptLogLength, err := CacheGetUserMaxPromptLogLength(log.UserId)
	if err != nil {
		userMaxPromptLogLength = 0 // 出错时使用系统默认配置
	}

	var maxPromptLogLength int64
	if userMaxPromptLogLength == 0 {
		maxPromptLogLength = config.MaxPromptLogLength // 使用系统默认配置
	} else {
		maxPromptLogLength = userMaxPromptLogLength // 使用用户配置
	}

	// 应用长度限制
	if maxPromptLogLength > 0 {
		prompt = TruncateOptimized(prompt, maxPromptLogLength, "")
	}

	// 添加响应长度限制检查
	maxResponseLength := int64(16777215) // MySQL longtext 的最大长度限制
	upstreamResponse = TruncateOptimized(upstreamResponse, maxResponseLength, "")
	fullResponse = TruncateOptimized(fullResponse, maxResponseLength, "")
	completion = TruncateOptimized(completion, maxResponseLength, "")

	logExtend := &LogExtend{
		LogId:            log.Id,
		LogUuid:          log.Uuid, // 设置UUID用于ClickHouse关联
		CreatedAt:        helper.GetTimestamp(),
		Prompt:           prompt,
		Completion:       completion,
		CompletionId:     completionId,
		UpstreamResponse: upstreamResponse,
		FullResponse:     fullResponse,
		RequestPath:      requestPath,
	}

	// 如果启用了新的日志存储系统，使用LogManager
	if config.LogStorageEnabled {
		logManager := GetLogManager()
		err = logManager.RecordLogExtend(ctx, logExtend)
		if err != nil {
			logger.Error(ctx, "<RecordLogExtend> failed to record log extend via LogManager: "+err.Error())
		}
		return
	}

	// 使用原有的直接数据库插入方式
	err = logExtend.Insert()
	if err != nil {
		logger.Error(ctx, "<RecordLogExtend> failed to record log extend: "+err.Error())
	}
}

// 删除关联不上主表的记录(删除当前表中 log_id 不存在于 log 表中的记录)
func DeleteInvalidLogExtend() error {
	// 如果启用了新的日志存储系统，使用LogManager
	if config.LogStorageEnabled {
		logManager := GetLogManager()
		return logManager.DeleteInvalidLogExtend()
	}

	// 使用原有的直接数据库删除方式
	batchSize := 1000 // 每批删除的数据量
	// 获取数据库版本信息
	var version string
	var isMysql57 bool
	if versionErr := LOG_DB.Raw("SELECT VERSION()").Scan(&version).Error; versionErr != nil {
		isMysql57 = false
	} else if version == "" {
		isMysql57 = false
	} else if strings.HasPrefix(version, "5.7") {
		isMysql57 = true
	}
	var deleteQuery string

	if isMysql57 {
		deleteQuery = `
            DELETE le FROM log_extends le
            WHERE NOT EXISTS (
                SELECT 1 FROM logs l WHERE l.id = le.log_id
            )
            LIMIT ?
        `
	} else {
		deleteQuery = `
            DELETE FROM log_extends le
            WHERE NOT EXISTS (
                SELECT 1 FROM logs l WHERE l.id = le.log_id
            )
            LIMIT ?
        `
	}

	querySql := `SELECT count(1) FROM log_extends le
            WHERE NOT EXISTS (
                SELECT 1 FROM logs l WHERE l.id = le.log_id
            )`

	if err := LOG_DB.Raw(querySql).Scan(&TotalShouldDeleteUnbindLogExCount).Error; err != nil {
		return err
	}

	for {
		result := LOG_DB.Exec(deleteQuery, batchSize)

		if result.Error != nil {
			return result.Error
		}
		rowsAffected := result.RowsAffected
		TotalAffectedUnbindLogExCount += rowsAffected
		if rowsAffected == 0 {
			break
		}
	}
	return nil
}

func DeleteLogExByTimestamp(targetTimestamp int64) (int64, error) {
	// 如果启用了新的日志存储系统，使用LogManager
	if config.LogStorageEnabled {
		logManager := GetLogManager()
		return logManager.DeleteLogExtendByTimestamp(targetTimestamp)
	}

	// 使用原有的直接数据库删除方式
	var totalDeleted int64
	batchSize := 2500 // 每批删除的数据量
	// 构建删除条件
	conditions := []string{"created_at < ?"}
	args := []interface{}{targetTimestamp}

	// 统计需要删除的总条数
	var count int64
	if len(conditions) > 0 {
		query := fmt.Sprintf("SELECT COUNT(1) FROM log_extends WHERE %s", strings.Join(conditions, " AND "))
		err := LOG_EX_DB.Raw(query, args...).Scan(&count).Error
		if err != nil {
			return 0, err
		}
	} else {
		err := LOG_EX_DB.Model(&Log{}).Where("created_at < ?", targetTimestamp).Count(&count).Error
		if err != nil {
			return 0, err
		}
	}
	TotalShouldDeleteHistoryLogDetailsCount = count
	// 分批删除数据
	for {
		var result *gorm.DB
		if len(conditions) > 0 {
			query := fmt.Sprintf("DELETE FROM log_extends WHERE %s LIMIT ?", strings.Join(conditions, " AND "))
			result = LOG_EX_DB.Exec(query, append(args, batchSize)...)
		} else {
			result = LOG_EX_DB.Exec("DELETE FROM log_extends LIMIT ?", batchSize)
		}

		if result.Error != nil {
			return 0, result.Error
		}

		rowsAffected := result.RowsAffected
		totalDeleted += rowsAffected
		TotalAffectedHistoryLogDetailsCount = totalDeleted

		if rowsAffected < int64(batchSize) {
			break
		}
	}
	return totalDeleted, nil
}

// TruncateLogExtendTable 使用TRUNCATE方法清空log_extends表
func TruncateLogExtendTable(ctx context.Context) error {
	// 如果启用了新的日志存储系统，使用LogManager
	if config.LogStorageEnabled {
		logManager := GetLogManager()
		return logManager.TruncateLogExtendTable(ctx)
	}

	// 使用原有的直接数据库操作方式
	result := LOG_EX_DB.Exec("TRUNCATE TABLE log_extends")
	if result.Error != nil {
		logger.Error(ctx, "<TruncateLogExtendTable> failed to truncate log_extends table: "+result.Error.Error())
		return result.Error
	}

	logger.Info(ctx, "<TruncateLogExtendTable> successfully truncated log_extends table")
	return nil
}

// ShouldLogDownstreamError 判断是否应该记录下游错误（考虑用户个性化配置）
func ShouldLogDownstreamError(userId int) bool {
	// 获取用户下游错误记录配置
	userConfig, err := CacheGetUserLogDownstreamErrorEnabled(userId)
	if err != nil {
		userConfig = 0 // 出错时使用系统默认配置
	}

	if userConfig == 0 {
		return config.LogDownstreamErrorEnabled // 使用系统默认配置
	} else {
		return userConfig == 1 // 1为记录，2为不记录
	}
}

// ShouldLogDetail 判断是否应该记录详细日志（考虑用户个性化配置）
func ShouldLogDetail(userId int) bool {
	logDetailEnabled, err := CacheGetUserLogDetailEnabled(userId)
	if err != nil {
		logDetailEnabled = 0 // 出错时使用系统默认配置
	}

	if logDetailEnabled == 0 {
		return config.LogDetailConsumeEnabled // 使用系统默认配置
	} else {
		return logDetailEnabled == 1 // 1为记录，2为不记录
	}
}

// ShouldLogUpstreamResponse 判断是否应该记录上游响应（考虑用户个性化配置）
func ShouldLogUpstreamResponse(userId int) bool {
	// 首先检查是否启用详细日志记录
	if !ShouldLogDetail(userId) {
		return false // 如果不记录详细日志，则不记录上游响应
	}

	// 获取用户上游响应记录配置
	userLogUpstreamResponseEnabled, err := CacheGetUserLogUpstreamResponseEnabled(userId)
	if err != nil {
		userLogUpstreamResponseEnabled = 0 // 出错时使用系统默认配置
	}

	if userLogUpstreamResponseEnabled == 0 {
		return config.LogUpstreamResponseEnabled // 使用系统默认配置
	} else {
		return userLogUpstreamResponseEnabled == 1 // 1为记录，2为不记录
	}
}

// ShouldLogFullResponse 判断是否应该记录完整响应（考虑用户个性化配置）
func ShouldLogFullResponse(userId int) bool {
	// 首先检查是否启用详细日志记录
	if !ShouldLogDetail(userId) {
		return false // 如果不记录详细日志，则不记录完整响应
	}

	// 获取用户完整响应记录配置
	userLogFullResponseEnabled, err := CacheGetUserLogFullResponseEnabled(userId)
	if err != nil {
		userLogFullResponseEnabled = 0 // 出错时使用系统默认配置
	}

	if userLogFullResponseEnabled == 0 {
		return config.LogFullResponseEnabled // 使用系统默认配置
	} else {
		return userLogFullResponseEnabled == 1 // 1为记录，2为不记录
	}
}

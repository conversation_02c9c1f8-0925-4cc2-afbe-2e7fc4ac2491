package model

import "encoding/json"

type MidjourneyPlus struct {
	Id          int    `json:"id"`
	RequestId   string `json:"request_id"`
	Code        int    `json:"code"`
	UserId      int    `json:"user_id" gorm:"index"`
	Mode        string `json:"mode"`
	Action      string `json:"action"`
	MjId        string `json:"mj_id" gorm:"index"`
	Prompt      string `json:"prompt"`
	PromptEn    string `json:"prompt_en"`
	Description string `json:"description"`
	State       string `json:"state"`
	SubmitTime  int64  `json:"submit_time"`
	StartTime   int64  `json:"start_time"`
	FinishTime  int64  `json:"finish_time"`
	ImageUrl    string `json:"image_url"`
	Status      string `json:"status" gorm:"type:varchar(20);index"`
	Progress    string `json:"progress" gorm:"varchar(20);index"`
	FailReason  string `json:"fail_reason"`
	Properties  string `json:"properties"`
	Buttons     string `json:"buttons"`
	ChannelId   int    `json:"channel_id"`
	Quota       int    `json:"quota"`
	// Video related fields
	VideoUrl  string `json:"video_url" gorm:"type:text"`  // 单个视频URL
	VideoUrls string `json:"video_urls" gorm:"type:text"` // 多个视频URL，JSON数组格式
}

func (m *MidjourneyPlus) TableName() string {
	return "midjourneys"
}

const (
	DefaultMJMode = "fast"
)

// TaskQueryParams 用于包含所有搜索条件的结构体，可以根据需求添加更多字段
type TaskQueryParams struct {
	ChannelID      string
	MjID           string
	StartTimestamp string
	EndTimestamp   string
}

type MjButton struct {
	CustomId string `json:"customId"`
	Emoji    string `json:"emoji"`
	Label    string `json:"label"`
	Style    int    `json:"style"`
	Type     int    `json:"type"`
}

func (m *Midjourney) GetButtons() []MjButton {
	var btns []MjButton
	if m.Buttons == "" {
		return btns
	}
	_ = json.Unmarshal([]byte(m.Buttons), &btns)
	return btns
}

func GetByMJIds(mjIds []any) []*Midjourney {
	var mj []*Midjourney
	var err error
	err = DB.Where("mj_id in (?)", mjIds).Find(&mj).Error
	if err != nil {
		return nil
	}
	return mj
}

func MjBulkUpdate(taskIDs []string, params map[string]any) error {
	return DB.Model(&Midjourney{}).
		Where("mj_id in (?)", taskIDs).
		Updates(params).Error
}

func MjBulkUpdateByID(ids []int, params map[string]any) error {
	return DB.Model(&Midjourney{}).
		Where("id in (?)", ids).
		Updates(params).Error
}

func GetMjPlusAllUnFinishTasks() []*MidjourneyPlus {
	var tasks []*MidjourneyPlus
	var err error
	// get all tasks progress is not 100%
	err = DB.Where("progress != ? and mj_id is not null and mj_id != '' ", "100%").Find(&tasks).Error
	if err != nil {
		return nil
	}
	return tasks
}

func (midjourney *MidjourneyPlus) Update() error {
	var err error
	err = DB.Save(midjourney).Error
	return err
}

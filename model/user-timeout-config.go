package model

import (
	"errors"
	"github.com/songquanpeng/one-api/common/helper"

	"gorm.io/gorm"
)

// UserTimeoutConfig 用户超时配置表
type UserTimeoutConfig struct {
	Id                int     `json:"id" gorm:"primaryKey"`
	UserId            int     `json:"user_id" gorm:"index:idx_user_model,priority:1;not null"`                    // 用户ID
	ModelName         string  `json:"model_name" gorm:"index:idx_user_model,priority:2;size:100;not null"`        // 模型名称，*表示通用配置
	FirstByteTimeout  int     `json:"first_byte_timeout" gorm:"type:int;default:30;comment:首字超时时间(秒)"`            // 首字超时时间(秒)
	TotalTimeout      int     `json:"total_timeout" gorm:"type:int;default:300;comment:总超时时间(秒)"`                 // 总超时时间(秒)
	TpsThreshold      float64 `json:"tps_threshold" gorm:"type:decimal(10,2);default:0;comment:TPS阈值，0表示不限制"`     // TPS阈值，每秒token数量，0表示不限制
	TimeoutCostBearer string  `json:"timeout_cost_bearer" gorm:"type:varchar(20);default:'user';comment:超时费用承担方"` // 超时费用承担方：user=用户承担，admin=管理员承担
	Enabled           bool    `json:"enabled" gorm:"default:1;comment:是否启用"`                                      // 是否启用
	CreatedTime       int64   `json:"created_time" gorm:"bigint"`                                                 // 创建时间
	UpdatedTime       int64   `json:"updated_time" gorm:"bigint"`                                                 // 更新时间
}

func (config *UserTimeoutConfig) TableName() string {
	return "user_timeout_configs"
}

// Insert 插入用户超时配置
func (config *UserTimeoutConfig) Insert() error {
	config.CreatedTime = helper.GetTimestamp()
	config.UpdatedTime = config.CreatedTime
	return DB.Create(config).Error
}

// Update 更新用户超时配置
func (config *UserTimeoutConfig) Update() error {
	config.UpdatedTime = helper.GetTimestamp()
	return DB.Save(config).Error
}

// Delete 删除用户超时配置
func (config *UserTimeoutConfig) Delete() error {
	return DB.Delete(config).Error
}

// GetUserTimeoutConfig 获取用户指定模型的超时配置
// 优先级：用户+模型 > 用户+通用(*) > 无配置时返回nil
func GetUserTimeoutConfig(userId int, modelName string) (*UserTimeoutConfig, error) {
	var config UserTimeoutConfig

	// 先查找用户+模型的具体配置
	err := DB.Where("user_id = ? AND model_name = ? AND enabled = ?", userId, modelName, true).First(&config).Error
	if err == nil {
		return &config, nil
	}
	if err != gorm.ErrRecordNotFound {
		return nil, err
	}

	// 如果没有找到，查找用户的通用配置
	err = DB.Where("user_id = ? AND model_name = ? AND enabled = ?", userId, "*", true).First(&config).Error
	if err == nil {
		return &config, nil
	}
	if err != gorm.ErrRecordNotFound {
		return nil, err
	}

	// 没有找到任何配置，返回nil
	return nil, nil
}

// GetUserTimeoutConfigs 获取用户的所有超时配置
func GetUserTimeoutConfigs(userId int) ([]*UserTimeoutConfig, error) {
	var configs []*UserTimeoutConfig
	err := DB.Where("user_id = ?", userId).Order("model_name").Find(&configs).Error
	return configs, err
}

// TimeoutConfigWithUser 包含用户信息的超时配置
type TimeoutConfigWithUser struct {
	UserTimeoutConfig
	Username string `json:"username"`
}

// GetAllUserTimeoutConfigs 管理员分页获取所有用户的超时配置
func GetAllUserTimeoutConfigs(page, size int, userIdFilter, modelNameFilter string) ([]*TimeoutConfigWithUser, int64, error) {
	var configs []*TimeoutConfigWithUser
	var total int64

	query := DB.Table("user_timeout_configs utc").
		Select("utc.*, u.username").
		Joins("LEFT JOIN users u ON utc.user_id = u.id")

	// 应用过滤条件
	if userIdFilter != "" {
		query = query.Where("utc.user_id = ?", userIdFilter)
	}
	if modelNameFilter != "" {
		query = query.Where("utc.model_name LIKE ?", "%"+modelNameFilter+"%")
	}

	// 计算总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	err = query.Order("utc.updated_time DESC").Offset(offset).Limit(size).Scan(&configs).Error
	if err != nil {
		return nil, 0, err
	}

	return configs, total, nil
}

// SetUserTimeoutConfig 设置用户超时配置
func SetUserTimeoutConfig(userId int, modelName string, firstByteTimeout, totalTimeout int, tpsThreshold float64, timeoutCostBearer string) error {
	var config UserTimeoutConfig

	// 查找是否已存在
	err := DB.Where("user_id = ? AND model_name = ?", userId, modelName).First(&config).Error
	if err == gorm.ErrRecordNotFound {
		// 创建新配置
		config = UserTimeoutConfig{
			UserId:            userId,
			ModelName:         modelName,
			FirstByteTimeout:  firstByteTimeout,
			TotalTimeout:      totalTimeout,
			TpsThreshold:      tpsThreshold,
			TimeoutCostBearer: timeoutCostBearer,
			Enabled:           true,
		}
		return config.Insert()
	} else if err != nil {
		return err
	}

	// 更新现有配置
	config.FirstByteTimeout = firstByteTimeout
	config.TotalTimeout = totalTimeout
	config.TpsThreshold = tpsThreshold
	config.TimeoutCostBearer = timeoutCostBearer
	config.Enabled = true
	return config.Update()
}

// DeleteUserTimeoutConfig 删除用户超时配置
func DeleteUserTimeoutConfig(userId int, modelName string) error {
	return DB.Where("user_id = ? AND model_name = ?", userId, modelName).Delete(&UserTimeoutConfig{}).Error
}

// Validate 验证超时配置的有效性
func (u *UserTimeoutConfig) Validate() error {
	if u.FirstByteTimeout <= 0 {
		return errors.New("首字节超时时间必须为正数")
	}
	if u.TotalTimeout <= 0 {
		return errors.New("总超时时间必须为正数")
	}
	if u.TpsThreshold <= 0 {
		return errors.New("TPS阈值必须为正数")
	}
	if u.FirstByteTimeout > u.TotalTimeout {
		return errors.New("首字节超时不能大于总超时")
	}
	if u.ModelName == "" {
		return errors.New("模型名称不能为空")
	}
	if u.TimeoutCostBearer != "user" && u.TimeoutCostBearer != "admin" {
		return errors.New("超时费用承担方必须是 'user' 或 'admin'")
	}
	return nil
}

// CreateUserTimeoutConfig 创建用户超时配置的便捷函数
func CreateUserTimeoutConfig(config *UserTimeoutConfig) error {
	if err := config.Validate(); err != nil {
		return err
	}
	return SetUserTimeoutConfig(config.UserId, config.ModelName, config.FirstByteTimeout, config.TotalTimeout, config.TpsThreshold, config.TimeoutCostBearer)
}

package model

import (
	"context"
	"fmt"

	"github.com/songquanpeng/one-api/common/config"
)

// DatabaseInterface 数据库操作接口
type DatabaseInterface interface {
	// Channel operations
	GetChannelById(id int, selectAll bool) (*Channel, error)
	GetAllChannels(startIdx int, num int, orderByStr string, selectAll bool, id int, name string, key string,
		group string, models string, channelGroupId int, status int, billingType int, baseUrl string,
		overFrequencyAutoDisable string, scope string, disableReason string) ([]*Channel, error)
	SearchChannels(startIdx int, num int, keyword string, channelGroupId int) ([]*Channel, error)
	CountChannels(keyword string, id int, name string, key string, group string, models string, channelGroupId int, status int, billingType int, baseUrl string, overFrequencyAutoDisable string, disableReason string) (int64, error)
	GetChannelBanStatistics(disableReason string) ([]*BanStatistics, error)
	GetChannelStatistics(statusFilter int, disableReason string, groupBy string) ([]*ChannelStatistics, error)
	GetChannelCreationSpeedStatistics(statusFilter int, disableReason string, groupBy string, timeGranularity string) (map[string][]*ChannelCreationSpeedItem, error)
	InsertChannel(channel *Channel) error
	UpdateChannel(channel *Channel) error
	DeleteChannel(channel *Channel) error
	BatchInsertChannels(channels []Channel) error
	UpdateChannelStatusById(id int, status int) error
	UpdateChannelAbilityStatusByIdReturnErr(id int, requestModel string, status int) error
	UpdateChannelDisableReasonById(id int, disableReason string) error
	UpdateChannelUsedQuota(id int, quota int64) error
	UpdateChannelBalance(id int, balance float64) error
	UpdateChannelResponseTime(id int, responseTime int64) error
	DeleteChannelByStatus(status int64) (int64, error)
	DeleteChannelByType(deleteType int) (int64, error)
	DeleteChannelByDisableReason(disableReason string) (int64, error)
	DeleteChannelByIds(ids []int) (int64, error)
	CountChannelByStatus() (map[int]int, error)
	GetChannelModelMappingsBySudoGroup(group string) ([]Channel, error)
	GetAllModels() ([]string, error)

	// Ability operations
	GetAllAbilities(startIdx int, num int, orderBy string, group string, model string, channelId int, enabled bool) ([]*Ability, error)
	CountAbilities(group string, model string, channelId int, enabled bool) (int64, error)
	InsertAbility(ability *Ability) error
	UpdateAbility(ability *Ability) error
	DeleteAbility(ability *Ability) error
	DeleteAbilitiesByIds(ids []int) (int64, error)
	GetAbilityByGroupModelChannel(group string, model string, channelId int) (*Ability, error)
	UpdateAbilityEnabledStatus(group string, model string, channelId int, enabled bool) error
	GetAbilitiesByChannelId(channelId int) ([]*Ability, error)
	GetRandomSatisfiedChannel(group string, model string, tokenBillingType int, inputHasFunctionCall bool, inputHasImage bool, excludeIds []int, ignoreFirstPriority bool, isV1MessagesPath bool) (*Channel, error)
	UpdateAbilityStatus(channelId int, status bool) error
	GetGroupModels(ctx context.Context, group string) ([]string, error)
	GetAvailableModelByGroup(group string) []string
	HasSearchSerperModel() bool
	DeleteAbilitiesNotExistsInChannel() (int64, error)
	GetFirstAbilityByChannelIdAndModel(channelId int, model string) (*Ability, error)
	GetAllDistinctModels() ([]string, error)
	GetAllEnabledDistinctModels() ([]string, error)

	// ChannelExtend operations
	GetChannelExtendByChannelId(channelId int) (*ChannelExtend, error)
	InsertChannelExtend(channelExtend *ChannelExtend) error
	UpdateChannelExtend(channelExtend *ChannelExtend) error
	DeleteChannelExtend(channelExtend *ChannelExtend) error
	DeleteChannelExtendByChannelId(channelId int) error
	BatchInsertChannelExtends(channelExtends []ChannelExtend, sqlOnly bool) error
	DeleteChannelExtendsNotExistsInChannel() (int64, error)

	// ChannelGroup operations
	GetChannelGroupById(id int, selectAll bool) (*ChannelGroup, error)
	GetAllChannelGroups(startIdx int, num int, selectAll bool, id int, name string, status int, group string) ([]*ChannelGroup, error)
	SearchChannelGroups(startIdx int, num int, keyword string) ([]*ChannelGroup, error)
	CountChannelGroups(id int, name string, group string) (int64, error)
	InsertChannelGroup(channelGroup *ChannelGroup) error
	UpdateChannelGroup(channelGroup *ChannelGroup) error
	DeleteChannelGroup(channelGroup *ChannelGroup) error
	BatchInsertChannelGroups(channelGroups []ChannelGroup) error
	UpdateChannelGroupStatusById(id int, status int) error
	DeleteChannelGroupByStatus(status int64) (int64, error)
	DeleteDisabledChannelGroup() (int64, error)

	// Batch operations
	BatchUpdateChannelsToChannelGroup(channelIds []int, channelGroupId int) error

	// Counter operations
	InitializeCounters() error
}

// DatabaseManager 数据库管理器
type DatabaseManager struct {
	sqlDB    DatabaseInterface
	nosqlDB  DatabaseInterface
	useNoSQL bool
}

var DBManager *DatabaseManager

// InitDatabaseManager 初始化数据库管理器
func InitDatabaseManager() error {
	DBManager = &DatabaseManager{
		useNoSQL: false, // 默认使用SQL
	}

	// 初始化SQL数据库实现
	sqlImpl := &SQLDatabaseImpl{}
	DBManager.sqlDB = sqlImpl

	// 根据配置决定是否启用NoSQL
	if GetChannelNoSQLEnabled() {
		var nosqlImpl DatabaseInterface
		var err error

		switch GetChannelNoSQLType() {
		case "mongodb":
			nosqlImpl, err = NewMongoDatabaseImpl()
		case "elasticsearch":
			// TODO: 实现Elasticsearch版本
			return fmt.Errorf("elasticsearch implementation not yet available")
		default:
			return fmt.Errorf("unsupported NoSQL type: %s", GetChannelNoSQLType())
		}

		if err != nil {
			return fmt.Errorf("failed to initialize NoSQL database: %v", err)
		}

		DBManager.nosqlDB = nosqlImpl
		DBManager.useNoSQL = true
	}

	return nil
}

// GetDB 获取当前使用的数据库实现
func (dm *DatabaseManager) GetDB() DatabaseInterface {
	if dm.useNoSQL && dm.nosqlDB != nil {
		return dm.nosqlDB
	}
	return dm.sqlDB
}

// GetSQLDB 获取SQL数据库实现
func (dm *DatabaseManager) GetSQLDB() DatabaseInterface {
	return dm.sqlDB
}

// GetNoSQLDB 获取NoSQL数据库实现
func (dm *DatabaseManager) GetNoSQLDB() DatabaseInterface {
	return dm.nosqlDB
}

// IsUsingNoSQL 检查是否正在使用NoSQL
func (dm *DatabaseManager) IsUsingNoSQL() bool {
	return dm.useNoSQL && dm.nosqlDB != nil
}

// SwitchToNoSQL 切换到NoSQL数据库
func (dm *DatabaseManager) SwitchToNoSQL() error {
	if dm.nosqlDB == nil {
		return fmt.Errorf("NoSQL database not initialized")
	}
	dm.useNoSQL = true
	return nil
}

// SwitchToSQL 切换到SQL数据库
func (dm *DatabaseManager) SwitchToSQL() {
	dm.useNoSQL = false
}

// MigrateData 执行数据迁移
func (dm *DatabaseManager) MigrateData() error {
	if dm.nosqlDB == nil {
		return fmt.Errorf("NoSQL database not initialized")
	}

	// 调用迁移服务的迁移功能
	migrationService := GetMigrationService()
	if migrationService == nil {
		return fmt.Errorf("migration service not initialized")
	}

	return migrationService.CheckAndMigrateOnce()
}

// 配置获取函数
func GetChannelNoSQLEnabled() bool {
	return config.ChannelNoSQLEnabled
}

func GetChannelNoSQLType() string {
	return config.ChannelNoSQLType
}

func GetChannelNoSQLConnectionString() string {
	return config.ChannelNoSQLConnectionString
}

func GetChannelNoSQLDatabase() string {
	return config.ChannelNoSQLDatabase
}

func GetChannelMigrationEnabled() bool {
	return config.ChannelMigrationEnabled
}

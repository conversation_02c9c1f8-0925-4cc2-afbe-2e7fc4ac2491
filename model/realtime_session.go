package model

import (
	"github.com/songquanpeng/one-api/common/helper"
	"time"
)

type RealtimeSession struct {
	Id            string
	UserId        int
	StartTime     time.Time
	EndTime       time.Time
	TokensUsed    int
	AudioDuration float64
}

func NewRealtimeSession(userId int) *RealtimeSession {
	return &RealtimeSession{
		Id:        helper.GenerateUUID(),
		UserId:    userId,
		StartTime: time.Now(),
	}
}

func (s *RealtimeSession) End() {
	s.EndTime = time.Now()
}

func (s *RealtimeSession) AddTokens(tokens int) {
	s.TokensUsed += tokens
}

func (s *RealtimeSession) AddAudioDuration(duration float64) {
	s.AudioDuration += duration
}

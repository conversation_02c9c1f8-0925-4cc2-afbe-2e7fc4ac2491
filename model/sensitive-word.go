package model

import (
	"strings"

	"github.com/songquanpeng/one-api/common/config"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type SensitiveWord struct {
	Id        int    `json:"id" gorm:"primaryKey;autoIncrement"`
	Word      string `json:"word" gorm:"type:varchar(500);uniqueIndex;not null;collate:utf8mb4_bin"`
	Category  string `json:"category" gorm:"type:varchar(100);index"`
	Severity  int    `json:"severity" gorm:"type:int;index"`
	CreatedAt int64  `json:"created_at" gorm:"bigint;index"`
}

func (sensitiveWord *SensitiveWord) Insert() error {
	var err error
	err = DB.Create(sensitiveWord).Error
	return err
}

func BatchInsertSensitiveWords(sensitiveWords []*SensitiveWord) (int64, error) {
	result := DB.CreateInBatches(sensitiveWords, len(sensitiveWords))
	return result.RowsAffected, result.Error
}
func BatchCreateSensitiveWords(words []*SensitiveWord) (int64, error) {
	tx := DB.Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "word"}},
		DoUpdates: clause.Assignments(map[string]interface{}{
			"category":   gorm.Expr("VALUES(category)"),
			"severity":   gorm.Expr("VALUES(severity)"),
			"created_at": gorm.Expr("VALUES(created_at)"),
		}),
	}).Create(&words)
	return tx.RowsAffected, tx.Error
}

func (sensitiveWord *SensitiveWord) Update() (int64, error) {
	// 排除创建时间
	result := DB.Model(sensitiveWord).Omit("created_at").Updates(sensitiveWord)
	return result.RowsAffected, result.Error
}

func (sensitiveWord *SensitiveWord) Delete() (int64, error) {
	result := DB.Delete(sensitiveWord)
	return result.RowsAffected, result.Error
}

func DeleteSensitiveWordById(id int) (int64, error) {
	result := DB.Where("id = ?", id).Delete(&SensitiveWord{})
	return result.RowsAffected, result.Error
}

func DeleteSensitiveWordByIds(ids []int) (int64, error) {
	result := DB.Where("id in (?)", ids).Delete(&SensitiveWord{})
	return result.RowsAffected, result.Error
}

func GetSensitiveWordById(id int) *SensitiveWord {
	var sensitiveWord *SensitiveWord
	var err error
	err = DB.Where("id = ?", id).First(&sensitiveWord).Error
	if err != nil {
		return nil
	}
	return sensitiveWord
}

func GetSensitiveWordByWord(word string) *SensitiveWord {
	var sensitiveWord *SensitiveWord
	var err error
	err = DB.Where("word = ?", word).First(&sensitiveWord).Error
	if err != nil {
		return nil
	}
	return sensitiveWord
}

func GetAllSensitiveWords(startIdx int, num int, category string, severity int, word string) ([]*SensitiveWord, error) {
	var sensitiveWords []*SensitiveWord
	var err error
	var tx = DB

	// 添加搜索条件
	if word != "" {
		tx = tx.Where("word LIKE ?", "%"+word+"%")
	}
	if category != "" {
		tx = tx.Where("category = ?", category)
	}
	if severity != 0 {
		tx = tx.Where("severity = ?", severity)
	}

	err = tx.Offset(startIdx).Limit(num).Find(&sensitiveWords).Error
	if err != nil {
		return nil, err
	}
	return sensitiveWords, nil
}

func CountSensitiveWords(category string, severity int, word string) (int64, error) {
	var count int64
	var err error
	var tx = DB

	// 添加搜索条件
	if word != "" {
		tx = tx.Where("word LIKE ?", "%"+word+"%")
	}
	if category != "" {
		tx = tx.Where("category = ?", category)
	}
	if severity != 0 {
		tx = tx.Where("severity = ?", severity)
	}

	err = tx.Model(&SensitiveWord{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

func GetAllSensitiveWordsNoPages(category string, severity int) ([]*SensitiveWord, error) {
	var sensitiveWords []*SensitiveWord
	var err error
	var tx = DB
	if category != "" {
		tx = tx.Where("category = ?", category)
	}
	if severity != 0 {
		tx = tx.Where("severity = ?", severity)
	}
	err = tx.Find(&sensitiveWords).Error
	if err != nil {
		return nil, err
	}
	return sensitiveWords, nil
}

// 校验敏感词,入参为待检测字符串
func CheckSensitiveWord(checkStr string) (bool, string, string) {
	// 校验入参敏感词
	if config.SensitiveWordsEnabled {
		if config.SensitiveWordsMap != nil && len(config.SensitiveWordsMap) > 0 {
			for word, _ := range config.SensitiveWordsMap {
				trimmedWord := strings.TrimSpace(word)
				if trimmedWord != "" && strings.Contains(checkStr, trimmedWord) {
					return true, trimmedWord, config.SensitiveWordsTips
				}
			}
		}
	}
	return false, "", ""
}

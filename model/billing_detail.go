package model

// FieldBillingDetail 单个字段的计费明细
type FieldBillingDetail struct {
	Field       string  `json:"field"`       // 字段路径
	Value       float64 `json:"value"`       // 字段值
	Multiplier  float64 `json:"multiplier"`  // 倍率
	BaseUnit    float64 `json:"base_unit"`   // 基数
	FieldQuota  float64 `json:"field_quota"` // 该字段产生的配额
	Description string  `json:"description"` // 字段描述
}

// BillingDetail 计费明细结构（存储在Log.Other字段中）
type BillingDetail struct {
	// 计费配置信息
	BillingType string  `json:"billing_type"` // 计费类型：fixed, dynamic_fields, sub_price_match
	BasePrice   float64 `json:"base_price"`   // 基础价格
	BaseUnit    float64 `json:"base_unit"`    // 基数单位（默认500000）
	Route       string  `json:"route"`        // 动态路由路径

	// 字段计费详情
	FieldDetails []FieldBillingDetail `json:"field_details"` // 存储每个字段的计费明细

	// 倍率信息
	GroupRatio        float64 `json:"group_ratio"`         // 分组倍率
	TopupConvertRatio float64 `json:"topup_convert_ratio"` // 充值转换倍率
	UserDiscount      float64 `json:"user_discount"`       // 用户折扣
	RouteDiscount     float64 `json:"route_discount"`      // 路由折扣

	// 计算结果
	BaseQuota  int64 `json:"base_quota"`  // 基础配额
	FinalQuota int64 `json:"final_quota"` // 最终配额（应用所有倍率后）

	// 其他字段（保持与原有Other字段的兼容性）
	AdminInfo       interface{} `json:"admin_info,omitempty"`
	CompletionRatio float64     `json:"completion_ratio,omitempty"`
	Frt             int         `json:"frt,omitempty"`
	ModelPrice      float64     `json:"model_price,omitempty"`
	ModelRatio      float64     `json:"model_ratio,omitempty"`
}

package model

import (
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/logger"
	"gorm.io/gorm"
)

// QuotaData 柱状图数据
type QuotaData struct {
	Id        int    `json:"id"`
	UserID    int    `json:"user_id" gorm:"index"`
	Username  string `json:"username" gorm:"index:idx_qdt_model_user_name,priority:2;size:64;default:''"`
	ModelName string `json:"model_name" gorm:"index:idx_qdt_model_user_name,priority:1;size:64;default:''"`
	CreatedAt int64  `json:"created_at" gorm:"bigint;index:idx_qdt_created_at,priority:2"`
	TokenUsed int    `json:"token_used" gorm:"default:0"`
	Count     int    `json:"count" gorm:"default:0"`
	Quota     int    `json:"quota" gorm:"default:0"`
	Ip        string `json:"ip" gorm:"index;size:64;default:''"`
	RemoteIp  string `json:"remote_ip" gorm:"index;size:64;default:''"`
	Channel   int    `json:"channel" gorm:"index;default:0"`
}

func UpdateQuotaData() {
	// recover
	defer func() {
		if r := recover(); r != nil {
			logger.SysLog(fmt.Sprintf("UpdateQuotaData panic: %s", r))
		}
	}()
	for {
		if config.DataExportEnabled {
			logger.SysLog("正在更新统计图数据...")
			SaveQuotaDataCache()
		}
		time.Sleep(time.Duration(config.DataExportInterval) * time.Minute)
	}
}

var CacheQuotaData = make(map[string]*QuotaData)
var CacheQuotaDataLock = sync.Mutex{}

func logQuotaDataCache(userId int, username string, modelName string, quota int, createdAt int64, tokenUsed int, ip string, remoteIp string, channel int) {
	// 如果开启了忽略渠道维度选项，则不包含渠道ID在key中
	var key string
	if config.IgnoreChannelDimensionEnabled {
		key = fmt.Sprintf("%d-%s-%s-%d", userId, username, modelName, createdAt)
	} else {
		key = fmt.Sprintf("%d-%s-%s-%d-%d", userId, username, modelName, createdAt, channel)
	}

	quotaData, ok := CacheQuotaData[key]
	if ok {
		quotaData.Count += 1
		quotaData.Quota += quota
		quotaData.TokenUsed += tokenUsed
		if ip != "" {
			quotaData.Ip = ip
		}
		if remoteIp != "" {
			quotaData.RemoteIp = remoteIp
		}
	} else {
		// 忽略渠道维度时，渠道ID统一设为0
		channelId := channel
		if config.IgnoreChannelDimensionEnabled {
			channelId = 0
		}

		quotaData = &QuotaData{
			UserID:    userId,
			Username:  username,
			ModelName: modelName,
			CreatedAt: createdAt,
			Count:     1,
			Quota:     quota,
			TokenUsed: tokenUsed,
			Ip:        ip,
			RemoteIp:  remoteIp,
			Channel:   channelId,
		}
	}
	CacheQuotaData[key] = quotaData
}

func LogQuotaData(userId int, username string, modelName string, quota int, createdAt int64, tokenUsed int, ip string, remoteIp string, channel int) {
	// 只精确到小时
	createdAt = createdAt - (createdAt % 3600)

	CacheQuotaDataLock.Lock()
	defer CacheQuotaDataLock.Unlock()
	logQuotaDataCache(userId, username, modelName, quota, createdAt, tokenUsed, ip, remoteIp, channel)
}

func SaveQuotaDataCache() {
	CacheQuotaDataLock.Lock()
	defer CacheQuotaDataLock.Unlock()
	size := len(CacheQuotaData)
	// 如果缓存中有数据，就保存到数据库中
	// 1. 先查询数据库中是否有数据
	// 2. 如果有数据，就更新数据
	// 3. 如果没有数据，就插入数据
	for _, quotaData := range CacheQuotaData {
		quotaDataDB := &QuotaData{}
		DB.Table("quota_data").Where("user_id = ? and username = ? and model_name = ? and created_at = ? and channel = ?",
			quotaData.UserID, quotaData.Username, quotaData.ModelName, quotaData.CreatedAt, quotaData.Channel).First(quotaDataDB)
		if quotaDataDB.Id > 0 {
			increaseQuotaData(quotaData.UserID, quotaData.Username, quotaData.ModelName, quotaData.Count, quotaData.Quota, quotaData.CreatedAt, quotaData.TokenUsed, quotaData.Ip, quotaData.RemoteIp, quotaData.Channel)
		} else {
			DB.Table("quota_data").Create(quotaData)
		}
	}
	CacheQuotaData = make(map[string]*QuotaData)
	logger.SysLog(fmt.Sprintf("保存统计图数据成功，共保存%d条数据", size))
}

func increaseQuotaData(userId int, username string, modelName string, count int, quota int, createdAt int64, tokenUsed int, ip string, remoteIp string, channel int) {
	updates := map[string]interface{}{
		"count":      gorm.Expr("count + ?", count),
		"quota":      gorm.Expr("quota + ?", quota),
		"token_used": gorm.Expr("token_used + ?", tokenUsed),
	}
	if ip != "" {
		updates["ip"] = ip
	}
	if remoteIp != "" {
		updates["remote_ip"] = remoteIp
	}
	err := DB.Table("quota_data").Where("user_id = ? and username = ? and model_name = ? and created_at = ? and channel = ?",
		userId, username, modelName, createdAt, channel).Updates(updates).Error
	if err != nil {
		logger.SysLog(fmt.Sprintf("increaseQuotaData error: %s", err))
	}
}

func GetQuotaDataByUsername(username string, startTime int64, endTime int64) (quotaData []*QuotaData, err error) {
	var quotaDatas []*QuotaData
	// 从quota_data表中查询数据
	err = DB.Table("quota_data").Where("username = ? and created_at >= ? and created_at <= ?", username, startTime, endTime).Find(&quotaDatas).Error
	return quotaDatas, err
}

func GetQuotaDataByUserId(userId int, startTime int64, endTime int64) (quotaData []*QuotaData, err error) {
	var quotaDatas []*QuotaData
	// 从quota_data表中查询数据
	err = DB.Table("quota_data").Where("user_id = ? and created_at >= ? and created_at <= ?", userId, startTime, endTime).Find(&quotaDatas).Error
	return quotaDatas, err
}

func GetAllQuotaDates(startTime int64, endTime int64, username string) (quotaData []*QuotaData, err error) {
	if username != "" {
		return GetQuotaDataByUsername(username, startTime, endTime)
	}

	var quotaDatas []*QuotaData
	query := DB.Table("quota_data").
		Select("model_name, sum(count) as count, sum(quota) as quota, sum(token_used) as token_used, created_at").
		Group("model_name, created_at")

	// 只有当时间参数有效时才添加时间条件
	if startTime > 0 || endTime > 0 {
		if startTime > 0 {
			query = query.Where("created_at >= ?", startTime)
		}
		if endTime > 0 {
			query = query.Where("created_at <= ?", endTime)
		}
	}

	err = query.Find(&quotaDatas).Error
	return quotaDatas, err
}

type UsageData struct {
	ModelName string  `json:"modelName"`
	UserId    string  `json:"userId"`
	Username  string  `json:"username"`
	Ip        string  `json:"ip"`
	RemoteIp  string  `json:"remote_ip"`
	SumQuota  int     `json:"sumQuota"`
	SumUsd    float64 `json:"sumUsd"`
	CostQuota int     `json:"costQuota"`
	CostUsd   float64 `json:"costUsd"`
	Date      string  `json:"date"`
}

type QueryParams struct {
	StartTime   int64
	EndTime     int64
	Username    string
	ModelName   string
	UserId      string
	Ip          string
	Granularity string
	Timezone    string
	Channel     string
}

func GetUsageStatsByDimension(params QueryParams, dimension string) ([]UsageData, error) {
	var results []UsageData
	query := DB.Table("quota_data")

	// 添加时间范围条件
	if params.StartTime > 0 {
		query = query.Where("created_at >= ?", params.StartTime)
	}
	if params.EndTime > 0 {
		query = query.Where("created_at <= ?", params.EndTime)
	}

	// 添加其他筛选条件
	if params.Username != "" {
		query = query.Where("username = ?", params.Username)
	}
	if params.UserId != "" {
		userID, _ := strconv.Atoi(params.UserId)
		query = query.Where("user_id = ?", userID)
	}
	if params.ModelName != "" {
		query = query.Where("model_name = ?", params.ModelName)
	}
	if params.Channel != "" {
		channelID, _ := strconv.Atoi(params.Channel)
		query = query.Where("channel = ?", channelID)
	}

	// 设置默认时区
	if params.Timezone == "" {
		params.Timezone = "UTC"
	}

	// 根据维度分组
	groupBy := []string{getTimeGroupBy(params.Granularity, params.Timezone)}
	var selectFields string

	switch dimension {
	case "user":
		groupBy = append(groupBy, "user_id", "username")
		selectFields = `
			user_id,
			username,
			MAX(model_name) as model_name,
			MAX(ip) as ip,
			MAX(remote_ip) as remote_ip,
			SUM(quota) as sum_quota,
			SUM(quota) / 500000 as sum_usd,
			0 as cost_quota,
			0 as cost_usd,
			` + getTimeSelect(params.Granularity, params.Timezone) + ` as date
		`
	case "model":
		groupBy = append(groupBy, "model_name")
		selectFields = `
			MAX(user_id) as user_id,
			MAX(username) as username,
			model_name,
			MAX(ip) as ip,
			MAX(remote_ip) as remote_ip,
			SUM(quota) as sum_quota,
			SUM(quota) / 500000 as sum_usd,
			0 as cost_quota,
			0 as cost_usd,
			` + getTimeSelect(params.Granularity, params.Timezone) + ` as date
		`
	case "ip":
		groupBy = append(groupBy, "ip")
		selectFields = `
			MAX(user_id) as user_id,
			MAX(username) as username,
			MAX(model_name) as model_name,
			ip,
			MAX(remote_ip) as remote_ip,
			SUM(quota) as sum_quota,
			SUM(quota) / 500000 as sum_usd,
			0 as cost_quota,
			0 as cost_usd,
			` + getTimeSelect(params.Granularity, params.Timezone) + ` as date
		`
	default:
		// 默认情况下只按时间分组，其他字段都使用聚合函数
		selectFields = `
			MAX(user_id) as user_id,
			MAX(username) as username,
			MAX(model_name) as model_name,
			MAX(ip) as ip,
			MAX(remote_ip) as remote_ip,
			SUM(quota) as sum_quota,
			SUM(quota) / 500000 as sum_usd,
			0 as cost_quota,
			0 as cost_usd,
			` + getTimeSelect(params.Granularity, params.Timezone) + ` as date
		`
	}

	// 执行查询
	err := query.Select(selectFields).
		Group(strings.Join(groupBy, ", ")).
		Order("date ASC").
		Scan(&results).Error

	return results, err
}

// 获取时间分组语句，添加时区支持
func getTimeGroupBy(granularity string, timezone string) string {
	// 如果未指定时区，默认使用 UTC
	if timezone == "" {
		timezone = "UTC"
	}

	// PostgreSQL 支持
	if common.UsingPostgreSQL {
		switch granularity {
		case "hour":
			return fmt.Sprintf("to_char((to_timestamp(created_at) AT TIME ZONE '%s'), 'YYYY-MM-DD HH24:00')", timezone)
		case "day":
			return fmt.Sprintf("to_char((to_timestamp(created_at) AT TIME ZONE '%s'), 'YYYY-MM-DD')", timezone)
		case "week":
			return fmt.Sprintf("to_char((to_timestamp(created_at) AT TIME ZONE '%s'), 'YYYY-WW')", timezone)
		case "month":
			return fmt.Sprintf("to_char((to_timestamp(created_at) AT TIME ZONE '%s'), 'YYYY-MM')", timezone)
		default:
			return fmt.Sprintf("to_char((to_timestamp(created_at) AT TIME ZONE '%s'), 'YYYY-MM-DD')", timezone)
		}
	}

	// SQLite 支持
	if common.UsingSQLite {
		// SQLite 不支持时区转换，使用时间戳偏移
		timezoneOffset := getTimezoneOffset(timezone)
		switch granularity {
		case "hour":
			return fmt.Sprintf("strftime('%%Y-%%m-%%d %%H:00', datetime(created_at + %d, 'unixepoch'))", timezoneOffset)
		case "day":
			return fmt.Sprintf("strftime('%%Y-%%m-%%d', datetime(created_at + %d, 'unixepoch'))", timezoneOffset)
		case "week":
			return fmt.Sprintf("strftime('%%Y-W%%W', datetime(created_at + %d, 'unixepoch'))", timezoneOffset)
		case "month":
			return fmt.Sprintf("strftime('%%Y-%%m', datetime(created_at + %d, 'unixepoch'))", timezoneOffset)
		default:
			return fmt.Sprintf("strftime('%%Y-%%m-%%d', datetime(created_at + %d, 'unixepoch'))", timezoneOffset)
		}
	}

	// MySQL 支持 - 尝试使用 CONVERT_TZ 函数
	var testResult string
	err := DB.Raw("SELECT CONVERT_TZ(NOW(), '+00:00', ?)", timezone).Scan(&testResult).Error

	if err == nil && testResult != "" {
		// CONVERT_TZ 可用
		switch granularity {
		case "hour":
			return fmt.Sprintf("DATE_FORMAT(CONVERT_TZ(FROM_UNIXTIME(created_at), '+00:00', '%s'), '%%Y-%%m-%%d %%H:00')", timezone)
		case "day":
			return fmt.Sprintf("DATE_FORMAT(CONVERT_TZ(FROM_UNIXTIME(created_at), '+00:00', '%s'), '%%Y-%%m-%%d')", timezone)
		case "week":
			return fmt.Sprintf("DATE_FORMAT(CONVERT_TZ(FROM_UNIXTIME(created_at), '+00:00', '%s'), '%%Y-%%u')", timezone)
		case "month":
			return fmt.Sprintf("DATE_FORMAT(CONVERT_TZ(FROM_UNIXTIME(created_at), '+00:00', '%s'), '%%Y-%%m')", timezone)
		default:
			return fmt.Sprintf("DATE_FORMAT(CONVERT_TZ(FROM_UNIXTIME(created_at), '+00:00', '%s'), '%%Y-%%m-%%d')", timezone)
		}
	} else {
		// CONVERT_TZ 不可用，使用时间戳偏移方法
		timezoneOffset := getTimezoneOffset(timezone)
		switch granularity {
		case "hour":
			return fmt.Sprintf("DATE_FORMAT(FROM_UNIXTIME(created_at + %d), '%%Y-%%m-%%d %%H:00')", timezoneOffset)
		case "day":
			return fmt.Sprintf("DATE_FORMAT(FROM_UNIXTIME(created_at + %d), '%%Y-%%m-%%d')", timezoneOffset)
		case "week":
			return fmt.Sprintf("DATE_FORMAT(FROM_UNIXTIME(created_at + %d), '%%Y-%%u')", timezoneOffset)
		case "month":
			return fmt.Sprintf("DATE_FORMAT(FROM_UNIXTIME(created_at + %d), '%%Y-%%m')", timezoneOffset)
		default:
			return fmt.Sprintf("DATE_FORMAT(FROM_UNIXTIME(created_at + %d), '%%Y-%%m-%%d')", timezoneOffset)
		}
	}
}

// 获取时间选择语句
func getTimeSelect(granularity string, timezone string) string {
	return getTimeGroupBy(granularity, timezone)
}

func GetUsageStatsByModel(params QueryParams) ([]UsageData, error) {
	return GetUsageStatsByDimension(params, "model")
}

// ModelUsageData 模型使用统计数据
type ModelUsageData struct {
	ModelName string `json:"modelName"`
	Cnt       int    `json:"cnt"`
	Date      string `json:"date"`
}

// GetModelUsageStats 获取模型使用统计数据
func GetModelUsageStats(params QueryParams) ([]ModelUsageData, error) {
	query := DB.Table("quota_data")

	// 添加时间范围条件
	if params.StartTime > 0 {
		query = query.Where("created_at >= ?", params.StartTime)
	}
	if params.EndTime > 0 {
		query = query.Where("created_at <= ?", params.EndTime)
	}

	// 添加其他筛选条件
	if params.Username != "" {
		query = query.Where("username = ?", params.Username)
	}
	if params.UserId != "" {
		userID, _ := strconv.Atoi(params.UserId)
		query = query.Where("user_id = ?", userID)
	}
	if params.ModelName != "" {
		query = query.Where("model_name = ?", params.ModelName)
	}
	if params.Channel != "" {
		channelID, _ := strconv.Atoi(params.Channel)
		query = query.Where("channel = ?", channelID)
	}

	// 构建查询语句 - 只按模型名称分组，不考虑时间
	selectFields := `
		model_name as model_name,
		sum(count) as cnt,
		'' as date
	`

	// 执行查询
	var results []ModelUsageData
	err := query.Select(selectFields).
		Group("model_name").
		Order("cnt DESC").
		Scan(&results).Error

	return results, err
}

// DeleteQuotaDataByTimestamp 根据时间戳删除统计图数据
func DeleteQuotaDataByTimestamp(targetTimestamp int64) (int64, error) {
	var totalDeleted int64
	batchSize := 2500 // 每批删除的数据量

	// 统计需要删除的记录数量
	var count int64
	err := DB.Table("quota_data").Where("created_at < ?", targetTimestamp).Count(&count).Error
	if err != nil {
		return 0, err
	}

	// 分批删除数据，避免长时间锁表
	for {
		var result *gorm.DB
		if common.UsingPostgreSQL {
			// PostgreSQL 不支持 DELETE ... LIMIT，使用子查询
			result = DB.Exec(`DELETE FROM quota_data WHERE id IN (
				SELECT id FROM quota_data WHERE created_at < ? ORDER BY id LIMIT ?
			)`, targetTimestamp, batchSize)
		} else {
			// MySQL 和 SQLite 支持 DELETE ... LIMIT
			result = DB.Exec("DELETE FROM quota_data WHERE created_at < ? LIMIT ?", targetTimestamp, batchSize)
		}

		if result.Error != nil {
			return totalDeleted, result.Error
		}

		rowsAffected := result.RowsAffected
		totalDeleted += rowsAffected

		if rowsAffected < int64(batchSize) {
			break
		}
	}

	return totalDeleted, nil
}

// TruncateQuotaDataTable 清空统计图数据表
func TruncateQuotaDataTable() error {
	// 使用原始SQL执行TRUNCATE操作
	result := DB.Exec("TRUNCATE TABLE quota_data")
	if result.Error != nil {
		logger.SysLog(fmt.Sprintf("<TruncateQuotaDataTable> failed to truncate quota_data table: %s", result.Error.Error()))
		return result.Error
	}

	logger.SysLog("<TruncateQuotaDataTable> successfully truncated quota_data table")
	return nil
}


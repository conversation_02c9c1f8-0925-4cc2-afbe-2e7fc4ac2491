package model

import (
	"database/sql/driver"
	"fmt"

	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

// LongText 自定义长文本类型，兼容MySQL和PostgreSQL
type LongText string

// GormDataType 实现 GormDataTypeInterface
func (*LongText) GormDataType() string {
	return "text"
}

// GormDBDataType 针对不同数据库返回不同类型
func (*LongText) GormDBDataType(db *gorm.DB, field *schema.Field) string {
	switch db.Dialector.Name() {
	case "mysql":
		return "LONGTEXT"
	case "postgres":
		return "TEXT"
	case "sqlite":
		return "TEXT"
	default:
		return "TEXT"
	}
}

// Value 实现 driver.Valuer 接口
func (lt *LongText) Value() (driver.Value, error) {
	if lt == nil {
		return nil, nil
	}
	return string(*lt), nil
}

// Scan 实现 sql.Scanner 接口
func (lt *LongText) Scan(value interface{}) error {
	if value == nil {
		*lt = ""
		return nil
	}
	switch v := value.(type) {
	case string:
		*lt = LongText(v)
	case []byte:
		*lt = LongText(v)
	default:
		return fmt.Errorf("cannot scan type %T into LongText", value)
	}
	return nil
}

// String 方便转换为字符串
func (lt *LongText) String() string {
	if lt == nil {
		return ""
	}
	return string(*lt)
}

// Ptr 返回字符串指针（用于兼容现有的*string字段）
func (lt *LongText) Ptr() *string {
	if lt == nil {
		return nil
	}
	s := string(*lt)
	return &s
}

// FromString 从字符串创建LongText
func LongTextFromString(s string) LongText {
	return LongText(s)
}

// FromStringPtr 从字符串指针创建LongText
func LongTextFromStringPtr(s *string) LongText {
	if s == nil {
		return ""
	}
	return LongText(*s)
}

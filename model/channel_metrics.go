package model

import (
	"context"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/songquanpeng/one-api/common/helper"

	"github.com/songquanpeng/one-api/common/config"

	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/logger"
)

// ChannelModelMetrics 存储渠道模型的性能指标
type ChannelModelMetrics struct {
	ID                int64   `json:"id" gorm:"primaryKey;autoIncrement"`
	ChannelID         int     `json:"channel_id" gorm:"index;not null"`
	Model             string  `json:"model" gorm:"index;not null"`
	Date              string  `json:"date" gorm:"index;not null;type:varchar(10)"` // 格式：YYYY-MM-DD
	Hour              int     `json:"hour" gorm:"index;not null"`                  // 0-23
	TotalRequests     int64   `json:"total_requests" gorm:"default:0"`
	SuccessRequests   int64   `json:"success_requests" gorm:"default:0"`
	FailedRequests    int64   `json:"failed_requests" gorm:"default:0"`
	TotalResponseTime int64   `json:"total_response_time" gorm:"default:0"` // 毫秒
	AvgResponseTime   int64   `json:"avg_response_time" gorm:"default:0"`   // 毫秒
	SuccessRate       float64 `json:"success_rate" gorm:"default:0"`        // 0-1
	Score             float64 `json:"score" gorm:"default:0"`               // 综合评分
	CreatedAt         int64   `json:"created_at" gorm:"bigint"`
	UpdatedAt         int64   `json:"updated_at" gorm:"bigint"`
}

// ChannelModelMetricsCache 用于管理性能指标的缓存和持久化
type ChannelModelMetricsCache struct {
	// 当Redis未启用时，使用内存缓存
	sync.RWMutex
	Metrics map[string]*ChannelModelMetricsItem // key: channelID_model_date_hour
}

type ChannelModelMetricsItem struct {
	ChannelID         int    `json:"channel_id"`
	Model             string `json:"model"`
	Date              string `json:"date"`
	Hour              int    `json:"hour"`
	TotalRequests     int64  `json:"total_requests"`
	SuccessRequests   int64  `json:"success_requests"`
	FailedRequests    int64  `json:"failed_requests"`
	TotalResponseTime int64  `json:"total_response_time"`
	UpdatedAt         int64  `json:"updated_at"`
	Dirty             bool   `json:"dirty"` // 标记是否有更新需要写入数据库
}

// MetricsStorage 定义指标存储的接口
type MetricsStorage interface {
	RecordRequest(channelID int, model string, success bool, responseTime int64)
	PersistToDB()
	GetMetricsSummary(channelID int, model string, timeRange string) (*ChannelModelMetricsSummary, error)
	GetAllModels() ([]string, error)
	GetChannelModels(channelId int) ([]string, error)
}

// RedisMetricsStorage Redis实现的指标存储
type RedisMetricsStorage struct{}

// MemoryMetricsStorage 内存实现的指标存储
type MemoryMetricsStorage struct {
	sync.RWMutex
	Metrics map[string]*ChannelModelMetricsItem
}

var (
	metricsStorage     MetricsStorage
	metricsStorageOnce sync.Once
)

// GetMetricsStorage 获取指标存储实例
func GetMetricsStorage() MetricsStorage {
	metricsStorageOnce.Do(func() {
		/*if common.RedisEnabled {
			// 使用Redis存储
			storage := &RedisMetricsStorage{}
			metricsStorage = storage
			// 启动定期持久化协程
			helper.SafeGoroutine(func() {
				periodicPersist(storage)
			})
		} else {
			// 使用内存存储
			storage := &MemoryMetricsStorage{
				Metrics: make(map[string]*ChannelModelMetricsItem),
			}
			metricsStorage = storage
			// 启动定期持久化协程
			helper.SafeGoroutine(func() {
				periodicPersist(storage)
			})
		}*/
		// 使用内存存储 因为redis频繁的scan会导致redis服务器的cpu飙升
		storage := &MemoryMetricsStorage{
			Metrics: make(map[string]*ChannelModelMetricsItem),
		}
		metricsStorage = storage
		// 启动定期持久化协程
		helper.SafeGoroutine(func() {
			periodicPersist(storage)
		})
	})
	return metricsStorage
}

// 定期持久化函数
func periodicPersist(storage MetricsStorage) {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		storage.PersistToDB()
	}
}

// Redis实现
func (r *RedisMetricsStorage) RecordRequest(channelID int, model string, success bool, responseTime int64) {
	// 如果未启用渠道指标统计，则直接返回
	if !config.ChannelMetricsEnabled {
		return
	}

	now := time.Now()
	date := now.Format("2006-01-02")
	hour := now.Hour()

	// 键格式: channel_metrics:{channelID}:{model}:{date}:{hour}
	key := fmt.Sprintf("channel_metrics:%d:%s:%s:%d", channelID, model, date, hour)

	ctx := context.Background()
	pipe := common.RDB.Pipeline() // 使用普通管道，不使用事务

	// 递增总请求数
	pipe.HIncrBy(ctx, key, "total_requests", 1)

	// 递增成功或失败请求数
	if success {
		pipe.HIncrBy(ctx, key, "success_requests", 1)
	} else {
		pipe.HIncrBy(ctx, key, "failed_requests", 1)
	}

	// 递增总响应时间
	pipe.HIncrBy(ctx, key, "total_response_time", responseTime)

	// 设置过期时间（仅当键不存在时）
	pipe.ExpireNX(ctx, key, 24*time.Hour)

	// 执行管道
	_, err := pipe.Exec(ctx)
	if err != nil {
		logger.SysError(fmt.Sprintf("Failed to record request to Redis: %v", err))
	}
}

func (r *RedisMetricsStorage) PersistToDB() {
	// 从Redis同步到MySQL
	syncRedisToMySQL()
}

func (r *RedisMetricsStorage) GetMetricsSummary(channelID int, model string, timeRange string) (*ChannelModelMetricsSummary, error) {
	// 从Redis获取指标汇总
	summary, err := getChannelMetricsSummaryFromRedis(channelID, model, timeRange)
	if err == nil {
		return summary, nil
	}

	// Redis获取失败，回退到数据库
	logger.SysWarn(fmt.Sprintf("Failed to get metrics from Redis: %v, falling back to database", err))
	return getChannelMetricsSummaryFromDB(channelID, model, timeRange)
}

// 内存实现
func (m *MemoryMetricsStorage) RecordRequest(channelID int, model string, success bool, responseTime int64) {
	// 如果未启用渠道指标统计，则直接返回
	if !config.ChannelMetricsEnabled {
		return
	}

	now := time.Now()
	date := now.Format("2006-01-02")
	hour := now.Hour()
	key := getMetricsKey(channelID, model, date, hour)

	m.Lock()
	defer m.Unlock()

	item, exists := m.Metrics[key]
	if !exists {
		item = &ChannelModelMetricsItem{
			ChannelID: channelID,
			Model:     model,
			Date:      date,
			Hour:      hour,
		}
		m.Metrics[key] = item
	}

	item.TotalRequests++
	if success {
		item.SuccessRequests++
	} else {
		item.FailedRequests++
	}
	item.TotalResponseTime += responseTime
	item.UpdatedAt = now.Unix()
	item.Dirty = true
}

func (m *MemoryMetricsStorage) PersistToDB() {
	m.Lock()
	dirtyItems := make([]*ChannelModelMetricsItem, 0)
	for _, item := range m.Metrics {
		if item.Dirty {
			dirtyItems = append(dirtyItems, item)
			item.Dirty = false
		}
	}
	m.Unlock()

	// 持久化到数据库
	for _, item := range dirtyItems {
		persistItemToDB(item)
	}
}

func (m *MemoryMetricsStorage) GetMetricsSummary(channelID int, model string, timeRange string) (*ChannelModelMetricsSummary, error) {
	// 直接从数据库获取
	return getChannelMetricsSummaryFromDB(channelID, model, timeRange)
}

// 辅助函数
// 从Redis同步数据到MySQL
func syncRedisToMySQL() {
	ctx := context.Background()

	// 使用SCAN命令获取所有metrics键
	var cursor uint64 = 0
	pattern := "channel_metrics:*"

	for {
		var keys []string
		var err error

		keys, cursor, err = common.RDB.Scan(ctx, cursor, pattern, 100).Result()
		if err != nil {
			logger.SysError(fmt.Sprintf("Failed to scan Redis keys: %v", err))
			return
		}

		// 处理当前批次的键
		for _, key := range keys {
			// 从键名解析出channelID, model, date, hour
			parts := strings.Split(key, ":")
			if len(parts) != 5 {
				continue
			}

			channelID, err := strconv.Atoi(parts[1])
			if err != nil {
				continue
			}

			model := parts[2]
			date := parts[3]
			hour, err := strconv.Atoi(parts[4])
			if err != nil {
				continue
			}

			// 获取Redis中的数据
			fields, err := common.RedisHashGetAll(key)
			if err != nil {
				logger.SysError(fmt.Sprintf("Failed to get hash fields from Redis: %v", err))
				continue
			}

			// 解析字段
			var totalRequests, successRequests, failedRequests, totalResponseTime int64
			for field, value := range fields {
				switch field {
				case "total_requests":
					totalRequests, _ = strconv.ParseInt(value, 10, 64)
				case "success_requests":
					successRequests, _ = strconv.ParseInt(value, 10, 64)
				case "failed_requests":
					failedRequests, _ = strconv.ParseInt(value, 10, 64)
				case "total_response_time":
					totalResponseTime, _ = strconv.ParseInt(value, 10, 64)
				}
			}

			// 更新或创建数据库记录
			updateOrCreateMetricsRecord(channelID, model, date, hour, totalRequests, successRequests, failedRequests, totalResponseTime)
		}

		if cursor == 0 {
			break
		}
	}
}

// 从数据库获取指标汇总
func getChannelMetricsSummaryFromDB(channelID int, model string, timeRange string) (*ChannelModelMetricsSummary, error) {
	now := time.Now()

	// 查询结果
	type QueryResult struct {
		TotalRequests     int64
		SuccessRequests   int64
		FailedRequests    int64
		TotalResponseTime int64
	}

	var result QueryResult
	query := DB.Model(&ChannelModelMetrics{}).
		Where("channel_id = ? AND model = ?", channelID, model)

	// 根据时间范围过滤
	switch timeRange {
	case "hour":
		// 当前小时的数据
		date := now.Format("2006-01-02")
		hour := now.Hour()
		query = query.Where("date = ? AND hour = ?", date, hour)
	case "day":
		// 今天的数据
		date := now.Format("2006-01-02")
		query = query.Where("date = ?", date)
	case "week":
		// 最近7天的数据
		startDate := now.AddDate(0, 0, -7).Format("2006-01-02")
		endDate := now.Format("2006-01-02")
		query = query.Where("date BETWEEN ? AND ?", startDate, endDate)
	}

	query.Select("SUM(total_requests) as total_requests, SUM(success_requests) as success_requests, SUM(failed_requests) as failed_requests, SUM(total_response_time) as total_response_time").
		Scan(&result)

	// 计算平均响应时间和成功率
	avgResponseTime := int64(0)
	successRate := float64(0)

	if result.TotalRequests > 0 {
		avgResponseTime = result.TotalResponseTime / result.TotalRequests
		successRate = float64(result.SuccessRequests) / float64(result.TotalRequests)
	}

	// 计算综合评分
	score := calculateScore(successRate, avgResponseTime)

	summary := &ChannelModelMetricsSummary{
		ChannelID:       channelID,
		Model:           model,
		TimeRange:       timeRange,
		TotalRequests:   result.TotalRequests,
		SuccessRequests: result.SuccessRequests,
		FailedRequests:  result.FailedRequests,
		AvgResponseTime: avgResponseTime,
		SuccessRate:     successRate,
		Score:           score,
	}

	return summary, nil
}

// 从Redis获取指标汇总
func getChannelMetricsSummaryFromRedis(channelID int, model string, timeRange string) (*ChannelModelMetricsSummary, error) {
	ctx := context.Background()
	now := time.Now()

	// 根据时间范围构建键模式
	var keyPattern string
	switch timeRange {
	case "hour":
		// 当前小时
		date := now.Format("2006-01-02")
		hour := now.Hour()
		keyPattern = fmt.Sprintf("channel_metrics:%d:%s:%s:%d", channelID, model, date, hour)
	case "day":
		// 今天所有小时
		date := now.Format("2006-01-02")
		keyPattern = fmt.Sprintf("channel_metrics:%d:%s:%s:*", channelID, model, date)
	case "week":
		// 最近7天
		keyPattern = fmt.Sprintf("channel_metrics:%d:%s:*:*", channelID, model)
	default:
		return nil, fmt.Errorf("unsupported time range: %s", timeRange)
	}

	// 使用SCAN命令获取所有匹配的键
	var keys []string
	var cursor uint64 = 0

	for {
		var scanKeys []string
		var err error

		scanKeys, cursor, err = common.RDB.Scan(ctx, cursor, keyPattern, 100).Result()
		if err != nil {
			return nil, err
		}

		keys = append(keys, scanKeys...)

		if cursor == 0 {
			break
		}
	}

	if len(keys) == 0 {
		return nil, fmt.Errorf("no keys found for pattern: %s", keyPattern)
	}

	// 聚合所有键的数据
	var totalRequests, successRequests, failedRequests, totalResponseTime int64

	for _, key := range keys {
		fields, err := common.RedisHashGetAll(key)
		if err != nil {
			continue
		}

		// 解析并累加字段
		for field, value := range fields {
			switch field {
			case "total_requests":
				val, _ := strconv.ParseInt(value, 10, 64)
				totalRequests += val
			case "success_requests":
				val, _ := strconv.ParseInt(value, 10, 64)
				successRequests += val
			case "failed_requests":
				val, _ := strconv.ParseInt(value, 10, 64)
				failedRequests += val
			case "total_response_time":
				val, _ := strconv.ParseInt(value, 10, 64)
				totalResponseTime += val
			}
		}
	}

	// 计算平均响应时间和成功率
	avgResponseTime := int64(0)
	successRate := float64(0)

	if totalRequests > 0 {
		avgResponseTime = totalResponseTime / totalRequests
		successRate = float64(successRequests) / float64(totalRequests)
	}

	// 计算综合评分
	score := calculateScore(successRate, avgResponseTime)

	return &ChannelModelMetricsSummary{
		ChannelID:       channelID,
		Model:           model,
		TimeRange:       timeRange,
		TotalRequests:   totalRequests,
		SuccessRequests: successRequests,
		FailedRequests:  failedRequests,
		AvgResponseTime: avgResponseTime,
		SuccessRate:     successRate,
		Score:           score,
	}, nil
}

// 将内存项持久化到数据库
func persistItemToDB(item *ChannelModelMetricsItem) {
	// 查找或创建数据库记录
	var metrics ChannelModelMetrics
	result := DB.Where("channel_id = ? AND model = ? AND date = ? AND hour = ?",
		item.ChannelID, item.Model, item.Date, item.Hour).First(&metrics)

	now := time.Now().Unix()
	if result.Error != nil {
		// 创建新记录
		metrics = ChannelModelMetrics{
			ChannelID:         item.ChannelID,
			Model:             item.Model,
			Date:              item.Date,
			Hour:              item.Hour,
			TotalRequests:     item.TotalRequests,
			SuccessRequests:   item.SuccessRequests,
			FailedRequests:    item.FailedRequests,
			TotalResponseTime: item.TotalResponseTime,
			CreatedAt:         now,
			UpdatedAt:         now,
		}

		// 计算平均响应时间和成功率
		if metrics.TotalRequests > 0 {
			metrics.AvgResponseTime = metrics.TotalResponseTime / metrics.TotalRequests
			metrics.SuccessRate = float64(metrics.SuccessRequests) / float64(metrics.TotalRequests)
		}

		// 计算综合评分
		metrics.Score = calculateScore(metrics.SuccessRate, metrics.AvgResponseTime)

		DB.Create(&metrics)
	} else {
		// 更新现有记录
		metrics.TotalRequests += item.TotalRequests
		metrics.SuccessRequests += item.SuccessRequests
		metrics.FailedRequests += item.FailedRequests
		metrics.TotalResponseTime += item.TotalResponseTime
		metrics.UpdatedAt = now

		// 重新计算平均响应时间和成功率
		if metrics.TotalRequests > 0 {
			metrics.AvgResponseTime = metrics.TotalResponseTime / metrics.TotalRequests
			metrics.SuccessRate = float64(metrics.SuccessRequests) / float64(metrics.TotalRequests)
		}

		// 重新计算综合评分
		metrics.Score = calculateScore(metrics.SuccessRate, metrics.AvgResponseTime)

		DB.Save(&metrics)
	}
}

// 更新或创建数据库记录
func updateOrCreateMetricsRecord(channelID int, model string, date string, hour int, totalRequests, successRequests, failedRequests, totalResponseTime int64) {
	var metrics ChannelModelMetrics
	result := DB.Where("channel_id = ? AND model = ? AND date = ? AND hour = ?",
		channelID, model, date, hour).First(&metrics)

	now := time.Now().Unix()
	if result.Error != nil {
		// 创建新记录
		metrics = ChannelModelMetrics{
			ChannelID:         channelID,
			Model:             model,
			Date:              date,
			Hour:              hour,
			TotalRequests:     totalRequests,
			SuccessRequests:   successRequests,
			FailedRequests:    failedRequests,
			TotalResponseTime: totalResponseTime,
			CreatedAt:         now,
			UpdatedAt:         now,
		}

		// 计算平均响应时间和成功率
		if metrics.TotalRequests > 0 {
			metrics.AvgResponseTime = metrics.TotalResponseTime / metrics.TotalRequests
			metrics.SuccessRate = float64(metrics.SuccessRequests) / float64(metrics.TotalRequests)
		}

		// 计算综合评分
		metrics.Score = calculateScore(metrics.SuccessRate, metrics.AvgResponseTime)

		DB.Create(&metrics)
	} else {
		// 更新现有记录
		metrics.TotalRequests = totalRequests
		metrics.SuccessRequests = successRequests
		metrics.FailedRequests = failedRequests
		metrics.TotalResponseTime = totalResponseTime
		metrics.UpdatedAt = now

		// 重新计算平均响应时间和成功率
		if metrics.TotalRequests > 0 {
			metrics.AvgResponseTime = metrics.TotalResponseTime / metrics.TotalRequests
			metrics.SuccessRate = float64(metrics.SuccessRequests) / float64(metrics.TotalRequests)
		}

		// 重新计算综合评分
		metrics.Score = calculateScore(metrics.SuccessRate, metrics.AvgResponseTime)

		DB.Save(&metrics)
	}
}

// 计算综合评分
func calculateScore(successRate float64, avgResponseTime int64) float64 {
	// 响应时间评分 (响应时间越短，评分越高)
	// 使用指数衰减函数，响应时间为0时得分为1，随着响应时间增加而减少
	responseTimeScore := math.Exp(-float64(avgResponseTime) / 5000.0) // 5000ms作为半衰期

	// 成功率评分 (直接使用成功率，范围0-1)
	// 为了放大差异，可以使用幂函数
	successRateScore := math.Pow(successRate, 2)

	// 综合评分 (成功率权重0.7，响应时间权重0.3)
	return 0.7*successRateScore + 0.3*responseTimeScore
}

// 生成缓存键
func getMetricsKey(channelID int, model string, date string, hour int) string {
	return fmt.Sprintf("%d_%s_%s_%d", channelID, model, date, hour)
}

// ChannelModelMetricsSummary 渠道模型性能指标汇总
type ChannelModelMetricsSummary struct {
	ChannelID       int     `json:"channel_id"`
	ChannelName     string  `json:"channel_name"` // 添加渠道名称字段
	Model           string  `json:"model"`
	TimeRange       string  `json:"time_range"` // hour, day, week, all
	TotalRequests   int64   `json:"total_requests"`
	SuccessRequests int64   `json:"success_requests"`
	FailedRequests  int64   `json:"failed_requests"`
	AvgResponseTime int64   `json:"avg_response_time"` // 毫秒
	SuccessRate     float64 `json:"success_rate"`      // 0-1
	Score           float64 `json:"score"`             // 综合评分
}

// GetChannelMetricsSummary 获取渠道模型性能指标汇总的便捷函数
func GetChannelMetricsSummary(channelID int, model string, timeRange string) (*ChannelModelMetricsSummary, error) {
	return GetMetricsStorage().GetMetricsSummary(channelID, model, timeRange)
}

// GetAllChannelMetricsScores 获取所有渠道的性能指标得分
func GetAllChannelMetricsScores(timeRange string, modelFilter string) ([]ChannelModelMetricsSummary, error) {
	// 获取所有活跃渠道
	var channels []Channel
	DB.Where("status = ?", common.ChannelStatusEnabled).Find(&channels)

	var results []ChannelModelMetricsSummary

	// 如果没有指定时间范围，默认为"day"
	if timeRange == "" {
		timeRange = "day"
	}

	// 从数据库直接获取聚合数据
	if modelFilter != "" {
		// 按特定模型过滤
		var metrics []ChannelModelMetrics
		query := DB

		// 根据时间范围构建查询
		now := time.Now()
		switch timeRange {
		case "hour":
			query = query.Where("created_at >= ?", now.Add(-1*time.Hour).Unix())
		case "day":
			query = query.Where("created_at >= ?", now.Add(-24*time.Hour).Unix())
		case "week":
			query = query.Where("created_at >= ?", now.Add(-7*24*time.Hour).Unix())
		case "month":
			query = query.Where("created_at >= ?", now.Add(-30*24*time.Hour).Unix())
		}

		query.Where("model = ?", modelFilter).Find(&metrics)

		// 按渠道ID分组
		channelMetrics := make(map[int][]ChannelModelMetrics)
		for _, m := range metrics {
			channelMetrics[m.ChannelID] = append(channelMetrics[m.ChannelID], m)
		}

		// 计算每个渠道的汇总指标
		for channelID, metrics := range channelMetrics {
			var channelName string
			for _, ch := range channels {
				if ch.Id == channelID {
					channelName = ch.Name
					break
				}
			}

			var totalRequests, successRequests, failedRequests int64
			var totalResponseTime int64

			for _, m := range metrics {
				totalRequests += m.TotalRequests
				successRequests += m.SuccessRequests
				failedRequests += m.FailedRequests
				totalResponseTime += m.TotalResponseTime
			}

			// 计算成功率和平均响应时间
			successRate := 0.0
			if totalRequests > 0 {
				successRate = float64(successRequests) / float64(totalRequests)
			}

			avgResponseTime := int64(0)
			if successRequests > 0 {
				avgResponseTime = totalResponseTime / successRequests
			}

			// 计算综合得分
			score := calculateScore(successRate, avgResponseTime)

			results = append(results, ChannelModelMetricsSummary{
				ChannelID:       channelID,
				ChannelName:     channelName,
				Model:           modelFilter,
				TimeRange:       timeRange,
				TotalRequests:   totalRequests,
				SuccessRequests: successRequests,
				FailedRequests:  failedRequests,
				AvgResponseTime: avgResponseTime,
				SuccessRate:     successRate,
				Score:           score,
			})
		}
	} else {
		// 获取所有模型的数据（不按模型过滤）
		var metrics []ChannelModelMetrics
		query := DB

		// 根据时间范围构建查询
		now := time.Now()
		switch timeRange {
		case "hour":
			query = query.Where("created_at >= ?", now.Add(-1*time.Hour).Unix())
		case "day":
			query = query.Where("created_at >= ?", now.Add(-24*time.Hour).Unix())
		case "week":
			query = query.Where("created_at >= ?", now.Add(-7*24*time.Hour).Unix())
		case "month":
			query = query.Where("created_at >= ?", now.Add(-30*24*time.Hour).Unix())
		}

		query.Find(&metrics)

		// 按渠道ID和模型分组
		channelModelMetrics := make(map[string][]ChannelModelMetrics)
		for _, m := range metrics {
			key := fmt.Sprintf("%d:%s", m.ChannelID, m.Model)
			channelModelMetrics[key] = append(channelModelMetrics[key], m)
		}

		// 计算每个渠道-模型组合的汇总指标
		for key, metrics := range channelModelMetrics {
			parts := strings.Split(key, ":")
			channelID, _ := strconv.Atoi(parts[0])
			model := parts[1]

			var channelName string
			for _, ch := range channels {
				if ch.Id == channelID {
					channelName = ch.Name
					break
				}
			}

			var totalRequests, successRequests, failedRequests int64
			var totalResponseTime int64

			for _, m := range metrics {
				totalRequests += m.TotalRequests
				successRequests += m.SuccessRequests
				failedRequests += m.FailedRequests
				totalResponseTime += m.TotalResponseTime
			}

			// 只有当有请求时才添加到结果中
			if totalRequests > 0 {
				// 计算成功率和平均响应时间
				successRate := 0.0
				if totalRequests > 0 {
					successRate = float64(successRequests) / float64(totalRequests)
				}

				avgResponseTime := int64(0)
				if successRequests > 0 {
					avgResponseTime = totalResponseTime / successRequests
				}

				// 计算综合得分
				score := calculateScore(successRate, avgResponseTime)

				results = append(results, ChannelModelMetricsSummary{
					ChannelID:       channelID,
					ChannelName:     channelName,
					Model:           model,
					TimeRange:       timeRange,
					TotalRequests:   totalRequests,
					SuccessRequests: successRequests,
					FailedRequests:  failedRequests,
					AvgResponseTime: avgResponseTime,
					SuccessRate:     successRate,
					Score:           score,
				})
			}
		}
	}

	// 按得分排序
	sort.Slice(results, func(i, j int) bool {
		return results[i].Score > results[j].Score
	})

	return results, nil
}

// GetAllMetricsModels 获取所有有指标数据的模型
func GetAllMetricsModels() ([]string, error) {
	// 从Redis或数据库中获取所有模型名称
	models, err := GetMetricsStorage().GetAllModels()
	if err != nil {
		return nil, err
	}

	return models, nil
}

// GetChannelModels 获取渠道使用过的所有模型
func GetChannelModels(channelId int) ([]string, error) {
	return GetMetricsStorage().GetChannelModels(channelId)
}

// RedisMetricsStorage 实现 GetAllModels 方法
func (r *RedisMetricsStorage) GetAllModels() ([]string, error) {
	ctx := context.Background()

	// 使用SCAN命令获取所有metrics键
	var cursor uint64 = 0
	pattern := "channel_metrics:*"

	modelSet := make(map[string]struct{})

	for {
		var keys []string
		var err error

		keys, cursor, err = common.RDB.Scan(ctx, cursor, pattern, 100).Result()
		if err != nil {
			return nil, err
		}

		// 从键名中提取模型名称
		for _, key := range keys {
			parts := strings.Split(key, ":")
			if len(parts) >= 3 {
				model := parts[2]
				modelSet[model] = struct{}{}
			}
		}

		if cursor == 0 {
			break
		}
	}

	// 转换为字符串数组
	models := make([]string, 0, len(modelSet))
	for model := range modelSet {
		models = append(models, model)
	}

	return models, nil
}

// RedisMetricsStorage 实现 GetChannelModels 方法
func (r *RedisMetricsStorage) GetChannelModels(channelId int) ([]string, error) {
	ctx := context.Background()

	// 使用SCAN命令获取特定渠道的所有metrics键
	var cursor uint64 = 0
	pattern := fmt.Sprintf("channel_metrics:%d:*", channelId)

	modelSet := make(map[string]struct{})

	for {
		var keys []string
		var err error

		keys, cursor, err = common.RDB.Scan(ctx, cursor, pattern, 100).Result()
		if err != nil {
			return nil, err
		}

		// 从键名中提取模型名称
		for _, key := range keys {
			parts := strings.Split(key, ":")
			if len(parts) >= 3 {
				model := parts[2]
				modelSet[model] = struct{}{}
			}
		}

		if cursor == 0 {
			break
		}
	}

	// 转换为字符串数组
	models := make([]string, 0, len(modelSet))
	for model := range modelSet {
		models = append(models, model)
	}

	return models, nil
}

// MemoryMetricsStorage 实现 GetAllModels 方法
func (m *MemoryMetricsStorage) GetAllModels() ([]string, error) {
	m.RLock()
	defer m.RUnlock()

	modelSet := make(map[string]struct{})

	// 从内存缓存中提取所有模型
	for key, _ := range m.Metrics {
		parts := strings.Split(key, "_")
		if len(parts) >= 2 {
			model := parts[1]
			modelSet[model] = struct{}{}
		}
	}

	// 转换为字符串数组
	models := make([]string, 0, len(modelSet))
	for model := range modelSet {
		models = append(models, model)
	}

	// 如果内存中没有数据，从数据库获取
	if len(models) == 0 {
		var metrics []ChannelModelMetrics
		result := DB.Select("DISTINCT model").Find(&metrics)
		if result.Error != nil {
			return nil, result.Error
		}

		for _, metric := range metrics {
			models = append(models, metric.Model)
		}
	}

	return models, nil
}

// MemoryMetricsStorage 实现 GetChannelModels 方法
func (m *MemoryMetricsStorage) GetChannelModels(channelId int) ([]string, error) {
	m.RLock()
	defer m.RUnlock()

	modelSet := make(map[string]struct{})

	// 从内存缓存中提取特定渠道的所有模型
	for _, item := range m.Metrics {
		if item.ChannelID == channelId {
			modelSet[item.Model] = struct{}{}
		}
	}

	// 转换为字符串数组
	models := make([]string, 0, len(modelSet))
	for model := range modelSet {
		models = append(models, model)
	}

	// 如果内存中没有数据，从数据库获取
	if len(models) == 0 {
		var metrics []ChannelModelMetrics
		result := DB.Select("DISTINCT model").Where("channel_id = ?", channelId).Find(&metrics)
		if result.Error != nil {
			return nil, result.Error
		}

		for _, metric := range metrics {
			models = append(models, metric.Model)
		}
	}

	return models, nil
}

// 在程序启动时初始化Redis缓存
func InitMetricsCache() {
	if !common.RedisEnabled {
		return
	}
	// 获取最近24小时的数据
	var metrics []ChannelModelMetrics
	startTime := time.Now().Add(-24 * time.Hour)

	DB.Where("created_at >= ?", startTime.Unix()).Find(&metrics)

	// 将数据加载到Redis
	for _, metric := range metrics {
		// 构建Redis键
		t := time.Unix(metric.CreatedAt, 0)
		date := t.Format("2006-01-02")
		hour := t.Hour()
		key := fmt.Sprintf("channel_metrics:%d:%s:%s:%d",
			metric.ChannelID, metric.Model, date, hour)

		// 设置Redis哈希表
		ctx := context.Background()
		common.RDB.HSet(ctx, key, map[string]interface{}{
			"total_requests":      metric.TotalRequests,
			"success_requests":    metric.SuccessRequests,
			"failed_requests":     metric.FailedRequests,
			"total_response_time": metric.TotalResponseTime,
			"success_rate":        metric.SuccessRate,
			"last_updated":        metric.UpdatedAt,
		})

		// 设置过期时间（例如7天）
		common.RDB.Expire(ctx, key, 7*24*time.Hour)
	}

	logger.SysLog(fmt.Sprintf("Loaded %d metrics records into Redis cache", len(metrics)))
}

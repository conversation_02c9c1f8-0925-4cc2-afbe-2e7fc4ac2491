package model

import (
	"fmt"
	"sync"

	"github.com/songquanpeng/one-api/common/config"
)

type Midjourney struct {
	Id          int    `json:"id"`
	RequestId   string `json:"request_id" gorm:"type:varchar(64)"`
	Code        int    `json:"code"`
	UserId      int    `json:"user_id" gorm:"index"`
	Mode        string `json:"mode" gorm:"type:varchar(20)"`
	Action      string `json:"action" gorm:"type:varchar(20)"`
	MjId        string `json:"mj_id" gorm:"index;type:varchar(64)"`
	Prompt      string `json:"prompt" gorm:"type:text"`
	PromptEn    string `json:"prompt_en" gorm:"type:text"`
	Description string `json:"description" gorm:"type:text"`
	State       string `json:"state" gorm:"type:text"` // 改为text类型，不限制长度
	SubmitTime  int64  `json:"submit_time"`
	StartTime   int64  `json:"start_time"`
	FinishTime  int64  `json:"finish_time"`
	ImageUrl    string `json:"image_url" gorm:"type:text"` // 改为text类型，不限制长度
	Status      string `json:"status" gorm:"type:varchar(20);index"`
	Progress    string `json:"progress" gorm:"type:varchar(20);index"`
	FailReason  string `json:"fail_reason" gorm:"type:text"`
	Properties  string `json:"properties" gorm:"type:text"`
	Buttons     string `json:"buttons" gorm:"type:text"`
	ChannelId   int    `json:"channel_id"`
	Quota       int    `json:"quota"`
	// Video related fields
	VideoUrl  string `json:"video_url" gorm:"type:text"`  // 单个视频URL
	VideoUrls string `json:"video_urls" gorm:"type:text"` // 多个视频URL，JSON数组格式
}

var deleteMidjourneyLogsLock sync.Mutex
var deleteMidjourneyLogsRunning = false
var TotalShouldDeleteMidjourneyLogsCount int64 = 0
var TotalAffectedMidjourneyLogsCount int64 = 0

func GetAllUserTask(userId int, startIdx int, num int, id int, code int, action string, mjId string, prompt string, promptEn string, description string, state string, submitTimeStart int, submitTimeEnd int, startTimeStart int, startTimeEnd int, finishTimeStart int, finishTimeEnd int, imageUrl string, status string, progress string, failReason string, channelId int, mode string, videoUrl string, videoUrls string) []*Midjourney {
	var tasks []*Midjourney
	var err error
	// 根据条件查询
	tx := DB
	if id != 0 {
		tx = tx.Where("id = ?", id)
	}
	if code != 0 {
		tx = tx.Where("code = ?", code)
	}
	if action != "" {
		tx = tx.Where("action = ?", action)
	}
	if mjId != "" {
		tx = tx.Where("mj_id = ?", mjId)
	}
	if prompt != "" {
		// 模糊
		tx = tx.Where("prompt like ?", "%"+prompt+"%")
	}
	if promptEn != "" {
		// 模糊
		tx = tx.Where("prompt_en like ?", "%"+promptEn+"%")
	}
	if description != "" {
		// 模糊
		tx = tx.Where("description like ?", "%"+description+"%")
	}
	if state != "" {
		tx = tx.Where("state = ?", state)
	}
	if submitTimeStart != 0 {
		tx = tx.Where("submit_time >= ?", submitTimeStart)
	}
	if submitTimeEnd != 0 {
		tx = tx.Where("submit_time <= ?", submitTimeEnd)
	}
	if startTimeStart != 0 {
		tx = tx.Where("start_time >= ?", startTimeStart)
	}
	if startTimeEnd != 0 {
		tx = tx.Where("start_time <= ?", startTimeEnd)
	}
	if finishTimeStart != 0 {
		tx = tx.Where("finish_time >= ?", finishTimeStart)
	}
	if finishTimeEnd != 0 {
		tx = tx.Where("finish_time <= ?", finishTimeEnd)
	}
	if imageUrl != "" {
		tx = tx.Where("image_url = ?", imageUrl)
	}
	if status != "" {
		tx = tx.Where("status = ?", status)
	}
	if progress != "" {
		tx = tx.Where("progress = ?", progress)
	}
	if failReason != "" {
		tx = tx.Where("fail_reason = ?", failReason)
	}
	if channelId != 0 {
		tx = tx.Where("channel_id = ?", channelId)
	}
	if mode != "" {
		tx = tx.Where("mode = ?", mode)
	}
	// 新增视频相关查询条件
	if videoUrl != "" {
		tx = tx.Where("video_url like ?", "%"+videoUrl+"%")
	}
	if videoUrls != "" {
		tx = tx.Where("video_urls like ?", "%"+videoUrls+"%")
	}
	err = tx.Where("user_id = ?", userId).Order("id desc").Limit(num).Offset(startIdx).Find(&tasks).Error
	if err != nil {
		return nil
	}
	for _, task := range tasks {
		task.ImageUrl = config.GetMjUrlByTaskIdDefaultOriginUrl(task.MjId, task.ImageUrl)
		// 处理视频URL，如果有视频URL则使用类似的处理方式
		if task.VideoUrl != "" {
			task.VideoUrl = config.GetMjUrlByTaskIdDefaultOriginUrl(task.MjId, task.VideoUrl)
		}
	}
	return tasks
}

func CountAllUserTask(userId int, id int, code int, action string, mjId string, prompt string, promptEn string, description string, state string, submitTimeStart int, submitTimeEnd int, startTimeStart int, startTimeEnd int, finishTimeStart int, finishTimeEnd int, imageUrl string, status string, progress string, failReason string, channelId int, mode string, videoUrl string, videoUrls string) (count int64, err error) {
	tx := DB.Model(&Midjourney{}).Where("user_id = ?", userId)
	if id != 0 {
		tx = tx.Where("id = ?", id)
	}
	if code != 0 {
		tx = tx.Where("code = ?", code)
	}
	if action != "" {
		tx = tx.Where("action = ?", action)
	}
	if mjId != "" {
		tx = tx.Where("mj_id = ?", mjId)
	}
	if prompt != "" {
		// 模糊
		tx = tx.Where("prompt like ?", "%"+prompt+"%")
	}
	if promptEn != "" {
		// 模糊
		tx = tx.Where("prompt_en like ?", "%"+promptEn+"%")
	}
	if description != "" {
		// 模糊
		tx = tx.Where("description like ?", "%"+description+"%")
	}
	if state != "" {
		tx = tx.Where("state = ?", state)
	}
	if submitTimeStart != 0 {
		tx = tx.Where("submit_time >= ?", submitTimeStart)
	}
	if submitTimeEnd != 0 {
		tx = tx.Where("submit_time <= ?", submitTimeEnd)
	}
	if startTimeStart != 0 {
		tx = tx.Where("start_time >= ?", startTimeStart)
	}
	if startTimeEnd != 0 {
		tx = tx.Where("start_time <= ?", startTimeEnd)
	}
	if finishTimeStart != 0 {
		tx = tx.Where("finish_time >= ?", finishTimeStart)
	}
	if finishTimeEnd != 0 {
		tx = tx.Where("finish_time <= ?", finishTimeEnd)
	}
	if imageUrl != "" {
		tx = tx.Where("image_url = ?", imageUrl)
	}
	if status != "" {
		tx = tx.Where("status = ?", status)
	}
	if progress != "" {
		tx = tx.Where("progress = ?", progress)
	}
	if failReason != "" {
		tx = tx.Where("fail_reason = ?", failReason)
	}
	if channelId != 0 {
		tx = tx.Where("channel_id = ?", channelId)
	}
	if mode != "" {
		tx = tx.Where("mode = ?", mode)
	}
	// 新增视频相关查询条件
	if videoUrl != "" {
		tx = tx.Where("video_url like ?", "%"+videoUrl+"%")
	}
	if videoUrls != "" {
		tx = tx.Where("video_urls like ?", "%"+videoUrls+"%")
	}
	err = tx.Count(&count).Error
	return count, err
}

func GetAllTasks(startIdx int, num int, id int, userId int, code int, action string, mjId string, prompt string, promptEn string, description string, state string, submitTimeStart int, submitTimeEnd int, startTimeStart int, startTimeEnd int, finishTimeStart int, finishTimeEnd int, imageUrl string, status string, progress string, failReason string, channelId int, mode string, videoUrl string, videoUrls string) []*Midjourney {
	var tasks []*Midjourney
	var err error
	// 根据条件查询
	tx := DB
	if id != 0 {
		tx = tx.Where("id = ?", id)
	}
	if userId != 0 {
		tx = tx.Where("user_id = ?", userId)
	}
	if code != 0 {
		tx = tx.Where("code = ?", code)
	}
	if action != "" {
		tx = tx.Where("action = ?", action)
	}
	if mjId != "" {
		tx = tx.Where("mj_id = ?", mjId)
	}
	if prompt != "" {
		// 模糊
		tx = tx.Where("prompt like ?", "%"+prompt+"%")
	}
	if promptEn != "" {
		// 模糊
		tx = tx.Where("prompt_en like ?", "%"+promptEn+"%")
	}
	if description != "" {
		// 模糊
		tx = tx.Where("description like ?", "%"+description+"%")
	}
	if state != "" {
		tx = tx.Where("state = ?", state)
	}
	if submitTimeStart != 0 {
		tx = tx.Where("submit_time >= ?", submitTimeStart)
	}
	if submitTimeEnd != 0 {
		tx = tx.Where("submit_time <= ?", submitTimeEnd)
	}
	if startTimeStart != 0 {
		tx = tx.Where("start_time >= ?", startTimeStart)
	}
	if startTimeEnd != 0 {
		tx = tx.Where("start_time <= ?", startTimeEnd)
	}
	if finishTimeStart != 0 {
		tx = tx.Where("finish_time >= ?", finishTimeStart)
	}
	if finishTimeEnd != 0 {
		tx = tx.Where("finish_time <= ?", finishTimeEnd)
	}
	if imageUrl != "" {
		tx = tx.Where("image_url = ?", imageUrl)
	}
	if status != "" {
		tx = tx.Where("status = ?", status)
	}
	if progress != "" {
		tx = tx.Where("progress = ?", progress)
	}
	if failReason != "" {
		tx = tx.Where("fail_reason = ?", failReason)
	}
	if channelId != 0 {
		tx = tx.Where("channel_id = ?", channelId)
	}
	if mode != "" {
		tx = tx.Where("mode = ?", mode)
	}
	// 新增视频相关查询条件
	if videoUrl != "" {
		tx = tx.Where("video_url like ?", "%"+videoUrl+"%")
	}
	if videoUrls != "" {
		tx = tx.Where("video_urls like ?", "%"+videoUrls+"%")
	}
	err = tx.Order("id desc").Limit(num).Offset(startIdx).Find(&tasks).Error
	if err != nil {
		return nil
	}
	for _, task := range tasks {
		task.ImageUrl = config.ServerAddress + "/mj/image/" + task.MjId
		// 视频URL暂时保持原样，不做特殊处理
		// 后续如果需要代理，可以参考图片URL的处理方式
	}
	return tasks
}

func CountAllTasks(id int, userId int, code int, action string, mjId string, prompt string, promptEn string, description string, state string, submitTimeStart int, submitTimeEnd int, startTimeStart int, startTimeEnd int, finishTimeStart int, finishTimeEnd int, imageUrl string, status string, progress string, failReason string, channelId int, mode string, videoUrl string, videoUrls string) (count int64, err error) {
	tx := DB.Model(&Midjourney{})
	if id != 0 {
		tx = tx.Where("id = ?", id)
	}
	if userId != 0 {
		tx = tx.Where("user_id = ?", userId)
	}
	if code != 0 {
		tx = tx.Where("code = ?", code)
	}
	if action != "" {
		tx = tx.Where("action = ?", action)
	}
	if mjId != "" {
		tx = tx.Where("mj_id = ?", mjId)
	}
	if prompt != "" {
		// 模糊
		tx = tx.Where("prompt like ?", "%"+prompt+"%")
	}
	if promptEn != "" {
		// 模糊
		tx = tx.Where("prompt_en like ?", "%"+promptEn+"%")
	}
	if description != "" {
		// 模糊
		tx = tx.Where("description like ?", "%"+description+"%")
	}
	if state != "" {
		tx = tx.Where("state = ?", state)
	}
	if submitTimeStart != 0 {
		tx = tx.Where("submit_time >= ?", submitTimeStart)
	}
	if submitTimeEnd != 0 {
		tx = tx.Where("submit_time <= ?", submitTimeEnd)
	}
	if startTimeStart != 0 {
		tx = tx.Where("start_time >= ?", startTimeStart)
	}
	if startTimeEnd != 0 {
		tx = tx.Where("start_time <= ?", startTimeEnd)
	}
	if finishTimeStart != 0 {
		tx = tx.Where("finish_time >= ?", finishTimeStart)
	}
	if finishTimeEnd != 0 {
		tx = tx.Where("finish_time <= ?", finishTimeEnd)
	}
	if imageUrl != "" {
		tx = tx.Where("image_url = ?", imageUrl)
	}
	if status != "" {
		tx = tx.Where("status = ?", status)
	}
	if progress != "" {
		tx = tx.Where("progress = ?", progress)
	}
	if failReason != "" {
		tx = tx.Where("fail_reason = ?", failReason)
	}
	if channelId != 0 {
		tx = tx.Where("channel_id = ?", channelId)
	}
	if mode != "" {
		tx = tx.Where("mode = ?", mode)
	}
	// 新增视频相关查询条件
	if videoUrl != "" {
		tx = tx.Where("video_url like ?", "%"+videoUrl+"%")
	}
	if videoUrls != "" {
		tx = tx.Where("video_urls like ?", "%"+videoUrls+"%")
	}
	err = tx.Count(&count).Error
	return count, err
}

func GetAllUnFinishTasks() []*Midjourney {
	var tasks []*Midjourney
	var err error
	// get all tasks progress is not 100%
	err = DB.Where("progress != ? and mj_id is not null and mj_id != '' ", "100%").Find(&tasks).Error
	if err != nil {
		return nil
	}
	return tasks
}

func GetByOnlyMJId(mjId string) *Midjourney {
	var mj *Midjourney
	var err error
	err = DB.Where("mj_id = ?", mjId).First(&mj).Error
	if err != nil {
		return nil
	}
	return mj
}

func GetByMJId(userId int, mjId string) *Midjourney {
	var mj *Midjourney
	var err error
	err = DB.Where("user_id = ? and mj_id = ?", userId, mjId).First(&mj).Error
	if err != nil {
		return nil
	}
	return mj
}

func GetMjByuId(id int) *Midjourney {
	var mj *Midjourney
	var err error
	err = DB.Where("id = ?", id).First(&mj).Error
	if err != nil {
		return nil
	}
	return mj
}

func UpdateProgress(id int, progress string) error {
	return DB.Model(&Midjourney{}).Where("id = ?", id).Update("progress", progress).Error
}

func (midjourney *Midjourney) Insert() error {
	var err error
	err = DB.Create(midjourney).Error
	return err
}

func (midjourney *Midjourney) Update() error {
	var err error
	err = DB.Save(midjourney).Error
	return err
}

func DeleteOldMidjourneyLogs(targetTimestamp int64) (int64, error) {
	var totalDeleted int64
	batchSize := 1000 // 每批删除的数据量

	targetTimestamp = targetTimestamp * 1000 // 转换为毫秒
	// 统计需要删除的总条数
	err := DB.Model(&Midjourney{}).Where("submit_time < ?", targetTimestamp).Count(&TotalShouldDeleteMidjourneyLogsCount).Error
	if err != nil {
		return 0, err
	}

	// 分批删除数据
	for {
		result := DB.Where("submit_time < ?", targetTimestamp).Limit(batchSize).Delete(&Midjourney{})
		if result.Error != nil {
			return totalDeleted, result.Error
		}

		rowsAffected := result.RowsAffected
		totalDeleted += rowsAffected
		TotalAffectedMidjourneyLogsCount = totalDeleted

		if rowsAffected < int64(batchSize) {
			break
		}
	}

	return totalDeleted, nil
}

func DeleteMidjourneyLogs(targetTimestamp int64, userId int, channelId int) (int64, error) {
	deleteMidjourneyLogsLock.Lock()
	if deleteMidjourneyLogsRunning {
		deleteMidjourneyLogsLock.Unlock()
		return 0, fmt.Errorf("已有一个删除Midjourney日志任务在运行中")
	}
	deleteMidjourneyLogsRunning = true
	TotalShouldDeleteMidjourneyLogsCount = 0
	TotalAffectedMidjourneyLogsCount = 0
	deleteMidjourneyLogsLock.Unlock()

	defer func() {
		deleteMidjourneyLogsLock.Lock()
		deleteMidjourneyLogsRunning = false
		deleteMidjourneyLogsLock.Unlock()
	}()

	query := DB.Where("submit_time < ?", targetTimestamp)
	if userId != 0 {
		query = query.Where("user_id = ?", userId)
	}
	if channelId != 0 {
		query = query.Where("channel_id = ?", channelId)
	}

	// 统计需要删除的总条数
	err := query.Model(&Midjourney{}).Count(&TotalShouldDeleteMidjourneyLogsCount).Error
	if err != nil {
		return 0, err
	}

	var totalDeleted int64
	batchSize := 1000 // 每批删除的数据量

	for {
		result := query.Limit(batchSize).Delete(&Midjourney{})
		if result.Error != nil {
			return totalDeleted, result.Error
		}

		rowsAffected := result.RowsAffected
		totalDeleted += rowsAffected
		TotalAffectedMidjourneyLogsCount = totalDeleted

		if rowsAffected < int64(batchSize) {
			break
		}
	}

	return totalDeleted, nil
}

// 添加一个新的函数来获取删除进度
func GetDeleteMidjourneyLogsProgress() (int, string) {
	if !deleteMidjourneyLogsRunning {
		return 0, "没有正在进行的删除任务"
	}

	progress := 0
	if TotalShouldDeleteMidjourneyLogsCount > 0 {
		progress = int(float64(TotalAffectedMidjourneyLogsCount) / float64(TotalShouldDeleteMidjourneyLogsCount) * 100)
	}

	message := fmt.Sprintf("已删除 %d 条记录，共 %d 条记录", TotalAffectedMidjourneyLogsCount, TotalShouldDeleteMidjourneyLogsCount)
	return progress, message
}

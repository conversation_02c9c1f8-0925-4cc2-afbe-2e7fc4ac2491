package model

import (
	"errors"
	"time"

	"gorm.io/gorm"
)

const (
	// Transaction Types
	TransactionTypeUnknown = iota
	TransactionTypeAgencyBonus
	TransactionTypeConsumption
	TransactionTypeRefund
	TransactionTypeInviteBonus
	TransactionTypeWithdrawal
	// ... 可以根据需要添加更多类型

)
const (
	// Transaction Status
	TransactionStatusUnknown = iota
	TransactionStatusPending
	TransactionStatusSuccess
	TransactionStatusFailed
	TransactionStatusCancelled
	// ... 可以根据需要添加更多状态
)

type CashTransaction struct {
	Id            int     `json:"id" gorm:"primaryKey"`
	UserId        int     `json:"user_id" gorm:"index"`
	RelatedUserId int     `json:"related_user_id" gorm:"index"`
	Type          int     `json:"type" gorm:"index"` // 使用数字代表类型
	Amount        float64 `json:"amount"`
	Balance       float64 `json:"balance"`
	SourceAmount  float64 `json:"source_amount"`
	RelatedId     string  `json:"related_id" gorm:"index"`
	Status        int     `json:"status" gorm:"index"` // 使用数字代表状态
	CreatedAt     int64   `json:"created_at" gorm:"bigint;index"`
	Description   string  `json:"description"`
}

func CreateCashTransaction(tx *CashTransaction) error {
	result := DB.Create(tx)
	return result.Error
}

func GetCashTransactionsByUserId(userId int, startIdx int, num int) (transactions []*CashTransaction, err error) {
	err = DB.Where("user_id = ?", userId).Order("created_at desc").Limit(num).Offset(startIdx).Find(&transactions).Error
	return transactions, err
}

func CountCashTransactionsByUserId(userId int) (count int64, err error) {
	err = DB.Model(&CashTransaction{}).Where("user_id = ?", userId).Count(&count).Error
	return count, err
}

func GetCashTransactionById(id int) (*CashTransaction, error) {
	var tx CashTransaction
	err := DB.First(&tx, id).Error
	if err != nil {
		return nil, err
	}
	return &tx, nil
}

func UpdateCashTransactionStatus(id int, status string) error {
	return DB.Model(&CashTransaction{}).Where("id = ?", id).Update("status", status).Error
}

func GetUserBalance(userId int) (float64, error) {
	var lastTransaction CashTransaction
	err := DB.Where("user_id = ?", userId).Order("created_at desc").First(&lastTransaction).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, nil
		}
		return 0, err
	}
	return lastTransaction.Balance, nil
}

func CreateCashTransactionAndUpdateBalance(tx *gorm.DB, userId int, amount float64, txType int, relatedId string, description string) error {
	return tx.Transaction(func(tx *gorm.DB) error {
		var currentBalance float64
		err := tx.Model(&CashTransaction{}).Where("user_id = ?", userId).Order("created_at desc").Select("balance").Row().Scan(&currentBalance)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}

		newBalance := currentBalance + amount
		cashTx := &CashTransaction{
			UserId:      userId,
			Type:        txType,
			Amount:      amount,
			Balance:     newBalance,
			RelatedId:   relatedId,
			Status:      TransactionStatusSuccess,
			CreatedAt:   time.Now().Unix(),
			Description: description,
		}

		if err := tx.Create(cashTx).Error; err != nil {
			return err
		}

		return nil
	})
}

func GetAgencyCommissions(agencyId int, startIdx int, num int) (transactions []*CashTransaction, err error) {
	err = DB.Where("user_id = ? AND type = ?", agencyId, TransactionTypeAgencyBonus).Order("created_at desc").Limit(num).Offset(startIdx).Find(&transactions).Error
	return transactions, err
}

func CountAgencyCommissions(agencyId int) (count int64, err error) {
	err = DB.Model(&CashTransaction{}).Where("user_id = ? AND type = ?", agencyId, TransactionTypeAgencyBonus).Count(&count).Error
	return count, err
}

func GetUserCommissionsForAgency(agencyId, userId int, startIdx int, num int) (transactions []*CashTransaction, err error) {
	err = DB.Where("user_id = ? AND related_user_id = ? AND type = ?", agencyId, userId, TransactionTypeAgencyBonus).Order("created_at desc").Limit(num).Offset(startIdx).Find(&transactions).Error
	return transactions, err
}

func CountUserCommissionsForAgency(agencyId, userId int) (count int64, err error) {
	err = DB.Model(&CashTransaction{}).Where("user_id = ? AND related_user_id = ? AND type = ?", agencyId, userId, TransactionTypeAgencyBonus).Count(&count).Error
	return count, err
}
func CountCashTransactions(userId int, transactionType string) (int64, error) {
	var count int64
	query := DB.Model(&CashTransaction{})

	if userId != 0 {
		query = query.Where("user_id = ?", userId)
	}

	if transactionType != "" {
		query = query.Where("type = ?", transactionType)
	}

	err := query.Count(&count).Error
	return count, err
}
func GetCashTransactions(offset int, limit int, userId int, transactionType string) ([]*CashTransaction, error) {
	var transactions []*CashTransaction
	query := DB.Model(&CashTransaction{})

	if userId != 0 {
		query = query.Where("user_id = ?", userId)
	}

	if transactionType != "" {
		query = query.Where("type = ?", transactionType)
	}

	err := query.Order("created_at desc").Offset(offset).Limit(limit).Find(&transactions).Error
	return transactions, err
}
